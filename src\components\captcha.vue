<template>
  <div>
    <div class="login-item group">
      <div class="input-wrap">
        <input type="text" autocomplete="off" placeholder="请输入图形验证码" v-model.trim="captcha"/>
      </div>
      <img class="captcha" :src="captchaUrl" alt="" @click="changeCaptcha"/>
    </div>
    <div class="login-item group">
      <div class="input-wrap">
        <input type="text" autocomplete="off" placeholder="请输入验证码" v-model.trim="valicode"/>
      </div>
      <btn-captcha class="btn-captcha" v-on:run="getValicode" ref="btnCaptcha"/>
    </div>
  </div>
</template>

<script>
  import {isphone} from '../common'
  import btnCaptcha from '@/components/btnCaptcha'
  
  export default {
    props:['mobile'],
    data() {
      return {
        captcha: '',    // 图形验证码
        valicode: '',   // 手机验证码
        timestamp: '',
      }
    },
    created(){
      this.timestamp=Date.now();
    },
    computed: {
      // 图形验证码
      captchaUrl: function () {
        return isphone(this.mobile) ? `/bcc/captcha?mobile=${this.mobile}&t=${this.timestamp}` : require('../assets/image/captcha.png');
      },
    },
    methods: {
      // 换一张验证码
      changeCaptcha: function () {
        this.timestamp = new Date().getTime();
        this.captcha = '';
      },
      // 获取手机验证码
      getValicode: function () {
        if (!isphone(this.mobile)) {
          this.$modal({message: '请输入手机号码', icon: 'warning'});
          return;
        }
        if (!this.captcha) {
          this.$modal({message: '请输入图形验证码', icon: 'warning'});
          return;
        }
  
        this.$refs.btnCaptcha.start();
        this.$http.post('/us/authCode/1.0/', {mobile: this.mobile, code: this.captcha}).then(res => {
          res.code !== '00' && this.$refs.btnCaptcha.clearTimer();
        });
      },
    },
    watch: {
      valicode: function () {
        this.$emit('input', this.valicode);
      }
    },
    components: {btnCaptcha}
  }
</script>

<style lang="less" scoped>
  .btn-captcha{
    width:1.28rem; height:.44rem; color:#fff; font-size:.16rem; border:none; background-color:#4e4f54; border-radius:.02rem;
    &:disabled{ opacity:.8; }
  }
</style>
