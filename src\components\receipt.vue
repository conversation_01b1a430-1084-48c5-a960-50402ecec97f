<template>
  <div id="receiptWrap" class="receipt-wrap" v-if="isShow" @click="isShow = false">
    <div class="receipt-cont" v-show="showReceipt">
      <div id="receipt" class="receipt rela">
        <!--
          自考的展示逻辑走接口
        -->
        <div class="receipt-t" v-if="receiptData.recruitType == '4' || receiptData.recruitType == '1'">
          <h2>{{ title }}</h2>
        </div>
        <div class="receipt-t" v-else>
          <h2>{{ ['国家开放大学', '广州城建职业学院', '汕尾职业技术学院'].includes(receiptData.unvsName) && receiptData.receiptRecruitYear.includes(parseInt(receiptData.grade)) ? '' : '代' }}收费收据</h2>
        </div>
        <div class="receipt-t2 cl">
          <div class="fl">
            <span>缴费日期:</span>
            <span v-html="payTime"></span>
          </div>
          <div class="fr mr-10">
            <span>收据编号：</span>
            <span>{{ receiptData.payNo.replace(/[A-Za-z]/g, '') }}</span>
          </div>
        </div>
        <div class="receipt-m rela">
          <table class="receipt-m-table">
            <tr>
              <td>姓名</td>
              <td>{{ receiptData.stdName }}</td>
              <template v-if="receiptData.recruitType == '4'">
                <td>类型</td>
                <td>考前辅导费</td>
              </template>
              <template v-else>
                <td>院校</td>
                <td>{{ receiptData.unvsName }}</td>
              </template>
              <td>报考层次</td>
              <td>{{ receiptData.pfsnLevel | findDict('pfsnLevel') }}</td>
            </tr>
            <tr>
              <td>专业</td>
              <td colspan="3">{{ receiptData.pfsnName }}</td>
              <td>年级</td>
              <td>{{ receiptData.grade }}{{ receiptData.grade !== '2022研' ? '级' : '' }}</td>
            </tr>
          </table>
          <table class="receipt-m-table2">
            <thead>
              <tr>
                <th>序号</th>
                <th>缴费项目</th>
                <th>收款金额</th>
                <th>收款方式</th>
              </tr>
            </thead>
            <tbody>
              <!--<tr v-for="(item,index) in receiptData.payInfos">-->
              <!--<td>{{+index+1}}</td>-->
              <!--<td>{{item.itemCode}}:{{item.itemName}}</td>-->
              <!--<td>{{item.payable}}</td>-->
              <!--<td>{{receiptData.paymentType|findDict('paymentType')}}</td>-->
              <!--</tr>-->
              <tr>
                <td>{{ 1 }}</td>
                <td>{{ receiptData.itemCode }}<span v-if="receiptData.itemCode">:</span>{{ receiptData.itemName }}</td>
                <td>{{ receiptData.payable }}</td>
                <td>线上支付</td>
              </tr>
              <tr v-if="deduction > 0">
                <td></td>
                <td>抵扣</td>
                <td>-{{ deduction }}</td>
                <td></td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="2">合计</td>
                <td>{{ facePay.toFixed(2) }}</td>
                <td></td>
              </tr>
            </tfoot>
          </table>
          <!--<div class="ab">-->
          <!--<span>白联</span><br><span>:</span><br><span>记账</span>-->
          <!--<span class="mt-10">红联</span><br><span>:</span><br><span>客户</span>-->
          <!--</div>-->
        </div>
        <div class="receipt-b">
          <div class="receipt-b-t cl">
            <div class="fl">
              <span>实收金额合计:</span>
              <span style="display: inline">人民币{{ payWord }}</span>
            </div>
            <div class="fr">
              <span>收款单位:</span>
              <span style="display: inline-block; width: 550px">{{ receiptData.financeCode }}</span>
              <!--<span>收款人:</span><span>{{receiptData.empName}}</span>-->
            </div>
          </div>
          <div class="receipt-b-b cl fl mr-10">
            <span>服务热线:&nbsp;400&nbsp;833&nbsp;6013</span>
          </div>
        </div>
        <div class="ab">
          <img :src="seal" alt="" crossorigin="anonymous" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import html2canvas from 'html2canvas'
import DG from '../assets/image/seals/2.png'
import HY from '../assets/image/seals/6.png'
import HZ from '../assets/image/seals/10.png'
import MZ from '../assets/image/seals/15.png'
import ZQ from '../assets/image/seals/39.png'
import YJ from '../assets/image/seals/40.png'
import ZB from '../assets/image/seals/42.png'
import SW from '../assets/image/seals/43.png'
import MM from '../assets/image/seals/44.png'
import ZJ from '../assets/image/seals/45.png'
import QY from '../assets/image/seals/53.png'
import CZ from '../assets/image/seals/54.png'
import GZ from '../assets/image/seals/1754661637535500494.png'

export default {
  data() {
    return {
      title: '',
      showReceipt: true,
      isShow: false,
      deduction: 0,
      receiptData: {},
      payTime: '',
      seal: '',
      homeCampusIds: {
        '25390594352339026': GZ,
        '25390594352339027': ZQ,
        25390594352339028: ZJ,
        '25390594352339029': YJ,
        25390594352339030: SW,
        '25390594352339031': MZ,
        '1754780369116522888': HZ,
        '1754780369116522889': ZB,
        '1754780369116522890': HY,
        '1754780369116522891': DG,
        '1754780369116522892': CZ,
        '1754780369116805119': DG
      },
      campusIds: {
        2: DG,
        6: HY,
        10: HZ,
        15: MZ,
        39: ZQ,
        40: YJ,
        42: ZB,
        43: SW,
        44: MM,
        45: ZJ,
        53: QY,
        54: CZ,
        '1754661637535500494': GZ
      },
      facePay: null,
      payWord: ''
    }
  },
  methods: {
    open: function (data) {
      this.receiptData = data || {}
      console.log(this.receiptData, 'this.receiptData')
      this.payTime = this.receiptData.payTime
      if (this.payTime) this.payTime = this.payTime.replace(' ', '&nbsp;') // 解决ios系统空格不显示问题
      this.$indicator.open()
      this.showReceipt = true
      this.isShow = true

      // 自考 收款单位、tile及公章; 20250703 接口兼容成教类型
      if (data.recruitType == '4' || data.recruitType == '1') {
        this.getReceiptBySubOrderNo()
      } else if (data.grade.startsWith('202') || data.grade.startsWith('20190') || (data.grade == '2019' && data.itemCode != 'Y0')) {
        //201903及以后的所有国开费用，2019级的学费书费和2020级及以后的所有费用
        this.seal = require('../assets/image/seals/zb.png')
        this.receiptData.financeCode = '广州远智教育科技有限公司'
        this.loadSeal(() => {
          this.$nextTick(() => {
            html2canvas(document.getElementById('receipt'), { width: 1666, height: 660, useCORS: true }).then(canvas => {
              this.$indicator.close()
              this.$emit('showImg', canvas.toDataURL())
              this.isShow = false
            })
          })
        })
      } else {
        this.seal = this.homeCampusIds[this.receiptData.homeCampusId] || this.campusIds[this.receiptData.campusId] || ZB
        this.loadSeal(() => {
          this.$nextTick(() => {
            html2canvas(document.getElementById('receipt'), { width: 1666, height: 660, useCORS: true }).then(canvas => {
              this.$indicator.close()
              this.$emit('showImg', canvas.toDataURL())
              this.isShow = false
            })
          })
        })
      }
      this.getDeduction()
    },
    // 获取自考发票信息 发票标题、印章、收款单位
    getReceiptBySubOrderNo() {
      this.$http.post('/bds/getReceiptBySubOrderNo/1.0/', { subOrderNo: this.receiptData.subOrderNo }).then(res => {
        if (res.code !== '00') return
        this.seal = res.body.companyFinanceSeal
        this.receiptData.financeCode = res.body.companyName
        this.title = res.body.title
        this.loadSeal(() => {
          this.$nextTick(() => {
            html2canvas(document.getElementById('receipt'), { width: 1666, height: 660, useCORS: true }).then(canvas => {
              this.$indicator.close()
              this.$emit('showImg', canvas.toDataURL())
              this.isShow = false
            })
          })
        })
      })
    },
    // 加载印章
    loadSeal: function (callback) {
      let img = new Image()
      img.src = this.seal
      img.onload = () => {
        setTimeout(() => {
          callback()
        }, 200)
      }
    },
    // 计算现金抵扣、智米抵扣的值
    // getDeduction: function () {
    //   let subSerials = this.receiptData.subSerials || [];
    //   let amount = (parseInt(this.receiptData.deduction) || 0) * 100;
    //   for (let item of subSerials) {
    //     if (item.accType === '3') { // 滞留金抵扣
    //       amount += +item.amount * 100;
    //     } else if (item.accType === '2') {  // 智米抵扣
    //       amount += +item.amount;
    //     }
    //   }
    //   this.deduction = (amount / 100).toFixed(2);
    // }
    getDeduction: function () {
      //智米抵扣
      let zmdeduction = parseFloat(this.receiptData.zmScale) || 0
      //滞留金抵扣
      let demurrageScale = parseFloat(this.receiptData.demurrageScale) || 0
      //优惠券抵扣
      let couponScale = parseFloat(this.receiptData.couponScale) || 0
      this.facePay = Number(this.receiptData.payAmount) + Number(demurrageScale)
      this.payWord = this.getpay(this.facePay)
      //  this.facePay=this.receiptData.payable+demurrageScale
      this.deduction = (zmdeduction + couponScale).toFixed(2)
    },

    getpay(money) {
      //汉字的数字
      var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖')
      //基本单位
      var cnIntRadice = new Array('', '拾', '佰', '仟')
      //对应整数部分扩展单位
      var cnIntUnits = new Array('', '万', '亿', '兆')
      //对应小数部分单位
      var cnDecUnits = new Array('角', '分', '毫', '厘')
      //整数金额时后面跟的字符
      var cnInteger = '整'
      //整型完以后的单位
      var cnIntLast = '元'
      //最大处理的数字
      var maxNum = 999999999999999.9999
      //金额整数部分
      var integerNum
      //金额小数部分
      var decimalNum
      //输出的中文金额字符串
      var chineseStr = ''
      //分离金额后用的数组，预定义
      var parts
      if (money == '') {
        return ''
      }
      money = parseFloat(money)
      if (money >= maxNum) {
        //超出最大处理数字
        return ''
      }
      if (money == 0) {
        chineseStr = cnNums[0] + cnIntLast + cnInteger
        return chineseStr
      }
      //转换为字符串
      money = money.toString()
      if (money.indexOf('.') == -1) {
        integerNum = money
        decimalNum = ''
      } else {
        parts = money.split('.')
        integerNum = parts[0]
        decimalNum = parts[1].substr(0, 4)
      }
      //获取整型部分转换
      if (parseInt(integerNum, 10) > 0) {
        var zeroCount = 0
        var IntLen = integerNum.length
        for (var i = 0; i < IntLen; i++) {
          var n = integerNum.substr(i, 1)
          var p = IntLen - i - 1
          var q = p / 4
          var m = p % 4
          if (n == '0') {
            zeroCount++
          } else {
            if (zeroCount > 0) {
              chineseStr += cnNums[0]
            }
            //归零
            zeroCount = 0
            chineseStr += cnNums[parseInt(n)] + cnIntRadice[m]
          }
          if (m == 0 && zeroCount < 4) {
            chineseStr += cnIntUnits[q]
          }
        }
        chineseStr += cnIntLast
      }
      //小数部分
      if (decimalNum != '') {
        var decLen = decimalNum.length
        for (var i = 0; i < decLen; i++) {
          var n = decimalNum.substr(i, 1)
          if (n != '0') {
            chineseStr += cnNums[Number(n)] + cnDecUnits[i]
          }
        }
      }
      if (chineseStr == '') {
        chineseStr += cnNums[0] + cnIntLast + cnInteger
      } else if (decimalNum == '') {
        chineseStr += cnInteger
      }
      return chineseStr
    }
  }
}
</script>

<style lang="less" scoped>
.receipt-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  overflow: auto;
}

.mr-10 {
  margin-right: 20px;
}

.mt-10 {
  margin-top: 20px;
}

.rela {
  position: relative;
}

.ab {
  position: absolute;
}

.receipt-cont {
  opacity: 0;
}

.receipt {
  width: 1666px;
  height: 660px;
  padding: 10px 60px 40px;
  color: #000;
  font-size: 28px;
  font-family: 'sans-serif';
  background-color: #fff;
}

.receipt h2 {
  margin: 0;
  line-height: 1.5;
  text-align: center;
  letter-spacing: 30px;
  color: #000;
  font-size: 40px;
  font-weight: 400;
}

.receipt span {
  color: #000;
}

.receipt > .ab {
  width: 300px;
  height: 300px;
  bottom: 50px;
  right: 350px;
}

.receipt > .ab img {
  width: 100%;
}

.receipt-t {
  width: 350px;
  margin: 0 auto;
  border-bottom: 1px solid #000;
}

.receipt-t2 .fl {
  margin-right: 60px;
}

.receipt-t2 span {
  margin-right: 20px;
  color: #000;
}

.receipt-m table {
  width: 1500px;
  border-collapse: collapse;
  margin-top: 20px;
}

.receipt-m table td,
.receipt-m table th {
  height: 50px;
  padding: 0;
  line-height: 50px;
  color: #383838;
  border: 1px solid #000;
  border-top: 2px solid #000;
}

.receipt-m table tr:nth-of-type(1) td {
  border-top: 3px solid #000;
}

.receipt-m .receipt-m-table2 tr:nth-of-type(1) td {
  border-top: 2px solid #000;
}

.receipt-m .receipt-m-table2 th {
  font-weight: 500;
  border-top: 3px solid #000;
}

.receipt-m table td:nth-of-type(odd) {
  text-align: center;
}

.receipt-m table td:nth-of-type(even) {
  text-indent: 20px;
}

.receipt-m-table2 tbody td:nth-of-type(2),
.receipt-m-table2 tbody td:nth-of-type(3),
.receipt-m-table2 tbody td:nth-of-type(4) {
  text-align: center;
}

.receipt-m-table2 tfoot td:nth-of-type(2) {
  text-indent: 0;
  text-align: center;
}

.receipt-m-table2 tfoot td:nth-of-type(3) {
  padding-right: 20px;
  text-align: right;
}

.receipt-m .ab {
  top: 0;
  right: -34px;
  width: 40px;
}

.receipt-m .ab span {
  display: inline-block;
  line-height: 30px;
}

.receipt-b-t {
  width: 1500px;
  padding: 10px;
  border-bottom: 1px solid #000;
  overflow: hidden;
}

.receipt-b-t .fl span {
  padding-left: 10px;
}

.receipt-b-t .fl span:nth-of-type(even) {
  display: inline-block;
  width: 300px;
  text-indent: 00px;
}

.receipt-b-t .fl:first-child span:nth-of-type(2) {
  width: 400px;
}

.receipt-b-b span {
  height: 50px;
  line-height: 50px;
  font-size: 24px;
}
</style>
