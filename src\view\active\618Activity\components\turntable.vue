<template>
  <div class="yz-lottery-box">
    <div class="table-bg">
      <img src="../../../../assets/image/active/618Activity/light.png" class="light" :class='{shake: shakeLight}' alt="">
      <LuckyWheel
        ref='luckyWheel'
        class='yz-luck-draw'
        width='2.97rem'
        height='2.97rem'
        :blocks="blocks"
        :buttons='buttons'
        :prizes='prizes'
        :default-config='defaultConfig'
        @start="startCallBack"
        @end="endCallBack"
      />
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import { LuckyWheel } from 'vue-luck-draw';
import { isLogin, toLogin, isEmployee } from "@/common";

import point from '../../../../assets/image/active/618Activity/point.png';
import g1Img from '../../../../assets/image/active/618Activity/g1.png';
import g2Img from '../../../../assets/image/active/618Activity/g2.png';
import g3Img from '../../../../assets/image/active/618Activity/g3.png';
import g4Img from '../../../../assets/image/active/618Activity/g4.png';
import g5Img from '../../../../assets/image/active/618Activity/g5.png';
import g6Img from '../../../../assets/image/active/618Activity/g6.png';
import g7Img from '../../../../assets/image/active/618Activity/g7.png';
import g8Img from '../../../../assets/image/active/618Activity/g8.png';

export default {
  components: { LuckyWheel },
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    lotteryCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      defaultConfig: {
        gutter: 3,
        stopRange: 0.1,
      },
      blocks: [
        { padding: '3px', background: '#FFA629' },
      ],
      buttons: [
        { radius: '30px', background: '#fff', imgs: [{ src: point, width: '96px', height: '110px', top: '-65px' }] },
      ],

      prizes: [
        {
          id: '3',
          background: '#FFF7D4',
          fonts: [{ text: '华为笔记本电脑', fontSize: '8px', top: '48%' }],
          imgs: [{ src: g4Img , width: '64px', height: '44px', top: '7px'}],
        },
        {
          id: '2',
          background: 'rgb(253,247,203)',
          fonts: [{ text: '小米运动手表', fontSize: '8px', top: '55%' }],
          imgs: [{ src: g2Img , width: '52px', height: '55px', top: '5px'}],
        },
        {
          id: '7',
          background: '#FFF7D4',
          fonts: [{ text: '机智哥笔记本', fontSize: '8px', top: '55%' }],
          imgs: [{ src: g3Img , width: '49px', height: '57px', top: '5px'}],
        },
        {
          id: '6',
          background: 'rgb(253,247,203)',
          fonts: [{ text: '上进学社读书\n周卡', fontSize: '8px', top: '55%' }],
          imgs: [{ src: g8Img , width: '65px', height: '65px', top: '1px'}],
        },
        {
          id: '8',
          background: '#FFF7D4',
          fonts: [{ text: '智米8', fontSize: '8px', top: '60%' }],
          imgs: [{ src: g6Img , width: '60px', height: '60px', top: '5px'}],
        },
        {
          id: '5',
          background: 'rgb(253,247,203)',
          fonts: [{ text: '上进学社月卡', fontSize: '8px', top: '50%' }],
          imgs: [{ src: g5Img , width: '61px', height: '39px', top: '15px'}],
        },
        {
          id: '4',
          background: '#FFF7D4',
          fonts: [{ text: '智米61800', fontSize: '8px', top: '60%' }],
          imgs: [{ src: g7Img , width: '63px', height: '59px', top: '5px'}],
        },
        {
          id: "1",
          background: 'rgb(253,247,203)',
          fonts: [{ text: '爱玛电动车', fontSize: '8px', top: '58%' }],
          imgs: [{ src: g1Img , width: '59px', height: '61px', top: '3px'}],
        },
      ],
      shakeLight: false,
      shareInverval: null,
    };
  },
  mounted() {
    this.startShakeLight();
  },
  methods: {
    startShakeLight(time = 400) {
      if (this.shareInverval) {
        clearInterval(this.shareInverval);
      }

      this.shareInverval = setInterval(() => {
        this.shakeLight = !this.shakeLight;
      }, time);
    },
    startCallBack() {
      if (!isLogin()) {
        toLogin.call(this, null);
        return;
      }
      if (isEmployee()) {
        Toast('助学老师不能抽奖噢～');
        return;
      }
      if (this.info.status == 0) {
        Toast('活动尚未开始');
        return;
      }
      if (this.info.status == 2) {
        Toast('很抱歉，该活动已结束～');
        return;
      }
      if (this.lotteryCount <= 0) {
        this.$emit('noChanges');
        return;
      }
      this.$refs.luckyWheel.play();
      this.startShakeLight(100);
      this.prizeDraw();
    },
    endCallBack() {
    },
    // 抽奖
    async prizeDraw() {
      try {
        const { code, body } = await this.$http.post("/mkt/prizeDraw/1.0/");
        if (code == "00") {
          const index = this.prizes.findIndex(item => item.id == body.prizeId);
          const winInfo = Object.assign({}, this.prizes[index], body);
          this.$refs.luckyWheel.stop(index);
          setTimeout(() => {
            this.startShakeLight();
            this.$emit('success', winInfo);
          }, 5000);
        } else {
          this.startShakeLight();
          this.$refs.luckyWheel.stop(-1);
        }
      } catch (error) {
        this.$refs.luckyWheel.stop(-1);
        this.startShakeLight();
      }

    },

  },
};
</script>

<style lang="less">
  .yz-lottery-box{
    height: 3.54rem;
    margin-top: 0.39rem;
    .table-bg{
      width: 3.38rem;
      height: 3.38rem;
      background: url(../../../../assets/image/active/618Activity/l-bg.png) no-repeat;
      background-size: 100% 100%;
      margin: 0 auto;
      position: relative;
      border-radius: 50%;
      box-shadow: 0 2px 6px 0 rgba(255, 0, 0, 0.2);
      padding-top: 0.2rem;
      padding-left: 0.2rem;
      .light{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        &.shake{
          transform: rotate(22.5deg);
        }
      }
    }
    .yz-luck-draw{

    }
  }
</style>
