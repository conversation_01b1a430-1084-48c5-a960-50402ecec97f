<template>
  <div class="bc-w">
    <div class="no-data" v-if="comments.length===0">
      <img src="../assets/image/no-data2.png" class="img" alt="">
      <p>暂无评论</p>
    </div>
    <comment v-else v-for="item in comments"
             :key="item.commentId"
             :name="item.userName"
             :headImgUrl="item.headImgUrl"
             :time="item.commentTime"
             :content="item.commentContent">
      <comment class="sub" slot="sub"
               v-show="item.reply.length>0" v-for="(subItem,index) in item.reply" :key="index"
               :name="`${subItem.userName} 回复`"
               :headImgUrl="subItem.headImgUrl"
               :time="subItem.replyTime"
               :content="subItem.replyContent"></comment>
    </comment>
  </div>
</template>

<script>
  import comment from '@/components/comment'

  export default {
    props: {
      comments: {
        type: Array,
        default: []
      }
    },
    components: {comment}
  }
</script>

<style lang="less">
  .no-data{
    text-align: center;
    color:rgba(23,6,6,0.4);
  }
  .no-data .img{
    width: 1.56rem;
  }
</style>
