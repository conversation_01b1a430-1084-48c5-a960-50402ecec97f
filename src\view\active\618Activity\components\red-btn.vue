<template>
  <button
    class="yz-red-btn"
    :disabled='disabled'
    :style='{ width: btnWidth }'
    :class='{big: big}'
    @click='click'
  >
    <slot>{{text}}</slot>
  </button>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
      default: '',
    },
    big: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    btnWidth() {
      return this.width || 'auto';
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="less">
  .yz-red-btn{
    background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
    color: #fff;
    font-weight: 600;
    font-size: 0.13rem;
    height: 0.25rem;
    line-height: 1;
    min-width: 0.7rem;
    border-radius: 100px;
    text-align: center;
    &.big{
      font-size: 0.14rem;
      height: 0.4rem;
      box-shadow: 0px 4px 6px 0px rgba(240, 111, 110, 0.4);
    }
    &:disabled{
      background: #B9B4B4;
      opacity: 1;
      box-shadow: none;
    }
  }
</style>
