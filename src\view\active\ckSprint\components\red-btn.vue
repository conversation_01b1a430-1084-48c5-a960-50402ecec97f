<template>
  <button class="yz-ck-btn" :disabled='this.disabled' @click='click'>
    <slot>{{text}}</slot>
  </button>
</template>

<script>
export default {
  data() {
    return {

    };
  },
  props: {
    text: String,
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    click() {
      this.$emit('click');
    },
  },
};
</script>

<style lang="less">
  .yz-ck-btn{
    min-width: 0.9rem;
    height: 0.3rem;
    padding: 0 0.15rem;
    text-align: center;
    color: #fff;
    font-size: 0.14rem;
    font-weight: 600;
    background: linear-gradient(0deg, #C90700, #FF733B);
    border-radius: 100px;
    line-height: 1;
    box-shadow: 0 0.04rem 0 #9F0500;
    &:disabled{
      background: #BFBFBF;
      box-shadow: 0 0.04rem 0 #7D7D7D;
    }
  }
</style>
