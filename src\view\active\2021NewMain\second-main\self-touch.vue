<template>
  <div class="yz-newMain-self">
    <template v-if='whiteTabActive == 0'>
      <div class="st1"></div>
      <!-- 本科 -->
      <div class="st-p1" v-if='pfsnLevel == 1'></div>
      <!-- 专科 -->
      <div class="st-p5" v-else></div>
      <div class="btns-box" >
        <button
            v-for="({value, label}, index) in mBtnArr"
            :key="index"
            @click='majorClick(value)'
        >
            {{ label }}
        </button>
      </div>
      <div class="st-school1" v-if='pfsnLevel == 1'></div>
      <div class="st-school5" v-else></div>
      <!-- <img src="../../../../assets/image/active/2021NewMain/st2.png" class="st2" alt=""> -->
      <div class="st2"></div>
    </template>
    <div class="self-text-img" v-else>
      <p>
        &nbsp;&nbsp;&nbsp;&nbsp; 高等教育自学考试，简称自学考试、自考，是个人自学和国家考试相结合的高等教育形式。其学习方式灵活，不受年龄、教育程度的限制，学员均可通过自考取得相应毕业证。<br />
        &nbsp;&nbsp;&nbsp;&nbsp; 自考学习者通过专业计划规定的全部课程考试，成绩合格并取得相应学分，完成毕业论文或其他教学时间任务，即准予毕业，获得国家承认的相应毕业证书。符合学位条件的本科毕业生，由有学位授予权的主考学校依照有关规定，授予学士学位。
      </p>
      <!-- <img src="../../../../assets/image/active/2021NewMain/st-time.png" class="st-time" alt=""> -->
      <div class="st-time"></div>
      <p class="mt20">
        实践类考核科目：一年进行2次安排，分为上半年考核和下半年考核，具体考核的形式及时间以远智教育教学计划安排为准。
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    inviteId: String,
    pfsnLevel: {
      type: [Number, String],
      default: '',
    },
    whiteTabActive: {
      type: [Number, String],
      default: 0,
    },
    regOrigin: [Number, String],
    regChannel: [Number, String],
  },
  data() {
    return {
        // 大专本科共有
        mConstBtnArr: [
            {
                label: '行政管理',
                value: 0
            },
        ],
        // 本科
        mBtnArr1: [
            {
                label: '会计学',
                value: 4
            },
            {
                label: '汉语言文学',
                value: 1
            },
            {
                label: '工商管理（商务管理）',
                value: 2
            },
            {
                label: '教育学',
                value: 3
            },
            {
                label: '环境设计',
                value: 5
            },
            {
                label: '人力资源管理',
                value: 6
            },
            {
              label: '护理学',
              value: 7
            },
            {
              label: '学前教育',
              value: 8
            },
        ],
        // 专科
        mBtnArr2: [
            {
                label: '学前教育',
                value: 2
            },
            {
                label: '会计',
                value: 3
            },
            {
                label: '视觉传播设计与制作',
                value: 4
            },
            {
              label: '护理',
              value: 5
            },
        ],
        mBtnArr: []

    }
  },
  created() {
    const arr = this.pfsnLevel == 1 
        ?  this.mBtnArr1
        :  this.mBtnArr2
    this.mBtnArr = this.mConstBtnArr.concat(arr)
  },
  methods: {
    // 点击专业
    majorClick(majorIndex) {
      this.$router.push({
        name: 'selfTought.major',
        query: {
          type: this.pfsnLevel == 1 ? 0 : 1 ,
          majorIndex,
          inviteId:this.inviteId,
          regOrigin: this.regOrigin,
          regChannel: this.regChannel,
        },
      });
    },
  },
};
</script>

<style lang="less">
  .yz-newMain-self{
    border-bottom: 0.1rem solid #F2F2F2;
    padding-top: 0.15rem;
    .st1{
      width: 3.25rem;
      height: 3rem;
      margin: 0 auto;
      background: url(../../../../assets/image/active/2021NewMain/st1.png) no-repeat;
      background-size: 100% 100%;
    }
    .st2{
      width: 100%;
      height: 2.44rem;
      background: url(../../../../assets/image/active/2021NewMain/st2.png) no-repeat;
      background-size: 100% 100%;
    }
    .st-p1{
      width: 1.39rem;
      height: 0.35rem;
      margin: 0.25rem auto;
      background: url(../../../../assets/image/active/2021NewMain/st-p1.png) no-repeat;
      background-size: 100% 100%;
    }
    .st-p5{
      width: 1.39rem;
      height: 0.35rem;
      margin: 0.25rem auto;
      background: url(../../../../assets/image/active/2021NewMain/st-p5.png) no-repeat;
      background-size: 100% 100%;
    }
    .st-school1{
    //   width: 3.11rem;
      width: 100%;
      height: 3.36rem;
      margin: 0.22rem 0;
      background: url(../../../../assets/image/active/2021NewMain/st-p1-s.png) no-repeat;
      background-size: 100% 100%;
        // background-size: contain;
    }
    .st-school5{
      // width: 2.91rem;
      width: 100%;
      height: 2.26rem;
      margin: 0.22rem 0;
      background: url(../../../../assets/image/active/2021NewMain/st-p5-s.png) no-repeat;
        background-size: 100% 100%;
    //   background-size: contain;
    }
    .btns-box{
      padding: 0 0.15rem;
      &.tac{
        text-align: center;
      }
      button{
        width: 1.68rem;
        height: 0.36rem;
        border-radius: 100px;
        color: #fff;
        font-size: 0.14rem;
        text-align: center;
        background: linear-gradient(180deg, #F9840A 0%, #F73706 100%);
        margin-right: 0.05rem;
        margin-bottom: 0.1rem;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.2);
        &:nth-child(2n) {
          margin-right: 0;
        }
      }
    }
    .self-text-img{
      padding: 0.1rem 0.15rem 0.25rem;
      font-size: 0.13rem;
      .st-time{
        width: 100%;
        height: 3.08rem;
        margin-top: 0.2rem;
        background: url(../../../../assets/image/active/2021NewMain/st-time.png) no-repeat;
        background-size: 100% 100%;
      }
    }
    .mt20{
        margin-top: 0.2rem;
    }
  }
</style>
