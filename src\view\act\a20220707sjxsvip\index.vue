<template>
  <div class="act-wrap">
    <!-- 微信分享组件 -->
    <share
      ref="share"
      :title="share.title"
      :desc="share.content"
      :link="share.url"
      :imgUrl="share.image"
      :regOrigin="share.regOrigin"
    />

    <div class="main">
      <img src="./img/poster_01.jpg" alt="">
      <img src="./img/poster_02.jpg" alt="">
      <img src="./img/poster_03.jpg" alt="">
      <img src="./img/poster_04.jpg" alt="">
      <img src="./img/poster_05.jpg" alt="">
    </div>

    <div class="kf" @click="TO_WX_CS">
      <div class="text">咨询客服</div>
    </div>

    <div class="fixed-btn" @click="toVipPage"></div>

  </div>
</template>

<script>
import appShare from '@/mixins/appShare';
import { AppShareUrl } from '@/config';
import share from "@/components/share";
import wxCustomerService from '../../aspirantUniversity/mixins/wxCustomerService';
import invite from "@/mixins/invite";
import { verifyVersion, isIOS, isAndroid } from '@/common'
export default {
  components: {
    share
  },
  mixins: [
    invite,
    appShare,
    wxCustomerService
  ],
  data() {
    return {
      share: {
        title: '考证畅学卡',
        content: '超省钱还有奖金等你来领取',
        url: AppShareUrl + '/act/a20220707sjxsvip',
        image: 'http://static.yzou.cn/weChatMsg/4b44e3e3-6fd8-4ee7.png',
        regOrigin: '', //邀约类型
      },
      teacherWeChatLink: '',
    }
  },
  mounted() {
    // 设置app分享, 只会在app执行
    this.initAppShare(()=> {
      // 由于 app,和 之前wx分享封装不一致，导致app分享的 url不需要拼接AppShareUrl
      let copyShare = JSON.parse(JSON.stringify(this.share));
      copyShare.url = '/act/a20220707sjxsvip';
      this.setShareParams(copyShare);
    })
    this.getTeacherWeChatLink();
  },
  methods: {
    toWeChat() {
      window.location.href = this.teacherWeChatLink;
    },
    // 获取学霸卡的 老师客服链接
    getTeacherWeChatLink() {
      let params = {
        goodsClass: '4',
        mappingId: null, // 暂时不用
      }
      this.$http.post('/pu/getGoodsServiceLinkUrl/2.0/', params).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          this.teacherWeChatLink = body;
        }
      })
    },
    toVipPage() {
      if (this.isAppOpen) {
        if (isAndroid()) {
          verifyVersion('*********', () => {
            if (this.teacherWeChatLink) {
              this.toWeChat();
            } else {
              this.$inviteJump('/aspirantUniversity/aspirantCard')
            }
          })
        }

        if (isIOS()) {
          verifyVersion('7.19.8', () => {
            if (this.teacherWeChatLink) {
              this.toWeChat();
            } else {
              this.$inviteJump('/aspirantUniversity/aspirantCard')
            }
          })
        }
      } else {
        // 如果链接有配置，则跳转去添加老师微信，没有则去学霸卡购买页
        if (this.teacherWeChatLink) {
          this.toWeChat();
        } else {
          this.$inviteJump('/aspirantUniversity/aspirantCard')
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.main {
  padding-bottom: 0.8rem;
  img {
    width: 100%;
  }
}

.fixed-btn {
  width: 3.75rem;
  height: 0.8rem;
  background: url('./img/btn.png') no-repeat;
  background-size: 100% ;
  position: fixed;
  bottom: 0;
  z-index: 2;
  user-select: none;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.kf {
  width: 0.92rem;
  height: 0.78rem;
  background: url('./img/pendant.png') no-repeat;
  background-size: 100% 100%;
  position: fixed;
  right: 0;
  bottom: 1.39rem;

  .text {
    position: absolute;
    left: 0.16rem;
    bottom: 0.1rem;
    text-align: center;
    font-size: 0.14rem;
    color: #7C1214;
    animation: scale 0.8s cubic-bezier(0.250, 0.460, 0.450, 0.940) infinite;
    font-weight: 600;
  }

  @keyframes scale {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.2);
    }
  }


}

</style>
