<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.pfsnId===item.pfsnId}" @click="selected(item)">{{item.pfsnName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="options.length"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';
  import dataJson from '../../view/active/scholarship/dataJson.json';
  import pfsnData from '../../view/active/scholarship/pfsn.json';
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data() {
      return {
        options: [],
        type: 'P',
        pfsnName: '',
        grade: '',
        pfsnLevel: '',
        unvsId: '',
        pageNum: 0,
        pageSize: 20,
        isLoading: false,
        allLoaded: false,
        activityName: '',
        cityCode: '',
        cityName: '',
        pfsnNames: [],
        taId:'',
        recruitType:''
      }
    },
    created() {
      this.activityName = this.datas.activityName;

      //this.getCity();
      this.grade = this.datas.grade.dictValue;
      this.pfsnLevel = this.datas.pfsnLevel.pfsnLevel;
      this.unvsId = this.datas.unvs.unvsId;
      this.pfsnName = this.$route.query.pfsnName || '';
      this.cityCode=this.datas.city.cityCode;
      this.recruitType=this.datas.recruitType.dictValue;
      this.taId=this.datas.ta.taId;
    },
    methods: {
      getUnvsList: function () {
        if(this.unvsId==='46'){
          this.isLoading = true;
          const data = {
            type: this.type,
            pfsnName: '',
            grade: this.grade,
            pfsnLevel: this.pfsnLevel,
            unvsId: this.unvsId,
            scholarship:this.datas.scholarship,
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            cityCode:this.cityCode,
            taId:this.taId,
            recruitType:'2'
          }
          this.$http.post('/mkt/getOpenPfsnByTaId/1.0/', data).then(res => {
            if (res.code !== '00') return;
            const datas = (res.body || []);
            this.options.push(...datas);
            this.$nextTick(() => {
              this.allLoaded = datas.length === 0;
              this.isLoading = this.allLoaded;
            });
          });
          return;
        }
        this.isLoading = true;
        let data = {
          type: this.type,
          pfsnName: '',
          grade: this.grade,
          pfsnLevel: this.pfsnLevel,
          unvsId: this.unvsId,
          scholarship:typeof(this.datas.scholarship)==='string'?[this.datas.scholarship]:this.datas.scholarship,
          recruitType:this.recruitType,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          cityCode:this.cityCode
        }
        let url = '/mkt/enrollInfo/1.0/';
        let headers = {};
        if (this.isDingding) {
          url = `${domain}/newStudentChange/getEnrollInfo.do`;
          data = qs.stringify(data);
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
          };
        }
        this.$http.post(url, data, {
          headers: headers,
        }).then(res => {
          if (res.code !== '00') return;
          const datas = (res.body || []);
          this.options.push(...datas);

          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      // 筛选专业 - 限时促销活动\奖学金活动
      /*filterPfsn: function (datas) {
        if (this.activityName === 'scholarship') {
          this.pfsnNames = pfsnData[this.unvsId].find(val => val.citys.includes(this.cityName)).pfsnList[this.pfsnLevel];
          datas = datas.filter(val => this.pfsnNames.includes(val.pfsnName));
        }
        return datas;
      },*/
      loadMore: function () {
        this.pageNum++;
        this.getUnvsList();
      },
      selected: function (val) {
        this.$emit('input', val);
      },
      getCity: function () {
        const city = this.datas.city.dictValue;
        const datas = dataJson;
        if (city) {
          this.cityCode = datas.cityCode[city] || '';
          this.cityName = datas.city[city] || '';
        }
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
