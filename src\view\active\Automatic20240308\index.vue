<template>
  <div class="launch-btns" @click.stop="toActivePage(1)">
    <wx-open-launch-weapp id="launch-btn" class="launch-btns" :appid="appId" :path="path" :env-version="version">
      <script type="text/wxtag-template">
        <style>.launch-ims {  width: 100%; height: 100%; }</style>
        <img class="launch-ims" @click.stop="toActivePage(2)" src="https://yzimstest.oss-cn-shenzhen.aliyuncs.com/newRedEnvelope/openwx.png" alt="" srcset="">
      </script>
    </wx-open-launch-weapp>
  </div>
</template>

<!-- 自动打开-上进青年小程序 -->
<script>
import openApp from '@/mixins/openApp'
export default {
  mixins: [openApp],
  data() {
    return {
      appId: 'wxa66eb064ae9cc76c',
      path: 'pages/321activity/24/index',
      version: 'trial'
    }
  },
  mounted() {
    console.log('自动打开-上进青年小程序：', process.env)
    if (process.env.RUN_ENV == 'production') {
      this.version = 'release'
    }
    this.wxCallAppInit()
    this.toActivePage()
  },
  methods: {
    toActivePage(type = 0) {
      type && console.log('点击事件-上进青年小程序：', type)
      window.location.href = `weixin://dl/business/?appid=${this.appId}&path=${this.path}&env_version=${this.version}`
    }
  }
}
</script>

<style lang="less" scoped>
.launch-btns {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-image: url(./openwx.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.launch-ims {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
