<template>
  <picker :slots="slots" :disabledSlots="timeDisabledSorts" :disabled="disabled" :isSetDefaultVal="isSetDefaultVal"
          v-model="val" ref="picker"/>
</template>

<script>
  import picker from '@/components/picker';

  export default {
    props: {
      dictName: String,
      options: Array,
      ext1: String,
      isSetDefaultVal: {
        type: Boolean,
        default: false
      },
      defaultIndex: {
        type: Number,
        default: 0
      },
      filterValue: Array
    },
    data() {
      return {
        slots: [],
        val: [{}],
        vals: [],
        timeDisabledSorts: [],
        disabled: false,
      }
    },
    created() {
      // this.init();substring
    },
    methods: {
      init: function () {
        this.slots = [{flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex}];
      },
      getOptions: function () {
        let options = [];
        if (this.options.length >= '1') {
          if (this.options[0].selectNumber >= this.options[0].number) {
            this.disabled = true;
          }

          this.vals = this.options || [];
          let timeDisabledSorts = [];
          for (let item of this.vals) {
            let tt = ""
            if (item.startTime.substring(0, 2) >='12') {
              tt = "下午"
            } else {
              tt = "上午"
            }

            let timeTxt = tt + item.startTime + "-" + item.endTime;
            let timeTxt2 = tt + item.startTime + "-" + item.endTime + " 上限" + item.selectNumber + "/" + item.number;

            if (item.selectNumber >= item.number) {
              timeDisabledSorts.push(timeTxt2)
            }

            Object.assign(item, {timeTxt: timeTxt, timeTxt2: timeTxt})
            options.push(timeTxt2);
          }
          this.timeDisabledSorts = timeDisabledSorts;
        }
        return options;
      },
      open: function () {
        this.$refs.picker.open();
      },
      setValues: function (value) {
        if (value) {
          const name = (this.vals.find(val => val.dictValue === value) || {}).dictName;
          if (name) {
            this.$refs.picker.setValues([name]);
          }
        }
      },
      onValuesChange: function () {
        this.$refs.picker.onValuesChange()
      }
    },
    watch: {
      val: function () {
        this.$emit('input', this.vals[this.val[0].index] || {});
      }
    },
    components: {picker}
  }
</script>
