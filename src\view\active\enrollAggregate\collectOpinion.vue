<template>
  <div class="yz-collect-opinion">
    <p class="big-title">希望提供的学习课程</p>
    <ul class="list">
      <li v-for="(item, index) in list" :key="index">
        <p class="title">{{item.title}}</p>
        <span :class='{active: j.isChecked}' v-for="(j, i) in item.courses" :key="i"  @click='select(j)'>{{j.courseName}}</span>
      </li>
      <li>
        <p class="title">其他</p>
        <input type="text" class="input" placeholder="请输入..." v-model="ofter">
      </li>
    </ul>
    <button class="submit" @click='submit' :disabled='selectNames.length == 0 && !ofter'>提交</button>
  </div>
</template>

<script>
import { Toast } from 'vant';

const courseType = {
  1: 'IT互联网',
  2: '设计创作',
  3: '职业技能',
  4: '外语语言',
};

export default {
  data() {
    return {
      list: [],
      ofter: '',
    };
  },
  computed: {
    selectNames() {
      const names = [];
      this.list.forEach((item) => {
        item.courses.forEach((v) => {
          if (v.isChecked) {
            names.push(v.courseName);
          }
        });
      });
      return names;
    },
  },
  mounted () {
    this.getOpinion();
  },
  methods: {
    select(item) {
      item.isChecked = !item.isChecked;
    },
    async getOpinion() {
      const res = await this.$http.post('/us/getUserOpinionCourseList/1.0/'); // 获取所有意见学习课程
      if (res.code == '00' && res.body) {
        let idArr = res.body.map(val => val.courseType);
        idArr = Array.from(new Set(idArr)); // 去重
        res.body.forEach(item => {
          item.isChecked = false;
        });
        this.list = idArr.map(val => {
          const courses = [];
          res.body.forEach(item => {
            if (item.courseType == val) {
              courses.push(item);
            }
          });
          return { courseType: val, courses, title: courseType[val] };
        });
      }
    },
    async submit() {
      if (this.selectNames.length == 0 && !this.ofter) {
        return;
      }
      const names = this.selectNames;
      if (this.ofter) {
        names.push(this.ofter);
      }
      const res = await this.$http.post('/us/addUserOpinion/1.0/', { courseNames: names.join(',') }); // 添加用户课程意见
      if (res.code == '00') {
        Toast.success('提交成功');
        this.list.forEach((item) => {
          item.courses.forEach((v) => {
            v.isChecked = false;
          });
        });
        this.list = [].concat(this.list);
        this.ofter = '';
        setTimeout(() => {
          this.$router.go(-1);
        }, 200);
      }
    },
  },
};
</script>

<style lang="less">
.yz-collect-opinion{
  padding-bottom: 0.36rem;
  .big-title{
    font-family: ysbh;
    font-size: 0.24rem;
    padding: 0.35rem 0 0.2rem;
    text-align: center;
  }
  .list{
    padding: 0 0.14rem;
    li{
      .title{
        font-size: 0.18rem;
        margin-bottom: 0.2rem;
        font-family: ysbh;
        margin-top: 0.15rem;
      }
      span{
        margin-right: 0.15rem;
        display: inline-block;
        border: 1px solid rgba(54,54,54,0.5);
        border-radius: 50px;
        padding: 0.1rem 0.18rem;
        font-size: 0.15rem;
        margin-bottom: 0.15rem;
        line-height: 1;
        &.active{
          border-color: rgba(240,110,108,0.5);
          color: #F06E6C;
        }
      }
      &:not(:last-child){
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      }
      .input{
        border:1px solid rgba(54,54,54,0.5);
        border-radius: 50px;
        width: 100%;
        height: 0.35rem;
        font-size: 0.15rem;
        display: block;
        padding: 0 0.16rem;
      }

    }
  }
  .submit{
    display: block;
    width: 1.65rem;
    margin: 0.36rem auto 0;
    background: #F06E6C;
    height: 0.36rem;
    font-size: 0.17rem;
    font-weight:600;
    border-radius: 50px;
    color: #fff;
    &:disabled{
      background: #9B9B9B;
    }
  }
}
</style>
