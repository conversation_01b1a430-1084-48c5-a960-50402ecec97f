import {setCookie, getCookie} from 'tiny-cookie';

export const beforeRtEnter = function (to, from, next) {
  const empId = getCookie('empId');
  const userId = getCookie('userId');
  
  if (empId) {
    setCookie('empId', empId, {expires: '30m'});
    setCookie('userId', userId, {expires: '30m'});
    next(vm => {
      //console.log(vm)
      //vm.empId=empId
      //vm.$set(vm.$data,'empId',empId)
    });
  } else {
    next({name: 'netRegistLogin'});
  }
}
