<template>
  <div class="main">
    <!-- 进行时 -->
    <div
      class="punch-box"
      v-if="learningData.learnStatus == 1 && learningData.challengeStatus == 2"
    >
      <img class="buckle-one" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="buckle-two" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="study-title" src="../../../../assets/image/active/signAct/study-punch.png" alt />
      <img class="flower" src="../../../../assets/image/active/signAct/flower.png" alt />

      <div class="content-box">
        <div class="header">
          <div class="lf">
            <div class="time">
              <div class="lf-txt">开始时间</div>
              <div class="rg-txt">{{ learningData.challengeStartTime | formatDate("yyyy.MM.dd") }}</div>
            </div>
            <div class="schedule" style="margin-top: 6px">
              <div class="lf-txt">进度</div>
              <div class="rg-txt">{{ learningData.learnCount }}/21</div>
            </div>
          </div>
          <div class="rg">
            <button
              class="bonus-btn"
              v-if="newCardData.length != 0"
              @click="
                toRead(
                  newCardData[0].semesterId,
                  newCardData[0].punchOrder,
                  newCardData[0].ageTime,
                  newCardData[0].vids,
                  newCardData[0].clockLimitTime,
                  newCardData[0].attendClassId
                )
              "
            >去打卡</button>
            <button
              class="bonus-btn"
              v-if="newCardData.length == 0 && patchCardData.length != 0"
              @click="
                toRead(
                  patchCardData[0].semesterId,
                  patchCardData[0].punchOrder,
                  patchCardData[0].ageTime,
                  patchCardData[0].vids,
                  patchCardData[0].clockLimitTime,
                  patchCardData[0].attendClassId
                )
              "
            >去补卡</button>
            <!-- <button
              class="completed-btn"
              v-if="patchCardData.length == 0 && newCardData.length == 0"
            >
              已完成
            </button>-->
            <!-- <img
              v-if="item.ageTime == true && item.start == true"
              src="../../../../assets/image/active/signAct/repair.png"
              alt=""
            />-->
            <img
              v-if="patchCardData.length == 0 && newCardData.length == 0"
              src="../../../../assets/image/active/signAct/punched.png"
              alt
            />
          </div>
        </div>
        <p>
          *再缺卡{{
            learningData.patchCardCount - learningData.lackCardCount
          }}次将挑战失败，您需在
          {{ learningData.learnEndTime | formatDate("yyyy.MM.dd") }}
          前完成打卡&补卡
        </p>
        <van-grid :gutter="0" :border="true" :column-num="6">
          <van-grid-item v-for="item in learningData.planPunchCardVos" :key="item.punchOrder">
            <!-- 已完成状态 -->
            <div
              class="finished-btn"
              v-if="
                item.punchTime != null &&
                item.start == true &&
                item.isPatchCard != 1
              "
            >
              <span>{{ item.punchOrder }}</span>
              <img src="../../../../assets/image/active/signAct/finish-logo.png" alt />
            </div>
            <!-- 已补卡状态 -->
            <div
              class="finished-btn"
              v-if="
                item.punchTime != null &&
                item.start == true &&
                item.isPatchCard == 1
              "
            >
              <span>{{ item.punchOrder }}</span>
              <img src="../../../../assets/image/active/signAct/repair-btn.png" alt />
            </div>
            <!-- 去打卡状态 -->
            <div
              class="punch-btn"
              v-if="
                item.punchTime == null &&
                item.ageTime == false &&
                item.start == true
              "
            >
              <span>{{ item.punchOrder }}</span>
              <button
                @click="
                  toRead(
                    item.semesterId,
                    item.punchOrder,
                    item.ageTime,
                    item.vids,
                    item.clockLimitTime,
                    item.attendClassId
                  )
                "
              >去打卡</button>
            </div>
            <!-- 补卡状态 -->
            <div
              class="punch-btn"
              v-if="
                item.punchTime == null &&
                item.ageTime == true &&
                item.start == true
              "
            >
              <span>{{ item.punchOrder }}</span>
              <button
                @click="
                  toRead(
                    item.semesterId,
                    item.punchOrder,
                    item.ageTime,
                    item.vids,
                    item.clockLimitTime,
                    item.attendClassId
                  )
                "
              >去补卡</button>
            </div>
            <div class="dis-btn" v-if="item.start == false">
              <span>{{ item.punchOrder }}</span>
              <img src="../../../../assets/image/active/signAct/lock.png" alt />
            </div>
          </van-grid-item>
        </van-grid>
      </div>
    </div>
    <!-- 学习打卡全部完成 -->
    <div
      class="punch-box"
      v-if="learningData.challengeStatus != 4 && learningData.learnStatus == 2 || learningData.challengeStatus == 3"
    >
      <img class="buckle-one" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="buckle-two" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="study-title" src="../../../../assets/image/active/signAct/study-punch.png" alt />
      <img class="flower" src="../../../../assets/image/active/signAct/flower.png" alt />
      <div class="complete">
        <img src="../../../../assets/image/active/signAct/finish-big.png" alt />
      </div>
    </div>
    <!-- 第一轮挑战自动分配 -->
    <div
      class="all-first-box"
      v-if="
        learningData.learnStatus == 1 &&
        learningData.remainChallengeCount == 2 &&
        learningData.challengeStatus == 1
      "
    >
      <div class="wait-box">
        <p>请留意挑战开始时间：</p>
        <span class="times">{{ learningData.challengeStartTime | formatDate("yyyy.MM.dd hh:mm") }}</span>
        <p>务必扫码添加老师，加入奖学金班级群</p>
        <!-- <p>已添加的同学请忽略</p> -->
        <img :src="codeUrl" alt />
        <span>长按二维码识别</span>
      </div>
    </div>
    <!-- 三次失败后 -->
    <div
      class="all-over-box"
      v-if="
        learningData.remainChallengeCount == 0 &&
        learningData.challengeStatus == 4
      "
    >
      <div class="over-box">
        <p>
          亲爱的学员，很抱歉～您已使用3次挑战机会，由于您未能完成本次活动挑战，所以对应的奖学金券不能激活。如有疑问请联系您的助学老师（{{
            mobile
          }}）。
        </p>
      </div>
    </div>
    <!-- 打卡挑战失败,等待下一轮 -->
    <div
      class="all-fail-box"
      v-if="
        learningData.remainChallengeCount != 2 &&
        learningData.challengeStatus == 1 &&
        learningData.learnStatus == 1
      "
    >
      <!-- <div class="all-fail-box"> -->
      <div class="fail-box">
        <p class="tips-txt">
          抱歉，由于您
          <span>学习打卡</span> 失败
        </p>
        <p class="tips-txt">
          将在
          <span>{{ learningData.challengeStartTime | formatDate("yyyy.MM.dd") }}</span>
          重新开启下一轮挑战
        </p>
        <p class="code-tips" style="margin-top: 30px;">请务必扫码添加老师微信</p>
        <p class="code-tips">向老师申请复训</p>
        <img :src="codeUrl" alt />
        <p class="code-txt">长按二维码识别</p>
      </div>
    </div>
    <!-- 学习打卡挑战失败，跑步成功,等待下一轮 -->
    <div
      class="only-study-fail-box"
      v-if="
        learningData.remainChallengeCount != 2 &&
        learningData.challengeStatus == 1 &&
        runningData.runningStatus == 2 &&
        learningData.learnStatus == 3
      "
    >
      <!-- <div class="only-study-fail-box"> -->
      <div class="fail-box">
        <p class="tips-txt">
          抱歉，由于您
          <span>学习打卡</span> 失败
        </p>
        <p class="tips-txt">
          将在
          <span>{{ learningData.challengeStartTime | formatDate("yyyy.MM.dd") }}</span>
          重新开启下一轮挑战
        </p>
        <p class="code-tips" style="margin-top: 30px">请务必扫码添加老师微信</p>
        <p class="code-tips">向老师申请复训</p>
        <img :src="codeUrl" alt />
        <p class="code-txt">长按二维码识别</p>
      </div>
    </div>
  </div>
</template>

<script>
import { imgPosterBaseURL } from "@/config/index";

export default {
  name: "StudyBonus",
  props: {
    runData: {
      type: Object,
      required: true,
    },
    studyData: {
      type: Object,
      required: true,
    },
    semesterData: {
      type: Object,
      required: true,
    },
    newCard: {
      type: Array,
      required: true,
    },
    patchCard: {
      type: Array,
      required: true,
    },
    mobile: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      imgUrl: null,
    };
  },
  computed: {
    runningData() {
      return this.runData;
    },
    learningData() {
      console.log(this.studyData);
      return this.studyData;
    },
    newCardData() {
      return this.newCard;
    },
    patchCardData() {
      return this.patchCard;
    },
    codeUrl() {
      return imgPosterBaseURL + this.semesterData.wechatQrCode;
    },
  },
  // watch: {
  //   semesterData: {
  //     handler(n, o) {
  //       console.log(n);
  //       this.imgUrl = imgPosterBaseURL + n.wechatQrCode;
  //       console.log(this.imgUrl);
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    //读书打卡  学期id，打卡序号，是否补卡,视频vids,学习时长
    toRead(semesterId, punchOrder, ageTime, vids,surplusDuration,attendClassId) {
      console.log(semesterId);
      console.log(punchOrder);
      console.log(ageTime);
      console.log(vids);
      this.$router.push({
        path: "/active/a20241023GuangTie/readBook",
        query: {
          semesterId: semesterId,
          punchOrder: punchOrder,
          isPatchCard: ageTime?1:0,
          vids: vids,
          surplusDuration:surplusDuration,
          attendClassId
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
@rem: 0.01rem;
.main {
  .punch-box {
    z-index: 2;
    box-sizing: border-box;
    padding: 11 * @rem 5 * @rem 6 * @rem 5 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #fdca65 0%, #f39229 100%);
    box-shadow: inset 0 * @rem 2 * @rem 4 * @rem 0 * @rem
        rgba(255, 255, 255, 0.5),
      inset 0 * @rem -2 * @rem 3 * @rem 0 * @rem rgba(255, 255, 255, 0.5);
    border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
    .buckle-one {
      position: absolute;
      top: -8 * @rem;
      left: 36 * @rem;
    }
    .buckle-two {
      position: absolute;
      top: -8 * @rem;
      right: 36 * @rem;
      z-index: 1;
    }
    .study-title {
      margin-bottom: 11 * @rem;
      width: 110 * @rem;
      height: 40 * @rem;
    }
    .flower {
      width: 1.41rem;
      height: 1.35rem;
      position: absolute;
      top: 0;
      right: 0;
    }
    .content-box {
      z-index: 9;
      box-sizing: border-box;
      padding: 13 * @rem 11 * @rem 6 * @rem 11 * @rem;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
      box-shadow: 0 * @rem 1 * @rem 1 * @rem 0 * @rem #f57100,
        inset 0 * @rem 1 * @rem 1 * @rem 0 * @rem rgba(255, 157, 55, 0.5);
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12 * @rem;
        .lf {
          display: flex;
          flex-direction: column;
          width: 50%;
          .schedule,
          .time {
            display: flex;
            align-items: center;
            justify-content: center;
            .lf-txt {
              width: 35%;
              text-align: right;
              display: inline-block;
              padding-right: 4 * @rem;
              margin-right: 4 * @rem;
              border-right: 1 * @rem solid #f58c38;
              font-size: 12 * @rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #f58c38;
            }
            .rg-txt {
              flex: 1;
              display: inline-block;
              font-size: 12 * @rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #8c4d0e;
            }
          }
        }
        .rg {
          .bonus-btn {
            width: 84 * @rem;
            height: 31 * @rem;
            background: linear-gradient(269deg, #fd8857 0%, #e61c26 100%);
            box-shadow: inset 0 * @rem 0 * @rem 3 * @rem 0 * @rem
              rgba(255, 255, 255, 0.5);
            border-radius: 20 * @rem;
            border: 1 * @rem solid #ffbb56;
            font-size: 16 * @rem;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #fce7ba;
            -webkit-animation: free_download 1s linear alternate infinite;
            animation: free_download 1s linear alternate infinite;
          }
          @keyframes free_download {
            0% {
              transform: scale(1);
            }
            100% {
              transform: scale(1.1);
            }
          }
          .completed-btn {
            width: 84 * @rem;
            height: 31 * @rem;
            background: linear-gradient(269deg, #adadad 0%, #d2d2d2 100%);
            box-shadow: inset 0 * @rem 0 * @rem 3 * @rem 0 * @rem
              rgba(255, 255, 255, 0.5);
            border-radius: 20 * @rem;
            border: 2 * @rem solid #aeaeae;
            font-size: 16 * @rem;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
          }
          img {
            width: 90 * @rem;
            height: 36 * @rem;
          }
        }
      }
      p {
        font-size: 11 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 11 * @rem;
        margin-bottom: 6 * @rem;
      }
      /deep/.van-grid {
        border-radius: 6px;
        overflow: hidden;
        padding: 1 * @rem 1 * @rem;
      }
      /deep/.van-grid-item__content {
        height: 62 * @rem;
        padding: 0 0;
      }
      /deep/ .van-grid-item__content::after {
        z-index: 0 !important;
        border-width: 0 0 0 0 !important;
      }
      .punch-btn {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(180deg, #fdd8ad 0%, #fffede 100%);
        border: 1px solid #fecd77;
        // padding: 12 * @rem 3 * @rem 8 * @rem 3 * @rem;
        padding: 0.12rem 0 0.08rem 0;
        span {
          font-size: 18 * @rem;
          font-weight: Bebas;
          color: #cc2725;
          line-height: 18 * @rem;
        }
        button {
          padding: 0.04rem 0.05rem;
          margin-top: 6 * @rem;
          background: linear-gradient(226deg, #fcaa83 0%, #e61d26 100%);
          border-radius: 10 * @rem;
          font-size: 12 * @rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #fce7ba;
        }
      }
      .finished-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 62 * @rem;
        background: linear-gradient(180deg, #fdd8ad 0%, #fffede 100%);
        border: 1 * @rem solid #fecd77;
        position: relative;
        span {
          margin-top: 12 * @rem;
          font-size: 20 * @rem;
          font-family: Bebas;
          color: #f99a35;
          line-height: 18 * @rem;
        }
        img {
          position: absolute;
          top: 17 * @rem;
          width: 39 * @rem;
          height: 39 * @rem;
        }
      }
      .dis-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 62 * @rem;
        background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
        border: 1px solid #d9d9d9;
        position: relative;
        span {
          margin-top: 12 * @rem;
          font-size: 20 * @rem;
          font-family: Bebas;
          color: #4c4c4c;
          line-height: 18 * @rem;
        }
        img {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24 * @rem;
          height: 27 * @rem;
        }
      }
      // .punch-btn:nth-child(1),
      // .finished-btn:nth-child(1),
      // .dis-btn.last {
      //   border-radius: 0 0 0 10px;
      // }
      div[radius] {
        border-radius: 0 0 0 10px;
      }
    }
    .complete {
      width: 100%;
      height: 314 * @rem;
      background: url("../../../../assets/image/active/signAct/study-complete.png")
        no-repeat;
      background-size: 100% 100%;
      position: relative;
      img {
        width: 78 * @rem;
        height: 78 * @rem;
        position: absolute;
        top: -20 * @rem;
        right: 0;
      }
    }
    .over-box {
      width: 100%;
      height: 268 * @rem;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 81 * @rem 28 * @rem 0 28 * @rem;
      p {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: 20 * @rem;
      }
    }
    .fail-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 59 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        text-align: center;
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
      }
      .tips-txt {
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: 22px;
        span {
          text-decoration: underline;
          color: #453838;
          font-weight: 600;
        }
      }
      img {
        margin: 15 * @rem 0 7 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      .code-txt {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 17 * @rem;
      }
    }
    .wait-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 59 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
      }
      .times {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: #ea5a59;
      }
      img {
        margin: 15 * @rem 0 6 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
    }
  }
  .edge {
    position: relative;
    top: -26 * @rem;
    width: 100%;
    height: 30 * @rem;
    background: linear-gradient(90deg, #f29c36 0%, #e16318 100%);
    border-radius: 0 0 11 * @rem 11 * @rem;
  }
  .all-fail-box {
    height: 3.92rem;
    width: 100%;
    background: url("../../../../assets/image/active/signAct/all-fail-bgc.png")
      no-repeat;
    background-size: 100% 100%;
    .fail-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 108 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        text-align: center;
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
      }
      .tips-txt {
        span {
          text-decoration: underline;
          color: #453838;
          font-weight: 600;
        }
        p {
          font-size: 0.14rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #170606;
        }
      }
      .code-tips {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
        // line-height: 20px;
      }
      img {
        margin: 15 * @rem 0 7 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      .code-txt {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 17 * @rem;
      }
    }
  }
  .all-over-box {
    height: 3.9rem;
    width: 100%;
    background: url("../../../../assets/image/active/signAct/all-over-bgc.png")
      no-repeat;
    background-size: 100% 100%;
    .over-box {
      width: 100%;
      height: 100%;
      padding: 149 * @rem 35 * @rem 0 37 * @rem;
      p {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: 20 * @rem;
      }
    }
  }
  .all-first-box {
    height: 3.92rem;
    width: 100%;
    background: url("../../../../assets/image/active/signAct/all-first-bgc.png")
      no-repeat;
    background-size: 100% 100%;
    .wait-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 130 * @rem 38 * @rem 29 * @rem 38 * @rem;
      p {
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
      }
      .times {
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: #ea5a59;
        margin-bottom: 0.2rem;
      }
      img {
        margin: 15 * @rem 0 6 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      span {
        font-size: 0.13rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 0.17rem;
      }
    }
  }
  .only-study-fail-box {
    height: 3.91rem;
    width: 100%;
    background: url("../../../../assets/image/active/signAct/only-study-fail.png")
      no-repeat;
    background-size: 100% 100%;
    .fail-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 108 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        text-align: center;
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
      }
      .tips-txt {
        span {
          text-decoration: underline;
          color: #453838;
          font-weight: 600;
        }
        p {
          font-size: 0.14rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #170606;
        }
      }
      .code-tips {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
        // line-height: 20px;
      }
      img {
        margin: 15 * @rem 0 7 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      .code-txt {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 17 * @rem;
      }
    }
  }
}
</style>
