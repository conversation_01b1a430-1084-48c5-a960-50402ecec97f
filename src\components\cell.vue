<template>
  <a class="cell bc-w" :class="{'disabled':disabled}"  v-if="to" @click="toUrl">
    <div class="tit">{{title}}</div>
    <div class="val">{{value}}</div>
    <i class="icons i-arr-r"></i>
  </a>
  <div class="cell bc-w" :class="{'disabled':disabled}" v-else>
    <div class="tit">{{title}}</div>
    <div class="val">{{value}}</div>
    <i class="icons i-arr-r" v-if="!disabled"></i>
  </div>
</template>

<script>
  export default {
    props: ['title', 'value', 'to','disabled','scholarship'],
    methods:{
      toUrl(){
        this.$router.push(this.to);
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .cell{
    display:flex;align-items:center;position:relative;padding:.06rem .12rem;
    min-height: .48rem ;
    &:after{.borderBottom}
    &.disabled{
      pointer-events: none;
    }
  }
  .tit{min-width:.88rem;color:#666;}
  .val{flex:1;text-align:right;color:#666;}
  .i-arr-r{width:.36rem;height:.36rem;margin-right:-0.12rem;background-image:url(../assets/image/public_ico_open_right.png);}
</style>
