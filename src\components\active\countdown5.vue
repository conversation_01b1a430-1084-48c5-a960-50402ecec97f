<template>
  <div class="countdown" v-if="isShow" :style="{color:txt_color}">
    <span class="num day">{{dayFirst}}</span><span class="num day">{{daySecond}}</span><p :style="{color:tipColor}" style="display:inline-block;line-height:.22rem">&thinsp;天</p>
    <span class="num hour">{{hourFirst}}</span><span class="num hour">{{hourSecond}}</span><span :style="{color:tipColor}">&thinsp;时</span>
    <span class="num minute">{{minuteFirst}}</span><span class="num minute">{{minuteSecond}}</span><span :style="{color:tipColor}">&thinsp;分</span>
    <span class="num second">{{secondFirst}}</span><span class="num minute">{{secondTwo}}</span><span :style="{color:tipColor}">&thinsp;秒</span>
    <span class="num big millisecond">{{millisecond}}</span>
  </div>
</template>

<script>
  export default {
    name: 'countdown',
    props: ['startTime','endTime','txt_color','tipColor'],
    data() {
      return {
        isShow: false,
        timer: null,
        day: '00',
        dayFirst:'0',
        daySecond:'0',
        hour: '00',
        hourFirst:'0',
        hourSecond:'0',
        minute: '00',
        minuteFirst:'0',
        minuteSecond:'0',
        secondFirst: '0',
        secondTwo:'0',
        millisecond: '0',
        // startTime: 1529510400000 ,   // 开始时间：2018-06-21 00:00:00
        // endTime: 1530374400000 ,  // 结束时间：2018-06-01 00:00:00
      }
    },
    beforeDestroy() {
      this.clearTimer();
    },
    created() {
       
    },
    mounted(){
      //  console.log(this.startTime);
        // console.log(this.endTime);
    },
    methods: {
      countdown: function () {
        // console.log(this.endTime)
        const now = new Date().getTime();
        const surplus = this.endTime - now;
        const end = now - this.startTime;
        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;

        this.day = this.complement(s / oneDay).toString();
        var dayIndex = this.day.split('');
        this.dayFirst = dayIndex[0];
        this.daySecond = dayIndex[1];

        this.hour = this.complement(s % oneDay / oneHour).toString();
        var hourIndex = this.hour.split('');
        this.hourFirst = hourIndex[0];
        this.hourSecond = hourIndex[1];


        this.minute = this.complement(s % oneDay % oneHour / 60).toString();
        var minuteIndex = this.minute.split('');
        this.minuteFirst = minuteIndex[0];
        this.minuteSecond = minuteIndex[1];

        this.second = this.complement(s % oneDay % oneHour % 60).toString();
        var secondIndex = this.second.split('');
        this.secondFirst = secondIndex[0];
        this.secondTwo = secondIndex[1];

        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      countdown2: function () {
        const now = new Date().getTime();
        const surplus = this.startTime - now;
        
        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;

        this.day = this.complement(s / oneDay).toString();
        var dayIndex = this.day.split('');
        this.dayFirst = dayIndex[0];
        this.daySecond = dayIndex[1];

        this.hour = this.complement(s % oneDay / oneHour).toString();
        var hourIndex = this.hour.split('');
        this.hourFirst = hourIndex[0];
        this.hourSecond = hourIndex[1];


        this.minute = this.complement(s % oneDay % oneHour / 60).toString();
        var minuteIndex = this.minute.split('');
        this.minuteFirst = minuteIndex[0];
        this.minuteSecond = minuteIndex[1];

        this.second = this.complement(s % oneDay % oneHour % 60).toString();
        var secondIndex = this.second.split('');
        this.secondFirst = secondIndex[0];
        this.secondTwo = secondIndex[1];

        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;

      },
      complement: function (num) {
        return num < 10 ? '0' + num : num;
      },
      clearTimer: function () {
        clearInterval(this.timer)
      },
      // 获取服务器时间，判断活动是否已结束
      getSystemDateTime: function (now) {
        console.log(now)
        // debugger
        //console.log("getSystemDateTime=>" + now);
        this.isShow = true;
     
        if ( now < this.startTime) {
          console.log(this.startTime,'1')
          this.timer = setInterval(this.countdown2, 100);
        }else {
             console.log(this.startTime,'2')
          this.timer = setInterval(this.countdown, 100);
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .mb5{
    margin-bottom: .5rem;
  }
  .countdown{
    position: relative;
    z-index: 1;
    /*height:.15rem;*/
    /*text-align:left;*/
    text-align: center;
    font-size: .12rem;
    color:#fff;
    /*background:url(../../assets/image/active/scholarship/countdown2.png) no-repeat;*/
    background-size:80%;background-position-x: .45rem;
    .num {
      display:inline-block;
      width:.13rem;
      height:.22rem;
      line-height: .22rem;
      border-radius: .04rem;
      vertical-align:bottom;
      background-color: rgba(242, 34, 34, .1);
      font-weight: 400 !important;
      margin: 0 .01rem;
      /*color:rgba(61,91,244,1);*/  
     color: #E91D0D!important;
      font-size:.18rem;
      background: white !important;
      &.big{
        width:.11rem;
        background-color: #fff;
        /*height:.34rem;*/
        /*line-height:.05rem;*/
        /*font-size:.24rem;*/
      }
      /*&.day{margin-left:.2rem;}*/
      /*&.hour{margin-left:.2rem;}*/
      /*&.minute{margin-left:.29rem;}*/
      /*&.second{margin-left:.28rem;}*/
      /*&.millisecond{margin-left:.37rem;}*/
    }
  }
</style>
