<template>
  <div id="uploader-demo">
    <!--用来存放item-->
    <div id="fileList" class="uploader-list"></div>
    <div :id="picker">
    </div>
    <slot><i class="icon i-upload"></i></slot>
  </div>
</template>

<script>
  export default {
    props:{
      picker:String,
      type:String,
      server:{
        type:String,
        default:'/proxy/uploadFileNew'
      },
      auto:{
        type:Boolean,
        default:true
      },
      list:{
        type:Boolean,
        default:false
      },
      addBtn:{
        type:String,
        default:''
      },
      progress:{
        type:Boolean,
        default:false
      }
    },
    data() {
      return {
        // 上传地址
        uploadUrl:'https://zm.yzou.cn/bcc/uploadFile',
        // 添加的文件数量
        fileCount: 0,
        // 添加的文件总大小
        // 优化retina, 在retina下这个值是2
        ratio: undefined,
        // 缩略图大小
        thumbnailWidth: 100,
        thumbnailHeight: 100,
        // 可能有pedding, ready, uploading, confirm, done.
        state: 'pedding',
        percentages: {},
        supportTransition: undefined,
        uploader: undefined,
        fileList: undefined,
        fileData: {
          refId: undefined,
          projectId: undefined
        },
      }
    },
    created(){

    },
    mounted() {
        if(!window.jQueryStatus){
          this.getjQuery()
        }else{
          this.init();
        }
    },
    methods: {
      init(){
        if(global.$){
          if(global.WebUploader){
            this.initWebuploader(this.picker);
            $(".webuploader-pick").css("height","100%")
          }else{
            this.getWebuploader()
          }
        }else{
            setTimeout(()=>{
              this.init()
            },100)
        }
      },
      getjQuery(){
        let $script=document.createElement("script");
        $script.type = 'text/javascript';
        $script.charset = 'utf-8';
        $script.src= '//lib.baomitu.com/jquery/2.2.4/jquery.js';
        $script.onload = () => {
          this.getWebuploader()
        };
        global.document.body.appendChild($script);
        window.jQueryStatus=true;
      },
      getWebuploader(){
        let $script=document.createElement("script");
        $script.type = 'text/javascript';
        $script.charset = 'utf-8';
        $script.src= '//cdn.staticfile.org/webuploader/0.1.0/webuploader.html5only.js';
        //$script.src= '/static/webuploader/index.js';
        $script.onload = () => {
          this.initWebuploader(this.picker);
          $(".webuploader-pick").css("height","100%")
        };
        global.document.body.appendChild($script);
      },
      initWebuploader(picker){
        var $ = jQuery,
          state = 'pending';
        this.uploader = WebUploader.create({
          // 选完文件后，是否自动上传。
          // 文件接收服务端。
          auto:this.auto,
          server: this.server,
          // 内部根据当前运行是创建，可能是input元素，也可能是flash.
          pick: {
           id:'#'+picker,
          },
          // 只允许选择图片文件。

          accept: {
            title: 'Images',
            extensions: 'gif,jpg,jpeg,bmp,png',
            mimeTypes: 'image/*'
          },
          duplicate :false,
          headers:{authToken:JSON.parse(localStorage.getItem('authToken'))}
        });
        var that=this;
        if(this.list){
          this.uploader.on('fileQueued', function (file) {
            $("#fileList").empty()
            var $li = $(
              '<div id="' + file.id + '" class="cp_img">' +
              '<img>' +
              '<div class="cp_img_jian"></div></div>'
              ),
              $img = $li.find('img');
            // $list为容器jQuery实例
            $("#fileList").append($li);
            var thumbnailWidth=$("#fileList").parent().parent().width(),thumbnailHeight=$("#fileList").parent().parent().height()
            // 创建缩略图
            // 如果为非图片文件，可以不用调用此方法。
            // thumbnailWidth x thumbnailHeight 为 100 x 100
            that.uploader.makeThumb(file, function (error, src) {
              if (error) {
                $img.replaceWith('<span>不能预览</span>');
                return;
              }
              $img.attr('src', src);
            }, thumbnailWidth, thumbnailHeight);
          });
        }
        if(this.progress){
          this.uploader.on( 'uploadProgress', function( file, percentage ) {
            var $li = $( '#fileList' ),
              $percent = $li.find('.progress span');
            // 避免重复创建
            if ( !$percent.length ) {
              $percent = $('<p class="progress"><span></span></p>')
                .appendTo( $li )
                .find('span');
            }
            $percent.css({"height":'5px',"display":"block","position":"absolute","backgroundColor":"#59b7ff","bottom":0,"borderRadius":"2px"})
            $percent.css( 'width', percentage * 100 + '%' );
          });
        }
        if(this.addBtn){
          this.uploader.addButton({
            id: '#'+this.addBtn,
            innerHTML: '选择文件'
          });
        }
        this.uploader.on( 'uploadSuccess', function( file ,res) {
          that.$indicator.close()
            if(res.code=='00') {
              console.log(that.type, res.body)
              that.$emit('upload', res.body, that.type);
            }else{
              that.$modal({message: res.message,icon:'warning'})
            }
        });
        this.uploader.on( 'uploadProgress', function( file, percentage ) {
            console.log(percentage * 100 + '%');
        });
        this.uploader.on( 'uploadError', function( file ) {

        });

      },
      startUpload(){
        this.$indicator.open();
        this.uploader.upload();
      }
    },

  }
</script>

<style scoped lang="less">
  #uploader{
    height: 100%;
  }
  .webuploader-container{
    display: block;
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    .webuploader-pick{
      height: 100%;
    }
  }
  #fileList{
    position: absolute;
    left:0;
    top:0;
    width: 100%;
    height: 100%;
  }
  .progress{
    position: absolute;
    bottom:0
  }
</style>
