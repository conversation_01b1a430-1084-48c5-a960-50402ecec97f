<template>
  <div></div>
</template>

<script>
import { isStudent, getIsAppOpen } from '../../../common'
import { toAppHome, toAppEnroll } from '@/common/jump'
// import bridge from '@/plugins/bridge';

export default {
  data() {
    return {
      city: '',
      activityName: '',
      scholarship: '',
      grade: '',
      pfsnLevel: '',
      unvs: {},
      actName: '',
      recruitType: '',
      idCard: '',
      regChannel: '',
      ifWitnessAct: '2',
      pfsnId: '',
      pfsnName: '',
      isAppOpen: false,
      stdName: '',
      isGk: false // 是否是广东开放大学
    }
  },
  created() {
    this.city = this.$route.query.city || ''
    this.activityName = this.$route.query.activityName || ''
    this.scholarship = this.$route.query.scholarship || ''
    this.pfsnLevel = this.$route.query.pfsnLevel || ''
    this.unvs = this.$route.query.unvs || ''
    this.recruitType = this.$route.query.recruitType || ''
    this.actName = this.$route.query.actName || ''
    this.regChannel = this.$route.query.regChannel || ''
    this.pfsnId = this.$route.query.pfsnId || ''
    this.pfsnName = this.$route.query.pfsnName || ''
    this.grade = this.$route.query.grade || ''
    this.ifWitnessAct = this.$route.query.ifWitnessAct || ''
    this.isGk = true && this.$route.query.isGk

    getIsAppOpen(bool => {
      this.isAppOpen = bool
    })
    setTimeout(() => {
      if (!isStudent()) {
        this.toEnroll()
      } else {
        this.getStdLearnInfo()
      }
    }, 100)
  },
  methods: {
    // 获取学员基础信息
    getStdLearnInfo() {
      this.$indicator.open()
      this.$http
        .post('/mkt/stdLearnInfo/1.0/', { recruitType: this.recruitType })
        .then(res => {
          this.$indicator.close()
          try {
            let { code, body } = res
            if (code === '00' && body) {
              // const idCard = body.idCard;
              // const stdName = body.std_name;
              this.idCard = body.idCard
              this.stdName = body.std_name
              body.learnInfos.sort(function (a, b) {
                return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
              })
              const learnInfos = (body.learnInfos || []).find(val => val.stdStage != '10') || {}
              if (!Array.isArray(learnInfos)) {
                if ((['1', '2'].includes(learnInfos.recruitType) && this.recruitType != '3') || (learnInfos.recruitType === '3' && this.recruitType == '3')) {
                  // this.$router.replace({
                  //   name: 'stuInfo',
                  //   query: {idCard: idCard, stdName: stdName, learnId: learnInfos.learnId, activityName: this.activityName}
                  // });
                  this.toHome(learnInfos)
                } else {
                  this.toEnroll()
                }
                const bindStudent = this.storage.getItem('bindStudent')
                if (['4', '5'].includes(learnInfos.recruitType) && bindStudent == '2') {
                // 自考 研究生
                  this.toEnroll()
                } else {
                  this.toHome(learnInfos)
                }
              } else {
                this.toHome(learnInfos)
              }
            } else {
              this.toEnroll()
            }
          } catch (error) {
            this.toEnroll()
          }
        })
        .catch((err) => {
          this.toEnroll()
          this.$indicator.close()
        })
    },
    toHome(learnInfos) {
      if (!this.isAppOpen) {
        this.$router.replace({
          name: 'stuInfo',
          query: {
            idCard: this.idCard,
            stdName: this.stdName,
            learnId: learnInfos.learnId,
            activityName: this.activityName
          }
        })
        return
      }
      toAppHome()
    },
    // 去报读
    toEnroll: function () {
      const query = {
        city: this.city,
        activityName: this.activityName,
        scholarship: this.scholarship,
        pfsnLevel: this.pfsnLevel,
        unvs: this.unvs,
        actName: this.actName,
        recruitType: this.recruitType,
        idC: this.idCard,
        pfsnId: this.pfsnId,
        pfsnName: this.pfsnName,
        ifWitnessAct: this.ifWitnessAct,
        isGk: this.isGk
      }
      if (!this.isAppOpen) {
        this.$router.replace({
          name: 'inviteEnroll',
          query
        })
        return
      }
      if (!this.grade) {
        query.grade = this.recruitType == 2 ? '202309' : '2024' // 必须字符串
      }
      if (this.unvs) {
        query.unvs = JSON.parse(this.unvs)
      }
      toAppEnroll(query)
      setTimeout(() => {
        this.$router.go(-1)
      }, 100)
    }
  }
}
</script>
