<template>
  <div class="yz-ck-forwardCircle">
    <div class="title-img"></div>

    <van-list v-model="loading" :finished="finished" class="moments" finished-text="没有更多了" @load="onLoad">
      <div class="item" v-for="(item,index) in postList" :key="index" @click="toDetail(item.id)">
        <!-- <post-detail :userInfo="item" :userName="item.realName" /> -->
        <post-detail :postInfo="item" :userName="item.realName" :followBtn="true" />
      </div>
    </van-list>
  </div>
</template>

<script>
import PostDetail from './post-detail.vue';
import { formatEmotions, formatWrap } from '@/common';
import { getLocationData } from '@/common/request';

export default {
  components: {
    PostDetail,

  },
  data () {
    return {
      finished: false,
      pagination: {
        num: 0,
        size: 20
      },
      postList: [],
      loading: false,
    };
  },
  computed: {},
  watch: {},
  methods: {

    onLoad () {
      this.pagination.num += 1;
      let params = {
        version: '1', // 有值就行
        pageNum: this.pagination.num,
        pageSize: this.pagination.size,
      }
      let cirLocationData = []

      this.$http.post('/us/selCircleDynamics/1.0/', params)
        .then(async res => {
          const { code, body } = res;
          if (code === '00') {
            if (Array.isArray(body)) {
              body.forEach((item, index) => {
                // 超过100文字裁剪
                item.showText2 = false
                if (typeof item.scText === 'string') {
                  if (item.scText.length > 100) {
                    item.scText2 = item.scText.substring(0, 100);
                    item.showText2 = true;
                  }
                }

                item.scText = formatWrap(item.scText);
                item.scText = formatEmotions(item.scText);

                item.scText2 = formatWrap(item.scText2);
                item.scText2 = formatEmotions(item.scText2);
                item.postType = 1
                if (item.subType == 2) {
                  item.postType = 3
                }
                if (item.subType == 1) {
                  item.postType = 2
                }
                // 图片地址集合分割成数组
                if (item.scPicUrl) {
                  item.scPicUrl = item.scPicUrl.split(",");
                } else {
                  item.scPicUrl = [];
                }

                if (Array.isArray(item.praiseList)) {
                  item.praiseList = item.praiseList.slice(0, 4);
                }
                // 有定位则加一下附近的人
                if (item.id && item.scType && item.addressName && item.cityName) {
                  cirLocationData.push({
                    id: item.id,
                    scType: item.scType,
                    latitude: item.gisLatitude,
                    longitude: item.gisLongitude,
                    mappingId: item.mappingId,
                    subType: item.subType,
                    ifGisData: item.gisLatitude && item.gisLongitude ? 1 : 0
                  })
                }
                // 只有 scType === -3 extContent 才会有信息
                try {
                  item.extContent = JSON.parse(item.extContent)
                } catch (e) {
                  item.extContent = null;
                }
              });
              console.log(cirLocationData, 'cirLocationData');
              if (cirLocationData.length > 0) {
                let locationdata = await getLocationData(cirLocationData)
                locationdata && locationdata.body && locationdata.body.forEach(el => {
                  body.forEach(item => {
                    if (el.id === item.id) {
                      item.cirLocationData = el
                    }
                  });
                });
              }
              this.postList = this.postList.concat(body);
              // 加载状态结束
              this.loading = false;
              //到底了

            }
          }
        })
    },
    toDetail (id) {
      window.location.href = `yuanzhiapp://yzwill.cn/Activity/Circle?params={"circleId":"${id}"}`;
    }
  },
  created () {
    this.onLoad()

  },
  mounted () { },

}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.yz-ck-forwardCircle {
  background-color: #fff;

  padding: 0.27rem 0;
  .title-img {
    background: url(../../../../assets/image/active/ckSprint/title-post.png) no-repeat;
    background-size: 100% 100%;
    width: 1.7rem;
    height: 0.2rem;
    margin: 0 auto 0.18rem;
  }
  .moments {
    background-color: #f7f7f7;
    .item {
      margin-bottom: 0.1rem;
    }
  }
}
</style>