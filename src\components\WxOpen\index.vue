<template>
  <wx-open-launch-app
    class="grid-wxopen"
    @error="callAppError"
    :appid="OpenAppId"
    :extinfo="extinfo"
  >
  <script type="text/wxtag-template">
        <style>
          .down{
            width: 100%;
            height: 100%;
            
          }
        </style>
        <span class='down'>去APP上课</span>
      </script>
  </wx-open-launch-app>
</template>

<script>
import openApp from '@/mixins/openApp';
export default {
  props: {
    extinfo: {
      typeof: String,
      default() {
        return ''
      }
    }
  },
  mixins: [openApp],
  mounted() {
    this.wxCallAppInit(); // 微信标签唤起app
  },
}
</script>

<style lang="less" scoped>
  .grid-wxopen{
    position: absolute;
    left:0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 2;
    opacity: 0;
  }
</style>
