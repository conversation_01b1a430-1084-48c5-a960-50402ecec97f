<template>
  <div class="study-main">
    <!-- 进行中 -->
    <div
      class="study-box"
      v-if="runningData.challengeStatus == 2 && runningData.runningStatus == 1"
    >
      <img class="buckle-one" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="buckle-two" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="study-title" src="../../../../assets/image/active/signAct/run-punch.png" alt />
      <img class="flower" src="../../../../assets/image/active/signAct/flower.png" alt />
      <div class="content-box">
        <div class="header">
          <div class="lf">
            <div class="time">
              <div class="lf-txt">开始时间</div>
              <div class="rg-txt">{{ runningData.challengeStartTime | formatDate("yyyy.MM.dd") }}</div>
            </div>
            <div class="schedule" style="margin-top: 6px">
              <div class="lf-txt">进度</div>
              <div class="rg-txt">{{ runningData.runningCount }}/21</div>
            </div>
          </div>
          <div class="rg">
            <img
              v-if="runningData.todayRunningStatus"
              src="../../../../assets/image/active/signAct/punched.png"
              alt
            />
            <button v-else @click="toRun()">去打卡</button>
          </div>
        </div>
        <p>
          *{{ runningData.runningRemainDays }}天内未完成将挑战失败，您需在{{
            runningData.runningEndTime | formatDate("yyyy.MM.dd")
          }}
          前完成
        </p>
        <div class="detail-box">
          <img src="../../../../assets/image/active/signAct/running-punch.png" alt />
          <div class="txt-box">
            <span>
              当前完成
              <strong>{{ runningData.runningCount }}</strong> 次
            </span>
            <span>
              目标还差
              <strong>{{ 21 - runningData.runningCount }}</strong> 次
            </span>
          </div>
          <van-progress
            :show-pivot="false"
            :percentage="runningData.runningCount * 5 || 0"
            stroke-width="10"
          />
        </div>
      </div>
    </div>
    <!-- 打卡挑战成功 -->
    <div
      class="study-box"
      v-if="runningData.challengeStatus != 4 && runningData.runningStatus == 2 || runningData.challengeStatus == 3"
    >
      <img class="buckle-one" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="buckle-two" src="../../../../assets/image/active/signAct/calendar-buckle.png" alt />
      <img class="study-title" src="../../../../assets/image/active/signAct/run-punch.png" alt />
      <img class="flower" src="../../../../assets/image/active/signAct/flower.png" alt />
      <div class="complete">
        <img src="../../../../assets/image/active/signAct/finish-big.png" alt />
      </div>
    </div>
    <!-- 跑步打卡挑战失败，学习成功,等待下一轮 -->
    <div
      class="only-run-fail-box"
      v-if="
        runningData.remainChallengeCount != 2 &&
        runningData.challengeStatus == 1 &&
        runningData.runningStatus == 1 &&
        learningData.learnStatus == 2
      "
    >
      <!-- <div class="only-run-fail-box"> -->
      <div class="fail-box">
        <p class="tips-txt">
          抱歉，由于您
          <span>跑步打卡</span> 失败
        </p>
        <p class="tips-txt">
          将在
          <span>{{ runningData.challengeStartTime | formatDate("yyyy.MM.dd") }}</span>
          重新开启下一轮挑战
        </p>
        <p class="code-tips" style="margin-top: 30px">请务必扫码添加老师微信</p>
        <p class="code-tips">向老师申请复训</p>
        <img :src="codeUrl" alt />
        <p class="code-txt">长按二维码识别</p>
      </div>
    </div>
  </div>
</template>

<script>
import { imgPosterBaseURL } from "@/config/index";

export default {
  name: "RunBonus",
  props: {
    studyData: {
      type: Object,
      required: true,
    },
    runData: {
      type: Object,
      required: true,
    },
    semesterData: {
      type: Object,
      required: false,
    },
    mobile: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      recordList: [
        {
          id: 1,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 2,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 3,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 4,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 1,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 2,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 3,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 4,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 1,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 2,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 3,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
        {
          id: 4,
          name: "liuWen",
          a: "12/13",
          b: "11/13",
        },
      ],
      imgUrl: null,
    };
  },
  computed: {
    learningData() {
      return this.studyData;
    },
    runningData() {
      console.log(this.runData);
      return this.runData;
    },
    codeUrl() {
      return imgPosterBaseURL + this.semesterData.wechatQrCode;
    },
  },
  // watch: {
  //   semesterData: {
  //     handler(n, o) {
  //       this.imgUrl = imgPosterBaseURL + n.wechatQrCode;
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    toRun() {
      this.$router.push({
        path: "/active/2024signActHuaXia/runRelease",
        query: {
          semesterId: this.runningData.nowChallengeSemesterId,
          punchOrder: "",
          isPatchCard: "",
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@rem: 0.01rem;
.study-main {
  margin-top: 0.3rem;
  .study-box {
    z-index: 2;
    box-sizing: border-box;
    padding: 11 * @rem 5 * @rem 6 * @rem 5 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #fdca65 0%, #f39229 100%);
    box-shadow: inset 0 * @rem 2 * @rem 4 * @rem 0 * @rem
        rgba(255, 255, 255, 0.5),
      inset 0 * @rem -2 * @rem 3 * @rem 0 * @rem rgba(255, 255, 255, 0.5);
    border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
    .buckle-one {
      position: absolute;
      top: -8 * @rem;
      left: 36 * @rem;
    }
    .buckle-two {
      position: absolute;
      top: -8 * @rem;
      right: 36 * @rem;
      z-index: 1;
    }
    .flower {
      width: 1.41rem;
      height: 1.35rem;
      position: absolute;
      top: 0;
      right: 0;
    }
    .study-title {
      margin-bottom: 11 * @rem;
      width: 110 * @rem;
      height: 40 * @rem;
    }
    .content-box {
      z-index: 9;
      box-sizing: border-box;
      padding: 13 * @rem 11 * @rem 24 * @rem 11 * @rem;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
      box-shadow: 0 * @rem 1 * @rem 1 * @rem 0 * @rem #f57100,
        inset 0 * @rem 1 * @rem 1 * @rem 0 * @rem rgba(255, 157, 55, 0.5);
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12 * @rem;
        .lf {
          display: flex;
          flex-direction: column;
          width: 50%;
          .schedule,
          .time {
            display: flex;
            align-items: center;
            justify-content: center;
            .lf-txt {
              width: 35%;
              text-align: right;
              display: inline-block;
              padding-right: 4 * @rem;
              margin-right: 4 * @rem;
              border-right: 1 * @rem solid #f58c38;
              font-size: 12 * @rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #f58c38;
            }
            .rg-txt {
              flex: 1;
              display: inline-block;
              font-size: 12 * @rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #8c4d0e;
            }
          }
        }
        .rg {
          button {
            width: 84 * @rem;
            height: 31 * @rem;
            background: linear-gradient(269deg, #fd8857 0%, #e61c26 100%);
            box-shadow: inset 0 * @rem 0 * @rem 3 * @rem 0 * @rem
              rgba(255, 255, 255, 0.5);
            border-radius: 20 * @rem;
            border: 1 * @rem solid #ffbb56;
            font-size: 16 * @rem;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #fce7ba;
            -webkit-animation: free_download 1s linear alternate infinite;
            animation: free_download 1s linear alternate infinite;
          }
          @keyframes free_download {
            0% {
              transform: scale(1);
            }
            100% {
              transform: scale(1.1);
            }
          }
          img {
            width: 90 * @rem;
            height: 36 * @rem;
          }
        }
      }
      p {
        font-size: 11 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 11 * @rem;
        margin-bottom: 6 * @rem;
      }
      .detail-box {
        margin-top: 18 * @rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        img {
          width: 102 * @rem;
          height: 114 * @rem;
        }
        .txt-box {
          margin-top: 0.1rem;
          width: 90%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            font-size: 12 * @rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #d0054f;
            line-height: 17 * @rem;
            strong {
              margin: 0 2 * @rem 0 6 * @rem;
              font-size: 20 * @rem;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #d0054f;
              line-height: 28 * @rem;
            }
          }
        }
        /deep/.van-progress {
          width: 90%;
        }
        /deep/.van-progress__portion {
          background: linear-gradient(360deg, #e62416 0%, #ffd180 100%);
          box-shadow: inset 0px 0px 3px 1px rgba(255, 255, 255, 0.5);
        }
      }
    }
    .complete {
      width: 100%;
      height: 234 * @rem;
      background: url("../../../../assets/image/active/signAct/run-complete.png")
        no-repeat;
      background-size: 100% 100%;
      position: relative;
      img {
        width: 78 * @rem;
        height: 78 * @rem;
        position: absolute;
        top: -20 * @rem;
        right: 0;
      }
    }
    .over-box {
      width: 100%;
      height: 268 * @rem;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 81 * @rem 28 * @rem 0 28 * @rem;
      p {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: 20 * @rem;
      }
    }
    .fail-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 59 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        text-align: center;
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
      }
      .tips-txt {
        text-align: center;
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        span {
          text-decoration: underline;
          color: #453838;
          font-weight: 600;
        }
      }
      img {
        margin: 15 * @rem 0 7 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      .code-txt {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 17 * @rem;
      }
    }
    .wait-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 16 * @rem 16 * @rem 6 * @rem 6 * @rem;
      padding: 59 * @rem 28 * @rem 20 * @rem 28 * @rem;
      p {
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
      }
      .times {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 500;
        color: #ea5a59;
      }
      img {
        margin: 15 * @rem 0 6 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
    }
  }
  .edge {
    position: relative;
    top: -26 * @rem;
    width: 100%;
    height: 30 * @rem;
    background: linear-gradient(90deg, #f29c36 0%, #e16318 100%);
    border-radius: 0 0 11 * @rem 11 * @rem;
  }
  .only-run-fail-box {
    height: 3.5rem;
    width: 100%;
    background: url("../../../../assets/image/active/signAct/only-run-fail.png")
      no-repeat;
    background-size: 100% 100%;
    .fail-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 109 * @rem 30 * @rem 0 30 * @rem;
      box-sizing: border-box;

      p {
        text-align: center;
        font-size: 13 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
      }
      .code-tips {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
      }
      .tips-txt {
        text-align: center;
        font-size: 16 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        span {
          text-decoration: underline;
          color: #453838;
          font-weight: 600;
        }
      }
      img {
        margin: 15 * @rem 0 7 * @rem 0;
        width: 105 * @rem;
        height: 104 * @rem;
      }
      .code-txt {
        font-size: 12 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e53d19;
        line-height: 17 * @rem;
      }
    }
  }
}
</style>
