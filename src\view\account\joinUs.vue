<template>
    <div class="joinUsBox">
        <top-bar title="加入我们一起上进学习"></top-bar>
        <div class="contentBox">
            <p class="contentOne">Hi~同学,终于等到你!</p>
            <!--<p class="contentTwo same">！</p>-->
            <p class="contentThree same">这里有<span class="num">60W+</span>同学</p>
            <p class="contentFour same">在这里一起学习上进哦</p>
            <input type="text" placeholder="输入姓名" v-model='realName' maxlength="10">
            <button @click="updateUserName()">下一步</button>
        </div>
    </div>
</template>

<script>
import topBar from '../../components/topBarTwo'
import storage from '../../plugins/storage'
export default {
    data() {
        return {
            realName:'',
            redirect:'',
            inviteId:''

        }
    },
    created() {
        this.redirect = this.$route.query.redirect
        this.inviteId = this.$route.query.inviteId || ''
    },
    methods:{
        updateUserName() {
            var pattern =/^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,10}$/;
            if(!pattern.test(this.realName)) {
            this.$modal({message: '请输入正确的姓名！', icon: 'warning'});
            return;
            }
           this.$indicator.open();
            this.$http.post('/us/updateUserName/1.0/',{realName:this.realName}).then( res=>{
                const {code,body}  = res;
                if(code =='00') {
                this.storage.setItem('realName', (this.realName || ''));
                this.$indicator.close();
                if(this.redirect) {
                  const { query } = this.$route;
                //   this.$router.push({path:this.redirect, query: { ...query, redirect: '' }});
                  this.$router.replace({path:this.redirect, query: { ...query, redirect: '' }});
                }else {
                  this.$router.replace({name:'home'})
                  }
                }
            }).catch(error => {
                this.$indicator.close();
            })
        }
    },

    components:{topBar}
}
</script>



<style lang="less" scoped>
.joinUsBox {
    min-height: 100vh;
    background: url('../../assets/image/joinUsBg.png') no-repeat ;
    background-size: 100% 100%;
    .contentBox {
        margin: .4rem .2rem 0 .2rem;
        background-color: #fff;
        border-radius:5px;
        box-shadow:0px 2px 14px 0px rgba(0,0,0,0.2);
        p {
            font-size: .17rem;
            color:rgba(68,68,68,1);
            line-height:25px;
            padding-left: .3rem;
        }
        .contentOne {
           padding-top: .25rem
        }
        .same{
            margin-top: .09rem;
        }
        .contentThree {
            .num {
                color: #F0465D;
                font-size: .19rem;
            }
        }
        input {
            height: .49rem;
            width: 2.85rem;
            border: none;
            border-radius:5px;
            border:1px solid rgba(232,91,87,1);
            margin: 0 .25rem;
            padding-left: .1rem;
            margin-bottom: .25rem;
            margin-top:.2rem;
        }
        button {
            width: 2.85rem;
            height: .49rem;
            background:linear-gradient(215deg,rgba(239,66,92,1) 0%,rgba(248,111,107,1) 100%);
            border-radius:5px;
            border:none;
            font-size: .16rem;
            font-weight:500;
            color:rgba(255,255,255,1);
            margin:0 .25rem .25rem .25rem;

        }
    }
}
</style>
