<template>
  <div class="yz-growth-loading">
    <div class="container">

      <div class="box">
        <div class="black"></div>
        <span class="s1">上进青年</span>
      </div>
      <p class="p1">
        加载中
        <span class="dot">...</span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    };
  },
  mounted() {
    setTimeout(() => {
      this.finish();
    }, 3800);
  },
  methods: {
    finish() {
      this.$emit('finish');
    },
  },
};
</script>

<style lang="less">
  .yz-growth-loading{
    height: 100vh;
    width: 100%;
    position: absolute;
    z-index: 2;
    background: #FD6357;
  }
  .container{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    .box{
      background: url(../../../assets/image/active/growthSystem/loading-bg.png) no-repeat;
      background-size: 100% 100%;
      width: 2.83rem;
      height: 0.73rem;
      padding-top: 0.2rem;
      padding-left: 0.26rem;
      letter-spacing: 0.24rem;
      font-size: 0.34rem;
      .s1{
        position: relative;
        z-index: 2;
      }
    }
    .black{
      position: absolute;
      left: 0.01rem;
      top: 0.19rem;
      height: 0.54rem;
      z-index: 1;
      // width: 0;
      background: #000;
      animation: blackWidth 2s ease 1.5s forwards;
    }
    .p1{
      position: relative;
      font-size: 0.14rem;
      text-align: center;
      margin-top: 0.2rem;
    }
    .dot{
      display: inline-block;
      width: 0.15rem;
      vertical-align: bottom;
      overflow: hidden;
      animation: dotting 2s infinite step-start;
      font-size: 0.14rem;
      position: absolute;
    }
    // .p1:after{
    //   content: '.';
    //   font-size: 0.14rem;
    //   animation: dotting2 2s infinite step-start;
    //   position: absolute;
    // }
  }
  @keyframes dotting {
    0% {
      width: 0.03rem;
      margin-right: 0.06rem;
    }
    33% {
      width: 0.07rem;
      margin-right: 0.04rem;
    }
    66% {
      width: 0.11rem;
      margin-right: 0;
    }
    100% {
      width: 0.03rem;
      margin-right: 0.06rem;
    }
  }

  @keyframes dotting2 {
    0% {
        content: '.';
    }
    33% {
        content: '..';
    }
    66% {
        content: '...';
    }
    100% {
        content: '.';
    }
  }
  @keyframes blackWidth{
    from{
      right: calc(100% - 0.21rem);
    }
    to{
      right: 0.21rem;
    }
  }
</style>
