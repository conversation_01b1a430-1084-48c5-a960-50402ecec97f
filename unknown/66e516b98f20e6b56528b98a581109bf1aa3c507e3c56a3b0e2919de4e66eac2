<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no,minimal-ui, viewport-fit=cover"
    />
    <meta
      http-equiv="Content-Security-Policy"
      content="script-src 'self' 'unsafe-eval' 'unsafe-inline' 'unsafe-inline' blob: qzonestyle.gtimg.cn pv.sohu.com unpkg.com res.wx.qq.com hm.baidu.com prtas.videocc.net  user-analysis.7moor.com yzims.oss-cn-shenzhen.aliyuncs.com static.yzou.cn img.yzwill.cn img2.yzwill.cn pingjs.qq.com api.map.baidu.com api0.map.bdimg.com webapi.amap.com restapi.amap.com player.polyv.net cdnjs.cloudflare.com lib.baomitu.com qiyukf.com web.sdk.qcloud.com cdn.staticfile.org *.polyv.net *.videocc.net; object-src player.polyv.net"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="x-rim-auto-match" content="none" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- <meta name="referrer" content="never" /> -->
    <title></title>
    <!-- loading CSS 注入 -->
    <%= htmlWebpackPlugin.options.loading.css %> 

    <!-- VConsole 调式 -->
    <% if ((!htmlWebpackPlugin.options.isDev) && htmlWebpackPlugin.options.env !== 'production') { %>
    <script src="/static/utils/VConsole/vconsole.min.js"></script>
    <script>
      var vConsole = window.VConsole && new window.VConsole();
    </script>
    <% } %>

    <script src="//player.polyv.net/script/player.js"></script>
  </head>

  <body>
    <div id="app"><%= htmlWebpackPlugin.options.loading.html %></div>
  </body>
  <script>
    window._AMapSecurityConfig = {
      securityJsCode: "54d97d2403546a950f41b2cf9701f70e",
    }; //在这里添加
  </script>
</html>
