//# webpack 开发环境配置

const { merge } = require('webpack-merge')
let baseConfig = require('./webpack.base')

const webpackDevConfig = merge(baseConfig, {
  mode: 'development',
  watchOptions: {
    // 允许 webpack 将这段时间内进行的任何其他更改都聚合到一次重新构建里。以毫秒为单位
    aggregateTimeout: 600,
    ignored: /node_modules/,
  },
  devtool: 'eval-cheap-module-source-map',
  devServer: {
    // 因为我们的路由模式是 history 所以必须配置这个
    // 使用 HTML5 History API 时，可能必须提供 index.html 页面来代替任何 404 响应。 通过将 historyApiFallback 设置为 true 来启用它：
    historyApiFallback: true,
    open: true,
    hot: true, //开启HMR 热替换功能
    port: 8081,
    proxy: {
      '/proxy': {
        changeOrigin: true,
        // target: "http://test0-zm.yzwill.cn", // 145环境
        // target: "http://test4.yzwill.cn", // 51环境
        target: 'http://192-pre.yzou.cn', // 预发布环境
        // target: "http://test1-zm.yzwill.cn", // 150环境
        // target: "http://test2-zm.yzwill.cn", // 171环境
        // target: "https://zm.yzou.cn", //正式环境
      },
    },
    client: {
      logging: 'error',
    },
  },
  cache: {
    type: 'filesystem', //开发环境下默认为memory类型，生产环境 cache 配置默认是关闭的不会生效。
    buildDependencies: {
      config: [__filename],
    },
  },
})

module.exports = webpackDevConfig
