<template>
    <div class="main">
        <template v-if="canJoin">
                <!-- <audio src="../../../../static/byInviation.mp3" id="audio" autoplay loop hidden class="bgMusic"></audio> -->
                <swiper :options="swiperOption" class="swiper-box" style="height:100vh">
                    <swiper-slide class="swiper-item">
                        <div class="index">
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                            <p>上滑查看</p>
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="invitation">
                            <p>亲爱的<span>{{std_name}}</span>同学</p>
                            <div class="detail">&emsp;&emsp; 祝贺你在今年的成考中取得优异的成绩，获得远智全额奖学金。感谢你选择与远智一起携手上进，不断努力奋斗，我们将于12月26日在华南农业大学五山校区举办全额奖学金颁奖典礼，为同学们颁发奖学金证书。
                            <br>&emsp;&emsp;远智教育全体师生现诚邀你，亲临现场参加“上进远智，超越自我——2021级全额奖学金”颁奖典礼，和大家一起见证人生高光时刻！</div>
                            <p class="sponsor">远智教育</p>
                            <p class="sponsor">2020年12月20日</p>
                            <p class="state">活动最终解释权归远智所有</p>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="abstract" :class="{'IOSabstract':isIOS}">
                            <p>&emsp;&emsp;远智教育成立至今，以严谨规范的办学宗旨得到了高校的高度认同，先后获得二十多所高校授权成为高校校外教学点，并与数所高校建立战略合作关系，现已在广东建有高校校外教学点19座，培养了数万名优秀毕业生，在读学员近10万人，学员转介绍率高达70%以上。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_1.png" alt=""></div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="road" :class="{'IOSroad':isIOS}">
                            <p>&emsp;&emsp;远智教育秉承“帮助每个人更上进”的使命，立志成为“上进文化的引领者”。在帮助学员获得学历文凭和能力提升的同时，我们更注重帮助学员建立上进的价值观，养成上进的行为习惯，从而影响身边更多的人成长为上进青年。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_2.png" alt=""></div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="past" :class="{'IOSpast':isIOS}">
                            <p>&emsp;&emsp;2018年12月、2019年1月，在广州总部校区举办了2届隆重的全额奖学金颁奖典礼，获奖学员相聚于此，带上家人好友，走过红毯，在颁奖台上领取属于自己的荣耀，并在许愿树下许下对新学期的美好愿望，共同分享喜悦，感受人生高光时刻。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_4.png" alt=""></div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="meeting" :class="{'IOSmeeting':isIOS}" >
                                <p>【典礼时间】</p>
                                <p>时间：2020年12月26日13:00</p>
                                <p>地址：广州市天河区五山路华南农业大学继续教育学院二楼报告厅210室</p>
                                <p style="margin-top:.1rem">【温馨提示】</p>
                                <p>1、全额奖学金证书是重要的领取凭证，请学员本人携带身份证原件准时到场领取。</p>
                                <p>2、正值疫情管控期间，进出校园限定人数且需提前预约登记，请参与活动的学员和其家人在报名页面登记身份信息，上传粤康码截图。</p>
                                <baidu-map :center="center" :zoom="zoom"  @ready="handler" class="bm-view" :class="{'IOSbm-view':isIOS}" ak="YOUR_APP_KEY" :double-click-zoom='false' :dragging='false' :pinch-to-zoom="false">
                                    <bm-marker :position="position" @click="infoWindowOpen" class="marker">
                                        <bm-info-window :show="show" @close="infoWindowClose" @open="infoWindowOpen">
                                        广州市天河区五山路华南农业大学继续教育学院
                                        <br />
                                        <span style="color:red" @click="toMap">点击去这里</span>
                                        </bm-info-window>
                                    </bm-marker>
                                </baidu-map>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="process">
                            <p class="item"><span>13 : 00</span><span style="margin-left:.4rem;">签到</span></p>
                            <p class="item"><span>13 : 20</span><span style="margin-left:.4rem;">抽福气环节</span></p>
                            <p class="item"><span>14 : 00</span><span style="margin-left:.4rem;">典礼开始</span></p>
                            <p class="item"><span>14 : 05</span><span style="margin-left:.4rem;">校长致辞</span></p>
                            <p class="item"><span>14 : 20</span><span style="margin-left:.4rem;">颁发证书</span></p>
                            <p class="item"><span>15 : 00</span><span style="margin-left:.4rem;">优秀学员代表讲话</span></p>
                            <p class="item"><span>15 : 15</span><span style="margin-left:.4rem;">入学仪式</span></p>
                            <p class="item"><span>15 : 40</span><span style="margin-left:.4rem;">全员合影</span></p>
                            <p class="item"><span>16 : 00</span><span style="margin-left:.4rem;">活动结束</span></p>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="canShare">
                            <div class="block" v-if="!join"></div>
                            <p class="hint" v-if="!join">疫情期间，应高校要求需要提供相关参会人信息</p>
                            <div class="btn" :class="{'gray':!annexUrl}" @click="submit" v-if="!join">马上报名</div>
                             <div class="apply" v-if="!join" :class="{'IOSapply':isIOS}">
                                <div class="addParticipants" @click="add">添加参会人</div>
                                <div class="participants">
                                    <span class="tit">参会人信息一 
                                        <img src="../../../assets/image/active/fullScholarshipInviation/down.png" v-if="!participants_1Show" @click="participantsShow" alt="">
                                        <img src="../../../assets/image/active/fullScholarshipInviation/up.png" v-else @click="participantsShow" alt="">
                                    </span>
                                    <div class="information" v-if="!participants_1Show">
                                        <p>姓名</p>
                                        <input type="text" class="ipt" v-model="std_name" disabled>
                                        <p>手机</p>
                                        <input type="text" class="ipt" v-model="mobile" disabled>
                                        <p>身份证</p>
                                        <input type="text" class="ipt" v-model="idCard" disabled>
                                        <div class="uploadInformation">
                                            <div class="add">
                                                <div>上传粤康码</div>
                                                <div class="file-box">
                                                    <img :src="annexUrl|basePath" v-if="annexUrl" @click="showImgFn(annexUrl,0)"/>
                                                    <webuploader @upload="uploadImg" :picker="'filePicker'+0" :type="'0'" v-else></webuploader>
                                                    <div class="idCard" v-if="!annexUrl"><img src="../../../assets/image/active/fullScholarshipInviation/add.png"/></div>
                                                </div>
                                            </div>
                                            <div class="demonstration">
                                                <div>示范</div>
                                                <div class="file-box" @click="clickImg">
                                                    <img src="../../../assets/image/active/fullScholarshipInviation/sample.png"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="addTemplate" v-for="(i,index) in invitatList">
                                    <span class="tit">参会人信息{{i.num | numFliter}} 
                                        <img src="../../../assets/image/active/fullScholarshipInviation/down.png" class="down" @click="down(i)" :class="'down'+i.num" alt="">
                                        <img src="../../../assets/image/active/fullScholarshipInviation/up.png" class="up" @click="up(i)" :class="'up'+i.num" alt="" style="display:none;">
                                    </span>
                                    <div class="information" :class="'information'+i.num">
                                        <p>姓名</p>
                                        <input type="text" class="ipt" v-model="i.stdName" @blur="resolveName(i.stdName,index)"  @click="onfocus"/>
                                        <p>手机</p>
                                        <input type="text" class="ipt" v-model="i.mobile" maxlength="11" @blur="resolveMobile(i.mobile,index)" @focus="onfocus"/>
                                        <p>身份证</p>
                                        <input type="text" class="ipt" v-model="i.idCard" maxlength="18" @blur="resolveCard(i.idCard,index)" @focus="onfocus"/>
                                        <div class="uploadInformation">
                                            <div class="add">
                                                <div>上传粤康码</div>
                                                <div class="file-box">
                                                    <img :src="i.urlImg|basePath" v-if="i.urlImg" @click="showImgFn(i.urlImg,index+1)"/>
                                                    <webuploader @upload="uploadAddImg" :picker="'filePicker'+i.num" :type="(index+1).toString()" v-else></webuploader>
                                                    <div class="idCard" v-if="!i.urlImg"><img src="../../../assets/image/active/fullScholarshipInviation/add.png"/></div>
                                                </div>
                                            </div>
                                            <div class="demonstration">
                                                <div>示范</div>
                                                <div class="file-box" @click="clickImg">
                                                     <img src="../../../assets/image/active/fullScholarshipInviation/sample.png"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="achieve" v-else>
                                <p style="font-size:.2rem;">恭喜你报名成功</p>
                                <div class="qrcode">
                                    <img :src="success" alt="">
                                </div>
                                <p style="font-size:.16rem;">全额奖学金群</p>
                                <p class="p1">长按识别二维码进群，和其他奖学金获奖者一起参与颁奖典礼吧！</p>
                            </div>
                            <div class="shareBtn" @click="guide"><img src="../../../assets/image/active/fullScholarshipInviation/shareBtn.png" alt=""></div>
                        </div>
                        <div class="lead" v-if="showOpen" @click="showOpen=false;">
                            <img src="../../../assets/image/active/fullScholarshipInviation/share.png" alt="">
                        </div>
                    </swiper-slide>
                </swiper>
        </template>
        <template v-else>
            <swiper :options="swiperOption" class="swiper-box" style="height:100vh">
                    <swiper-slide class="swiper-item">
                        <div class="index">
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                            <p>上滑查看</p>
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="invitation" :class="{'IOSinvitation':isIOS}">
                            <p>亲爱的<span>{{std_name}}</span>同学</p>
                            <div class="detail">&emsp;&emsp;您好！属于远智人的“2021级全额奖学金”颁奖典礼，已如约而至。12月26日，在华南农业大学五山校区，全额奖学金颁奖典礼正式开启，即使无法亲临现场，也可来到直播间,体验现场欢快爆棚的气氛，不仅能跟“高分学霸”云聊天，还有至少3轮的百份限量定制款Q萌好物等你来抽取。
                            <br>&emsp;&emsp;心动不如行动，12月26日下午13:20，锁定远智教育直播间，抱枕、水杯、风衣等好礼和你不见不散~</div>
                            <p class="sponsor">远智教育</p>
                            <p class="sponsor">2020年12月20日</p>
                            <p class="state">活动最终解释权归远智所有</p>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="meeting" :class="{'IOSmeeting':isIOS}">
                            <p>【直播时间】</p>
                            <p>2020年12月26日13:20</p>
                            <p style="margin-top:.1rem">【观看直播方式】</p>
                            <p>打开远智教育App——发现——上进直播live——全额奖学金颁奖典礼</p>
                            <p class="way"><img src="../../../assets/image/active/fullScholarshipInviation/step_1.png" alt=""></p>
                            <p class="step">第一步：点击底部「发现」</p>
                            <p class="way"><img src="../../../assets/image/active/fullScholarshipInviation/step_2.png" alt=""></p>
                            <p class="step">第二步：找到「上进直播Live」</p>
                            <div class="btnDown" @click="downApp">点此下载远智教育App</div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="process">
                            <p class="item"><span>13 : 00</span><span style="margin-left:.4rem;">签到</span></p>
                            <p class="item"><span>13 : 20</span><span style="margin-left:.4rem;">抽福气环节</span></p>
                            <p class="item"><span>14 : 00</span><span style="margin-left:.4rem;">典礼开始</span></p>
                            <p class="item"><span>14 : 05</span><span style="margin-left:.4rem;">校长致辞</span></p>
                            <p class="item"><span>14 : 20</span><span style="margin-left:.4rem;">颁发证书</span></p>
                            <p class="item"><span>15 : 00</span><span style="margin-left:.4rem;">优秀学员代表讲话</span></p>
                            <p class="item"><span>15 : 15</span><span style="margin-left:.4rem;">入学仪式</span></p>
                            <p class="item"><span>15 : 40</span><span style="margin-left:.4rem;">全员合影</span></p>
                            <p class="item"><span>16 : 00</span><span style="margin-left:.4rem;">活动结束</span></p>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="past" :class="{'IOSpast':isIOS}">
                            <p>&emsp;&emsp;2018年12月、2019年1月，在广州总部校区举办了2届隆重的全额奖学金颁奖典礼，获奖学员相聚于此，带上家人好友，走过红毯，在颁奖台上领取属于自己的荣耀，并在许愿树下许下对新学期的美好愿望，共同分享喜悦，感受人生高光时刻。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_4.png" alt=""></div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="abstract" :class="{'IOSabstract':isIOS}">
                            <p>&emsp;&emsp;远智教育成立至今，以严谨规范的办学宗旨得到了高校的高度认同，先后获得二十多所高校授权成为高校校外教学点，并与数所高校建立战略合作关系，现已在广东建有高校校外教学点19座，培养了数万名优秀毕业生，在读学员近10万人，学员转介绍率高达70%以上。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_1.png" alt=""></div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                    </swiper-slide>
                    <swiper-slide class="swiper-item">
                        <div class="road" :class="{'IOSroad':isIOS}">
                            <p>&emsp;&emsp;远智教育秉承“帮助每个人更上进”的使命，立志成为“上进文化的引领者”。在帮助学员获得学历文凭和能力提升的同时，我们更注重帮助学员建立上进的价值观，养成上进的行为习惯，从而影响身边更多的人成长为上进青年。</p>
                            <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_2.png" alt=""></div>
                            <div class="shareBtn" @click="guide"><img src="../../../assets/image/active/fullScholarshipInviation/shareBtn.png" alt=""></div>
                            <div class="lead" v-if="showOpen" @click="showOpen=false;">
                                <img src="../../../assets/image/active/fullScholarshipInviation/share.png" alt="">
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
        </template>
        <img-popup ref="imgpop" v-if="showImg" :imgSrc="showImgSrc.src" v-on:changeImg="changeImg"></img-popup>
        <share
            :title="`【${std_name}】邀请你来围观2021级全额奖学金颁奖典礼啦！`"
            desc="上进远智，超越自我！远智教育2021级全额奖学金颁奖典礼，12月26日远智教育总部校区正式开典！"
            :imgUrl="imgUrlL"
            :link="shareLink"
            ref="share"
            regOrigin="31"
        />
    </div>
</template>
<script>
import {isIDCard, isphone, } from '../../../common';
import { BaiduMap, BmMarker, BmInfoWindow } from "vue-baidu-map";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import config from "../../../config";
import share from "@/components/share";
import { Toast ,Dialog} from 'vant';
import webuploader from "@/components/uploader";
import imgPopup from "@/components/imgPopupChange";
import {isIOS} from '../../../common';
import { ImagePreview } from 'vant';
export default {
    filters: {
      basePath: function(url) {
        if (!url) return url;
        if (url.indexOf("std/") != "-1") {
          return config.imgBaseURL + url;
        } else {
          return config.imgBasePath + url;
        }
      },
      numFliter:function(val){
          if(val == '')return;
          if(val == '1')return '二'
          if(val == '2')return '三'
      }
    },
  data() {
    return {
        center: { lng: 0, lat: 0 },
        position: { lng: 113.3475, lat: 23.1665 },
        zoom: 15,
        showOpen:false,
        swiperOption: {
            direction: "vertical",
            slidesPerView: 1,
            noSwipingClass:'apply',
            mousewheel: true
        },
        std_name:'',
        mobile:'',
        idCard:'',
        annexUrl:'',
        showImg: false,
        showImgSrc: {
            src: "",
            type: ""
        },
        participants_1Show:false,
        invitatList:[],
        liveCode:'http://yzims.oss-cn-shenzhen.aliyuncs.com//shangjin/直播群.png',
        success:'http://yzims.oss-cn-shenzhen.aliyuncs.com//shangjin/报名成功群.png',
        canJoin:false,
        join:false,
        show:true,
        shareLink: `${window.location.origin}/active/fullScholarshipInviation/shareInvitation`,
        imgUrlL: "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/invitation.jpg",
        isIOS:false,
        sampleImg:require('../../../assets/image/active/fullScholarshipInviation/sample.png'),
        can:true
    };
  },
  created(){
      this.getUserInfo();
  },
  mounted(){
      window.sessionStorage.setItem("regOrigin",'13');
      this.keyboard();
  },
  methods: {
        handler({ BMap, map }) {
            this.center.lng = 113.348;
            this.center.lat = 23.169;
            this.zoom = 16;
        },
        infoWindowClose() {
            this.show = false;
        },
        infoWindowOpen() {
          this.show = true;
        },
        toMap() {
            window.location.href = `http://api.map.baidu.com/marker?location=${this.position.lat},${this.position.lng}&title=广州市天河区五山路华南农业大学继续教育学院&output=html`;
        },
        resolveName(Name,index){
            this.onblur();
            var pattern =/^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,10}$/;
            if(Name!=''&&!pattern.test(Name)) {
                Toast.fail('请输入正确的姓名！');
                this.invitatList[index].stdName = '';
                return;
            }
        },
        resolveCard(idCard,index){
            this.onblur();
            if(idCard!=''&& !isIDCard(idCard)){
                Toast.fail('请输入正确的身份证号码');
                this.invitatList[index].idCard = '';
                return
            }
        },
        resolveMobile(mobile,index){
            this.onblur();
            if(mobile!=''&& !isphone(mobile)){
                Toast.fail('请输入正确的手机号码');
                this.invitatList[index].mobile = '';
                return
            }
        },
        add(){
            if(!this.annexUrl){
                Toast('请先完善信息');
                return;
            }
            if(this.invitatList.length>=2){
                Toast('因场地有限，最多只能带两人参加！');
                return;
            }
            if(this.invitatList.length>=1){
                if(!this.invitatList[0].stdName || !this.invitatList[0].mobile || !this.invitatList[0].idCard || !this.invitatList[0].urlImg){
                    Toast('请先完善信息');
                    return;
                }
            }
            this.participants_1Show=true;
            if(this.invitatList.length==1){
                document.querySelectorAll('.down')[0].style.display='none';
                document.querySelectorAll('.up')[0].style.display='inline-block';
                document.querySelectorAll('.information')[0].style.display='none';
            }
            this.invitatList.push({
                num:this.invitatList.length+1,
                stdName:'',
                mobile:'',
                idCard:'',
                urlImg:'',
            })
            Toast('因场地有限，建议只带一人参加');
        },
        uploadImg(body, type) {
            this.annexUrl = body;
        },
        uploadAddImg(body, type){
            this.invitatList[Number(type-1)].urlImg = body;
        },
        participantsShow(){
            if(this.participants_1Show){
                document.querySelectorAll('.down').forEach(el=>{
                    el.style.display='none';
                })
                document.querySelectorAll('.up').forEach(el=>{
                    el.style.display='inline-block';
                })
                document.querySelectorAll('.information').forEach(el=>{
                    el.style.display='none';
                })
            }
            this.participants_1Show=!this.participants_1Show;
        },
        down(i){
            let down='down'+i.num;
            let up='up'+i.num;
            let information='information'+i.num;
            document.querySelector('.down'+i.num).style.display='none';
            document.querySelector('.up'+i.num).style.display='inline-block';
            document.querySelector('.information'+i.num).style.display='none';
        },
        up(i){
            let down='down'+i.num;
            let up='up'+i.num;
            document.querySelectorAll('.down').forEach(el=>{
                el.style.display='none';
            })
            document.querySelectorAll('.up').forEach(el=>{
                el.style.display='inline-block';
            })
            document.querySelector('.down'+i.num).style.display='inline-block';
            document.querySelector('.up'+i.num).style.display='none';
            this.participants_1Show=true;
            document.querySelectorAll('.information').forEach(el=>{
                el.style.display='none';
            })
            document.querySelector('.information'+i.num).style.display='block';
        },
        showImgFn(imgSrc, type) {
            if (!imgSrc) {
                return;
            }
            this.showImg = true;
            this.showImgSrc.src = imgSrc;
            this.showImgSrc.type = type;
            this.$nextTick(() => {
                this.$refs.imgpop.fileType = type;
                this.$refs.imgpop.imgSrcs = imgSrc;
                this.$refs.imgpop.open();
            });
        },
        changeImg(fileType, src) {
            fileType=this.$refs.imgpop.fileType;
            if(fileType>0){
                this.invitatList[fileType-1].urlImg = src ;
            }else{
                this.annexUrl = src;
            }
        },
        getUserInfo() {
            this.$http.post("/mkt/stdLearnInfo/1.0/").then(res=>{
                const {body} = res;
                if(!body){
                    return;
                }
                const keys = ['std_name', 'mobile', 'idCard'];
                for (const key in body) {
                    if (body[key]) {
                        this[key] = body[key];
                    }
                }
                this.isJoinPurview();
                this.IOS();
            })
        },
        //是否在名单
        isJoinPurview(){
            this.$http.post("/us/isUserJoinInvitationPurview/1.0/",{joinCode:'2020_scholarship'}).then(res=>{
                if(res.code==='00'){
                    if(res.body){
                        this.canJoin=true;
                        setTimeout(() => {
                            if(isIOS()){
                                document.querySelector('.marker').click();
                            }
                        }, 0);
                        //获奖用户访问次数
                        MtaH5.clickStat("wechatawardwinninguser");
                    }else{
                        //非获奖用户访问次数
                        MtaH5.clickStat("wechatnonawardwinninguser");
                    }
                    this.getJoinLog();
                }
            })
        },
        //报名记录
        getJoinLog(){
            this.$http.post("/us/joinLog/1.0/",{joinType:'4'}).then(res=>{
                if(res.code!='00')return;
                if(res.body){
                    this.join=true;
                }
            })
        },
        guide(){
            this.showOpen=true;
            //点击分享按钮次数
            MtaH5.clickStat("wechatclicksharebtn");
        },
        downApp() {
            window.location.href ="https://sj.qq.com/myapp/detail.htm?apkName=cn.yzou.yzxt";
        },
        submit(){
            if(!this.annexUrl){
                Toast('请先完善信息');
                return
            }
            if(this.can==false){
                return
            }
            this.can=false;
            if(this.invitatList[0]){
                if(!this.invitatList[0].idCard || !this.invitatList[0].mobile || !this.invitatList[0].stdName || !this.invitatList[0].urlImg){
                    let message='参会人二信息未填写完善，将不计入参会，是否确认提交报名？';
                    Dialog.confirm({
                        title: '',
                        message: message,
                        cancelButtonText:'继续完善'
                    })
                    .then(() => {
                        // on confirm
                        this.invitatList.splice(this.invitatList.indexOf(this.invitatList[0]),1);
                        this.$http.post("/us/invitation/1.0/",{"stdName":this.std_name,"mobile":this.mobile,"idCard":this.idCard,"urlImg":this.annexUrl,inviterInfoJson:this.invitatList}).then(res=>{
                            if(res.code!='00')return;
                            if(res.body){
                                this.join=true;
                                this.can=true;
                            }
                        })
                    })
                    .catch(() => {
                        // on cancel
                        this.can=true;
                    });
                    return
                }
            }
            if(this.invitatList[1]){
                let message='参会人三信息未填写完善，将不计入参会，是否确认提交报名？';
                if(!this.invitatList[1].idCard || !this.invitatList[1].mobile || !this.invitatList[1].stdName || !this.invitatList[1].urlImg){
                    Dialog.confirm({
                        title: '',
                        message: message,
                        cancelButtonText:'继续完善'
                    })
                    .then(() => {
                        // on confirm
                        this.invitatList.splice(this.invitatList.indexOf(this.invitatList[1]),1);
                        this.$http.post("/us/invitation/1.0/",{"stdName":this.std_name,"mobile":this.mobile,"idCard":this.idCard,"urlImg":this.annexUrl,inviterInfoJson:this.invitatList}).then(res=>{
                            if(res.code!='00')return;
                            if(res.body){
                                this.join=true;
                                this.can=true;
                            }
                        })
                    })
                    .catch(() => {
                        // on cancel
                        this.can=true;
                    });
                    return
                }
            }
            this.$http.post("/us/invitation/1.0/",{"stdName":this.std_name,"mobile":this.mobile,"idCard":this.idCard,"urlImg":this.annexUrl,inviterInfoJson:this.invitatList}).then(res=>{
                if(res.code!='00')return;
                if(res.body){
                    this.join=true;
                    this.can=true;
                }
            })
        },
        IOS(){
            if(isIOS()){
                var scrWidth=window.document.documentElement.getBoundingClientRect().width;
                var scrheight=window.screen.height;
                if((scrWidth===375&&scrheight===667)||(scrWidth===414&&scrheight===736)||(scrWidth===360&&scrheight===640)){
                    this.isIOS=true;
                }
            }
        },
        onfocus() {
            if(!isIOS()){
                let CH =window.screen.height;
                const innerHeight = window.innerHeight-1;
                document.querySelector('.canShare').style.height=CH  +'px';
                window.addEventListener('resize', () => {
                    const newInnerHeight = window.innerHeight;
                    if (innerHeight < newInnerHeight) {
                        // 键盘收起事件处理
                        document.querySelector('.canShare').style.height='100%';
                    }else{
                        document.querySelector('.canShare').style.height=CH  +'px';
                    }
                });
            }
        },
        onblur() {
            if(!isIOS()){
                document.querySelector('.canShare').style.height='100%';
            }
        },
        clickImg(){
            let arr=[];
            arr.push(`${this.sampleImg}`)
            ImagePreview(arr);
        },
        keyboard(){
            const innerHeight = window.innerHeight;
            window.addEventListener('resize', () => {
                const newInnerHeight = window.innerHeight;
                if (innerHeight < newInnerHeight) {
                    // 键盘收起事件处理
                    document.querySelector('.canShare').style.height='100%';
                }else{
                    document.querySelector('.canShare').style.height=CH  +'px';
                }
            });
        }
  },
  components: {
    swiper,
    swiperSlide,
    share,
    webuploader,
    BaiduMap,
    BmMarker,
    BmInfoWindow,
    imgPopup,
    [ImagePreview.Component.name]: ImagePreview.Component
  }
};
</script>
<style lang="less" scoped>
.main {
  position: relative;
  height: 100%;
  .swiper-box {
    width: 100%;
    height: 100%;
    transform:translate3d(0,0,0);
  }
}
.swiper-item{
    width: 3.75rem;
    height: 100vh;
    transform:translate3d(0,0,0);
    position: relative;
    img{
        width: 100%;
        height: 100%;
    }
    .index{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_1.png') no-repeat;
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        p{
            position: absolute;
            bottom: .25rem;
            width: 3.75rem;
            text-align: center;
            color: #eec08a;
        }
    }
    .invitation{
        position: relative;
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_2.png') no-repeat;
        .detail{
            text-align: justify;
            margin-bottom: .1rem;
        }
        .sponsor{
            text-align: right;
            margin-top: .05rem;
            
        }
        span{
            text-decoration: underline;
        }
        .state{
            position: absolute;
            bottom: 1.12rem;
            left: 0;
            width: 3.75rem;
            text-align: center;
            font-size: .12rem;
            
        }
    }
    .abstract{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_3.png') no-repeat;
        .photo{
            margin-top: .2rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .road{
        position: relative;
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_4.png') no-repeat;
        .photo{
            margin-top: .4rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .shareBtn{
            width: .25rem;
            height: .25rem;
            position: absolute;
            right: .4rem;
            top: .4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .past{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_5.png') no-repeat;
        .photo{
            margin-top: .4rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .meeting{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_6.png') no-repeat;
        .bm-view {
            width: 2.67rem;
            height: 1.8rem;
            margin-top: .1rem;
        }
        .liveShow{
            color: #FFFFFF;
            width: 100%;
            overflow: hidden;
            margin-top: .3rem;
            .liveCode{
                width: .98rem;
                height: .98rem;
                float: left ;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .text{
                width: 1.4rem;
                margin-left: .2rem;
                padding-top: .2rem;
                float: left ;
                p{
                    font-size: .18rem;
                }
                span{
                    font-size: .12rem;
                }
            }
        }
        .way{
            margin-top: .34rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .step{
            text-align: center;
            font-weight: bold;
            color: #FFFFFF;
            margin-top: .1rem;
        }
        .btnDown{
            margin-top: .3rem;
            width: 2.67rem;
            height: .5rem;
            text-align: center;
            line-height: .5rem;
            color: #FFFFFF;
            border-radius: .25rem;
            background: linear-gradient(180deg, #FFCF6F 0%, #FFA72A 100%);
        }
    }
    .process{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_10.jpg') no-repeat;
        .item{
            margin-left: .5rem;
            margin-top: .1rem;
        }
    }
    .canShare{
        width: 100%;
        height: 100%;
        position: relative;
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_8.png') no-repeat;
        background-size: 100% 100%;
        .block{
            width: 3.75rem;
            height: 1.5rem;
            background: border-box;
            z-index: 9;
        }
        .hint{
            position: absolute;
            bottom: .55rem;
            color: #FFFFFF;
            font-size: .1rem;
            width: 3.75rem;
            text-align: center;
        }
        .btn{
            width: 3.75rem;
            height: .5rem;
            text-align: center;
            line-height: .5rem;
            color: #FFFFFF;
            position: absolute;
            bottom: 0;
            left: 0;
            background: linear-gradient(180deg, #FFCF6F 0%, #FFA72A 100%);
        }
        .gray{
            background: #C9C9C9;
        }
        .apply{
            width: 100%;
            height: 4.52rem;
            padding: 0 .54rem 0 .54rem;
            color: #FFFFFF;
            text-align: justify;
            overflow-y: auto;
            position: relative;
            .addParticipants{
                width: .77rem;
                height: .22rem;
                text-align: center;
                line-height: .22rem;
                border-radius: .11rem;
                border: 1px solid #FFFFFF;
                font-size: .1rem;
                position: absolute;
                right: .54rem;
                top: 0.02rem;
            }
            .addTemplate{
                margin-top: .15rem;
            }
            .participants,.addTemplate{
                .tit{
                    display: inline-block;
                    font-size: .16rem;
                    img{
                        width: .14rem;
                        height: .08rem;
                        vertical-align: middle;
                        margin-left: .1rem;
                    }
                }
                p{
                    font-size: .12rem;
                    color: #FFFFFF;
                    margin-top: .1rem;
                }
                .ipt{
                    width: 2.67rem;
                    margin-top: .1rem;
                    height: 0.35rem;
                    background: border-box;
                    border: 1px solid #FFFFFF;
                    // border-radius: 0.05rem;
                    padding-left: 0.07rem;
                    color: #FFFFFF;
                }
                .uploadInformation{
                    display: flex;
                    align-items: center;
                    text-align: center;
                    margin-top: .1rem;
                    .add,.demonstration{
                        width: 50%;
                    }
                    .file-box{
                        margin: 0 auto;
                        margin-top: .1rem;
                        position: relative;
                        width: 1.03rem;
                        height: .97rem;
                        img{
                            width: 100%;
                            height: 100%;
                        }
                        .idCard {
                            width: 1.03rem;
                            height: .97rem;
                            text-align: center;
                            padding: .24rem 0;
                            background-color: #FFFFFF;
                            border-radius: .1rem;
                            img{
                                width: .5rem;
                                height: .48rem;
                            }
                        }
                    }
                }
            }
        }
        .achieve{
            width: 100%;
            height: 100%;
            background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_9.png') no-repeat;
            background-size: 100% 100%;
            font-size: .15rem;
            text-align: justify;
            padding-top: 1.98rem;
            color: #FFFFFF;
            .qrcode{
                width: .98rem;
                height: .98rem;
                margin: .2rem auto;
                background: #FFFFFF;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            p{
                width: 100%;
                text-align: center;
                font-weight: bold;
            }
            .p1{
                width: 2.2rem;
                margin: 0 auto;
                font-size: .12rem;
                margin-top: .05rem;
            }
        }
        .shareBtn{
            width: .25rem;
            height: .25rem;
            position: absolute;
            right: .4rem;
            top: .4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .invitation,.abstract,.road,.past,.meeting,.process{
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        padding: 1.8rem .54rem 0 .54rem;
        font-size: .15rem;
        color: #eec08a;
        text-align: justify;
    }
}
.lead{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    img{
        width: 2.2rem;
        height: 3.07rem;
        margin-top: 1rem;
        margin-left: .95rem;
    }
}
.IOSinvitation,.IOSabstract,.IOSroad,.IOSpast{
    padding-top: 1.4rem !important;
}
.IOSmeeting{
    padding-top: 1.3rem !important;
    .IOSbm-view{
        height: 1.6rem !important;
    }
}
.IOSapply{
    height: 3.81rem !important;
}
.topIcon {
    position: absolute;
    width: 0.35rem !important;
    height: 0.21rem !important;
    bottom: .45rem;
    left: 1.7rem;
    animation: moveup 1.2s ease-out forwards infinite;
}
@keyframes moveup {
  from {
    transform: translateY(0);
    opcity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}
</style>