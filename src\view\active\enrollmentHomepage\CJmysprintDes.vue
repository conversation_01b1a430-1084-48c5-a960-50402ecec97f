<template>
    <div>
      <div class="sprint">
        <div class="sprint_wrap">

          <div class="sprint_success">
            <img src="../../../assets/image/active/sprintBeforeExam/sprint_success2.png" alt="" class="success">
            <img :src="coverWitnessHead?coverWitnessHead+headLimit:coverWitnessHead|defaultAvatar" class="head" alt>
            <div class="headTxt">
              我是上进青年{{coverWitnessName>6?coverWitnessName.substr(0,6)+'...':coverWitnessName}},<br />
              邀请你为我上进读书做见证!
            </div>
            <p class="sprintTxt">本人(姓名: {{coverWitnessName>6?coverWitnessName.substr(0,6)+'...':coverWitnessName}})正在申请远智教育2022级成人学历报读上进奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。 </p>
              <div class="info_success">
                <div class="info" >
                  <p>见证人: {{witnessName}}</p>
                  <p>职务: {{workUnit}}</p>
                  <p>见证时间: {{Number(createTime)|formatDate('yyyy.MM.dd hh:mm:ss')}}</p>
                </div>
                <div class="comment">
                  <span>上进点评:</span>{{comment}}
                </div>
              </div>
          </div>
        </div>
        <div class="sprint_list">
            <div class="headBox_school">
              <img src="../../../assets/image/active/oneYear/xx.png" alt="">
              最新见证动态
            </div>
            <div class="list">
              <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50" >
                  <ul>
                    <li v-for="item in sprintList" @click="changeInfo(item.coverWitnessName,item.witnessName,item.comment,item.workUnit,item.createTime,item.coverWitnessHead)">

                      <div class="head_info">
                        <div class="head">
                          <img :src="item.coverWitnessHead|defaultAvatar" alt="">
                        </div>
                        <div class="info">
                          <p>
                            <span class="name">{{item.coverWitnessName}}</span>
                            <span class="time">{{item.createTime | formatDate('yyyy.MM.dd hh:mm:ss')}}</span>
                          </p>
                          <p>完成了上进见证, 激活了6000元奖学金!</p>
                        </div>
                      </div>
                      <div class="detail">
                        <p><span>见证人：</span>{{item.witnessName}} </p>
                        <p><span>职务:</span>{{item.workUnit}}</p>
                        <p><span>上进点评：</span>{{item.comment}}</p>
                      </div>
                    </li>
                  </ul>
                  <div class="empty" v-if="sprintList.length==0">
                    暂无
                  </div>
              </div>
            </div>
          </div>
      </div>
      <button class="bottomBtn" @click="gotoEnroll(type)">了解活动 >></button>
    </div>
</template>

<script>
  import share from "@/components/share"
  import {toLogin,filterEmoji} from "../../../common"
  import loadBar from "@/components/loadBar"
    export default {
        name: "mysprint",
      components:{share,loadBar},
      data(){
          return{
            share:{
              title:'',
              desc:'',
              shareLink:''
            },
            imgUrl:'',
            headImg:'',
            headLimit:"?x-oss-process=image/resize,m_fixed,h_38,w_38",
            sprintList:[],
            name:'',
            work_address:'',
            comment:'',
            job:"",
            sprintStatus:1,
            coverWitnessName:'',
            sprintInfo:{witnessName:'',workUnit:'',createTime:0},
            sprint_token:'',
            invite:{},
            isSubmit:false,
            allLoaded:false,
            isLoading:false,
            pageNum:0,
            pageSize:10,
            coverWitnessName:'',
            witnessName:'',
            comment:'',
            workUnit:'',
            createTime:'',
            coverWitnessHead:"",
            scholarship: "103",
            type:''
          }
      },
      created(){
          this.coverWitnessName = this.$route.query.coverWitnessName || '';
          this.witnessName = this.$route.query.witnessName || '';
          this.comment = this.$route.query.comment || '';
          this.workUnit = this.$route.query.workUnit || '';
          this.createTime = this.$route.query.createTime || '';
          this.coverWitnessHead = this.$route.query.coverWitnessHead || '';
          this.type=this.$route.query.type||'cj';
          //this.getSptintList();
      },
      mounted () {
        window.addEventListener('scroll', this.scrollToTop)
      },
      destroyed () {
        window.removeEventListener('scroll', this.scrollToTop)
      },

      methods:{
        changeInfo(coverWitnessName,witnessName,comment,workUnit,createTime,coverWitnessHead){
          this.coverWitnessName = coverWitnessName;
          this.witnessName = witnessName;
          this.comment = comment;
          this.workUnit = workUnit;
          this.createTime = createTime;
          this.coverWitnessHead = coverWitnessHead
          document.body.scrollTop = 0
          document.documentElement.scrollTop = 0
        },
        gotoEnroll(res) {if(res=="hx"){ this.$router.push({name:'gzHuaXia',query:{inviteId:this.$route.query.inviteId || ''}});}else{
           this.$router.push({name:'newOneYearSchool',query:{inviteId:this.$route.query.inviteId || ''}});
        }

        },
        //见证列表
        getSptintList(){
          this.$http.post('/mkt/getAdvanceWitnessList/1.0/',{
            pageNum:this.pageNum,
            pageSize:this.pageSize,
            scholarship:this.scholarship
            }).then(res=>{
            if(res.code=='00'){
              const datas = (res.body || []);
              this.sprintList.push(...res.body);
              this.$nextTick(()=>{
                this.allLoaded = datas.length === 0;
                this.isLoading = this.allLoaded;
              })
            }
          })
        },
        getInviteInfo() {
          let inviteId = (
            decodeURIComponent(
              this.$route.query.sprint_token ||
              this.getQueryString(this.redirect, "sprint_token") ||
              ""
            )
          ).replace(/ /g, "+");
          if (inviteId) {
            this.$http
              .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
              .then(res => {
                let { code, body } = res;
                if (code !== "00") return;
                this.invite = body || {};
              });
          }
        },
        loadMore(){
            this.pageNum++;
            this.getSptintList()
        },

      },
      watch:{
          name:function(newValue,oldValue){
            if(newValue.length>10){
              this.name=newValue.substr(0,10)
            }
          },
        job:function(newValue,oldValue){
          if(newValue.length>20){
            this.job=newValue.substr(0,20)
          }
        },
        work_address:function(newValue,oldValue){
          if(newValue.length>20){
            this.work_address=newValue.substr(0,20)
          }
        },
        comment:function(newValue,oldValue){
          if(newValue.length>100){
            this.comment=newValue.substr(0,100)
          }
        },
      }
    }
</script>

<style scoped lang="less">
.sprint{
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  .sprint_wrap{
    width: 100%;
    height: auto;
    overflow: hidden;
    background-color: rgba(110, 16, 2, 1);
    // background-image:linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
    .sprint_invite{
      width: 100%;
      height: 4.93rem;
      background-image: url("../../../assets/image/active/sprintBeforeExam/envelope.png");
      background-size: 100%;
      position: relative;
      &.active{
        height: 7.74rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/bg2.png");
        background-size: 100%;
      }
      .head{
        width: .6rem;
        height: .6rem;
        border-radius: 50%;
        position: absolute;
        top:.35rem;
        left:50%;
        margin-left: -.3rem;
      }
      .headTxt{
        width: 100%;
        position: absolute;
        color: rgba(54, 54, 54, 1);
        line-height: .3rem;
        font-size: .17rem;
        top:.98rem;
        text-align: center;
        font-weight: bold;
      }
      .sprintTxt{
        font-size: .14rem;
        color: rgba(54, 54, 54, 1);
        line-height: .23rem;
        position: absolute;
        width: 2.43rem;
        top:2.24rem;
        left:50%;
        margin-left: -1.21rem;
        text-indent: 2em;
      }
      .receive{
        width: 1.4rem;
        height: .4rem;
        line-height: .4rem;
        text-align: center;
        background-color: rgba(252, 66, 27, 1);
        font-size: .17rem;
        color: #fff;
        background-image: linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
        border: none;
        position: absolute;
        top:4.09rem;
        left:50%;
        margin-left: -.7rem;
      }
      .info_edit {
        width: 100%;
        position: absolute;
        top:3.6rem;
        .headBox{
          width: .92rem;
          height: 0.3rem;
          text-align: center;
          margin: 0.4rem auto 0rem;
          color:rgba(54, 54, 54, 1) ;
          font-size: .15rem;
          position: relative;
          &:before{
            content:"";
            position: absolute;
            left:-.4rem;
            top:0rem;
            width: .39rem;
            height:.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_left.png");
            background-size: .39rem;
          }
          &:after{
            content:"";
            position: absolute;
            right:-.4rem;
            top:0rem;
            width: .39rem;
            height:.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
            background-size: .39rem;
          }
        }
        p{
          width: 3.22rem;
          height: .5rem;
          margin-left: .26rem;
          padding-left: .2rem;
          &.red{
            color: rgba(252, 66, 27, 1);
            font-size: .12rem;
            line-height: .2rem;
            margin-top: .05rem;
          }
          span{
            display: block;
            float: left;
            height: .5rem;
            width: .63rem;
            text-align: justify;
            font-size: .14rem;
            line-height: .5rem;
            text-align-last: justify;
          }
          input{
            padding-left: .1rem;
            width: 2.21rem;
            height: .45rem;
            border: none;
            border-bottom: solid 1px rgba(54, 54, 54, .6);
          }
        }
      }
    }
    .sprint_success{
      width: 100%;
      height:auto;
      min-height: 5.95rem;
      background-image: url("../../../assets/image/active/sprintBeforeExam/bg6.png");
      background-size: 100% 100%;
      position: relative;
      overflow: hidden;
      .success{
        width: 1.37rem;
        height: .69rem;
        position: absolute;
        top:.53rem;
        right: .19rem;
      }
      .head{
        width: .6rem;
        height: .6rem;
        border-radius: 50%;
        position: absolute;
        top:.57rem;
        left:50%;
        margin-left: -.3rem;
      }
      .headTxt{
        width: 100%;
        position: absolute;
        color: rgba(54, 54, 54, 1);
        line-height: .3rem;
        font-size: .17rem;
        top:1.28rem;
        text-align: center;
        font-weight: bold;
      }
      .sprintTxt{
        font-size: .14rem;
        color: rgba(54, 54, 54, 1);
        background-image: url("../../../assets/image/active/sprintBeforeExam/regular.png");
        background-repeat: no-repeat;
        background-size: 100%;
        padding: .15rem .2rem .3rem .2rem;
        line-height: .23rem;
        position: absolute;
        width: 2.83rem;
        top:2.13rem;
        left:50%;
        margin-left: -1.41rem;
      }
      .receive{
        width: 1.4rem;
        height: .4rem;
        line-height: .4rem;
        text-align: center;
        background-color: rgba(252, 66, 27, 1);
        font-size: .17rem;
        color: #fff;
        background-image: linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
        border: none;
        position: absolute;
        top:4.09rem;
        left:50%;
        margin-left: -.7rem;
      }
      .info_success{
        /*position: absolute;*/
        margin:4.1rem auto .4rem;
        /*left:.47rem;*/
        width: 2.82rem;
        .info{
          width: 100%;
          p{
            line-height: .14rem;
            margin-bottom: .1rem;
            font-size: .14rem;
            &:last-of-type{
              margin-bottom: 0;
            }
          }
        }
        .comment{
          margin-top: .1rem;
          /*position: absolute;*/
          /*top:4.72rem;*/
          /*left:.47rem;*/
          width: 2.82rem;
          font-size: .14rem;
          line-height: .25rem;
          word-break: break-all;
          span{
            color: rgba(252, 66, 27, 1);
            font-size: .14rem;
            line-height: .2rem;
            margin-top: .05rem;
          }
        }
        a{
          /*position: absolute;*/
          /*left:1.4rem;*/
          /*top:5.41rem;*/
          margin-left: .94rem;
          margin-top: .15rem;
          color: rgba(252, 66, 27, 1);
        }
      }
    }
  }
  .sprint_list{
    background-color: white;
    height:auto;
    overflow: hidden;
    .empty{
      width: 100%;
      height: 1rem;
      line-height: 1rem;
      text-align: center;
      color: rgba(102,102,102,.5);
    }
   .headBox_school{
      width: 1.23rem;
      height: 0.3rem;
      text-align: center;
      margin: 0.4rem auto 0rem;
      color:rgba(54, 54, 54, 1) ;
      font-size: .16rem;
      position: relative;
      font-family:PingFang-SC-Bold,PingFang-SC;
      font-weight:bold;
      img{
          width: .22rem;
         height:.23rem;
      }
    }
    .list{
      width: 100%;
      height: auto;
      overflow: hidden;
      margin-bottom: .5rem;
      ul{
        li{
          margin: .1rem auto;
          width: 3.52rem;
          height: auto;
          overflow: hidden;
          padding: .1rem;
          background-color: white;
          border-bottom: 1px solid rgba(54,54,54,.08);
          .head_info{
            .head{
              width: .38rem;
              height: .38rem;
              overflow: hidden;
              float: left;
              border-radius: 50%;
              img{
                width: 100%;
              }
            }
            .info{
              float: left;
              width: 2.8rem;
              margin-left: .05rem;
              margin-top: .03rem;
              p{
                clear: both;
                margin-bottom: .1rem;
                font-size: .14rem;
                .name{
                  float: left;
                }
                .time{
                  float: right;
                  color: rgba(54, 54, 54, .4);
                  font-size: .12rem;
                }
              }
            }
          }
         .detail{
            clear: both;
            width: 3.04rem;
            padding: .1rem;
            border: dashed .01rem  rgba(218, 41, 47, 1);
            margin-left: .3rem;
            font-size: .14rem;
            color:rgba(218, 41, 47, 1);
            line-height: .25rem;
            span{
              color: rgba(69, 56, 56, 1);
            }
          }
        }
      }
    }
  }
}
  .red_bag{
    position: fixed;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    z-index: 9991;
    .bg{
      top:0;
      left:0;
      width: 100%;
      height: 100%;
      position: fixed;
      background-color: rgba(0,0,0,.8);
    }
    .presented{
      width: 2rem;
      height: .4rem;
      line-height: .4rem;
      text-align: center;
      color: rgba(130, 77, 9, 1);
      font-size: .17rem;
      background-color: rgba(252, 66, 27, 1);
      background-image: linear-gradient(to bottom,rgba(254, 232, 158, 1),rgba(237, 170, 37, 1));
      border-radius: .2rem;
      border: none;
      position: absolute;
      top:50%;
      margin-top: 1.23rem;
      left:.88rem;
      font-weight: bold;
    }
    p{
      text-align: center;
      position: fixed;
      z-index: 88;
      width: 1.8rem;
      height: .61rem;
      color: rgba(130, 77, 9, 1);
      font-size: .15rem;
      top:50%;
      margin-top: -1.3rem;
      font-weight: bold;
      left:1.03rem;
    }
    img{
      position: fixed;
      top:50%;
      left:50%;
      margin-left: -1.6rem;
      margin-top: -1.78rem;
      width: 3.19rem;
      height: 3.57rem;
    }
    .close{
      width: .09rem;
      height: .09rem;
      position: fixed;
      top:50%;
      left:50%;
      margin-left: 1.15rem;
      margin-top: -1.48rem;
      z-index: 999;
    }
  }
.activity_rule{
  position: absolute;
  left: 0;
  text-align: center;
  height: .34rem;
  top:.15rem;
  width: .93rem;
  font-size: .14rem;
  z-index: 999;
  background-image: url("../../../assets/image/active/sprintBeforeExam/group_left.png");
  background-size: 100%;
  img{
    width: .16rem;
    height: .14rem;
    float: left;
    margin-left: .05rem;
    margin-top: .07rem;
  }
  a{
    float: left;
    display: block;
    width: .59rem;
    line-height: .3rem;
    padding-left: .02rem;
    text-align: center;
    color: rgba(130, 77, 9, 1);
  }
}
  .btn_submit{
    position: absolute;width: 2rem;height:.5rem;opacity: 0;top:6.85rem;left:50%;margin-left: -1rem;
  }
  .bottomBtn {
      position: fixed;
      width: 3.75rem;
      background:linear-gradient(0deg,rgba(254,103,52,1),rgba(252,57,21,1));
      line-height: .45rem;
      text-align: center;
      font-size: .17rem;
      font-weight: 600;
      color: #fff;
      bottom: 0;
      border: none;
  }
</style>
