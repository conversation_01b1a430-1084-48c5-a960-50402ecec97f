<template>
  <div class="home-list">
    <div class="bd bc-w clearfix" v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
      <router-link class="item h2"
                   v-for="item in list"
                   :key="item.salesId"
                   :to="{name:'lottery',params:{id:item.salesId},query:{salesType:2}}">
        <div class="pic"><img class="img" :src="item.annexUrl|imgBaseURL"></div>
        <div class="txt">
          <status class="static" :status="item.salesStatus"></status>
          <div class="tit row2">[第{{item.planCount}}期]{{item.salesName}}</div>
          <price class="mt" :salesPrice="item.salesPrice" :originalPrice="item.originalPrice"></price>
          <template v-if="item.salesStatus==='2'">
            <div class="fsc">开始时间:{{item.startTime|formatDate('yyyy.MM.dd hh:mm')}}</div>
          </template>
          <template v-else-if="item.salesStatus==='3'">
            <progress-bar :joinCount="parseInt(item.joinCount)" :headCount="parseInt(item.runCount)"></progress-bar>
            <div class="fsc clearfix">
              <span class="fl">总需:<span class="fc">{{item.runCount}}</span>人</span>
              <span class="fr">剩余:<span class="fc">{{item.runCount-item.joinCount}}</span>人</span>
            </div>
          </template>
          <template v-else>
            <div class="fsc row1">中奖人:{{item.winUser}}</div>
          </template>
        </div>
      </router-link>
      <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="list.length"></load-bar>
    </div>
  </div>
</template>

<script>
  import status from '@/components/status'
  import price from '@/components/price'
  import progressBar from '@/components/progressBar'
  import loadBar from '@/components/loadBar'

  export default {
    props: ['salesType', 'active'],
    data() {
      return {
        pageNum: 0,
        pageSize: 10,
        goodsType: '',
        list: [],
        isLoading: true,
        allLoaded: false
      }
    },
    created() {
      this.loadMore();
    },
    methods: {
      getGoodsList: function () {
        this.isLoading = true;
        let data = {
          salesType: this.salesType,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          goodsType: this.goodsType
        };
      
        this.$http.post('/gs/goodsList/1.0/', data).then(res => {
          if (res.code !== '00') return;
          
          const datas = res.body || {};
          this.list.push(...(datas.list || []));
  
          this.$nextTick(() => {
            this.allLoaded = datas.list.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        if (!this.active) return;
        this.pageNum++;
        this.getGoodsList();
      }
    },
    watch: {
      active: function () {
        this.isLoading = !(this.active && !this.allLoaded);
      }
    },
    components: {status, price, progressBar, loadBar}
  }
</script>

<style lang="less" scoped>
  .progress-bar{ margin:.03rem 0; }
</style>
