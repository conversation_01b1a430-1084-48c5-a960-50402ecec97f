<template>
    <div>
      <div class="commonPop" v-if="isShowFlag">
        <div class="bg" @click="isShowFlag=false;"></div>
        <div class="commonPop-box">
          <div class="title">提示</div>
          <p class="commonPop-content">

            <slot></slot>
          </p>
          <div class="button">
            <button @click="cancel">取消</button>
            <button @click="submit">确定</button>
          </div>
        </div>
      </div>
    </div>
</template>

<script>
    export default {
        name: "modal",
      data(){
          return{
            isShowFlag:false
          }
      },
      methods:{
          open(){
            this.isShowFlag=true
          },
          cancel(){
            this.isShowFlag=false
            this.$emit('cancel')
          },
        submit(){
          this.isShowFlag=false
          this.$emit('submit')
        }
      }
    }
</script>

<style scoped lang="less">
  .commonPop{
    position: fixed;
    width: 100%;
    height:100%;
    min-height: 100vh;
    z-index: 90;
    left: 0;
    top:0;
    .bg{
      width: 100%;
      height: 100%;
      position: absolute;
      left:0;
      top:0;
      z-index: 20;
      background-color: rgba(0,0,0,.5);
    }
  }
  .commonPop-box{
    position: relative;
    z-index: 50;
    width: 2.7rem;
    height: 1.51rem;
    background-color: white;
    margin: 1.97rem auto;
    padding: 0.14rem 0 0;
    overflow: hidden;
    border-radius: .14rem;
    .title{
      color: rgba(0, 0, 0, 1);
      font-size: .18rem;
      text-align: center;
      line-height: .25rem;
      margin-bottom: .1rem;
    }
  }
  .commonPop-content{
    text-align: center;
    font-size: .13rem;
    font-weight: 500;
    line-height: .18rem;
    height: .36rem;
    color: rgba(0, 0, 0, 1);
  }
  .button{
    width: 100%;
    margin: 0 auto;
    border-top: solid 1px rgba(225, 225, 225, 1);
    height: .45rem;
    margin-top: .2rem;
    button{
      background-color: white;
      float: left;
      color:white;
      border: none;
      width: 50%;
      height: .45rem;
      line-height: .45rem;
      border-radius: .03rem;
      font-size: .16rem;
      color: rgba(153, 153, 153, 1);
      &:last-of-type{
        color: rgba(0, 122, 255, 1);
        border-left: solid 2px rgba(225, 225, 225, 1);
      }
    }
  }
</style>
