<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.taId===item.taId}" @click="selected(item)">{{item.taName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="options.length"></load-bar>
  </div>
</template>

<script>
  import { isNewSelf } from '@/common';
  import loadBar from '@/components/loadBar';
  import dataJson from '../../view/active/scholarship/dataJson.json';
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data() {
      return {
        options: [],
        type: 'T',
        taName: '',
        pfsnId: '',
        pageNum: 0,
        pageSize: 20,
        isLoading: false,
        allLoaded: false,
        cityCode:'',
        unvsId:'',
        pfsnLevel:'',
        grade:'',
        recruitType:''
      }
    },
    created() {
      this.pfsnId = this.datas.pfsn.pfsnId;
      this.cityCode=this.datas.city.cityCode;
      this.unvsId = this.datas.unvs.unvsId;
      this.grade = this.datas.grade.dictValue;
      this.pfsnLevel= this.datas.pfsnLevel.pfsnLevel;
      const city = this.datas.city.cityCode;
      this.recruitType=this.datas.recruitType.dictValue;
      if (city) {
        this.taName = dataJson.city[city] || '';
      }
    },
    methods: {
      getUnvsList: function () {
        // if(this.unvsId==='46'){
        //   this.isLoading = true;
        //   const data = {
        //     cityCode:this.cityCode,
        //     type: this.type,
        //     taName: this.taName,
        //     pfsnId: this.pfsnId,
        //     scholarship:this.datas.scholarship,
        //     pageNum: this.pageNum,
        //     pageSize: this.pageSize,
        //     recruitType:'2',
        //     grade:this.grade,
        //     pfsnLevel:this.pfsnLevel,
        //   }
        //   this.$http.post('/mkt/getOpenTestAreaByCity/1.0/', data).then(res => {
        //     if (res.code !== '00') return;
        //     const datas = res.body || [];

        //     //停止广州天河录入
        //     let data=datas;
        //     //data = data.filter(val => !["广州天河","清远清城","肇庆市辖","肇庆高要","肇庆端州","江门蓬江","肇庆四会","江门市辖"].includes(val.taName));

        //     this.options.push(...data);
        //     this.$nextTick(() => {
        //       this.allLoaded = datas.length === 0;
        //       this.isLoading = this.allLoaded;
        //     });
        //   });
        //   return;
        // }

        let url = '/mkt/enrollInfo/1.0/';
        let scholtype
        if(typeof(this.datas.scholarship)==='string'){
          if(this.unvsId === '46'){
            scholtype =this.datas.scholarship
          }else{
            scholtype = [this.datas.scholarship]
          }
        }else{
            scholtype =this.datas.scholarship
        }
        let data = {
          cityCode:this.cityCode,
          type: this.type,
          taName: this.taName,
          pfsnId: this.pfsnId,
          scholarship:scholtype,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          recruitType:this.recruitType
        };
        if (this.unvsId === '46') {
          url = '/mkt/getOpenTestAreaByCity/1.0/';
          data.recruitType = '2';
          data.pfsnLevel = this.pfsnLevel;
          data.grade = this.grade;
        }
        if (this.recruitType == 4 && !isNewSelf) { // 自考获取城市跟别的不一样
          url = '/mkt/selectSelfExamArea/1.0/';
        }
        let headers = {};
        if (this.isDingding) {
          url = `${domain}/newStudentChange/getEnrollInfo.do`;
          data = qs.stringify(data);
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
          };
        }
        this.isLoading = true;
        this.$http.post(url, data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || [];

          //停止广州天河录入
          // let data=datas;
          //data = data.filter(val => !["广州天河","清远清城","肇庆市辖","肇庆高要","肇庆端州","江门蓬江","肇庆四会","江门市辖"].includes(val.taName));

          this.options.push(...datas);

          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        this.pageNum++;
        this.getUnvsList();
      },
      selected: function (val) {
        this.$emit('input', val);
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
