<template>
  <div class="agreement">
    <p class="agreement-title">本升硕留学服务协议书</p>
    <pre class="text">
甲  方： <span class="underline">&nbsp;广州远智教育科技有限公司&nbsp;</span>    
乙  方： <span class="underline">                      </span>                                             
依据《中华人民共和国民法典》及相关法律法规，甲乙双方本着合理、合法、合规并诚信、自愿、协商一致的原则，就甲方为乙方自费留学提供相关的留学服务事宜，达成如下协议：
<b>一、服务内容</b>
1.甲方为乙方提供境外硕士升学相关咨询及协助服务，包括书面介绍境外大学的学校概况、院校、院系、专业信息、培养方案、毕业条件等，根据乙方需求提供相关咨询解答和指导服务，乙方按本协议约定向甲方支付服务费；
2．乙方姓名：<span class="underline">&nbsp;&nbsp;&nbsp;&nbsp;</span>证件类型：<span class="underline">&nbsp;&nbsp;&nbsp;&nbsp;</span>证件号：<span class="underline">&nbsp;&nbsp;&nbsp;&nbsp;</span>申请<span class="underline">俄罗斯东北联邦大学</span>、<span class="underline">&nbsp;&nbsp;&nbsp;&nbsp;</span>专业学习，升学类别属于学历教育；根据申请情况乙方需服从专业调剂；
3.甲方指导乙方准备申请就读俄罗斯东北联邦大学                           
专业硕士研究生学位所需的相关材料，乙方应及时提供并保证乙方提交的所有文件、材料和陈述的内容均合法、真实、有效；
4.按照俄罗斯东北联邦大学的相关要求（如因境外学制要求、俄罗斯东北联邦大学毕业考试或毕业答辩规定）需出国学习的，由甲方安排相关人员或第三方机构负责指导乙方办理出国前的相关手续；
5.申请费：经甲方审核相关材料无误并审核通过后，乙方需向甲方缴纳申请费<span class="underline">人民币（大写）：        （¥：      元）。</span>甲方协助助乙方向俄罗斯东北联邦大学递交入学申请，如乙方入学申请未获俄罗斯东北联邦大学通过，则甲方应于大学确定不予录取之日起7个工作日内全额退回乙方缴纳的申请费。
6.服务费：甲方协助乙方取得俄罗斯东北联邦大学录取通知书（电子版）三日内，乙方需向甲方全额缴纳服务费<span class="underline">人民币（大写）：                （¥：      元，</span>含乙方已向甲方缴纳的申请费）。此服务费不包含俄罗斯东北联邦大学收取的学费，学费以每年五月份俄方高校官方公布的学费为准，由学员自行向校方提供的指定账户缴费。
7.乙方在被俄罗斯东北联邦大学硕士研究生专业正式录取之后（电子版录取通知书），须在收到甲方通知后3天内全额缴纳本协议约定的服务费，如未能全额缴纳服务费，则本协议书自动解除，甲方取消乙方在俄罗斯东北联邦大学硕士研究生专业的入读申请和录取资格，甲方已经收取的申请费不予退回。
<b>二、甲方责任</b>
1.向乙方介绍就读大学的基本情况，入学要求等留学相关信息；协助并指导乙方办理护照、签证、公证材料等，并为乙方提供留学期间所需食宿等相关费用的大致预算等咨询信息；
2.对乙方提供的所有材料，包括个人隐私，甲方均负有保密义务，除涉及乙方入学申请、签证申请需提供相应材料，不得向任何第三方透露乙方基本信息；
3.甲方保证，根据签订本合同时俄罗斯及我国现行法规、教育政策要求，甲方指导乙方准备申请就读的大学为俄罗斯正规的公立大学，中国教育部涉外监管网可查，为中国承认的国外研究生学历，乙方取得的毕业证书可以向中国教育部留学服务中心申请学历认证；
4.在乙方保证其提供的入学申请材料为真实无虚假的前提下，甲方协助乙方顺利入学；在乙方保证正常学时、参加考试、论文答辩的前提下，甲方协助乙方获得毕业证书；
5.乙方取得俄罗斯东北联邦大学硕士研究生毕业证书后，甲方应指导、协助乙方进行中国的留学学历学位认证，乙方需要提供符合中留服认证政策及其他政策所要求的全部材料。
<b>三、乙方责任</b>
1.乙方应符合中国公民自费出国留学条件，遵守国家关于公民自费留学的规定。乙方入学就读大学期间，若出现旷课，打架斗殴等违反高校规定或违反当地法律法规等任何事由，或因乙方自身原因中断、终止就读导致乙方不能毕业的，由乙方自行承担相应后果；无权向甲方主张退还服务费。
2.乙方需遵从甲方的相应安排，在甲方协助下完成入学前所需的相关手续办理工作（包括但不限于补充入学资料等）；
3.需乙方提供中国承认的大学本科毕业证及学位证，若乙方不能按时如实提供，因此而造成的后果由其自负；
4.乙方因学业需要在境外就读期间需遵守俄罗斯国家法律法规，如因乙方个人原因发生违法行为及违法活动并造成相关后果的，均由乙方自行承担。
<b>四、退学、退费规定</b>
1.如乙方未被俄罗斯东北联邦大学录取，甲方全额退还乙方申请费；
2.乙方被俄罗斯东北联邦大学成功录取后，视为甲方已为乙方提供本合同下主要服务，除非因甲方原因导致乙方未能取得俄罗斯东北联邦大学毕业证书，甲方已收取的服务费不予退回。
3.在乙方全额缴纳服务费后，如乙方拒绝办理入学申报，或不办理入学手续、撤回入学申请、单方中断或终止入学、单方要求解除本合同的，甲方收取的服务费不予退回。
<b>五、协议未尽事宜及其它约定</b>
1.本协议未尽事宜，甲乙双方可另行签定补充协议。补充协议为本协议的有效组成部分，与本协议具有同等法律效力。双方应当自本协议签订后，按照本协议约定履行各自义务。
2.在留学申请办理过程中，如前往国家的留学政策、签证政策或申请留学院校的入学要求发生变化，乙方应根据新的要求，及时提供补充材料；
3.乙方在境外留学期间，相关人身安全问题由乙方向就读的俄罗斯院校和驻俄中国大使馆寻求帮助；
<b>六、适用的法律及争议解决方法</b>
1.本协议的解释与履行适用中华人民共和国境内的相关法律法规；
2.甲乙双方在履行本协议中如发生争议，应由双方协商解决，如协商不成，可向甲方所在地管辖法院提起诉讼；
3.如遇不可抗力因素，本协议自然终止。本协议里的不可抗力仅指战争、疫情、地震、塌方、地陷、洪水、台风、等自然灾害和非因甲方或乙方原因而发生的火灾、爆炸以及战争、疫情而导致的协议双方均无法逆转的障碍。
<b>七、协议生效</b>
本协议自甲乙方签字之日起生效，一式两份，双方各执一份，每份具有同等法律效力。
<div class="agreement-footer">
  <div class="item">
    <p class="target">甲方：</p>
    <p class="date">日期：<span class="underline">   </span>年<span class="underline">   </span>月<span class="underline">   </span>日</p>
  </div>
  <div class="item">
    <p class="target">乙方：</p>
    <p class="date">日期：<span class="underline">   </span>年<span class="underline">   </span>月<span class="underline">   </span>日</p>
  </div>
</div>
    </pre>

    <div class="btn" v-if="!agreen" :disabled="show" @click="goAgreem">{{time?`阅读倒计时${time}秒`:"同意签署协议"}}</div>
  </div>
</template>

<script>
export default {
  props: ['agreen', 'imgUrl'],
  data() {
    return {
      learnId: "",
      show: true,
      time: 5,
    }
  },
  created() {
    this.learnId = this.$route.query.learnId || "";
  },
  mounted() {
    if(this.time>0){
      this.count();
    }
  },
  methods: {
    goAgreem() {
      if (this.time != 0) return;

      if (this.imgUrl) return this.$emit("closeMain", false);
      
      this.agreementSigning();
    },
    async agreementSigning() {
      await this.$http.post('/bds/agreementSigning/1.0/', {
        learnId: this.learnId,
      }).then(()=>{
        this.$emit("closeMain", false);
      });
    },
    count() {
      let start = setInterval(() => {
        if (this.time > 1) {
          this.time = this.time - 1;
        } else {
          this.time = this.time - 1;
          clearInterval(start);
          this.show = false;
          return;
        }
      }, 1000);
    },
  }
}
</script>

<style lang="less" scoped>
.agreement {
  width: 100%;
  padding: 0.2rem;

  .agreement-title {
    color: #170606;
    font-size: .17rem;
    font-weight: 600;
    margin-bottom: .1rem;
    text-align: center;
  }
  .text {
    margin: 0;
    white-space: pre-wrap;
  }
  .underline {
    text-decoration: underline;
  }
  .agreement-footer {
    display: flex;
    justify-content: space-between;
    color: #444;
    align-items: center;
    line-height: 1;
  }
  .btn {
    width: 100%;
    height: 0.4rem;
    left: 0;
    position: fixed;
    bottom: 0rem;
    text-align: center;
    line-height: 0.4rem;
    background: linear-gradient(
      135deg,
      rgba(240, 145, 144, 1) 0%,
      rgba(240, 120, 119, 1) 66%,
      rgba(240, 110, 108, 1) 100%
    );
    font-size: 0.15rem;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
  }
  .btn[disabled] {
    background: white;
    background: rgba(187, 182, 182, 1);
  }
}
</style>