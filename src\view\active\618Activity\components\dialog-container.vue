<template>
  <van-dialog
    v-model="show"
    width='3.35rem'
    class='yz-act618-dialog'
    :show-confirm-button='false'
    :close-on-click-overlay='closeOnClickOverlay'
    @close='close'
  >
    <div class="d-header">
      <p class="title">{{title}}</p>
      <div v-if='showClose' class="close cl" @click='close'>
        <img src="../../../../assets/image/active/618Activity/ic_close.png" alt="">
      </div>
    </div>
    <!-- 内容区域 -->
    <slot></slot>
  </van-dialog>
</template>

<script>
import { Dialog } from 'vant';
export default {
  components: { Dialog },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: false,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    value() {
      this.show = this.value;
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    close() {
      this.$emit('input', false);
      this.$emit('close');
    },
  },
};
</script>

<style lang="less">
.yz-act618-dialog{
  border-radius: 0.1rem;
  .d-header{
    padding: 0.15rem 0;
    position: relative;
    color: #000;
    font-size: 0.16rem;
    text-align: center;
    font-weight: 600;
    .close{
      padding: 0.1rem;
      position: absolute;
      z-index: 2;
      right: 0;
      top: 0;
      img{
        width: 0.16rem;
        height: 0.16rem;
        vertical-align: middle;
        float: left;
      }
    }
  }
}
</style>
