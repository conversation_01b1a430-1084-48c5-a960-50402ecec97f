<template>
  <div class="yz-growth-input">
    <div class="input-box">
      <template v-if='!finish'>
        <p class="p1">输入姓名，获得您的专属</p>
        <p class="p2">“上进荣誉墙“</p>
      </template>
      <p class="p3" v-else>上进青年荣誉墙生成中…</p>
      <input v-model.trim="name" type="text" class="input" :disabled='finish' placeholder="点击输入您的名字">
      <!-- <div class="disabled" v-show='finish'>{{name}}</div> -->
    </div>
    <img v-show='finish' src="../../../assets/image/active/growthSystem/paper.png" class="paper" alt="">
    <div class="img-box" v-if='!finish' @click='goFinish'>
      <img src="../../../assets/image/active/growthSystem/input-btn.png" class="btn" alt="">
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';

export default {
  data() {
    return {
      finish: false,
      name: '',
    };
  },
  methods: {
    goFinish() {
      if (!this.name) {
        Toast('请输入您的名字');
        return;
      }
      const patrn = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/im;
      if (patrn.test(this.name)) {
        Toast('名字不能含有特殊符号');
        return;
      }
      if (this.name.length > 10) {
        Toast('名字不能超过10个字符');
        return;
      }
      this.finish = true;
      setTimeout(() => {
        this.$emit('finish', { index: 2, name: this.name });
      }, 2700);
    },
  },
};
</script>

<style lang="less">
  .yz-growth-input{
    background: url(../../../assets/image/active/growthSystem/bg.png) no-repeat;
    background-size: 100% 100%;
    height: 100vh;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    text-align: center;
    padding-top: 1.98rem;
    .input-box{
      width: 2.84rem;
      height: 1.48rem;
      margin: 0 auto;
      background: url(../../../assets/image/active/growthSystem/box.png) no-repeat;
      background-size: 100% 100%;
      position: relative;
      z-index: 2;
      .p1{
        padding-top: 0.28rem;
        font-size: 0.12rem;
        margin-left: -0.1rem;
      }
      .p2{
        font-size: 0.2rem;
        margin-top: 0.05rem;
        margin-bottom: 0.12rem;
        margin-left: -0.1rem;
      }
      .p3{
        padding: 0.45rem 0 0.26rem;
      }
      .input{
        display: inline-block;
        width: 1.94rem;
        height: 0.44rem;
        background: #fff;
        border: none;
        color: #000000;
        border-radius: 0.04rem;
        text-align: center;
        margin-left: -0.1rem;
        font-size: 0.14rem;
        &::placeholder{
          color: #B0B0B0;
          text-align: center;
        }
        &:disabled{
          opacity: 1;
          color: #000000;
        }
      }
    }
    .paper{
      position: absolute;
      width: 2.4rem;
      height: 0.78rem;
      z-index: 1;
      left: 0.59rem;
      top: 2.61rem;
      animation: paperAnimate 2.5s ease forwards;
    }
    .img-box{
      margin-top: 0.2rem;
      img{
        width: 1rem;
        height: 0.42rem;
      }

    }
  }
  @keyframes paperAnimate {
    from{
      top: 2.61rem;
    }
    to{
      top: 3.38rem;
    }
  }
</style>
