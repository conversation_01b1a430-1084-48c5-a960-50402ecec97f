<template>
  <form action="" @submit.prevent="submit">
    <div class="edit-box">
      <div class="cl">
        <i id="btnEmotion" class="fl icons btn-emotion" @click="$refs.emotion.toggleBox()"></i>
        <button class="fr btn" :disabled="!content" @click="submit" :class="{active:change}">{{btnText}}</button>
        <div class="ipt-wrap">
          <input ref='edit' type="text" :placeholder="inputPlaceholder" v-model.trim="content" @focus="inputFocus" v-if="scroll"/>
          <input ref='edit' type="text" :placeholder="inputPlaceholder" v-model.trim="content"  v-else/>
        </div>
      </div>
      <emotion-list v-model="content" ref="emotion"/>
    </div>
  </form>
</template>

<script>
  import emotionList from '@/components/emotionList';

  export default {
    components: {emotionList},

    props: {
      isShow: {
        type: Boolean,
        default: false,
      },
      isSocketConnect: {
        type: Boolean,
        default: false
      },
      inputPlaceholder: {
        type: String,
        default: '我要留言'
      },
      btnText:{
        type:String,
        default:'提交'
      },
      change:{
        type:Boolean,
        default:false
      },
      scroll:{
        type:Boolean,
        default:true
      }
    },
    data() {
      return {
        content: ''
      }
    },
    mounted() {
      this.init();
    },

    watch: {
      content(val) {
        if (!val) {
          this.storage.removeItem('draft'); // 清除草稿
        } else {
          this.setDraft(val);
        }
      },
    },
    methods: {
      init() {
        let draft = this.storage.getItem('draft');
        if (draft) {
          draft = JSON.parse(draft);
          const cha = Date.now() - draft.time;
          if (cha <= (12 * 60 * 60 * 1000)) {
            this.content = draft.val;
          }
        }
      },
      setDraft(val) {
        setTimeout(() => {
          const time = Date.now();
          const json = { val, time };
          this.storage.setItem('draft', JSON.stringify(json));
        }, 100);
      },
      formatEmotions: function (content) {
        return this.$refs.emotion.formatEmotions(content);
      },
      submit: function () {
        let content = this.content;
        if (!content) return;
        if (!this.isSocketConnect) {
          this.$modal({message: '请稍等，连接中…', icon: 'warning'});
          return;
        }
        this.$emit('submit', content);
        this.content = '';
      },
      inputFocus: function () {
        setTimeout(function () {
          window.scrollTo(0, document.getElementsByTagName('body')[0].offsetHeight);
        }, 600);
      },
      editFocus() {
        this.$refs.edit.focus();
      },
    },

  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/variable";

  .edit-box{
    position:absolute;right:0;bottom:0;left:0;z-index:1;
    > .cl{max-width:@maxWidth;margin:0 auto;padding:.07rem .15rem;background-color:#fff;}
    .btn{
      line-height:.36rem;border:none;background-color:transparent;color:@color;font-size:.14rem;
      &.active{background-color: #E85B57;color: white;width: .5rem;height: .3rem;line-height: .3rem;text-align: center}
    }
    .ipt-wrap{
      position:relative;margin-left:.3rem;margin-right:.4rem;
      input{width:90%;height:.3rem;padding:0.08rem 0.1rem 0.08rem 0.11rem;line-height:.2rem;font-size:.14rem;color:#333;border:none;background-color:#f7f7f7;border-radius:.04rem;}
    }
  }
  .btn-emotion{width:.32rem;height:.32rem;background-image:url(../../assets/image/student/ic_course_face.png);}
  .i-quiz{position:absolute;top:.08rem;left:.1rem;width:.16rem;height:.17rem;vertical-align:middle;background-image:url(../../assets/image/live_ico_quiz.png);}
</style>
