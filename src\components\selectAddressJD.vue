<template>
  <div class="children-page">
    <top-bar :title="title">
      <router-link :to="{name:'addressEditJD',query:{backType:'back',title:title,saType:saType,onlyGD:onlyGD}}" slot="rt" :style="{'padding-right':'0.1rem'}">添加</router-link>
    </top-bar>
    <div class="inner">
      <div class="addressNone txt-c" v-if="!hasAddressJD">
        <img src="../assets/image/addressNone.png" alt="">
        <p>您还没有添加收货地址</p>
      </div>
      <template v-else>
        <div class="address-list" v-if="!isLoadingJD">
          <div class="hd">请选择您的收货地址：</div>
          <div class="bd bc-w">
            <div class="item"
                 v-for="item in addressesJD"
                 :key="item.saId"
                 :class="{active:selectedAddress.saId===item.saId}"
                 @click="selected(item)">
              <div class="lf"><i class="icons i-selected"></i></div>
              <div class="mid">
                <div class="name">{{item.saName}} {{item.mobile}}</div>
                <div class="addr">
                  {{item.provinceName}}{{item.cityName}}{{item.districtName}}{{item.streetName}} {{item.address}}
                </div>
              </div>
              <div class="rt">
                <router-link class="btn-edit" :to="{name:'addressEditJD',query:{saId:item.saId,backType:'back',title:title,saType:saType,onlyGD:onlyGD}}"></router-link>
              </div>
            </div>
          </div>
        </div>
        <load-bar :isLoading="isLoadingJD" :allLoaded="allLoadedJD"/>
        <foot-bar :title="buttonText" v-on:submit="submit"></foot-bar>
      </template>
    </div>
    <van-popup v-model="show" round :style="{ minHeight: '2.08rem' }">
      <div class="information">
        <img src="../assets/image/<EMAIL>" class="close" @click='show = false' alt="">
        <p class="dialog-text">你选择的教材收货信息是:</p>
        <div class="grey">
          {{selectedAddress.saName}} {{selectedAddress.mobile}} <br/>{{selectedAddress.provinceName}}{{selectedAddress.cityName}}{{selectedAddress.districtName}}{{selectedAddress.streetName}} {{selectedAddress.address}}
        </div>
      </div>
      <div class="btn" @click="confirm">确认</div>
    </van-popup>
    <!-- <van-dialog
      v-model="show"
      show-confirm-button
      confirm-button-text='确定'
      confirm-button-color='#F06E6C'
      close-on-click-overlay
      @confirm='confirm'>
      <img src="../assets/image/<EMAIL>" class="close" @click='show = false' alt="">
      <p class="dialog-text">
        你选择的教材收货信息是: <br />
        <span class="grey">
          {{selectedAddress.saName}} {{selectedAddress.mobile}} <br/>{{selectedAddress.provinceName}}{{selectedAddress.cityName}}{{selectedAddress.districtName}}{{selectedAddress.streetName}} {{selectedAddress.address}}
        </span>
      </p>
    </van-dialog> -->
  </div>
</template>

<script>
  import { Toast, Dialog } from 'vant';
  import {provinceName, cityName, districtName} from '../common'
  import topBar from '@/components/topBar';
  import footBar from '@/components/footBar';
  import loadBar from '@/components/loadBar';

  export default {
    filters: {provinceName, cityName, districtName},
    props: {
      addressId: null,
      buttonText: {type: String, default: '提交'},
      title: {type: String, default: '选择收货地址'},
      saType: {type: String, default: '3'},
      onlyGD: {type: String, default: '1'},
      learnId:String,
      taskId:String
    },
    data() {
      return {
        hasAddressJD: true,
        addressesJD: [],
        selectedAddress: {},
        isLoadingJD: true,
        allLoadedJD: false,
        saName:'',
        show: false,
      }
    },
    created(){
      this.getAddress();
      this.getStudentTask();
      this.saName=this.storage.getItem('realName')
    },
    methods: {
      getStudentTask(){
        let data={
          learnId:this.learnId,
          taskId:this.taskId
        }
        this.$http.post('/bds/getStudentTask/1.0/',data).then(res=>{
          if (res.code !== '00') return;
          if(res.body.task_status==='1'&&res.body.task_type==='3'){
            this.$modal({message:'该任务已完成',icon:'success',duration:6000})
          }
        })
      },
      getAddress() {
        this.$http.post('/us/myAddress/1.0/',{saType :this.saType}).then(res => {
          this.isLoadingJD = false;
          if (res.code !== '00') return;

          this.addressesJD = res.body || [];
          // 去掉默认选中
          // this.selectedAddress = this.addressesJD.find(val => {
          //   if (this.addressId) {
          //     return val.saId === this.addressId;
          //   } else {
          //     return val.isDefault === '1';
          //   }
          // }) || this.addressesJD[0];
          this.hasAddressJD = this.addressesJD.length > 0;
        });
      },
      selected: function (item) {
        this.selectedAddress = item;
      },
      submit: function () {
        if(!this.selectedAddress.saId){
          this.$modal({message: '请选择收货地址', icon: 'warning'});
          return;
        }
        this.show = true;

      },
      confirm() {
        this.$emit('selected', this.selectedAddress);
      },
    },
    components: {topBar, footBar, loadBar, Dialog}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .addressNone{
    img{ width:1.1rem; margin-top:1.6rem; }
    p{ margin-top:.18rem; color:#999; }
  }
  .address-list{
    .hd{ padding:.12rem .12rem .02rem; color:#999; font-size:.12rem; }
    .item{
      display:flex; align-items:center; position:relative; padding:.22rem 0 .22rem .12rem;
      &:after{ .borderBottom }
    }
    .mid{ flex:1; padding-left:.06rem; }
    .lf,
    .rt{ font-size:0; }
    .addr{ padding-top:.03rem; color:#666; font-size:.13rem; }
    .btn-edit{ display:block; width:.36rem; height:.36rem; background:url(../assets/image/public_ico_open_right.png) no-repeat 0 0; background-size:100% 100%; }
    .i-selected{ width:.16rem; height:.16rem; background-image:url(../assets/image/i-unselected.png); }

    .active{
      .i-selected{ background-image:url(../assets/image/i-selected.png); }
    }
  }


  .information{
    width: 2.86rem;
    // height: 2.08rem;
    padding: 0.48rem 0.26rem 0.28rem;
    .close{
      position: absolute;
      top: 0;
      right: 0;
      width: 0.4rem;
      height: 0.4rem;
      z-index: 2;
    }
    .dialog-text{
      color: #170606;
      font-size: 0.17rem;
    }
    .grey{
      margin-top: .04rem;
      color: rgba(23, 6, 6, 0.4);
      line-height: 0.2rem;
      font-size: 0.14rem;
    }
  }
  .btn{
    width: 100%;
    height: .5rem;
    line-height: .5rem;
    color: #F06E6C;
    text-align: center;
    position: absolute;
    bottom: 0;
    border-top: 1px solid rgba(23, 6, 6, .08);
  }
  .van-popup--center.van-popup--round{
    border-radius: .15rem;
  }
</style>
