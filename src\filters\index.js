/**
 * 过滤器
 */
import moment from "moment";
import config from "@/config";
import { fmtDate } from "../common";

export const findDict = (dictValue, dictId, defaultValue) => {
  return (
    (
      (dictJson[dictId] || []).find((item) => item.dictValue === dictValue) ||
      {}
    ).dictName ||
    defaultValue ||
    ""
  );
};

/**
 * 格式化日期
 * */
export const formatDate = fmtDate;

/**
 * 图片地址
 * */
export const imgBaseURL = (url) => {
  if (!url) return "";
  return `${config.imgBaseURL}${url}`;
};

/**
 * oss图片地址
 * */
export const imgOssURL = (url) => {
  if (!url) return "";
  return config.articleURL + url;
};

/**
 * 上传图片后地址
 * */
export const imgPosterBaseURL = (url) => {
  if (!url) return "";
  return `${config.imgPosterBaseURL}${url}`;
};
/**
 * 默认头像
 * */
export const defaultAvatar = (url) => {
  let newUrl = url || require("../assets/image/settings/ic_mine_head_man.png");
  return newUrl.replace(/^http:/, "");
};
export const defaultAvatar2 = (url) => {
  let newUrl = url || require("../assets/image/xiaoyuan-1.png");
  return newUrl.replace(/^http:/, "");
};

export const defaultAvatar3 = (url) => {
  let newUrl =
    url || require("../assets/image/settings/enrollList/headImg.png");
  return newUrl.replace(/^http:/, "");
};

/**
 * 默认二维码
 * */
export const defaultCode = (url) => {
  let newUrl = url || require("../assets/image/teacherKit/code.png");
  return newUrl.replace(/^http:/, "");
};
/**
 * 隐藏手机号码中间四位
 * */
export const hidePhone = (phone, len = 6) => {
  if (!phone) return phone;
  return phone.replace(
    new RegExp("^(\\d{3}).+(\\d{" + (8 - len) + "})$"),
    `$1${"*".repeat(len)}$2`
  );
};

/**
 * 隐藏姓名第一位以外的,保留三位
 * */
export const hideName = (name) => {
  if (!name) return name;
  return name.replace(/^(.).+/, "$1**");
};

/**
 * 隐藏昵称
 * */
export const hideNickname = (name) => {
  if (!name) return name;
  if (name.length === 1) {
    return name.replace(/^(.)/, "$1**");
  } else if (name.length === 2) {
    return name.replace(/^(.).+/, "$1**");
  } else {
    return name.replace(/^(.).+(.)$/, "$1*$2");
  }
};

/**
 * 隐藏身份证
 * */
export const hideIdCard = (idCard) => {
  if (!idCard) return idCard;
  return idCard.replace(idCard.substr(6, 8), "****");
};

/**
 * 隐藏身份证号
 * */
export const hideIdCard1 = (idCard) => {
  if (!idCard) return idCard;
  return idCard.replace(/^(\w{6})(\w+)(\w{4})$/, "$1********$3");
};

// 商品类型
export const goodsType = (code) => {
  const obj = { 1: "商品", 2: "活动", 3: "培训课程", 4: "培训教材" };
  return obj[code] || "商品";
};

/**
 * 学员头像
 * */
export const avatar = (sex) => {
  const obj = {
    1: require("../assets/image/student_man.png"),
    2: require("../assets/image/student_women.png"),
  };
  return obj[sex] || "";
};
/**
 * 微信好友代付的学员头像
 * */
export const avatar2 = (sex) => {
  const obj = {
    1: require("../assets/image/xiaozhi-1.png"),
    2: require("../assets/image/xiaoyuan-1.png"),
  };
  return obj[sex] || "";
};

/**
 * 签到分享图片
 * */
// export const signshare = (str) => {
//    const obj = {0: require('../assets/image/bg1.jpg'), 1: require('../assets/image/bg2.jpg')}
//   return obj[Math.floor(Math.random()*2)] || '';
//  }
/*
 * 数量单位
 * */
export const numEllipsis = (num) => {
  if (typeof num != "number") return num;
  if (num < 10000000) {
    return num < 10000
      ? num
      : num % 10000 > 1
      ? parseInt(num / 10000) + "w+"
      : parseInt(num / 10000) + "w";
  } else {
    return "999w+";
  }
};
/**
 * 任务类型
 * */
export const taskType = (code) => {
  const obj = {
    2: "跳转",
    3: "地址确认",
    4: "考场确认",
    5: "考场确认",
    13: "考点确认",
    7: "跳转",
    20: "跳转",
    8: "报名通知",
    16: "考试费缴费",
    17: "现场确认",
  };
  return obj[code] || "通知";
};

export const taskClass = (taskType) => {
  const obj = { 1: "read", 2: "router", 3: "chose" };
  return obj[taskType];
};

/**
 * 人民币大写
 * */
export const convertCurrency = (n) => {
  if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(n)) return n;
  if (+n === 0) return "零分";

  let unit = "仟佰拾亿仟佰拾万仟佰拾元角分";
  let str = "";
  n += "00";
  let p = n.indexOf(".");
  if (p >= 0) n = n.substring(0, p) + n.substr(p + 1, 2);
  unit = unit.substr(unit.length - n.length);
  for (let i = 0; i < n.length; i++)
    str += "零壹贰叁肆伍陆柒捌玖".charAt(n.charAt(i)) + unit.charAt(i);
  return str
    .replace(/零(仟|佰|拾|角)/g, "零")
    .replace(/(零)+/g, "零")
    .replace(/零(万|亿|元)/g, "$1")
    .replace(/(亿)万|壹(拾)/g, "$1$2")
    .replace(/^元零?|零分/g, "")
    .replace(/元$/g, "元整");
};

// 京东图片地址拼接
export const jdImgUrl = (url = "", size = "n0") => {
  return `${config.jsImgHost + size}/${url}`;
};

// 数组倒叙
export const reverseArr = (arr) => {
  let newArr = [];
  for (let i = arr.length; i--; i > 0) {
    newArr.push(arr[i]);
  }
  return newArr;
};

/**
 * 证明类型
 * */
export const proveType = (code) => {
  const obj = {
    1: "在读证明申请",
    2: "课时证明申请",
    3: "发票申请",
    4: "收据申请",
    5: "其它申请",
    8: "花名册和成绩单（在校证明）",
  };
  return obj[code] || "证明申请";
};

/**
 * 证明类型
 * */
export const proveStatus = (code) => {
  // const obj = {'1': '审核中', '2': '受理中', '3': '已通过', '4': '已驳回'};
  const obj = {
    "-1": "已驳回",
    0: "审核中",
    1: "待缴费",
    2: "待发货",
    3: "已完成",
  };
  return obj[code] || "待审核";
};

// base64转码
export const b64DecodeUnicode = (str) => {
  let name;
  try {
    name = decodeURIComponent(
      atob(str)
        .split("")
        .map(function (c) {
          return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
        })
        .join("")
    );
  } catch (e) {
    name = "上进青年";
  }
  return name;
};
export const timeBefore = (valueTime) => {
  if (valueTime) {
    valueTime = valueTime.replace(/-/g, "/");
    valueTime = new Date(valueTime);
    valueTime = valueTime.getTime();
    var newData = Date.parse(new Date());
    var diffTime = Math.abs(newData - valueTime);
    if (diffTime > 7 * 24 * 3600 * 1000) {
      var date = new Date(valueTime);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? "0" + m : m;
      var d = date.getDate();
      d = d < 10 ? "0" + d : d;
      var h = date.getHours();
      h = h < 10 ? "0" + h : h;
      var minute = date.getMinutes();
      var second = date.getSeconds();
      minute = minute > 10 ? minute : "0" + minute;
      second = second < 10 ? "0" + second : second;
      return m + "-" + d + " " + h + ":" + minute;
    } else if (
      diffTime < 7 * 24 * 3600 * 1000 &&
      valueTime < new Date(new Date(new Date().toLocaleDateString()).getTime())
    ) {
      var week;
      var date = new Date(valueTime);
      if (date.getDay() == 0) week = "星期日";

      if (date.getDay() == 1) week = "星期一";

      if (date.getDay() == 2) week = "星期二";

      if (date.getDay() == 3) week = "星期三";

      if (date.getDay() == 4) week = "星期四";

      if (date.getDay() == 5) week = "星期五";

      if (date.getDay() == 6) week = "星期六";
      var h = date.getHours();
      h = h < 10 ? "0" + h : h;
      var minute = date.getMinutes();
      minute = minute > 10 ? minute : "0" + minute;
      return `${week} ${h}:${minute}`;
    } else if (diffTime < 24 * 3600 * 1000 && diffTime > 3600 * 1000) {
      var date = new Date(valueTime);
      var h = date.getHours();
      h = h < 10 ? "0" + h : h;
      var minute = date.getMinutes();
      minute = minute > 10 ? minute : "0" + minute;
      return `${h}:${minute}`;
    } else if (diffTime < 300 * 1000 && diffTime >= 0) {
      return "刚刚";
    } else if (diffTime < 3600 * 1000 && diffTime > 0) {
      var dayNum = Math.floor(diffTime / (60 * 1000));
      return dayNum + "分钟前";
    }
  }
};

// 小数点
export const transformCentFixed = (val, fixed = 2) => {
  return val ? Number(val).toFixed(fixed) : "0.00";
};

/**
 * 使用momentjs格式化时间
 * @param {String | Number} val 时间,可以是字符串(2020-01-01 00:00:00)也可以是时间戳
 * @param {String} format 格式话格式
 * @returns 返回你需要的时间格式
 */
export function formatTimeByMoment(val, format = "YYYY-MM-DD HH:mm:ss") {
  if (!val) {
    return "";
  }
  return moment(val).format(format);
}

/**
 * 转换时间格式
 * @param {Strig} val 时间字符串(格式：YYYY-MM-DD HH:mm:ss)
 */
export const transfromTimeStr = (val) => {
  if (!val) return;
  let time = Date.now() - new Date(val.replace(/-/g, "/")).getTime();
  let timeStr = "";
  if (time < 5 * 60 * 1000) {
    timeStr = "刚刚";
  } else if (time / 60000 > 5 && time / 60000 < 60) {
    timeStr = parseInt(time / 60000) + "分钟前";
  } else if (time / 3600000 > 1 && time / 3600000 < 24) {
    if (val.substr(0, 10) !== moment().format("L")) {
      timeStr = moment(val.replace(/-/g, "/")).subtract(0, "days").calendar();
    } else {
      timeStr = moment(val.replace(/-/g, "/")).format("H:mm");
    }
  } else if (time / (3600000 * 24 * 7) < 1) {
    timeStr = moment(val.replace(/-/g, "/")).format("dddd H:mm");
  } else {
    timeStr = val.substr(0, 16);
  }
  return timeStr;
};

/**
 * 用于个人主页圈子时间格式
 * @param {Strig} val 时间字符串(格式：YYYY-MM-DD HH:mm:ss)
 */
export const transfromCircleTime = (val) => {
  if (!val) return;
  let time = Date.now() - new Date(val.replace(/-/g, "/")).getTime();
  let timeStr = "";
  if (time < 5 * 60 * 1000) {
    timeStr = "刚刚";
  } else if (time / 60000 > 5 && time / 60000 < 60) {
    timeStr = parseInt(time / 60000) + "分钟前";
  } else if (time / 3600000 > 1 && time / 3600000 < 24) {
    timeStr = moment(val.replace(/-/g, "/")).format("HH:mm");
  } else if (time / (3600000 * 24 * 7) < 1) {
    timeStr = moment(val.replace(/-/g, "/")).fromNow();
  } else {
    timeStr = val.substr(0, 16);
  }
  return timeStr;
};

/**
 * 用于任务推送时间转换
 * @param {Strig} val 时间字符串(格式：YYYY-MM-DD HH:mm:ss)
 */

export const timestampFormat = (val) => {
  let timestamp = new Date(val.replace(/-/g, "/")).getTime();
  function zeroize(num) {
    return (String(num).length == 1 ? "0" : "") + num;
  }

  var curTimestamp = parseInt(new Date().getTime() / 1000); //当前时间戳
  var timestampDiff = curTimestamp - timestamp; // 参数时间戳与当前时间戳相差秒数

  var curDate = new Date(curTimestamp * 1000); // 当前时间日期对象
  var tmDate = new Date(timestamp); // 参数时间戳转换成的日期对象

  var Y = tmDate.getFullYear(),
    m = tmDate.getMonth() + 1,
    d = tmDate.getDate();
  var H = tmDate.getHours(),
    i = tmDate.getMinutes(),
    s = tmDate.getSeconds();
  if (
    curDate.getFullYear() == Y &&
    curDate.getMonth() + 1 == m &&
    curDate.getDate() == d
  ) {
    return "今天 " + zeroize(H) + ":" + zeroize(i);
  } else {
    var newDate = new Date((curTimestamp - 86400) * 1000); // 参数中的时间戳加一天转换成的日期对象
    if (
      newDate.getFullYear() == Y &&
      newDate.getMonth() + 1 == m &&
      newDate.getDate() == d
    ) {
      return "昨天 " + zeroize(H) + ":" + zeroize(i);
    } else {
      return (
        Y +
        "年" +
        zeroize(m) +
        "月" +
        zeroize(d) +
        "日 " +
        zeroize(H) +
        ":" +
        zeroize(i)
      );
    }
  }
};

// 秒转小时
export const sTransfromH = (s) => {
  if (s < 59) {
    return s + "秒";
  }

  if (s > 60 && s < 3600) {
    return parseInt(s / 60) + "分钟";
  }

  if (s >= 3600) {
    let m = s % 3600;
    let surplusS = m % 60;
    return parseInt(s / 3600) + "小时" + parseInt(m / 60) + "分钟";
  }
};
