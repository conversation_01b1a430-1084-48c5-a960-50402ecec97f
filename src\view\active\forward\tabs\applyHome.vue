<template>
  <div class="apply-home">
    <!-- 分享组件 -->
    <share title="限时千元活动，报读可赢取上进专项奖学金！" desc="活动旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！"
           imgUrl='http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png' :link="shareLink" :isActivity="true"
           scholarship="125" ref="share" />

    <!-- 主图 -->
    <div class="main-img">
      <swiperEnroll v-if='isActivityStart' class="gcc-swiper" text='报读成功' textColor='#ffffff' />
      <div class="ac-rule-btn" @click="$router.push('/active/forward/activityRule')">活动规则 ></div>
    </div>

    <!-- 倒计时 -->
    <div class="time-box">
      <countdown :startTime='startTime' :endTime='endTime - 10 * 60 * 1000 ' :text='countDownText' ref='countdown' />
      <span v-if='isActivityEnd'>活动已结束～亲下次请赶早哦！</span>
    </div>

    <!-- 奖学金 -->
    <div class="scholarship">
      <h2 class="title">299元成考上进礼包</h2>
      <div class="volume">
        <!-- <div class="gold"></div> -->
        <div class="volume-img">
          <img class="scholarship" src="../../../../assets/image/active/forward/gold.png" />
        </div>
        <div class="content">
          <div class="text">
            <i class="iocn"></i>
            <span>考前辅导课(礼包价199)</span>
          </div>
          <div class="text">
            <i class="iocn"></i>
            <span>三本辅导教材(礼包价100)</span>
          </div>
          <!-- 领取按钮 -->
          <div class="button-box">
            <!-- <button class="receive-btn" @click="vouchers"></button> -->
            <button class="receive-btn" v-if="!isReceive" @click="vouchers">领取奖学金</button>
            <button class="receive-btn" v-if="!isSeikyo && isReceive" @click="toEnrolmentPage">马上报读</button>
            <button class="receive-btn" v-if="isReceive && isSeikyo" @click="switchTab">马上激活</button>

          </div>
          <!-- 领取人数 -->
          <!-- <div class="recipients-num" v-if='isActivityStart'>
            <span>已有{{ 6889 + receiveCount}}人领取了奖学金</span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 报读步骤 -->
    <div class="enrollment-steps">
      <img src="../../../../assets/image/active/gccScholarship/enrollmentSteps.png" />
    </div>

    <!-- 专业价格 -->
    <div class="price">
      <div class="title"></div>
      <div class="info">
        <div class="table dsnm">
          <div class="table-t">
            <p class="fl schoolName">
              肇庆学院<span v-if='!pfsnLevel'>(招生层次:高起专、专升本)</span><span
                    v-else>(招生层次:{{pfsnLevel == 1 ? "专升本" : "高起专"}})</span>
            </p>
            <span class="fr">学制/年：3年</span>
            <br />
            <span class="fr">学杂费：400元/学年（多退少补），按2.5年收取</span>
            <br />
            <span class="schoolPf"></span>
          </div>
          <div class="table-b">
            <table>
              <tbody>
                <template v-if='pfsnLevel == 5 || !pfsnLevel'>
                  <tr>
                    <td class="hasBg" colspan="3" align="center">高起专</td>
                  </tr>
                  <tr>
                    <td rowspan='3' class="text_top">文史类</td>
                    <td class="text-l">
                      小学语文教育
                    </td>
                    <td class="ric">
                      <p class="red">2300</p>
                      <p>元/学年</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="text-l">
                      小学英语教育
                    </td>
                    <td class="ric">
                      <p class="red">2645</p>
                      <p>元/学年</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="text-l">
                      小学教育、学前教育
                    </td>
                    <td class="ric">
                      <p class="red">2300</p>
                      <p>元/学年</p>
                    </td>
                  </tr>
                </template>
                <template v-if='pfsnLevel == 1 || !pfsnLevel'>
                  <tr>
                    <td class="hasBg" colspan="3" align="center">专升本</td>
                  </tr>
                  <tr>
                    <td rowspan='2' class="text_center">理工类</td>
                    <td class="text-l">会计学、人力资源管理、行政管理</td>
                    <td class="ric">
                      <p class="red">2500</p>
                      <p class>元/学年</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="text-l">电气工程及其自动化</td>
                    <td class="ric">
                      <p class="red">2875</p>
                      <p>元/学年</p>
                    </td>
                  </tr>
                  <tr>
                    <td class="text_center">文史类</td>
                    <td class="text-l">汉语言文学、小学教育、学前教育</td>
                    <td class="ric">
                      <p class="red">2500</p>
                      <p class>元/学年</p>
                    </td>
                  </tr>
                </template>

              </tbody>
            </table>
          </div>
        </div>
        <p class="special-instructions">
          <span class="point">•</span>
          特殊说明：英语教育仅限肇庆考区报读
        </p>

        <div class="table dsnm" v-if='pfsnLevel == 5 || !pfsnLevel'>
          <div class="table-t">
            <p class="fl schoolName">
              清远职业技术学院<span>(招生层次:高起专)</span>
            </p>
            <span class="fr">学制/年：3年</span>
            <br />
            <span class="fr">学杂费：400元/学年（多退少补），按2.5年收取</span>
            <br />
            <span class="schoolPf"></span>
          </div>
          <div class="table-b">
            <table>
              <tbody>
                <tr>
                  <td class="text_top">文史类</td>
                  <td class="text-l">
                    行政管理、市场营销、大数据与会计、学前教育、旅游管理、电子商务
                  </td>
                  <td class="ric">
                    <p class="red">2300</p>
                    <p>元/学年</p>
                  </td>
                </tr>
                <tr>
                  <td class="text_top">
                    理工类
                  </td>
                  <td class="text-l">
                    药学、护理、汽车检测与维修技术
                  </td>
                  <td class="ric">
                    <p class="red">2600</p>
                    <p>元/学年</p>
                  </td>
                </tr>
                <tr>
                  <td class="text_top">
                    外语类
                  </td>
                  <td class="text-l">
                    商务英语
                  </td>
                  <td class="ric">
                    <p class="red">2600</p>
                    <p>元/学年</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
    </div>

    <!-- 学员风采 -->
    <div class="studentStory">
      <div class="headBox">
        <img src="../../../../assets/image/active/gccScholarship/xyfc.png" alt="" class="fourright">
      </div>
      <div class="swiper">
        <swiper :options="swiperOption">
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/1.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/9.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/2.png">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/3.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/4.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/5.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/6.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/7.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/8.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/10.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/11.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/12.jpg">
          </swiper-slide>
          <swiper-slide>
            <img class="swiperImg" src="../../../../assets/image/active/fullTimeSystem/student/13.jpg">
          </swiper-slide>
        </swiper>
      </div>
    </div>

    <!-- 留言区，常见问题 -->
    <div class="service">
      <van-tabs v-model="serviceActive">
        <!-- <van-tab title="留言区">
          <userMessageContent :scholarship="scholarship"></userMessageContent>
        </van-tab> -->
        <van-tab title="常见问题">
          <quest></quest>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 活动声明 -->
    <div class="acStatement">
      <p>

        承办单位: 广州远智教育科技有限公司<br />
        邮编: 516600 粤ICP备12034252号-1
      </p>
    </div>

    <!-- 邀约 员工显示 -->
    <!-- v-if="isEmployee" -->
    <div class="Invite" v-if="isEmployee && !isAppOpen">
      <router-link :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'gccScholarship'}}">
        <img class="InviteBtn" src="../../../../assets/image/active/gccScholarship/InviteBtn.png" alt="">
      </router-link>
    </div>

  </div>
</template>
<script>
import swiperEnroll from "../../enrollAggregate/components/swiper-enroll"
import countdown from '../components/countdown'
import { swiper, swiperSlide } from "vue-awesome-swiper";
import quest from "@/components/activePage/questionList";
import userMessageContent from "@/components/activePage/userMessageContent";
import { isEmployee, isLogin } from "@/common";
import bridge from '@/plugins/bridge';
import share from "@/components/share";
import { toAppLogin } from '@/common/jump';
import recruitType from '../recruitType'

export default {
  components: {
    swiperEnroll,
    countdown,
    swiper,
    swiperSlide,
    quest,
    userMessageContent,
    share
  },
  mixins: [recruitType],
  props: {
    pfsnLevel: [String, Number],
    regChannel: [String, Number],
    unvs: {
      default: () => { },
      type: Object,
    },
  },
  data () {
    return {
      shareLink: window.location.origin + '/active/forward/index',
      isAppOpen: false,
      isEnroll: false,//是否报读国开/成教
      activityData: {},
      scholarship: '125',// 活动类型
      volumeType: '06_sj',//卷类型
      receiveCount: 0,//领劵总人数
      isReceive: false,
      isActivityStart: true, //活动是否开始
      isActivityEnd: false, //活动是否结束
      acStatus: 0, //活动状态 0：活动未开始，1：活动开始中，2：活动已结束 3：活动结束7天后
      countDownText: "",
      countdownShouw: true,
      startTime: 0,//活动开始时间
      endTime: 0, //活动结束时间
      serviceActive: 0,
      isEmployee: isEmployee(),
      inviteId: '', //邀约人id
      swiperOption: {
        initialSlide: 1,
        autoplay: 2000,
        centeredSlides: true,
        loop: true,
        slidesPerView: "auto",
        loopedSlides: 1,
        autoplayDisableOnInteraction: false,
      },
      timer: null,
    }
  },
  mounted () {
    this.inviteId = this.$route.query.inviteId || "";
    bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        this.isAppOpen = true;
      }
    });
    this.getVoucherStatus();
    this.getActivityInfo();
    this.getRecipientsNum();
  },
  methods: {
    //活动开始倒计时
    countdown () {
      this.timer = setInterval(() => {
        const now = new Date().getTime();
        if (now > this.activityData.StartTime) {
          this.startTime = this.activityData.StartTime;
          this.endTime = this.activityData.EndTime;
          this.activeProcessing(now, this.activityData);
          this.$emit('setActiveStatus', this.acStatus);
          clearInterval(this.timer);
        }
      }, 1000);
    },
    //活动开始前
    activityBefore (now, acConfig) {
      /**
         1、活动未开始不可以领券、报读，显示距活动开始倒计时
         2、不显示券的领取人数
         3、不显示领取成功轮播
         4、点击--领取奖学金/马上报读--提示：活动还未开始~请耐心等待噢！
       */
      this.countDownText = '距离报名开始';
      this.acStatus = 0;
      this.isActivityStart = false;
      if (acConfig.isShowEnd === 1) {
        this.countdownShouw = true;
        this.$refs.countdown.getSystemDateTime(now);
      }

    },
    //活动进行中
    activeProcessing (now, acConfig) {
      this.countDownText = '距离报名结束';
      this.acStatus = 1;
      // this.isActivityStart = true;
      if (acConfig.isShowEnd === 1) {
        this.countdownShouw = true;
        this.$refs.countdown.getSystemDateTime(now);
      };
    },
    //活动结束后
    activityAfter () {
      this.acStatus = 2;
      this.countdownShouw = false;
      this.isActivityEnd = true;
    },
    //活动结束7天后
    ac7DaysLater () {
      this.acStatus = 3;
    },
    switchTab () {
      if (this.isAppOpen && !isLogin()) {
        toAppLogin();
        return;
      }
      this.$emit('setTabIndex', 1);
    },
    toEnrolmentPage () {
      if (this.isAppOpen && !isLogin()) {
        toAppLogin();
        return;
      }
      const now = new Date().getTime();

      //活动开始
      if (now < this.activityData.StartTime) {
        this.$modal({
          message: "活动还未开始~请耐心等待噢！",
          icon: "warning",
        });
        return;
      }
      //活动结束
      if (now > this.activityData.EndTime) {
        this.$modal({
          message: "活动已结束~下次赶早哦！",
          icon: "warning",
        });
        return;
      };

      // 是否App打开
      if (this.isAppOpen) {
        if (this.isSeikyo) {
          window.location.href = `yuanzhiapp://yzwill.cn/Home?params={"tab":3}`;
          return
        } else {
          window.location.href = `yuanzhiapp://yzwill.cn/Enroll/InfoInput?params={"activeName":"${this.activityData.actName}","grade":"2022","scholarship":"125"}`;
          // window.location.href = `yuanzhiapp://yzwill.cn/Enroll/InfoInput?params={"activeName":'${this.activityData.actName}',"grade":"2022","scholarship":'125'}`;
          return;
        }
      };

      let route = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName: this.activityData.actName,
          recruitType: '1',
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
          unvs: JSON.stringify(this.unvs),
        }
      };
      this.$router.push(route);
    },
    //领劵
    vouchers () {
      if (this.isAppOpen && !isLogin()) {
        toAppLogin();
        return;
      }
      //活动开始钱
      if (this.acStatus == 0) {
        this.$modal({
          message: "活动还未开始~请耐心等待噢！",
          icon: "warning",
        });
        return;
      }
      //活动进行
      if (this.acStatus == 1 || this.acStatus == 2) {
        this.$http.post("/mkt/giveCouponByType/1.0/", { couponType: this.volumeType }).then((res) => {
          const { code } = res;
          if (code == '00') {
            this.$router.push('/active/forward/success?inviteId=' + this.inviteId);
          }
        });
        return
      }
      //活动结束
      if (this.acStatus == 3) {
        this.$modal({
          message: "活动已结束~下次赶早哦！",
          icon: "warning",
        });
        return;
      }
    },
    //是否已领取优惠劵
    getVoucherStatus () {
      if (!this.storage.getItem("authToken")) {
        return;
      }
      this.$http
        .post("/mkt/isGiveCouponByType/1.0/", { couponType: this.volumeType })
        .then((res) => {
          if (res.code == "00") {
            if (res.body == true) {
              this.isReceive = true;
            } else {
              this.isReceive = false;
            }
            this.$emit('isReceive', this.isReceive)
          }
        });
    },
    //获取优惠卷总人数
    getRecipientsNum () {
      let api = '/mkt/getCouponGiveAllNumByType/1.0/';
      let params = {
        couponType: this.volumeType,
      }
      this.$http.post(api, params).then((res) => {
        if (res.code == "00") {
          this.receiveCount = parseInt(res.body || 0);
        }
      });
    },
    getCookie (name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return null;
      }
    },
    //请求活动信息
    getActivityInfo () {
      let data = {
        scholarship: this.scholarship
      }
      this.$http.post("/mkt/getActivityInfo/1.0/", data).then(res => {
        const { code, body } = res;
        if (code === '00') {
          let sevenDaysLater = body.EndTime + (7 * 24 * 60 * 60 * 1000); //7天后时间
          this.$emit('setAcInfo', body);
          this.actName = body.actName;
          this.activityData = body;
          // this.startTime = body.StartTime;
          // this.endTime = body.EndTime;
          const now = new Date().getTime();

          //活动前
          if (now < body.StartTime) {
            this.startTime = now;
            this.endTime = body.StartTime;
            this.activityBefore(now, body);
            this.countdown();
          }
          // 活动中
          if ((now >= body.StartTime && body.EndTime >= now)) {
            this.startTime = body.StartTime;
            this.endTime = body.EndTime;
            this.activeProcessing(now, body);
          }
          // 活动结束
          if (now > body.EndTime) {
            this.activityAfter();
          };
          //活动结束7天后
          if (now > sevenDaysLater) {
            this.ac7DaysLater();
          }

          this.$emit('setActiveStatus', this.acStatus);
        }
      })
    },
  },
  beforeDestroy () {
    clearInterval(this.timer);
  },
}
</script>
<style lang="less" scoped>
.apply-home {
  background-color: #fff;
  // margin-bottom: 1rem;
  // 主图
  .main-img {
    width: 100%;
    height: 4.15rem;
    background: url('../../../../assets/image/active/forward/main-bg.png') no-repeat;
    // background-size: cover;
    background-size: 100% 100%;
    position: relative;

    .gcc-swiper {
      position: absolute;
      top: 0.21rem;
      left: 0.1rem;
      width: 1.19rem;
    }
    .ac-rule-btn {
      position: absolute;
      top: 0.23rem;
      right: 0.15rem;
      padding: 0.02rem 0.08rem;
      background: #fff;
      border-radius: 0.12rem;
      color: #d81009;
      text-align: center;
      font-size: 0.12rem;
      background-size: contain;
    }
  }

  //倒计时
  .time-box {
    margin-top: 0.3rem;
    margin-bottom: 0.22rem;
    text-align: center;
  }

  //奖学金
  .scholarship {
    .title {
      font-size: 0.18rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ff443f;
      position: relative;
      text-align: center;
      line-height: 0.25rem;
    }
    .title::after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      right: 0.17rem;
      top: 0.1rem;
      border-top: 0.03rem solid transparent;
      border-left: 0.85rem solid #fd7551;
      border-bottom: 0.03rem solid transparent;
    }
    .title::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      left: 0.17rem;
      top: 0.1rem;
      border-top: 0.03rem solid transparent;
      border-right: 0.85rem solid #fd7551;
      border-bottom: 0.03rem solid transparent;
    }

    .volume {
      width: 3.75rem;
      height: 3.84rem;
      background: url('../../../../assets/image/active/forward/scholarship-bg.png') no-repeat;
      background-size: contain;
      position: relative;

      .gold {
        width: 3.75rem;
        height: 2.02rem;
        background: url('../../../../assets/image/active/forward/gold.png') no-repeat;
        background-size: contain;
        position: absolute;
        z-index: 2;
      }

      .volume-img {
        .scholarship {
          margin-left: 0.35rem;
          margin-top: 0.2rem;
          width: 3rem;
          object-fit: contain;
        }
      }

      .content {
        // padding-top: 1.89rem;
        padding-top: 0.5rem;
        // .text + .text{
        //   margin-top:.12rem;
        // }

        .text {
          padding-left: 0.58rem;
          color: #fff0d4;
          font-weight: bold;
          font-size: 0.14rem;
          line-height: 0.2rem;
          font-family: PingFang-SC-Bold, PingFang-SC;
          margin-top: 0.22rem;

          .iocn {
            width: 0.18rem;
            height: 0.18rem;
            display: inline-block;
            background: url('../../../../assets/image/active/gccScholarship/icon-gift.png') no-repeat;
            background-size: contain;
            vertical-align: top;
            margin-right: 0.06rem;
          }
          span {
            vertical-align: top;
          }
        }

        .note {
          padding-left: 0.81rem;
          font-size: 0.12rem;
          color: #fff0d4;
          font-weight: 200;
          margin-top: 0.05rem;
        }

        .button-box {
          text-align: center;
          margin-top: 0.2rem;
          margin-bottom: 0.08rem;
          .receive-btn {
            width: 2.52rem;
            height: 0.44rem;
            line-height: 0.44rem;
            outline: none;
            // background: url('../../../../assets/image/active/gccScholarship/receiveBtn.png') no-repeat;
            background: url('../../../../assets/image/active/gccScholarship/TyrantGoldBtnBg.png') no-repeat;
            font-size: 0.16rem;
            color: #ff443f;
            font-weight: bold;
            letter-spacing: 0.1em;
            background-size: contain;
            animation: Zoom 1s linear alternate infinite;
          }

          @keyframes Zoom {
            from {
              transform: scale(1.1);
            }
            to {
              transform: scale(1);
            }
          }
        }

        .recipients-num {
          text-align: center;
          span {
            font-size: 0.12rem;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: 300;
            color: #ffffff;
          }
        }
      }
    }
  }

  // 步骤
  .enrollment-steps {
    width: 3.55rem;
    height: 1.12rem;
    margin: 0.2rem 0.1rem;
    margin: 0.2rem 0.1rem 0.46rem 0.1rem;
    img {
      width: 100%;
    }
  }

  //  专业收费
  .price {
    .title {
      height: 0.18rem;
      background: url('../../../../assets/image/active/gccScholarship/professionalFees.png') no-repeat;
      background-position: center;
      background-size: 0.96rem 0.18rem;
      margin-bottom: 0.18rem;
    }
  }

  //学员风采
  .studentStory {
    background: rgb(255, 255, 255);
    width: 3.75rem;
    height: auto;
    overflow: hidden;
    .headBox {
      width: 100%;
      height: 0.6rem;
      position: relative;
      .fourright {
        width: 80%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .swiper {
      background-color: white;
      background-image: none;

      .swiper-container {
        padding-bottom: 0;
      }
    }
    .swiper {
      width: 100%;
      height: 1.6rem;
      overflow: hidden;
    }
    .swiper-slide {
      width: 80%;
      height: 1.6rem;
    }
    .swiper-slide-active img {
      display: block;
      margin: 2% auto;
      width: 92%;
      height: 96%;
    }
    .swiperImg {
      display: block;
      margin: 0 auto;
      margin-top: 3.5%;
      width: 100%;
      height: 85%;
      border-radius: 0.05rem;
      background: #ffffff;
    }
  }

  // 常见问题，留言区
  .service {
    margin-top: 0.35rem;
    /deep/ .van-tab {
      color: #b9b4b4;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      font-size: 0.16rem;
      background: #fefaf0;
    }

    /deep/ .van-tabs__line {
      display: none;
    }
    /deep/ .van-tab--active {
      background-color: #fbc464;
      font-weight: bold;
      color: #170606;
    }
    /deep/ .van-tabs__wrap,
    .van-tabs__wrap scroll-view,
    .van-tabs__nav,
    .van-tab {
      height: 0.5rem !important;
    }
  }

  //承办单位
  .acStatement {
    padding-bottom: 0.2rem;
    p {
      font-size: 0.12rem;
      text-align: center;
    }
  }

  // 邀约
  .Invite {
    text-align: center;
    margin-top: 0.32rem;
    padding-bottom: 0.28rem;
    .InviteBtn {
      width: 1.6rem;
      height: 0.4rem;
    }
  }
}

///////////
.info {
  width: 3.55rem;
  margin: 0 auto;
  margin-top: 0.2rem;
  .headBox {
    height: 0.3rem;
    line-height: 0.3rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    .headTitle {
      font-size: 0.15rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 0.9);
      display: inline-block;
    }
    .headTitleIcon {
      width: 0.24rem;
      height: 0.24rem;
    }
  }
  .table {
    position: relative;
    border-radius: 0.1rem;
    background-color: #fff;
    border-left: 1px solid rgba(239, 186, 6, 1);
    border-right: 1px solid rgba(239, 186, 6, 1);
    border-top: 1px solid rgba(239, 186, 6, 1);
    overflow: hidden;
    margin-top: 0.2rem;
    margin-bottom: 0.27rem;
    .table-t {
      padding: 0.1rem;
      background: rgba(255, 244, 219, 1);
      border-radius: 10px 10px 0px 0px;
      .schoolName {
        height: 0.22rem;
        font-size: 0.16rem;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: rgba(193, 57, 53, 1);
        line-height: 0.22rem;
        span {
          font-size: 0.12rem;
          margin-left: 0.1rem;
        }
      }
      .schoolPf {
        color: rgba(54, 54, 54, 0.8);
        font-size: 13px;
      }
      .fr {
        width: 1.87rem;
        height: 0.17rem;
        font-size: 0.12rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(193, 57, 53, 1);
        line-height: 0.17rem;
      }
      .fl,
      .fr {
        float: none;
      }
    }
    .table-b {
      .ric {
        .red {
          color: #c13935;
          font-size: 0.14rem;
          font-weight: bold;
          margin-bottom: 0.05rem;
        }
      }
      table {
        width: 100%;
        border-top: 1px solid rgba(239, 186, 6, 1);
        th {
          height: 0.2rem;
          font-size: 0.14rem;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: rgba(69, 56, 56, 1);
          line-height: 0.2rem;
        }
        td {
          width: 0.67rem;
          font-size: 0.12rem;
          font-weight: 400;
          vertical-align: middle;
          border-bottom: 1px solid rgba(239, 186, 6, 1);
          border-right: 1px solid rgba(239, 186, 6, 1);
          background-color: #fff;
          padding: 0.06rem 0.06rem;
          font-family: PingFangSC-Regular, PingFang SC;
          color: rgba(69, 56, 56, 1);
          line-height: 0.17rem;
          text-align: center;
          &:last-of-type {
            border-right: none;
          }
          span {
            color: rgba(193, 57, 53, 1);
            font-weight: bold;
            font-size: 0.12rem;
          }
        }
        .text_top {
          // height: 1.08rem;
        }
        .text_center {
          // height: 1.19rem;
        }
        .text_center-luang {
          height: 0.6rem;
        }
        .text-l {
          width: 2.16rem;
          font-size: 0.12rem;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #453838;
          line-height: 0.17rem;
          text-align: center;
          padding: 0.15rem;
        }
        .orage {
          color: #ed5206;
        }
      }
      .title_calss {
        height: 0.51rem;
        border: 1px solid rgba(242, 195, 61, 1);
      }
    }
    &:after {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: #ffd100;
      right: -0.07rem;
      top: -0.1rem;
      z-index: -1;
    }
  }
}
.hasBg {
  background-color: #fff4db !important;
}
.special-instructions {
  margin-top: -0.1rem;
  color: rgba(23, 6, 6, 0.6);
  padding-left: 0.1rem;
  .point {
    color: rgb(240, 110, 108);
  }
}
</style>
