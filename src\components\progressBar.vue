<template>
  <div class="progress-bar">
    <div class="percent" :style="styleObject"></div>
  </div>
</template>

<script>
  export default {
    props: {
      joinCount: {
        type: Number,
        default: 0
      },
      headCount: {
        type: Number,
        default: 10
      }
    },
    computed: {
      percentage: function () {
        return `${parseInt(this.joinCount / this.headCount * 100)}%`
      },
      styleObject: function () {
        return {width: this.percentage}
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .progress-bar{
    height:.04rem; background-color:#d8d8d8; border-radius:.02rem;
    .percent{ width:0; height:100%; background: @bgColor; border-radius:.02rem; transition:width .4s; }
  }
</style>
