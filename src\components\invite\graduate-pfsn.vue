<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.pfsnName===item.pfsnId}" @click="selected(item)">{{item.pfsnName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';

  export default {
    props: ['value', 'datas'],
    data() {
      return {
        options: [],
        type: 'U',
        pageNum: 0,
        pageSize: 20,
        unvsName: '',
        pfsnLevel: '',
        cityCode: '',
        unvsIds: '',
        isLoading: false,
        allLoaded: false,
        activityName: '',
        grade:'',
        unvsId:''
        //recruitType:''
      }
    },
    created() {
        let unvs = this.$route.query.unvs?JSON.parse(this.$route.query.unvs):this.datas.unvs || '';
        this.unvsId = unvs.unvsId || '';
    },
    methods: {
      getUnvsList: function () {
        this.isLoading = true;
        const data = {
          unvsId:this.unvsId,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        this.$http.post('/bds/getResearchPfsn/1.0/', data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || [];
          this.options.push(...datas);
          // console.log(datas);
          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },

      loadMore: function () {
        this.pageNum++;
        this.getUnvsList();
      },
      selected: function (val) {
        this.$emit('input', val);
        this.$emit('onChange','pfsn')

      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
