<template>
  <!--<div class="record" v-if="isWeixin">-->
  <div class="record" >
    <h2>{{tipMsg}}</h2>
    <div class="btn_wrap">
      <button v-if="state=='begin'"  class="begin_btn btn" @click="beginAudio" id="startRecord" ></button>
      <button v-if="state=='doing'" class="btn doing_btn" @click="stopAudio" id="deleteVoice"></button>
      <button v-if="state=='stop'" class="btn stop_btn"  id="uploadVoice"></button>
    </div>
    <p><span>{{time}}</span>/01:00</p>
    <div class="function" v-if="state=='stop'">
      <button class="listen" @click="listen">试听</button>
      <button class="save" @click="upload">保存发表</button>
      <span @click="deleteAudio">重新录制</span>
    </div>
    <div class="tip" :class="{active:state=='stop'}">录制不少于10s</div>
    <modal ref="modal" @cancel="cancel" @submit="submit">{{txt}}</modal>
  </div>
</template>

<script>
  import Record from '../../../common/recorder';
  import {isWeixin,isIOS} from "../../../common";
  import config from "../../../config"
  import modal from "./modal"
  // import Vconsole from "vconsole"
  // import OSS from "ali-oss"
  export default {
    components:{modal},
    data () {
      return {
        isVoice: false,
        isFinished: false,
        tipMsg: '点击录音',
        audio: "",
        recorder: new Record(),
        client:null,
        time:'00:00',
        timer:null,
        wxConfig:{},
        state:'begin',
        isWeixin:isWeixin(),
        i:0,
        voice :{
          localId: '',
          serverId: ''
        },
        store:false,
        stopClearTimeout :null,
        txt:'确定要重新录制吗？',
        auth:false,
        type:''
      }
    },
    created(){
      if(isWeixin()){
        this.getWxScript();
      }
    },
    mounted(){
    },
    methods: {
      back(){
        this.$router.push({name:'321Activity'})
      },
      getWxScript: function (action) {
        if (!isWeixin()) return;
        if (!window.wx) {
          let $script = document.createElement('script');
          $script.type = 'text/javascript';
          $script.charset = 'utf-8';
          $script.src = config.jweixin;
          $script.onload = () => {
            this.wxJsapiSign(action);
          };
          global.document.body.appendChild($script);
        } else {
          this.wxJsapiSign(action);
        }
      },
      wxJsapiSign: function (action) {
        //if (!this.isLogin) return;
        if (this.wxConfig && this.wxConfig.appid) {
          this.wxAuth(this.wxConfig, action);
          return;
        }
        const url = isIOS()?window.entryUrl:window.location.origin + this.$route.fullPath.split('#')[0];
        this.$http.post('/bds/jsapiSign/1.0/', {url: url}).then(res => {
          if (res.code !== '00') return;
          this.wxConfig = res.body;
          if (this.wxConfig && this.wxConfig.appid) {
            this.wxAuth(this.wxConfig, action);
          } else {
            this.$modal({message: '微信签名失败，请重试', icon: 'warning'});
          }
        });
      },
      cancel(){

      },
      submit(){
        if(this.type=='nopublish'){
          this.store=true;
         this.$router.go(-1)
          return;
        }
        //删除语音,重新录音;
        this.voice.localId = '';
        this.beginAudio()
        this.store=false;
        this.i=0;
      },
      beginAudio(){
        var that=this;
        wx.startRecord({
          success: function(){
            that.tipMsg = '正在录制';
            that.state='doing'
            that.timer=setInterval(()=>{
              that.i++;
              if(that.i==60){
                that.time='01:00';
                clearInterval(that.timer)
              }
              that.time='00:'+(that.i>9?that.i:'0'+that.i);
            },1000)
            //60秒后自动停止;
            that.stopClearTimeout = setTimeout(function(){
              wx.stopRecord({
                success: function (res) {
                  that.voice.localId = res.localId;
                  that.tipMsg = '录制完成';
                  that.state = 'stop';
                }
              });
            },60000);
          }
        });
      },
      stopAudio(){
        var that=this;
       wx.stopRecord({
         success: function (res) {
            clearTimeout(that.stopClearTimeout);
            that.tipMsg = '录制完成';
            that.state = 'stop';
           that.voice.localId = res.localId;
            clearInterval(that.timer)
         },
          fail: function (res) {
            console.log(JSON.stringify(res));
          }
       });

      },
      listen(){
        if (this.voice.localId == '') {
          return;
        }
        wx.playVoice({
          localId: this.voice.localId
        });
      },
      wxAuth(data,action){
        wx.config({
          debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
          appId: data.appid, // 必填，公众号的唯一标识
          timestamp: data.timestamp, // 必填，生成签名的时间戳
          nonceStr: data.noncestr, // 必填，生成签名的随机串
          signature: data.signature,// 必填，签名
          jsApiList: [
            "startRecord",
            "stopRecord",
            "onVoiceRecordEnd",
            "playVoice",
            "pauseVoice",
            "stopVoice",
            "onVoicePlayEnd",
            "uploadVoice",
            "downloadVoice",]// 必填，需要使用的JS接口列表
        });
        wx.ready(() => {
          this.init();
        });
      },
      init(){
        //文档加载完后立即执行
          var startRecord = document.querySelector('#startRecord');
          var stopClearTimeout;

        wx.error(function (res) {
          console.log("出现错误请重试:"+res.errMsg);
        });

},
      deleteAudio(){
        this.$refs.modal.open()

      },
      upload(){
        //确认上传语音
          if (this.voice.localId == '') {
            this.$modal({message:'请先录制一段声音',icon:'warning'});
            return;
          }
          if(this.i<10||this.i>60){
            this.$modal({message:'上传的内容要在10s~60s之间哦，请重新录制',icon:'warning'})
            return;
          }
          var that=this
          wx.uploadVoice({
            localId: that.voice.localId, // 本地微信语音文件标识
            isShowProgressTips: 1, // 开启上传提示
            success: function (res) {
              //上传成功后从微信服务器下保存
              that.voice.serverId = res.serverId;
              that.$http.post('/proxy/uploadWeChatAudio/1.0/',{serverId :res.serverId,type:'upDeclaration'}).then(res=>{
                if(res.code=='00'){
                  that.store=true;
                  that.publish(res.body)
                }
              })
            },
            error:function(res){

              console.log("上传失败");
            }
          });
      },
      publish(url){
        let data={resourcesUrl:url,udType:'1',resourcesTime:parseInt(this.i)}
        this.$http.post('/mkt/publishUpDeclaration/1.0/',data).then(res=>{
          if(res.code=='00'){
            this.$router.push({name:'321Activity',query:{publishInfo:JSON.stringify(res.body)}})
          }
        })
      }
    },
    beforeRouteLeave(to, from, next) {
      if((!this.store)&&this.voice.localId){
        this.txt='你的内容还未发布确认退出吗？'
        this.type='nopublish'
        this.$refs.modal.open();
        return;
      }
      next()
    }
  }
</script>
<style scoped lang="less">
.record{
  width: 100%;
  height: auto;
  overflow: hidden;
  min-height: 100vh;
  background-color: rgba(251, 248, 251, 1);
  padding-top: .89rem;
  max-width: 640px;
  margin: 0 auto;
  h2{
    color: rgba(102, 102, 102, 1);
    font-size: .2rem;
    line-height: .28rem;
    text-align: center;
    margin-bottom: .12rem;
  }
  .btn_wrap{
    width: 100%;
    background-color: rgba(251, 248, 251, 1);
    .btn{
      background-color: rgba(251, 248, 251, 1);
      display: block;
      width: 2.08rem;
      height: 2.08rem;
      margin: 0 auto;
      &.begin_btn{
        background-image: url("../../../assets/image/active/321Activity/audio_begin.png");
        background-size: 100% 100%;
      }
      &.doing_btn{
        background-image: url("../../../assets/image/active/321Activity/audio_ing.png");
        background-size: 100% 100%;
      }
      &.stop_btn{
        background-image: url("../../../assets/image/active/321Activity/audio_play.png");
        background-size: 100% 100%;
      }
    }
  }
  p{
    text-align: center;
    margin-top: .12rem;
    font-size: .2rem;
    line-height: .28rem;
    color: rgba(51, 51, 51, 1);
    span{
      color: rgba(248, 137, 49, 1);
    }
  }
  .tip{
    width: 100%;
    text-align: center;
    color: rgba(153, 153, 153, 1);
    font-size: .12rem;
    position: fixed;
    bottom:.55rem;
    left:0;
    &.active{
      position: relative;
      margin-top: .23rem;
      bottom:0
    }
  }
  .function{
    margin-top: .77rem;
    .listen{
      display: block;
      width: 1.7rem;
      height: .39rem;
      margin: 0 auto;
      border-radius: .28rem;
      border:solid 1px rgba(248, 137, 49, 1);
      color: rgba(248, 137, 49, 1);
      background-color: white;
    }
    .save{
      display: block;
      width: 1.7rem;
      height: .39rem;
      margin: 0.2rem auto .31rem;
      border-radius: .28rem;
      border:solid 1px rgba(248, 137, 49, 1);
      color: white;
      background-color: rgba(248, 137, 49, 1);
    }
    span{
      color: rgba(153, 153, 153, 1);
      text-align: center;
      display: block;
      margin: 0 auto;
      width: 1.2rem;
      text-decoration: underline;
    }
  }
}
</style>
