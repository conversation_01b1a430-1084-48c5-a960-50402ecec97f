<template>
  <div class="success_wrap">
    <div class="swiper-container">
      <div class="swiper-wrapper">
      <div class="swiper-slide"  v-for="item,i in list" :key="item.udNum">
       <div class="box">
         <div class="img">
           <img src="../../../assets/image/active/321Activity/top_1.png" alt="">
           <p>我是第{{item.udNum}}位上进者</p>
         </div>
         <div class="mediawrap">
           <div class="head">
             <img :src="item.headImg | defaultAvatar" alt="">
             <p>{{item.nikeName||'' |b64DecodeUnicode}}</p>
           </div>
           <div class="media">
             <div class="audio" v-if="item.udType=='1'" @click="play(item.udType,i,item.udId,item)">{{item.resourcesTime}}
               <img src="../../../assets/image/active/321Activity/''@2x.png" alt="">
               <img src="../../../assets/image/active/321Activity/audio_icon.png" alt="" v-if="item.state!='play'">
               <img src="../../../assets/image/active/321Activity/audio.gif" alt="" v-else>
               <audio :src="item.resourcesUrl|imgBaseURL" ref="media"></audio>
             </div>
             <div class="video" v-if="item.udType=='2'" @click="play(item.udType,i,item.udId,item)">
               <template v-if="item.state!='play'">
                 <div class="bg"></div>
                 <img :src="item.resourcesUrl+'?x-oss-process=video/snapshot,t_1000,f_jpg,ar_auto' | imgBaseURL" alt="">
                 <img src="../../../assets/image/active/321Activity/play_black.png" alt="">
               </template>
               <video :src="item.resourcesUrl|imgBaseURL"  ref="media" loop="true" ></video>
             </div>
           </div>
           <div class="heart">
             <p>{{item.praiseNum||0}}人祝福了我</p>
             <div v-if="!item.isPraise" @click="heart(item.udId,item.isPraise,i)">
               <img src="../../../assets/image/active/321Activity/heart_yellow_empty.png" alt="" >
               <p>祝福ta</p>
             </div>
             <div v-else @click="heart(item.udId,item.isPraise,i)">
               <img src="../../../assets/image/active/321Activity/heart_yellow.png" alt="" >
               <p class="small">您已祝福</p>
             </div>
           </div>
         </div>
       </div>
      </div>
    </div>
    <div class="swiper-button-next"></div>
    <div class="swiper-button-prev"></div>
    </div>
    <img class="beauty" src="../../../assets/image/active/321Activity/beaty.png" alt="">
    <share title="我在参加321上进日活动，快来围观我的上进宣言，为上进发声！" :imgUr="imgUrl" desc="3月21日，发布上进宣言，干一件上进的事，影响更多的人一起上进。" :link="shareLink" />
  </div>
</template>

<script>
  import config from "../../../config"
  import Swiper from "swiper";
  import share from "@/components/share"
  import {Toast} from 'vant'
  export default {
    name: "success",
    components:{share},
    props:["type","video","audio"],
    data(){
      return{
        list:[],
        udId:0,
        endTime:new Date(2020,2,28,0,0,0).getTime(),
        swiperOption: {
          initialSlide: 1,
          autoplay: false,
          loop:false,
          autoplayDisableOnInteraction: false,
          navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev'
          }
        },
        index:0,
        pageNum:0,
        imgUrl:'http://yzims.oss-cn-shenzhen.aliyuncs.com/logo/321act/321logo.png',
        shareLink:'',
        flag:true
      }
    },
    created(){
      this.list=this.storage.getItem('mediaList')||[];
      this.pageNum=this.$route.query.pageNum
      this.shareLink=`${window.location.origin}/active/321Activity`;
      if(!this.list){
        this.pageNum=0
        this.getList('add',true)
      }
    },
    mounted(){

      this.index=this.$route.query.index||0;
      var that=this;
      var swiper = new Swiper('.swiper-container', {
        initialSlide:this.index,
        loop:false,
        autoplay: false,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        },
        observer:true,
        observeSlideChildren:true,
        on: {
          slideChange: function(){
            if(swiper.realIndex>=that.list.length-2){
              that.getList('add')
            }else if(swiper.realIndex<=1&&that.pageNum>1){
              that.getList('inc')
            }
            that.pauseAll(swiper.realIndex)
          },
        }
      });
    },
    methods:{
      pauseAll(i){
        for (let j=0;j<this.$refs.media.length;j++){
          if(!this.$refs.media[j].paused){
            this.$refs.media[j].pause()
            var item=this.list[j];
            item.state='stop'
            this.$set(this.list,j,item)
          }
        }
        var item=this.list[i]
        if(this.list[i].udType==2){
          this.$refs.media[i].play()
          this.$refs.media[i].currentTime=0
          item.state='play'
        }
        this.$set(this.list,i,item)
      },
      addPlay(udId){
        this.$http.post('/mkt/playUpDeclaration/1.0/',{udId:udId}).then(res=>{
          if(res.code=='00'){

          }
        })
      },
      getInfo(){

      },
      getList(type,begin=false){
        type=='add'?this.pageNum++:this.pageNum--;
        this.$http.post('/mkt/getUpDeclarationList/1.0/',{pageSize:10,pageNum:this.pageNum}).then(res=>{
         if(res.code=='00'){
           type=='add'?this.list.push(...res.body):this.list.unshift(...res.body)
           if(begin){
             var that=this;
             var swiper = new Swiper('.swiper-container', {
               initialSlide:that.index,
               loop:false,
               autoplay: false,
               navigation: {
                 nextEl: '.swiper-button-next',
                 prevEl: '.swiper-button-prev',
               },
               observer:true,
               observeSlideChildren:true,
               on: {
                 slideChange: function(){
                   if(swiper.realIndex>=that.list.length-2){
                     that.getList('add')
                   }else if(swiper.realIndex<=1&&that.pageNum>1){
                     that.getList('inc')
                   }
                   that.pauseAll(swiper.realIndex)
                 },
               }
             });
           }
         }
        })
      },
      upload(){
        this.$emit('upload')
      },
      modal(){
        this.$modal({message:'今年321上进日活动已经结束了哦~欢迎您明年赶早来参与上进啦！',icon:'warning'})
      },
      heart(praiseId,isPraise,i){
        if(this.endTime<Date.now()){
          this.modal();
          return;
        }
        if(this.flag) {
          this.flag = false;
          let fabulousNum=isPraise?-1:1;
          this.$http.post('/mkt/praiseUpDeclaration/1.0/',{praiseType:4,praiseId:praiseId,fabulousNum:fabulousNum}).then(res=>{
            if(res.code=='00'){
              this.list[i].isPraise=!isPraise;
              isPraise?this.list[i].praiseNum--:this.list[i].praiseNum++;
              this.flag = true;
            }
          })
        }else {
          Toast('手速慢一点!')
        }

      },
      isPlay(){
        if(this.$refs.zom.children[0]){
          //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
          if(this.$refs.zom.children[0].paused){
            this.music=false;
          }
        }
      },
      stop(){
        if(this.$refs.zom.children[0]!==null){
          //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
          if(!this.$refs.zom.children[0].paused){
            this.$refs.zom.children[0].pause();// 这个就是暂停
          }
        }
      },
      play(type,i,udId,item){
        //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
        console.log(this.$refs.media);
        this.$refs.media[i].paused?this.$refs.media[i].play():this.$refs.media[i].pause()
        if(!this.$refs.media[i].paused){
          this.addPlay(udId)
          item.state='play'
          this.$set(this.list,i,item)
          this.$refs.media[i].addEventListener('pause',()=>{
            item.state='stop'
            this.$set(this.list,i,item)
          })
        }
      },
      publish(){
        this.$emit('publish')
        // this.$http.post('/mkt/publishUpDeclaration/1.0/',{resourcesUrl:this.url,udType:this.type=='audio'?1:2}).then(res=>{
        //   if(res.code=='00'){
        //
        //   }
        // })
      }
    }
  }
</script>

<style scoped lang="less">
  .success_wrap{
    width: 100%;
    /*height: auto;*/
    /*overflow: hidden;*/
    height: 100vh;
    background-color: rgba(181, 19, 14, 1);
    background-image: url("../../../assets/image/active/321Activity/rule_bg.png");
    background-size: 100% 100%;
    z-index: 1000;
    position: fixed;
    left:0;
    top:0;
    .box{
      width: 3.45rem;
      /*height: 6.12rem;*/
      height:95%;
      background-color: white;
      border-radius: .18rem;
      box-shadow: 0 0 .2rem 0 rgba(248, 137, 49, 1) inset;
      margin: 0 .15rem 0;
      clear: both;
      position: relative;
      z-index: 2;
      .mediawrap{
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        height: 100%;
        position: absolute;
        bottom:0;
        margin: auto;
        padding-top: .3rem;
        padding-bottom: .1rem;
      }
      .head{
        width: 100%;
        text-align: center;
        clear: both;
        img{
          width: .62rem;
          height: .62rem;
          border:  solid 2px rgba(253, 188, 0, 1);
          border-radius: 50%;
        }
        p{
          font-size: .12rem;
          color:rgba(102, 102, 102, 1);
          margin-top: .06rem;
          height: .17rem;
          line-height: .17rem;
        }
      }
      .img{
        width: 2.81rem;
        height:.5rem;
        margin: -.2rem .32rem .15rem;
        position: relative;
        float: left;
        img{
          width: 100%;
        }
        p{
          position: absolute;
          width: 100%;
          line-height: .5rem;
          text-align: center;
          top:0;
          font-size: .2rem;
          color: #333;
          font-weight: bold;
          font-family: initial;
        }
      }
      .media{
        width: 100%;
        padding: 0 .56rem;
        height: 3.34rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        /*margin-top: .12rem;*/
        .audio{
          width: 2.24rem;
          height: .32rem;
          border-radius: .04rem;
          background-color: rgba(132, 201, 102, 1);
          font-size: .16rem;
          line-height: .32rem;
          padding-left: .13rem;
          margin: .18rem auto .23rem;
          img:first-of-type{
            width: .06rem;
            height: .06rem;
            margin-top: .04rem;
          }
          img:last-of-type{
            width: .11rem;
            height: .14rem;
            margin-top: .1rem;
            margin-left: .05rem;
          }
        }
        .video{
          float: left;
          width: 2.32rem;
          height: 3.34rem;
          position: relative;
          /*margin: 0 auto .16rem;*/
          border: solid .03rem rgba(248, 137, 49, 1);
          border-radius: .1rem;
          overflow: hidden;
          .bg{
            width: 100%;
            height: 100%;
            position: absolute;
            left:0;
            top:0;
            background-color: rgba(0, 0, 0, .1);
            z-index: 2;
          }
          img:first-of-type{
            width: 100%;
            height: 100%;
            position: absolute;
          }
          video{
            width: 100%;
            height: 100%;
          }
          img:last-of-type{
            position: absolute;
            width: .32rem;
            height: .32rem;
            left: 0;
            right: 0;
            top:0;
            bottom:0;
            margin: auto;
          }
        }
        p{
          text-align: center;
          color: rgba(153, 153, 153, 1);
          font-size: .12rem;
        }
      }
      .heart{
        /*position: absolute;*/
        width: 100%;
        /*bottom:.22rem;*/
        text-align: center;
        height: .9rem;
        p{
          color: rgba(253, 188, 0, 1);
          &.small{
            font-size: .1rem;
          }
        }
        img{
          width: .38rem;
          height: .34rem;
          margin: .1rem auto;
          display: block;
        }
      }
    }
    .beauty{
      display: block;
      width: 3.3rem;
      height: .83rem;
      margin: -.3rem auto 0;
    }
  }
  .swiper-button-next{
    right: .25rem;
    top:50%;
    margin-top: -.22rem;
    width: .25rem;
    height: .44rem;
    background-image: url("../../../assets/image/active/321Activity/arrow_right.png");
    background-size: 100% 100%;
  }
  .swiper-button-prev{
    left:.25rem;
    width: .25rem;
    height: .44rem;
    top:50%;
    margin-top: -.22rem;
    background-image: url("../../../assets/image/active/321Activity/arrow_left.png");
    background-size: 100% 100%;
  }
  .swiper-slide{
    padding-top: .34rem;
  }
  .swiper-container{
    width: 100%;
    height: 100%;
  }
</style>
