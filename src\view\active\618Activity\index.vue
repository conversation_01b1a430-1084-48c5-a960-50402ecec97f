<template>
  <div class="yz-618-activity">
    <inviteTop text='邀请您参加618活动' @getInviteId='getInviteId' />
    <!-- 分享组件 -->
    <share
      :title="shareTitle"
      :desc="shareDec"
      :link="shareLink"
      :isActivity="true"
      :scholarship="scholarship"
      regOrigin='40'
      :imgUrl="imgUrl"
      ref="share"
    />
    <!-- 大奖轮播 -->
    <y-swiper v-if='swiperList.length > 0' class='y-swiper' :class='{invite: hasInviteId}' :list='swiperList' />
    <!-- 走马灯 -->
    <swiper-murquee class='swiper-murquee1' :class='{invite: hasInviteId}' :list='murquee1List' />
    <swiper-murquee class='swiper-murquee2' :class='{invite: hasInviteId}' :time='20' :list='murquee2List' />
    <!-- 活动时间 -->
    <div class="act-header">
      <template v-if='activityInfo.status == 2'>
        <p class="time-txt"><b>活动时间</b></p>
        <p class="time">
          {{ activityInfo.startTime | formatTimeByMoment("MM月DD日 HH:mm") }}
          -
          {{ activityInfo.endTime | formatTimeByMoment("MM月DD日 HH:mm") }}
        </p>
      </template>

      <time-clock v-else :endTime='activityInfo.endTime'  />
      <!-- 轮盘 -->
      <turntable :info='activityInfo' :lotteryCount='lotteryCount' @noChanges='noChangesHandle' @success='turnTableFinish' />

      <button class="act-rule" @click="showRuleDialog">活动规则</button>
      <div class="yellow-box" :class="{ nologin: !isLogin }">
        <span v-if="!isLogin">抽奖需要先登录帐号</span>
        <span v-else class="t1"
          >你还剩 <span class="red">{{ lotteryCount || 0 }}</span> 次机会</span
        >
        <button @click="getChanges">
          {{ !isLogin ? "立即前往" : "获取机会" }}
        </button>
      </div>
    </div>
    <!-- 中间名单 -->
    <div class="winner-list">
      <div class="title">
        <img src="../../../assets/image/active/618Activity/list.png" class="img" alt="">
      </div>
      <div class="no-data" v-if="winnerList.length == 0">
        竟然还没有人获奖，赶紧抽一发～
      </div>
      <template v-else>
        <ul class="list">
          <template v-for="(item, index) in winnerList">
            <li :key="index" v-if="index <= 3">
              <div class="left">
                恭喜 {{ item.userName | hideNickname }} 抽中了 {{ item.prizeVO.prizeName }}
              </div>
              <div class="right">
                {{ item.lotteryTime | formatTimeByMoment("MM.DD HH:mm") }}
              </div>
            </li>
          </template>
        </ul>
        <div class="btns">
          <red-btn big width="1.5rem" @click="showAllList = true">全部名单</red-btn>
          <red-btn big width="1.5rem" @click="showMyPrize">我的奖品</red-btn>
        </div>
      </template>
    </div>
    <!-- 大奖列表 -->
    <div class="pic-box cl">
      <img src="../../../assets/image/active/618Activity/p1.png" alt="" />
    </div>

    <!-- 往期中奖名单 -->
    <div class="pic-box cl before">
      <img src="../../../assets/image/active/618Activity/p2.png" alt="" />
    </div>

    <!-- 回到顶部 -->
    <div class="back-top" @click="backTop">
      <div class="arrow"></div>
      <p>回到顶部</p>
    </div>

    <!-- 更多活动 -->
    <div class="more-activity" v-if='!isAppOpen'>
      <div class="title-box">
        <img src="../../../assets/image/active/618Activity/p3.png" alt="" />
      </div>
      <ul class="list cl">
        <router-link
          :to="{
            name: 'newDreamBuild',
            query: { ...$route.query, tab: 'selfTought' },
          }"
          tag="li"
          class="line"
        >
          <p class="t1">自考学历</p>
          <p>精英计划</p>
          <img src="../../../assets/image/active/618Activity/1.png" alt="" />
        </router-link>
        <router-link
          :to="{ name: 'newOneYearSchool', query: $route.query }"
          tag="li"
        >
          <p class="t1">一年学费读大学</p>
          <p>专科</p>
          <img src="../../../assets/image/active/618Activity/2.png" alt="" />
        </router-link>
        <router-link
          :to="{ name: 'gccScholarship.home', query: $route.query }"
          tag="li"
          class="line"
        >
          <p class="t1">3000元奖学金</p>
          <p>本科</p>
          <img src="../../../assets/image/active/618Activity/3.png" alt="" />
        </router-link>
        <router-link
          :to="{
            name: 'newDreamBuild',
            query: { ...$route.query, tab: 'graduate' },
          }"
          tag="li"
        >
          <p class="t1">1万元上进奖学金</p>
          <p>在职研究生</p>
          <img src="../../../assets/image/active/618Activity/4.png" alt="" />
        </router-link>
        <router-link
          :to="{ name: 'newDreamBuild', query: $route.query }"
          tag="li"
        >
          <p class="t1">在线报读大学</p>
          <p>专本学历</p>
          <img src="../../../assets/image/active/618Activity/5.png" alt="" />
        </router-link>
      </ul>
    </div>
    <!-- 分享遮罩 -->
    <div
      v-show="showShareBg"
      class="share-black animated fadeIn"
      @click="cancelShareBg"
    >
      <img
        src="../../../assets/image/active/618Activity/share-arrow.png"
        class="fr"
        alt=""
      />
      <div class="text fr">
        请点击右上角<br />将它发送给指定朋友<br />或者分享到朋友圈
      </div>
    </div>

    <!-- 弹窗 -->
    <!-- 中奖弹窗 -->
    <win-prize v-model="showWinPrize" :prizeInfo='prizeInfo' @openAddress='openAddress' />
    <!-- 活动规则 -->
    <rule-dialog v-model="isShowRule" :info="activityInfo" />
    <!-- 全部名单 -->
    <all-winner v-model="showAllList" :list="winnerList" :loading='isLoadMore' @loadMore="loadMore" />
    <!-- 我的奖品 -->
    <my-prize
      v-model="isShowMyPrize"
      :endTime="activityInfo.endTime"
      :list="myPrizeList"
      @openAddress='openAddress'
    />
    <!-- 获取抽奖机会 -->
    <get-changes v-model="showGetChanges" :title='changesTitle' :isAppOpen='isAppOpen' @invite="inviteDearmBuild" />
    <!-- 填写地址 -->
    <address-dialog v-model="showAddressDialog" :lotteryId='addressLotteryId' @update='updateMyPrize' />
  </div>
</template>

<script>
import { Toast } from 'vant';
import { isLogin, toLogin } from "@/common";
import share from "@/components/share";
import appShare from "@/mixins/appShare";
import inviteTop from "../enrollAggregate/components/invite-top";
import NoData from "./components/no-data";
import RedBtn from "./components/red-btn";
import RuleDialog from "./components/rule-dialog";
import AllWinner from "./components/all-winner";
import TimeClock from "./components/time-clock";
import MyPrize from "./components/my-prize";
import GetChanges from "./components/get-changes";
import AddressDialog from "./components/address-dialog";
import SwiperMurquee from "./components/swiper-murquee";
import Turntable from "./components/turntable";
import WinPrize from "./components/win-prize";
import YSwiper from "./components/swiper";

export default {
  components: {
    share,
    inviteTop,
    NoData,
    RuleDialog,
    RedBtn,
    AllWinner,
    MyPrize,
    GetChanges,
    AddressDialog,
    Turntable,
    WinPrize,
    YSwiper,
    SwiperMurquee,
    TimeClock,
  },
  mixins: [appShare],
  data() {
    return {
      shareLink: window.location.origin + "/active/618Activity",
      isShowRule: false,
      showAllList: false,
      isShowMyPrize: false,
      showGetChanges: false,
      showShareBg: false,
      showAddressDialog: false,
      showWinPrize: false,
      isLogin: isLogin(),
      winnerList: [],
      myPrizeList: [],
      prizeList: [],
      lotteryCount: 0, // 抽奖次数
      pageNum: 1,
      isAll: false, // 全部中奖名单
      isLoadMore: false,
      activityInfo: {},
      swiperList: [],
      prizeInfo: {},
      addressLotteryId: '',
      shareTitle: '远智618，电脑电动车带回家！',
      shareDec: '更多华为笔记本、华为手表丰富奖品等你来抽！',
      scholarship: '',
      imgUrl: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/321/share618.png',
      changesTitle: '',
      murquee1List: [], // 走马灯1
      murquee2List: [], // 走马灯2
      hasInviteId: false,

    };
  },
  mounted() {
    setTimeout(() => {
      MtaH5.clickStat("618activity");
    }, 3000);
    this.initAppShare(() => {
      this.initShare();
    });
    this.lotteryInfoList();
    this.lotteryInfoList("carousel");
    this.getPrizeLotteryInfo();
    this.getPrizeList();
    this.getMruqueeList(1);
    this.getMruqueeList(2);
    if (this.isLogin) {
      this.lotteryInfoList("single");
      this.getLotteryCount();
    }
  },
  methods: {
    getInviteId(id) {
      this.hasInviteId = true;
    },
    initShare() {
      const params = {
        title: '远智618，电脑电动车带回家！',
        content: '更多华为笔记本、华为手表丰富奖品等你来抽！',
        url: '/active/618Activity',
        image: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/321/share618.png',
        scholarship: '',
        regOrigin: 40,
      };
      this.shareTitle = params.title;
      this.shareDec = params.content
      this.shareLink = window.location.origin + params.url;
      this.imgUrl = params.image;
      this.scholarship = params.scholarship;
      if (this.isAppOpen) {
        this.setShareParams(params);
      }
    },
    noChangesHandle() {
      this.changesTitle = '你的抽奖机会不足';
      this.showGetChanges = true;
    },
    // 显示活动弹窗
    showRuleDialog() {
      this.isShowRule = true;
    },
    getChanges() {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      if (this.activityInfo.status == 0) {
        Toast('活动尚未开始');
        return;
      }
      if (this.activityInfo.status == 2) {
        Toast('很抱歉，该活动已结束～');
        return;
      }
      this.changesTitle = '获得抽奖机会';
      this.showGetChanges = true;
    },
    backTop() {
      document.body.scrollTop = document.documentElement.scrollTop = 0;
    },
    loadMore() {
      this.lotteryInfoList("all", true);
    },
    showMyPrize() {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      this.isShowMyPrize = true;
    },
    turnTableFinish(body) {
      this.showWinPrize = true;
      this.prizeInfo = body;
      this.getLotteryCount();
      this.pageNum = 1;
      this.lotteryInfoList("all");
      this.lotteryInfoList("single");
      this.lotteryInfoList("carousel");
    },

    openAddress(id) {
      this.showAddressDialog = true;
      this.addressLotteryId = id;
    },
    // 邀约报读
    inviteDearmBuild() {
      const params = {
        title: '在线报读，名校录取，学信网可查，助你拿大专本科学历！',
        content: '筑梦计划是远智教育携手各大高校联合推出，旨在帮助更多产业工人和上进青年实现大学梦的奖励计划。',
        url: '/active/newDreamBuildUpdate',
        image: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png',
        scholarship: '164',
        regOrigin: 40,
      };
      this.showShareBg = true;
      if (!this.isAppOpen) {
        this.shareTitle = params.title;
        this.shareDec = params.content;
        this.shareLink = window.location.origin + params.url;
        this.imgUrl = params.image;
        this.scholarship = params.scholarship;
        return;
      }
      this.setShareParams(params);
    },

    cancelShareBg() {
      this.initShare();
      this.showShareBg = false;
    },

    updateMyPrize() {
      this.lotteryInfoList("single");
    },

    // 获取活动信息
    async getPrizeLotteryInfo() {
      const { code, body } = await this.$http.post("/mkt/getPrizeLotteryInfo/1.0/");
      if (code == "00") {
        this.activityInfo = body;
      }
    },
    // 抽奖次数
    async getLotteryCount() {
      const { code, body } = await this.$http.post("/mkt/userLotteryCount/1.0/");
      if (code == "00") {
        this.lotteryCount = body;
      }
    },
    // 奖品信息
    async getPrizeList() {
      const { code, body } = await this.$http.post("/mkt/getPrizeInfoList/1.0/");
      if (code == "00") {
        this.prizeList = body;
      }
    },
    // 中奖信息接口
    async lotteryInfoList(type = "all", isLoading = false) {
      // all->查所有 single->查个人 carousel:轮播
      if (this.isLoadMore) {
        return;
      }
      if (type == "all" && this.isAll) {
        return;
      }
      this.isLoadMore = isLoading;
      let pageSize = 10;
      if (type == 'single') {
        pageSize = 50;
      } else if (type == 'carousel') {
        pageSize = 20;
      }

      const pageNum = type == 'all' ? this.pageNum : 1;
      const { code, body } = await this.$http.post("/mkt/lotteryInfoList/1.0/",{
        type,
        pageNum,
        pageSize,
      });
      if (code == "00") {
        this.isAll = body.length == 0 && type == 'all';
        if (body.length > 0) {
          if (type == "single") {
            this.myPrizeList = body;
          } else if (type == "carousel") {
            this.swiperList = body;
          } else {
            this.pageNum += 1;
            this.winnerList = this.pageNum == 1 ? body : this.winnerList.concat(body);
          }
        }
      }
      setTimeout(() => {
        this.isLoadMore = false;
      }, 200);
    },
    // 获取走马灯数据
    async getMruqueeList(pageNum) {
      const { code, body } = await this.$http.post("/mkt/lotteryInfoList/1.0/", {
        type: 'all',
        pageNum,
        pageSize: 20,
      });
      if (code == "00") {
        if (pageNum == 1) {
          this.murquee1List = body;
        } else {
          this.murquee2List = body;
        }
      }
    },
  },
};
</script>

<style lang="less">
@import "./index.less";
</style>
