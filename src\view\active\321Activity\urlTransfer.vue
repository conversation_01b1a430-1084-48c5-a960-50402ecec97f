<template>
    <div class="bgBox">
              
    </div>

</template>


<script>

export default {
    data(){
        return {
            id:''
        }
    },
    created() {
        this.id = this.$route.query.u;
        this.getUrl()
    },
    methods:{  
        getUrl() {
            this.$http.post('/proxy/getShortUrl/1.0/',{key:this.id}).then((res=>{
                const {code, body} = res;
                if(code !=='00') return;
                console.log(res)
                window.location.href = body;
            }))
        }

    },

    
}
</script>

<style lang="less" scoped>


</style>

