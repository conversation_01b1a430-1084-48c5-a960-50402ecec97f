<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.pfsnLevel===item.pfsnLevel}" @click="selected(item)">{{item.pfsnLevelName}}</div>
    <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="options.length"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data(){
      return {
        options: [],
        type:'L',
        grade:'',
        unvsId:'',
        pageNum: 0,
        pageSize: 20,
        scholarship: '',
        isLoading:false,
        allLoaded:false,
        recruitType:''
      }
    },
    created(){
      this.activityName = this.datas.activityName;

      this.grade = this.datas.grade.dictValue;
      this.unvsId = this.datas.unvs.unvsId;
      this.recruitType=this.datas.recruitType.dictValue;
    },
    methods: {
      selected: function (val) {
        this.$emit('input', val);
      },
      getPfsnLevel(){
        this.isLoading = true;
        if(this.datas.scholarship=='1') {
          this.unvsId='';
        }
        let data = {
          type: this.type,
          pfsnName: '',
          grade: this.grade,
          pfsnLevel: this.pfsnLevel,
          unvsId: this.unvsId || '',
          scholarship:typeof(this.datas.scholarship)==='string'?[this.datas.scholarship]:this.datas.scholarship,
          recruitType:this.recruitType,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        let url = '/mkt/enrollInfo/1.0/';
        let headers = {};
        if (this.isDingding) {
          url = `${domain}/newStudentChange/getEnrollInfo.do`;
          data = qs.stringify(data);
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
          };
        }
        this.$http.post(url, data, {
          headers: headers,
        }).then(res => {
          if (res.code !== '00') return;
          const datas = (res.body || []);
          this.options.push(...datas);

          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        this.pageNum++;
        this.getPfsnLevel();
      },
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
