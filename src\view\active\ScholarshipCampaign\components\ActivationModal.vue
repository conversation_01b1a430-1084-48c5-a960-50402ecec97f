<template>
  <!-- 领取优惠卷弹框 -->
  <van-popup round v-model="visible" :close-on-click-overlay="false">
    <div class="activation-box">
      <h3>恭喜您！获得激活奖学金的资格</h3>
      <div class="img-box">
        <div class="flag">未激活</div>
        <img
          :src="`https://static.yzou.cn/zmc/${cdnPath}/coupons.png`"
          alt
        />
      </div>
      <p>
        注意：本奖学金券需要完成2个挑战项来激活，否则挑战失败将自动作废奖学金。
      </p>
      <button @click="handleGetCoupon">点击立即参与</button>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'ActivationModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    cdnPath: {
      type: String,
      default: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleGetCoupon() {
      this.$emit('get-coupon')
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.activation-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  height: 100;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10px;
  padding: 20 * @rem 20 * @rem 18 * @rem 20 * @rem;

  h3 {
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #a44f0c;
    line-height: 22 * @rem;
  }
  
  .img-box {
    position: relative;
    margin-top: 20 * @rem;
    width: 270 * @rem;
    height: 83 * @rem;
    
    .flag {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 0.47rem;
      height: 0.18rem;
      background: #979797;
      border-radius: 0.04rem 0 0.09rem 0;
      font-size: 0.12rem;
      font-weight: 500;
      color: #fff;
      position: absolute;
      top: 0;
      left: 0;
    }

    img {
      width: 100%;
      height: 100%;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  p {
    margin-top: 18 * @rem;
    font-size: 13 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 18 * @rem;
  }

  button {
    margin-top: 28 * @rem;
    width: 276 * @rem;
    height: 40 * @rem;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    border-radius: 22 * @rem;
    border: none;
    font-size: 16 * @rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
  }
}
</style>
