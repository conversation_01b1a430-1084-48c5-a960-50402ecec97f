
import router from '@/router'

/*
* 上进大学去支付
* goods: 商品id
* params 需要额外附带跳转的参数
* */
export const ToPay = (goodsId, params = {}) => {
  let route = router.history.current; // 当前路由信息
  router.push({
    path: '/pay/postagePayment',
    query: {
      aspirantId: goodsId, // 商品id
      inviteId: route.query.inviteId || undefined, // 邀约人Token
      regOrigin: route.query.regOrigin || undefined, // 邀约类型
      regChannel: route.query.regChannel || undefined, // 邀约渠道
      did: route.query.did || undefined, // 分销人Token
      ...params
    }
  })
}
