@import "variable";
.banner{
  position:relative; height:2.17rem; background-color:#fff ;
  .banner-inner{ display:block; height:100%; }
  .swipe-wrap{ z-index:1 }
  .ps{ position:absolute; right:0; bottom:0; left:0; z-index:2; line-height:.3rem; text-align:center; color:#fff; font-size:.12rem;background: rgba(23, 6, 6, 0.4) }
  // .img{ object-fit:contain; }
}

.back-arrow{
  //  transform: rotate(180deg);
  width: 0.32rem;
  height: 0.32rem;
  position: absolute;
  top: 0.08rem;
  left: 0.12rem;
  z-index: 10;
  border-radius: 50%;
}
.title-box{
  word-break: break-all;
  // font-size: 0.15rem;
}
.price-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.2rem;
  .price{
    display: flex;
    align-items: center;
  }
  .img{
    width: 0.15rem;
  }
  .money{
    font-size: 0.18rem;
    font-weight:600;
    color: #F85731;
  }
  .grey{
    font-size: 0.14rem;
    color: #A29B9B;
    .s{
      font-size: 0.12rem;
    }
  }
  .limit-express{
    display: flex;
    align-items: center;
  }
  .limit{
    color: #F06E6C;
    font-size: 0.12rem;
    margin-right: 0.05rem;
  }
  .express{
    background: rgba(240, 110, 108, 0.1);
    border-radius: 0.05rem;
    padding: 0 0.03rem;
    line-height: 0.2rem;
    color: #F06E6C;
  }

}

.count-down-box{
  padding: 0.1rem 0;
  background: #fff;
}

.product-detail{
  .h1{
    line-height:.21rem;
    font-size: 0.15rem;
    font-weight:600;
    text-indent: 0.65rem;
    color: #170606;
    &.short{
      text-indent: 0.55rem;
    }
  }
  .tit{
    position:relative;
    padding:.12rem;
    .inner{ position:relative; }
  }
  .tit.bb::after{
    height:1px;
    background:rgba(23,6,6,0.1);
    position: absolute;
    content: '';
    left: 0.1rem;
    right: 0.1rem;
    bottom: 0;
  }
  .subtitle{ word-break:break-all; color:rgba(23,6,6,0.6); font-size:.12rem;margin-top: 0.07rem;line-height: 1; }
  .info{ padding:.12rem; padding-bottom: 0;font-size:.14rem; color:#666; }
  .infoTwo {padding:0.15rem .12rem .1rem; font-size:.14rem; color:#666;}
  .infoTwo.smallPt{ padding-top: 0.08rem }
  .progress-bar{ margin-bottom:.04rem; }

  .address{
    position:relative; padding:0 .12rem; line-height:.44rem; font-size:.13rem; color:#666;
    &:after{ .borderTop }
  }

  .i-arr-r{ width:.07rem; height:.13rem; margin:.13rem 0 0 .1rem; background-image:url(../image/arrow-r.png); }
  .tab-nav{
    .item{
      height: 0.5rem;
      line-height: 0.5rem;
      font-size: 0.15rem;
    }
  }
}
.list-box{
  padding-bottom: 0.1rem;
}
.record-list{
  .item{
    position:relative; margin:0 .12rem; padding:.12rem 0;
    &:after{ .borderBottom }
    &:last-child:after{ border-bottom:none; height: 0 }
  }
  .head-img{
    height: 0.4rem;
    width: 0.4rem;
    border-radius: 50%;
    object-fit: cover;
    float: left;
  }
  .content{
    margin-left: 0.4rem;
    padding-left: 0.05rem;
    display: flex;
    justify-content: space-between;
  }
  // .name{ font-size:.14rem; }
  .time{ text-align: right; }
  .fs12{font-size: 0.12rem;}
  .fs14{font-size: 0.14rem;}
}
.detail-ft{
  .lf{ line-height:.5rem; }
  .amount-box{ margin:.11rem .12rem 0 0; font-size:0; }
  .btn-minus,
  .btn-plus{ width:.27rem; height:.27rem; vertical-align:middle; border:none; background-color:transparent; background-size:100% 100%; }
  .btn-minus{ background-image:url(../image/btn-minus.png); }
  .btn-plus{ background-image:url(../image/btn-plus.png); }
  .input1{ width:.48rem; height:.27rem; vertical-align:middle; text-align:center; border:none; color:@color; font-size:.16rem; }
  .btn1{
    height: 0.4rem;
    width: 100%;
    margin-top: 0.05rem;
    border-radius: 100px;
    font-size: 0.15rem;
    font-weight:600;
    background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
    &:disabled{
      opacity: 1;
      background: #B9B4B4;
    }
  }
}

.num-box{
  margin-bottom: 0.08rem;
  margin-top: 0.03rem;
  line-height: 1;
}

.link{ color:#5eb1d3; font-size:.12rem; }

.fs1{ font-size:.1rem; }
.fc1{ color:rgba(23,6,6,0.4); }
.fc4{ color:#170606; }
.fc2{ color:@color2; }
.fc3{ color:#2A2A2A; }
.fsc1{ color:#888; font-size:.11rem; }
.fsc2{ color:rgba(23,6,6,0.6); font-size:.12rem; }
.fsc3{ color:@color2; font-size:.18rem; font-weight:bold; }
.fsc4{ color: #746A6A; }
.fsc5{ color: #A29B9B; }
