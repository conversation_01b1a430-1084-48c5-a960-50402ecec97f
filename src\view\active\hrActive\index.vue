<template>
  <div class='yz-hr-active' :class='{iphonex: isIphoneX}'>
    <transition name="fade">
      <inviteTop @getInviteId='getInviteId' />
    </transition>

    <share
      :title="shareTitle"
      :desc="shareDesc"
      :link="link"
      :isActivity="true"
      :scholarship="scholarship"
      ref="share"
    />

    <div class='head-img-box'>
      <swiperEnroll v-if='isStart' left='0.2' top='0.2' textColor='#ffffff' background='rgba(255, 255, 255, 0.1)' />
      <div class="right-text" @click='showRule = true;'>
        <span>招生规则</span>
        <img src="../../../assets/image/active/hrActive/arrow.png" class="arrow" alt="">
      </div>
      <button class="red-btn head-btn" @click='toEnroll'>¥299立即报名</button>
    </div>

    <div class="big-box">
      <p class="big-title"># 在成长计划中能获得什么？</p>
      <ul class="clearfix five-ul">
        <li>
          <div class="title">
            <img src="../../../assets/image/active/hrActive/t1.png" class="img" alt="">
            <span>提升学历</span>
          </div>
          <p class="content">一年学费读本科,赠考前辅导课 + 教材</p>
        </li>
        <li>
          <div class="title">
            <img src="../../../assets/image/active/hrActive/t2.png" class="img" alt="">
            <span>行业交流</span>
          </div>
          <p class="content">大咖职场培训课+线下学习营机会</p>
        </li>
        <li>
          <div class="title">
            <img src="../../../assets/image/active/hrActive/t3.png" class="img" alt="">
            <span>丰厚奖学金</span>
          </div>
          <p class="content">成考过295分, 享三年学费全免</p>
        </li>
        <li>
          <div class="title">
            <img src="../../../assets/image/active/hrActive/t4.png" class="img" alt="">
            <span>精准行业圈</span>
          </div>
          <p class="content">加入高端HR交流合作圈子</p>
        </li>
        <li class="full">
          <div class="title">
            <img src="../../../assets/image/active/hrActive/t5.png" class="img" alt="">
            <span>学完可获双证</span>
          </div>
          <p class="content">完成培训当年可获得上进HR培训结业证书，毕业后再拿学历证书</p>
        </li>
      </ul>
      <p class="mt22 big-title"># 如何加入成长计划？</p>
      <div class="plan-box">
        <div class="clearfix">
          <div class="box">
            <img src="../../../assets/image/active/hrActive/a1.png" class="img" alt="">
            <p class="text">· 人力资源专员及以上岗位<br />· 行政主管及以上岗位</p>
          </div>
          <div class="box ml">
            <img src="../../../assets/image/active/hrActive/a2.png" class="img" alt="">
            <p class="text">· 提供加盖单位公章的在职证明</p>
          </div>
        </div>
        <div class="plan-btn">
          <button class="red-btn w165" @click='btnClick'>{{btnText}}</button>
          <p class="people" v-if='isStart'>名额仅剩{{lastNum}}个</p>
        </div>
      </div>
      <p class="mt37 big-title"># 招生院校及专业</p>
      <img src="../../../assets/image/active/hrActive/school.png" class="school" alt="">
      <p class="mt22 big-title"># 学习计划安排</p>
      <img src="../../../assets/image/active/hrActive/plan.png" class="plan-img" alt="">
      <p class="mt22 big-title"># 技能训练</p>
      <div class="skill-box">
        <p class="t1">聚焦人力资源行业的职场痛点，全方位提升工作问题的解决能力</p>
        <div class="skill-img">
          <img src="../../../assets/image/active/hrActive/w.png" class="" alt="">
        </div>
        <ul class="plan-text-ul">
          <li>
            <span class="tips">学习</span>
            <div class="right">各行各业知名人力资源讲师, 远智内部高管独家内容亲授</div>
          </li>
          <li>
            <span class="tips">练习</span>
            <div class="right">社群学习小组, 全程助教陪伴跟进</div>
          </li>
          <li>
            <span class="tips">输出</span>
            <div class="right">1个月1-2次线下交流会、系统学习训练营<br />根据主题，按小组输出作业，刻意练习复盘总结汇报能力</div>
          </li>
          <li>
            <span class="tips">反馈</span>
            <div class="right">“打破”常规生活交际圈<br />同岗跨行的小组成员讨论交流, 碰撞出更多思考活力</div>
          </li>
          <li>
            <span class="tips">优化</span>
            <div class="right">远智导师团定期将小组成员项目作业汇总讨论<br />给予荣誉颁发及意见优化</div>
          </li>
        </ul>
      </div>
      <div class="down-upload">
        <span class="title">在职证明上传</span>
        <div class="line">
          <!-- <a :href="templateUrl" download="template.jpg"> </a> -->
          <button class="red-btn w165" @click='down'>下载在职证明模版</button>
          <!-- @click='down' -->
        </div>
        <div class="line2">
          <button class="red-btn w165" @click='btnClick'>上传在职证明</button>
        </div>
      </div>
    </div>
    <!-- <div class="msg-box">
      <img src="../../../assets/image/active/hrActive/msg.png" alt="">
      <span>留言区</span>
    </div>
    <div class="user-pr">
      <userMessage :scholarship="scholarship" style='border-radius: 0'></userMessage>
    </div> -->


    <footer-bar
      @isIphoneX="isIphoneX=true"
      :scholarship="scholarship"
      :inviteId="inviteId"
      :actName="actName"
      :btnEvent='btnClick'
      :btnText='btnText'
    />
    <!-- 米瓣 -->
    <div class="bg_copyright">
      <p>
        Copyright: 上进HR成长计划<br/>
        承办单位: 广州远智教育科技有限公司<br/>
        邮编: 516600 粤ICP备12034252号-1
      </p>
      <div class="link" v-if='isEmployee && !isAppOpen'>
        <router-link class="miban"
                      :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'hrActive'}}"
        >  <img src="../../../assets/image/miban.png" alt=""> </router-link>
      </div>
    </div>
    <!-- 招生规则 -->
    <van-popup v-model="showRule" position="bottom" class="rule-popup">
      <div class="popup-title">
        <span>招生规则</span>
        <div class="close-box" @click='showRule = false;'>
          <img src="../../../assets/image/closeX.png" class='close' alt="">
        </div>
      </div>
      <div class="container-pop">
        <ul>
          <li>
            <p class="title">招生对象</p>
            <div class="content">在广州、东莞、深圳、佛山、惠州5个城市从事【人力资源专员及以上岗位】、【行政主管及以上岗位】的在职上进青年</div>
          </li>
          <li>
            <p class="title">招生名额</p>
            <div class="content">300人</div>
          </li>
          <li>
            <p class="title">招生时间</p>
            <div class="content">2021年1月5日开始（以缴费为准，名额报满即止）</div>
          </li>
          <li>
            <p class="title">报名方式</p>
            <div class="content">【缴纳299元报名费】后，7天内上传【加盖公章的在职证明】</div>
          </li>
          <li>
            <p class="title">奖励政策</p>
            <div class="content">
              <p>1、就读后，奖励第二、第三年等值学费</p>
              <p>2、成考分过295分，就读后，奖励三年学费</p>
              <p>3、报读就送【考前辅导课】和【三本辅导教材】</p>
              <p>*上述考分不含政策性加分</p>
              <p>* 凭高校缴费发票申请奖励学费</p>
              <p>*若审核未通过，缴纳的费用可全额退回</p>
            </div>
          </li>
        </ul>
        <p class="color-grey">活动最终解释权归远智教育所有</p>
      </div>
    </van-popup>
    <!-- 上传证件 -->
    <upload-pop ref='uploadPop' :learnId='learnId' @success='uploadSuccess' />
    <photoswipe ref='photoswipe' />
  </div>
</template>

<script>
import bridge from '@/plugins/bridge';
import { Toast, Popup } from 'vant';
import share from "@/components/share";
import inviteTop from "../enrollAggregate/components/invite-top";
import userMessage from "../enrollAggregate/components/userMessage";
import footerBar from "./footer";
import uploadPop from "./upload-pop";
import swiperEnroll from "../enrollAggregate/components/swiper-enroll"
import { toLogin, isEmployee, isLogin, transformImgToBase64 } from "@/common";
import { toEnrollWithApp, toAppHome } from '@/common/jump';
import appShare from '@/mixins/appShare';
import photoswipe from '@/components/photoswipe';
import templateImg from '@/assets/image/active/hrActive/template.jpg';

export default {
  components: {
    swiperEnroll,
    userMessage,
    inviteTop,
    share,
    footerBar,
    Popup,
    uploadPop,
    photoswipe,
  },
  mixins: [appShare],
  data() {
    return {
      inviteId: '',
      scholarship: '123',
      isEmployee: isEmployee(),
      link: window.location.origin + "/active/hrActive",
      actName: '',
      startTime: 0,
      endTime: 0,
      learnCount: 0,
      learnId: '',
      isStart: false, // 是否开始
      isFull: false, // 满员
      isEnd: false,
      isIphoneX: false,
      showRule: false,
      showUpload: true,
      learnInfos: [],
      inviteId: '',
      hasLearnCurrent: false, // 是否已报读当前优惠类型
      lastNum: 300, // 剩余名额
      shareTitle: '一年学费读本科，斩获双证，限量报名！',
      shareDesc: '精准高端的HR行业交流学习群，学历+技能提升双轨制培养，毕业合格可获双证。远智教育首期“上进HR·成长计划”专项优惠，限量300个名额，快来提升自己吧！',
    };
  },
  mounted() {
    this.getActivityInfo();
    this.initAppShare(() => {
      this.setShareParams({
        title: this.shareTitle,
        content: this.shareDesc,
        url: '/active/hrActive',
        scholarship: this.scholarship,
      });
    });

    if (isLogin()) {
      this.getStdLearnInfo();
    }
  },
  computed: {
    btnText() {
      return this.hasLearnCurrent ? '上传在职证明' : '¥299立即报名';
    },
    templateUrl() {
      return 'http://yzimstemp.oss-cn-shenzhen.aliyuncs.com/prove.docx';
    },
  },
  methods: {
    getInviteId(id) {
      this.inviteId = id;
    },
    toEnroll(){
      if (isLogin() && this.learnInfos.length > 0) {
        const isHasInfo = this.learnInfos.find(item => item.recruitType == 1 || item.recruitType == 2); // 有成教和国开学籍
        if (isHasInfo) {
          if (this.isAppOpen) {
            toAppHome();
            return;
          }
          this.$router.push({ name: 'stuInfo' });
          return;
        }
      }

      if (!this.isCanLearnActive(1)) {
        return;
      }

      toEnrollWithApp({
        actName: this.actName,
        grade: '2022',
        scholarship: this.scholarship,
        recruitType: 1,
        inviteId: this.inviteId,
      }, this.isAppOpen);
    },
    //活动信息
    getActivityInfo() {
      this.$http.post("/mkt/getActivityInfo/1.0/", { scholarship: this.scholarship })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          const now = Date.now();
          this.actName = body.actName;
          this.learnCount = body.learnCount;
          this.isFull = body.limitCount == body.learnCount;
          const lastNum = body.limitCount - body.learnCount;
          this.lastNum = lastNum < 0 ? 0 : lastNum;
          this.isStart = now >= body.StartTime;
          this.isActiveEnd = now >= body.EndTime;
          this.isEnd = now >= (body.EndTime + (30 * 3600 * 24 * 1000)); // 结束后30天
        });
    },

    async getStdLearnInfo() {
      const res = await this.$http.post('/mkt/stdLearnInfo/1.0/');
      if (res.code == '00') {
        this.learnInfos = (res.body && res.body.learnInfos) ? res.body.learnInfos : [];
        if (this.learnInfos.length > 0) {
          const current = this.learnInfos.find(item => item.scholarship == this.scholarship);
          if (current) {
            this.learnId = current.learnId;
            this.hasLearnCurrent = true;
          }
        }
      }
    },

    // 是否能参加活动
    isCanLearnActive(type = 0) { // 0是上传在职证明 1是报名
      if (!isLogin()) {
        toLogin.call(this, null);
        return false;
      }
      if (!this.isStart) {
        Toast('活动未开始，请耐心等待噢！');
        return false;
      }
      if (type == 1 && this.isActiveEnd) {
        Toast('报名名额已满，下次赶早噢！');
        return;
      }
      if ((this.isFull && !this.hasLearnCurrent)) {
        Toast('报名名额已满，下次赶早噢！');
        return false;
      }
      if (this.isEnd && type == 0) {
        Toast('活动已结束，不能上传在职证明');
        return false;
      }
      return true;
    },

    down() {
      if (this.isAppOpen) {
        const img = new Image();
        img.crossOrigin = "";  // 支持跨域图片
        img.setAttribute("crossOrigin",'Anonymous');//跨域在前
        img.src = templateImg;
        img.onload = () => {
          const base64 = transformImgToBase64(img, 'image/jpeg');
          const imgdata = base64.split(",")[1];
          bridge.callHandler('receiptImage', { imgdata });
        };
        return;
      }
      this.$refs.photoswipe.open([{title: '长按图片保存模板', url: templateImg, type: 'base64'}]);
    },

    btnClick() {
      const type = this.hasLearnCurrent ? 0 : 1;
      if (!this.isCanLearnActive(type)) {
        return;
      }
      if (this.hasLearnCurrent) {
        this.$refs.uploadPop.open();
        return;
      }
      this.toEnroll();
    },
    uploadSuccess() {
      this.$refs.uploadPop.close();
    },
  },
};
</script>

<style lang="less">
  .yz-hr-active{

    background: #fff;
    padding-bottom: 0.7rem;
    &.iphonex{
      padding-bottom: 1.04rem;
    }
    .mt22{
      margin-top: 0.22rem;
    }
    .mt37{
      margin-top: 0.37rem;
    }
    .head-img-box{
      width: 100%;
      height: 3.73rem;
      background: url(../../../assets/image/active/hrActive/head-img.png) no-repeat;
      background-size: 100% 100%;
      position: relative;
    }
    .big-title{
      color:#fff;
      font-size: .18rem;
      font-weight: 600;
      text-align: center;
    }
    .big-box{
      background: url(../../../assets/image/active/hrActive/long-bg.png) no-repeat;
      background-size: 100% 100%;
      min-height: 22.76rem;
      padding: 0.07rem 0.15rem 0.15rem;
      margin-top: -1px;
      color: #fff;
    }
    .head-btn{
      position: absolute;
      bottom: 0.6rem;
      left: 0.62rem;
      z-index: 2;
    }
    .red-btn{
      border-radius: 100px;
      padding: 0.09rem 0.18rem;
      color: #fff;
      background: linear-gradient(180deg, #DB3135, #A50609);
      box-shadow: 0px 2px 2px 0px rgba(166, 19, 8, 0.4);
      font-weight: 600;
      font-size: 0.15rem;
      text-align: center;
      border: 0;
      &.w165{
        width: 1.65rem;
      }
    }
    .right-text{
      position: absolute;
      right: 0.15rem;
      top: 0.26rem;
      z-index: 2;
      font-size: 0.13rem;
      font-weight: 600;
      color: #fff;
      display: flex;
      align-items: center;
      .arrow{
        width: 0.13rem;
        height: 0.13rem;
        margin-left: 0.06rem;
      }
    }
    .five-ul{
      padding: 0.2rem 0 0.15rem;
      li{
        float: left;
        background: rgba(255, 255, 255, 0.2);
        position: relative;
        width: 1.65rem;
        height: 0.82rem;
        padding: 0.11rem 0.15rem;
        margin-bottom: 0.15rem;
        border-radius: 0.05rem;
        &.full{
          width: 100%;
          margin-bottom: 0;
        }
        &:nth-child(2n) {
          margin-left: 0.15rem;
        }
        &::before{
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 0.05rem;
          height: 0.42rem;
          background: linear-gradient(0deg, #84F5DE, #56E8F2, #4CC5F8);
          border-radius: 0 0.03rem 0.03rem 0;
        }
        .title{
          display: flex;
          align-items: center;
          font-size: 0.17rem;
          font-weight: 600;
          line-height: 1;
          .img{
            margin-right: 0.1rem;
            width: .24rem;
            height: 0.24rem;
          }
        }
        .content{
          font-size: 0.12rem;
          margin-top: 0.03rem;
        }
      }
    }
    .plan-box{
      border-radius: .05rem;
      background: rgba(255, 255, 255, 0.2);
      padding: 0.3rem 0 0.17rem 0.2rem;
      margin-top: 0.2rem;
      margin-bottom: 0.15rem;
      .box{
        float: left;
        width: 1.5rem;
        .img{
          width: 1rem;
          height: 0.4rem;
          vertical-align: middle;
        }
        .text{
          margin-top: 0.2rem;
          font-size: 0.12rem;
        }
      }

      .ml{
        margin-left: 0.2rem;
      }
      .plan-btn{
        margin-top: 0.3rem;
        text-align: center;
        .people{
          margin-top: 0.12rem;
          font-size: 0.12rem;
        }
      }
    }
    .school{
      margin-top: 0.14rem;
      margin-bottom: 0.18rem;
      height: 2.62rem;
    }
    .plan-img{
      width: 100%;
      margin-top: 0.2rem;
    }
    .skill-box{
      .t1{
        font-size: 0.11rem;
        text-align: center;
        margin-top: 0.14rem;
      }
      .skill-img{
        text-align: center;
        padding: 0.22rem 0;
        img{
          width: 2.34rem;
        }
      }
    }
    .plan-text-ul{
      li{
        margin-bottom: 0.2rem;
        font-size: 0.12rem;
        .tips{
          float: left;
          width: 0.42rem;
          padding: 0.04rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 0.03rem;
          text-align: center;
          line-height: 1;
        }
        .right{
          margin-left: 0.47rem;
          padding-top: 0.01rem;
        }
      }
    }
    .down-upload{
      text-align: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: .05rem;
      margin-top: 0.37rem;
      padding: 0.3rem 0;
      .title{
        position: relative;
        display: inline-block;
        line-height: 1;
        font-size: 0.18rem;
        font-weight: 600;
        &::before{
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 0.05rem;
          height: 0.05rem;
          border-radius: 50%;
          background: #fff;
          left: -0.12rem;
        }
        &::after{
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 0.05rem;
          height: 0.05rem;
          border-radius: 50%;
          background: #fff;
          right: -0.12rem;
        }
      }
      .line{
        margin-top: 0.25rem;
      }
      .line2{
        margin-top: 0.2rem;
      }
    }
    .msg-box{
      padding: 0.36rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #363636;
      font-size: 0.16rem;
      img{
        width: 0.19rem;
        height: 0.17rem;
        margin-right: .04rem;
      }
    }
    .user-pr{
      padding-right: 0.2rem;
    }
    .rule-popup{
      color: #000000;
      border-radius: 0.1rem 0.1rem 0 0;
      // height: 5.64rem;
    }
    .popup-title{
      height: 0.54rem;
      border: 1px solid rgba(0, 0, 0, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding:0 0.11rem;
      font-size: 0.15rem;
      .close-box{
        padding: 0.1rem;
        img{
          width: 0.14rem;
          height: 0.14rem;
          vertical-align: middle;
          opacity: 0.7;
          float: left;
        }
      }
    }
    .container-pop{
      max-height: 5rem;
      overflow: auto;
      & > ul{
        padding-top: 0.2rem;
        li{
          padding-left: 0.12rem;
          margin-bottom: 0.28rem;
          position: relative;
          &::before{
            content: '';
            width: 0.05rem;
            height: 0.22rem;
            background: linear-gradient(0deg, #84F5DE, #56E8F2, #4CC5F8);
            border-radius: 0 0.03rem 0.03rem 0;
            position: absolute;
            left: 0;
            top: 0;
          }
          .title{
            font-size: 0.17rem;
          }
          .content{
            color: rgba(0, 0, 0, 0.6);
            font-size: 0.12rem;
            & > p{
              margin-top: 0.2rem;
            }
          }
        }
      }

    }
    .color-grey{
      color: rgba(0, 0, 0, 0.4);
      padding-left: 0.12rem;
      font-size: 0.1rem;
      padding-bottom: 0.3rem;
    }
    .bg_copyright{
      background-color: #fff;
      p{
        text-align: center;
        margin-bottom: .2rem;
      }
      .link{
        display: flex;
        justify-content: center;
        margin-bottom: .15rem;
        margin-top: 0.2rem;
        .miban{
          /*margin-right: .3rem;*/
        }
        .yz,.miban{
          width: 1.63rem;
          height: .44rem;
          img{
            width: 100%;
          }
        }
        .yz2{
          width: 1.93rem;
          height: .44rem;
          img{
            width: 100%;
            height:100%;
          }
        }
      }
    }
  }
</style>
