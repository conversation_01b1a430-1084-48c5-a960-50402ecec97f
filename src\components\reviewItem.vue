<template>
  <div class="review-item">
    <div class="tit">[第{{planCount}}期]揭晓时间：{{time|formatDate('yyyy.MM.dd hh:mm:ss')}}</div>
    <div class="content clearfix">
      <div class="cover"><img class="img" :src="headImgUrl|defaultAvatar" alt=""/></div>
      <div class="cont">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['planCount', 'time', 'headImgUrl']
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  
  .review-item{
    position:relative; margin:0 .12rem; padding:.12rem 0;
    &:after{ .borderBottom }
    .tit{ padding-bottom:.05rem; font-size:.12rem; }
    .content{ display:flex; align-items:center; }
    .cover{ width:.34rem; height:.34rem; overflow:hidden; border-radius:50%; }
    .cont{ flex:1; margin-left:.1rem; }
    .fsc1{ color:#666; font-size:.11rem; }
    .fc1{ color:@color; }
  }
</style>
