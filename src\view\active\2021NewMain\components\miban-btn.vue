<template>
  <div v-if='isEmployee' class="yz-miban">
    <button class="yz-miban-btn" :class='{pink: pink}' @click='go'>
      <img src="../../../../assets/image/active/2021NewMain/w-code.png" alt="">
      <span>生成米瓣邀约</span>
    </button>
  </div>
</template>

<script>
import { isEmployee } from '@/common';

export default {
  props: {
    pink: {
      type: Boolean,
      default: false,
    },
    scholarship: {
      type: [String, Number],
      default: '',
    },
    scholarshipName: { // 暂时没啥用
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isEmployee: isEmployee(),
    }
  },
  mounted() {
  },
  methods: {
    go() {
      this.$yzStatistic('marketing.base.browse', '23', '米瓣生成器');
      this.$router.push({
        name:'inviteLink',
        query: { scholarship: this.scholarship },
      });
    },
  },
};
</script>

<style lang="less" scoped>
  .yz-miban-btn{
    display: flex;
    margin: 0 auto 0.3rem;
    width: 1.58rem;
    height: 0.44rem;
    color: #fff;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 0.15rem;
    background: linear-gradient(180deg, #DA3338 0%, #A70A0D 100%);
    box-shadow: 0px 2px 2px 0px rgba(166, 19, 8, 0.4);
    border-radius: 100px;
    &.pink{
      background: linear-gradient(180deg, #DA3338 0%, #F185A8 0%, #EA3671 100%);
    }
    img{
      width: 0.22rem;
      height: 0.22rem;
      margin-right: 0.04rem;
    }
  }
</style>
