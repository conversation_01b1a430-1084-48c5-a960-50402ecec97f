const engine = require('store/src/store-engine');
const storages = [
  require('store/storages/localStorage'),
  require('store/storages/cookieStorage')
];
const plugins = [
  require('store/plugins/defaults'),
  require('store/plugins/expire')
];
let store = engine.createStore(storages, plugins);
store.setItem = store.set;
store.getItem = store.get;
store.removeItem = store.remove;
store.clear = store.clearAll;

export default store;
