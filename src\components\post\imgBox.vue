<template>
  <div class="imgBox">
    <template v-for="(item, imgindex) in scPicUrl">
      <template v-if="scPicUrl.length == '1'">
        <img @click.stop="showImgClick(imgindex,scPicUrl)" :src="(item + autoLimit) | imgBaseURL" alt class="commentImg"
             style="width: 1.12rem; height: auto; max-height: 3rem" />
      </template>
    </template>
    <!-- <template v-for="(item, imgindex) in scPicUrl" v-if="scPicUrl.length == '2'">
      <img @click.stop="showImgClick(imgindex,scPicUrl)" :src="item | imgBaseURL" alt class="commentImg"
           style="margin-left: 0.04rem" />
    </template> -->
    <template v-for="(item, imgindex) in scPicUrl" v-if="scPicUrl.length >= '2'">
      <img @click.stop="showImgClick(imgindex,scPicUrl)" :src="item | imgBaseURL" alt class="commentImg"
           style="margin-left: 0.02rem" />
    </template>
  </div>
</template>

<script>
import { imgBaseURL } from "@/config";
import { ImagePreview } from "vant";

export default {
  components: {},
  data () {
    return {
      autoLimit: "?x-oss-process=image/auto-orient,1",
      imgbaseurl: imgBaseURL

    };
  },
  props: {
    scPicUrl: {
      type: Array,
      default: () => ([])
    },

  },
  computed: {},
  watch: {},
  methods: {
    showImgClick (index, arr) {
      arr = arr.map(item => (imgBaseURL + item))
      ImagePreview({
        images: arr,
        startPosition: index,
        closeable: true,
        closeOnPopstate: true
      })
    },
  },
  created () { },
  mounted () { },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
.imgBox {
  zoom: 1;
  overflow: hidden;
  .commentImg {
    width: 1.12rem;
    height: 1.12rem;
    border-radius: 0.025rem;
    margin-top: 0.02rem;
    object-fit: cover;
  }
}
</style>