<template>
  <div class="yz-big-swiper">
    <!-- 证书的轮播 -->
    <swiper :class='swiperClass' :options='swiperOption' ref='mySwiper'>
      <swiper-slide :class='swiperSlideClass' v-for="(item, index) in list" :key="index">
        <div class="swipter-item">
          <slot :item='item'><img :src="item.image" class="default-img" alt=""></slot>
        </div>
      </swiper-slide>
    </swiper>
    <div class="swiper-pagination" :id='pageId'></div>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";

export default {
  components: { swiper, swiperSlide },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    swiperClass: {
      type: String,
      default: '',
    },
    swiperSlideClass: {
      type: String,
      default: '',
    },
    pageId: {
      type: String,
      default: `page${parseInt(Date.now() * Math.random())}`,
    },
    loop: {
      type: Boolean,
      default: true
    },
    autoplay: {
      type: Boolean | Number,
      default: 3000
    }
  },
  data () {
    return {
      swiperOption: {
        initialSlide: this.loop ? 1 : 0,
        autoplay: this.autoplay,
        centeredSlides: true,
        spaceBetween: 10,
        loop: this.loop,
        slidesPerView: "auto",
        loopedSlides: 1,
        autoplayDisableOnInteraction: false,
        pagination: `#${this.pageId}`,
        on: {
          click: () => {
            // let swiper = this.$refs.mySwiper.swiper;
            // console.log(swiper);
            // let i = swiper.activeIndex;
            // vm.showIndex(i)
          },

        },
        // onSlideChangeEnd: (swiper) => {
        //   console.log(swiper, 22222)
        //   this.$emit('activeIndex', swiper.activeIndex)
        // },

      },
    }
  },
  methods: {

  },
};
</script>

<style lang="less">
.yz-big-swiper {
  position: relative;
  .swiper-slide {
    transition: width 0.2s, height 0.2s, margin-top 0.2s;
  }
  .swipter-item {
    height: 100%;
    .default-img {
      vertical-align: middle;
      width: 100%;
      height: 100%;
      border-radius: 0.05rem;
    }
  }
  .swiper-pagination {
    text-align: center;
    width: 100%;
  }
  .swiper-pagination-bullet {
    opacity: 1;
    background: #e5e5e5;
    border-radius: 10px;
    width: 0.04rem;
    height: 0.04rem;
    transition: width 0.2s;
    &:not(:first-child) {
      margin-left: 0.04rem;
    }
  }
  .swiper-pagination-bullet-active {
    width: 0.09rem;
  }
}
</style>
