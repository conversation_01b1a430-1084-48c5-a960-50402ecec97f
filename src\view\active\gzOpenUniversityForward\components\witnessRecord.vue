<template>
  <!-- 见证记录 list -->
  <div class="witness-record">
    <div class="title">
      <div class="img-box">
        <img src="../../../../assets/image/active/gccScholarship/dynamicTitle.png" alt="">
      </div>
    </div>

    <div
      class="record"
      v-infinite-scroll="loadmore"
      :infinite-scroll-immediate="false"
      :infinite-scroll-distance="50"
    >
      <ul>
        <li class="item" v-for="(item,index) in witnesstList" :key="index" @click="goPage(item)">
          <div class="top">
            <div class="avatar">
              <img :src="item.coverWitnessHead | defaultAvatar" >
            </div>
            <div class="user">
              <p class="user-name">{{item.coverWitnessName}}</p>
              <p class="user-time">{{item.createTime | formatDate('yyyy-MM-dd')}}</p>
            </div>
          </div>
          <div class="center">完成了上进见证，激活了3000元奖学金！</div>
          <div class="reviews">
            <div class="text">
              见证人：<span> {{item.witnessName}} {{item.workUnit}}</span>
            </div>
            <div class="text">
              上进点评：<span>{{item.comment}}</span>
            </div>
          </div>
        </li>
      </ul>
      <div class="empty" v-if="witnesstList.length==0">暂无见证动态</div>
    </div>
  </div>

</template>
<script>
import invite from '../invite'
export default {
  props:['isJump'],
  mixins:[ invite ],
  data(){
    return {
      scholarship:'126',
      witnesstList: [],
      pageNum: 0,
      pageSize: 10,
      inviteId:'',
    }
  },
  mounted(){
    this.inviteId = this.$route.query.inviteId || ""
  },
  methods:{
    loadmore(){
      this.pageNum += 1;
      this.getWitnesstList();
    },
     //见证列表
    getWitnesstList() {
      this.$http
        .post("/mkt/getAdvanceWitnessList/1.0/", {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          scholarship: this.scholarship,
        })
        .then((res) => {
          if (res.code == "00") {
            const datas = res.body || [];
            this.witnesstList.push(...res.body);
            // this.$nextTick(() => {
            //   this.allLoaded = datas.length === 0;
            //   this.isLoading = this.allLoaded;
            // });
          }
        });
    },
    goPage(item){
      let route = {
        path:'/active/gzOpenUniversityForward/witnessDetails',
        query:{
          inviteId:this._inviteId,
          regOrigin:this._regOrigin,
          regChannel:this._regChannel,
          ...item,
        }
      }

      if(this.isJump){
        this.$router.push(route);
      }else{
        this.$emit('see',item);
      }


    },
  }
}
</script>
<style lang="scss" scoped>
.witness-record{
  background: #fff;

  .title{
    .img-box{
      width: 1.25rem;
      height: 0.22rem;
      margin: 0 auto;
      img{
        width: 100%;
      }
    }
  }

  .record{
    height: auto;
    overflow: hidden;
    padding:0.2rem 0.1rem;
    .empty {
      text-align: center;
      color: #ccc;
    }
    ul{
      .item + .item {
        margin-top: 0.26rem;
      }
      .item{
        .top{
          .avatar{
            display: inline-block;
            width: 0.42rem;
            height: 0.42rem;
            vertical-align: top;
            margin-left: 0.05rem;

            img{
              width: 100%;
              border-radius: 50%;
            }
          }
          .user{
            display: inline-block;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: bold;
            .user-name{
              color: #453838;
              font-size: 0.14rem;
            }
            .user-time{
              color:#A29B9B;
              font-size: 0.12rem;
            }
          }
        }
        .center{
          font-size: 0.14rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #453838;
          margin: 0.1rem 0;
        }
        .reviews{
          width: 3.55rem;
          border-radius: 6px;
          border: 1px dashed #DA292F;
          padding: 0.15rem;
          .text{
            font-size: 0.14rem;
            color:#453838;
            word-break: break-all;
            span{
              color:#DA292F;
              font-weight: bold;
            }
          }
          .text + .text{
            margin-top: 0.1rem;
          }
        }
      }
    }
  }
}
</style>
