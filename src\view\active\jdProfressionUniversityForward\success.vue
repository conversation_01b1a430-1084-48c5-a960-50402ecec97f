<template>
  <div class="main">
    <div class="icon">
      <img class="sucess" src="../../../assets/image/active/success/true.png" alt="">
    </div>
    <h2 class="success-text">领取成功</h2>
    <div class="note">
      <span>您可在</span>
      <span class="link" @click="toCoupons">“服务大厅-奖学金”</span>
      <span>查看，快去报读见证激活奖学金吧！</span>
    </div>
    <div class="volumes">
      <div class="item">
        <img src="../../../assets/image/active/jdProfressionUniversityForward/gold.png" alt="">
      </div>
    </div>

    <div class="footer">
      <div @click="toCjread" class="btn"  v-if="!isSeikyo">马上报读</div>
      <div @click="toSprint" class="btn" v-else>马上激活</div>
    </div>

  </div>
</template>
<script>
import mixin from './mixin';
import recruitType from './recruitType';
import bridge from '@/plugins/bridge';
export default {
  mixins:[ mixin, recruitType],
  data(){
    return {
      isEnroll: false,
      inviteId:'',//邀约人id
      scholarship:"130",
      inviteId:'',
      isAppOpen: false
    }
  },
   mounted(){
    this.setIsAppOpen();
    // this.isEnroll = this.storage.getItem('relation')=='4'?true:false;
    this.inviteId = this.$route.query.inviteId || "";
  },
  methods:{
    getCookie(name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return null;
      }
    },
    setIsAppOpen() {
      bridge.callHandler('isAppOpen').then((res) => {
        if (res.appOpen) {
          this.isAppOpen = true;
        }
      });
    },
    toCoupons(){
      if(this.isAppOpen){
        window.location.href =`yuanzhiapp://yzwill.cn/Coupons`;
      }else{
        this.$router.push({path:'/settings/coupon'})
      }
    },
    // 马上报读
    toCjread(){
      const now = new Date().getTime();

      if(now > this.AC_INFO.EndTime) {
        this.$modal({
          message: "活动已结束~下次赶早哦！",
          icon: "warning",
        });
        return;
      };

      if(this.isAppOpen) {
        //是否报读
        if(this.isSeikyo) {
          window.location.href =`yuanzhiapp://yzwill.cn/Home?params={"tab":3}`;
          return
        }else {
          window.location.href = `yuanzhiapp://yzwill.cn/Enroll/InfoInput?params={"activeName":"2022级广东机电职业技术学院奖学金","grade":"2022","scholarship":"130"}`;
          return;
        }
      };

      //活动进行中和活动结束后
      if(this.AC_STATUS == 1 || this.AC_STATUS == 2){
        let router = {
          name:'adultExamEnrollCheck',
          query:{
            action:'login',
            activityName:'scholarship',
            inviteId:this.inviteId,
            scholarship:this.scholarship,
            actName:this.AC_INFO.actName,
          }
        }
        this.$router.push(router)
      }

    },
    //马上激活
    toSprint(){
      const now = new Date().getTime();
      console.log(this.AFTER7DAYS,'this.AFTER7DAYS')
      if(now > this.AFTER7DAYS) {
        this.$modal({
          message: "活动已结束~下次赶早哦！",
          icon: "warning",
        });
        return;
      };

      this.$router.push({name:'jdProfressionUniversityForward.home',params:{tabIndex:1}});
    }
  },
}
</script>
<style lang="less" scoped>
.main{
  background-color: #ffffff;
  min-height: 100vh;
  .icon{
    padding-top: 0.6rem;
    margin-bottom: 0.21rem;
    text-align: center;
    img{
      width: 0.95rem;
      height: 1.13rem;
      margin: 0 auto;
    }
  }
  .success-text{
    font-size: .18rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #170606;
    text-align: center;
  }
  .note{
    margin-top: 0.26rem;
    text-align: center;
    padding: 0 0.69rem;
    font-size: 0.12rem;
    .link{
      color:#C13935;
    }
  }
  .volumes{
    margin-top: 0.4rem;
    .item + .item{
      margin-top: 0.25rem;
    }
    .item{
      width: 2.9rem;
      height: 1.07rem;
      margin-left: 0.45rem;
      img{
        width: 100%;
      }
    }
  }
  .footer{
    .btn{
      position: fixed;
      bottom: 0;
      width: 100%;
      height:.6rem;
      background:rgba(249,78,84,1);
      line-height: .6rem;
      font-size:.16rem;
      font-family:PingFang-SC-Bold,PingFang-SC;
      font-weight:bold;
      color:rgba(255,255,255,1);
      text-align: center;
    }
  }
}
</style>
