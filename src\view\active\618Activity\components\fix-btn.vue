<template>
  <div class="yz-618-fixBtn" v-if='isShow'>
    <img class='img1' src="../../../../assets/image/active/618Activity/g.gif" @click='go' alt="">
    <div>
      <img class='img2' src="../../../../assets/image/active/618Activity/img-close.png" @click='close' alt="">
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
    };
  },
  mounted() {
    this.getPrizeLotteryInfo();
  },
  methods: {
    go() {
      const query = this.$route.query;
      this.$router.push({
        name: '618Activity',
        query,
      });
    },
    close() {
      this.isShow = false;
    },
    // 获取活动信息
    async getPrizeLotteryInfo() {
      const { code, body } = await this.$http.post("/mkt/getPrizeLotteryInfo/1.0/");
      if (code == "00") {
        this.isShow = body.status == 1;
      }
    },
  },
};
</script>

<style lang="less">
  .yz-618-fixBtn{
    position: fixed;
    bottom: 2.91rem;
    right: 0.05rem;
    text-align: center;
    z-index: 9999;
    .img1{
      vertical-align: middle;
      width: 1.2rem;
      height: 0.93rem;
    }
    .img2{
      width: 0.19rem;
      height: 0.19rem;
      margin-top: 0.02rem;
      margin-left: 0.1rem;
    }
  }
</style>
