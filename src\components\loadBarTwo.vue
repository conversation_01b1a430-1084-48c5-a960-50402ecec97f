<template>
  <div>
    <div class="loadmore" v-show="isLoading&&!allLoaded">
      <mt-spinner class="mint-loadmore-spinner" :size="20" type="fading-circle"></mt-spinner>
      <span class="mint-loadmore-text">加载中...</span>
    </div>
    <template v-if="allLoaded"><slot></slot></template>
    <slot name="noData" v-if="isNoData" >
        <div class="imgBox"><img class="no-data-bg" src="../assets/image/<EMAIL>" alt=""></div>
        <div class="no-dataText">暂时还没有课程哦~</div>
        </slot>
  </div>
</template>

<script>
  export default {
    props: {
      isLoading: {
        type: Boolean,
        default: false
      },
      allLoaded: {
        type: Boolean,
        default: false
      },
      total: null
    },
    computed: {
      isNoData: function () {
        return 'number' === typeof this.total && this.total === 0 && this.allLoaded;
      }
    }
  }
</script>

<style lang="less" scoped>
  .loadmore{ padding:.13rem 0; clear:both; text-align:center; color:#868686; font-size:.12rem; }
  .imgBox {
      text-align: center;
      margin-top:1rem;
      .no-data-bg {
        width: 1.42rem;
        margin: 0 auto;
      }
  }
    .no-dataText {
        margin-top: .3rem;
        color:rgba(68,68,68,1);
        text-align: center;
    }  
  
</style>
