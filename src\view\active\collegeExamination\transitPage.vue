<template>
  <div class="transitPage-wrapper">
        <!-- <button class="btn"  style="color: red;">打开小程序</button> -->
    <!-- <img class="launch-img" src="http://test2.yzwill.cn/static/images/e2a2f203536f1aebd0cf..png" alt /> -->
    <wx-open-launch-weapp
      id="launch-btn"
      class="launch-follow"
      appid="wx9b6c444edb3f466c"
      :path='mPath'
      env-version="trial"
    >
      <script type="text/wxtag-template">
        <style>
          .btn{
            width: 1000px;
            height: 1000px;
            z-index: 9999;
          }
        </style>
        <!-- <span class="btn">open</span> -->
     <!-- <img class="launch-img" src="./img/transitImage.png" alt /> -->
     <!-- <img class="launch-img" src="http://test2.yzwill.cn/static/images/e2a2f203536f1aebd0cf..png" alt /> -->
        <div class="btn" style="color: red;"></div>
      </script>
    </wx-open-launch-weapp>
  </div>
  <!-- <div class="transitPage-wrapper" @click="toActivePage">
    <img src="./img/bg.png" alt />
  </div> -->
</template>
<script>
import { isIOS, downloadApp } from "@/common";
import openApp from "@/mixins/openApp";
export default {
  mixins: [openApp],
  data() {
    return {
    //   url: `pages/other/newYearActive/index`,
      url: `/pages/active/20240420/index?inviteToken=${this.$route.query.token?this.$route.query.token.replace(/\ +/g, "+"): ""}&inviteMobile=${this.$route.query.inviteMobile}`,
    };
  },
  created() {
    // this.toActivePage();
  },
  computed: {
    mPath() {
        const token = this.$route.query.inviteToken ? this.$route.query.inviteToken.replace(/\ +/g, "+") : ""
        const mobile = this.$route.query.inviteMobile || ''
        console.log(token,mobile,`/pages/active/20240420/index?inviteToken=${token}&inviteMobile=${mobile}`,'8888888888');
        return `/pages/active/20240420/index?inviteToken=${token}&inviteMobile=${mobile}`
    }
  },
  mounted() {
    this.wxCallAppInit(); // 微信标签唤起app
  },
  methods: {
    getLink() {
      const inviteToken = this.$route.query.token
        ? this.$route.query.token.replace(/\ +/g, "+")
        : "";
      const id = this.$route.query.id;
      const type = this.$route.query.type;
      const regOrigin = this.$route.query.regOrigin;
      const regChanel = this.$route.query.regChanel;
      const data = {
        appletsUrl: "/pages/other/newYearActive/index",
        query: `inviteToken=${inviteToken}&regChanel=${regChanel}&circleId=${id}&type=${type}&regOrigin=${regOrigin}`,
      };
      return this.$http.post("/us/getAppletsToLink/1.0/", data);
    },
    async toActivePage() {
      const res = await this.getLink();
      if (res.body) {
        location.href = res.body;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.transitPage-wrapper {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
  background: url('https://yzpres.oss-cn-guangzhou.aliyuncs.com/Spring/18888.png') no-repeat center;
  background-size: 100%;
    overflow: hidden;
    img {
        width: 100%;
        height: 100%;
    }
}
.launch-follow{
    // position: absolute;
    // top: 0;
    // left: 0;
    // bottom: 0;
    // right: 0;
    // width: 100%;
    // height: 100%;
    // z-index: 4;
    // overflow: hidden;
}
.launch-img{
 width: 100%;
  height: 100%;
            // z-index: 2;
}
</style>