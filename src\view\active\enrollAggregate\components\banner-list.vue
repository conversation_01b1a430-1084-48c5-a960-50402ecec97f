<template>
  <swiper class="yz-newintroduce-swiper" v-show='!Expired' :options='options' ref='mySwiper'>
    <swiper-slide v-for="(item) in bannerList" :key="item.bannerUrl">
      <a href="javascript:;" @click='itemClick(item)'>
        <img :src="item.bannerUrl | imgOssURL" alt="">
      </a>
    </swiper-slide>
    <div v-show='this.bannerList.length > 1' class="swiper-pagination page-custom" slot="pagination"></div>
  </swiper>
</template>

<script>
import { getQueryString } from '@/common';
import { swiper, swiperSlide } from "vue-awesome-swiper";
import 'swiper/dist/css/swiper.css';

export default {
  components: {
    swiper,
    swiperSlide,
  },
  props: {
    Expired: {
      type: Boolean,
      default: false,
    },
    inviteId: String,
    zmBannerType: {
      type: [String, Number],
      default: 5
    },
  },
  data () {
    return {
      bannerList: [],
      options: {
        initialSlide: 0,
        autoplay: 3000,
        loop: false,
        // autoHeight: true,
        pagination: '.swiper-pagination',
        paginationType: 'fraction',
        autoplayDisableOnInteraction: false,
      },
    };
  },
  mounted() {
    this.getBannerList();
  },
  methods: {
    itemClick(item) {
      if (!item.redirectUrl) {
        this.$modal({message: '跳转链接不存在', icon: 'warning'});
        return;
      }
      const query = this.$route.query;
      let searchUrl = '';
      const activeShip = getQueryString(item.redirectUrl, 'activeShip');
      if(item.redirectUrl.includes('/active/elevenCoupon')) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=119`;
      } else if (activeShip) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=${activeShip}`;
      } else {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}`;
      }

      if (item.redirectUrl.includes('scholarship') && !item.redirectUrl.includes('active/registerReading/index')) {
        this.$emit('toEnroll');
      } else {
        const url = item.redirectUrl.includes('?') ? `${item.redirectUrl}&${searchUrl}` : `${item.redirectUrl}?${searchUrl}`;
        window.location.href = url;
      }
    },
    async getBannerList() {
      const res = await this.$http.post('/bds/bannerList/1.0/', { zmBannerType: this.zmBannerType });
      if (res.code == '00') {
        this.bannerList = res.body;
      }
    },
  }
};
</script>

<style lang="less" scoped>
.yz-newintroduce-swiper{
  position: relative;
  min-height: 3.72rem;
  img{
    width: 100%;
    height: 3.72rem;
    object-fit: cover;
  }

  .page-custom{
    position: absolute;
    background: rgba(0, 0, 0, 0.4);
    right: 0.15rem;
    bottom: 0.14rem;
    color: #fff;
    font-size: 0.15rem;
    padding: 0.03rem 0.07rem;
    border-radius: 0.05rem;
    width: auto;
    left: auto;
    line-height: 1;
    font-family: PingFangSC-Regular,PingFang SC,'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  }
}
</style>
