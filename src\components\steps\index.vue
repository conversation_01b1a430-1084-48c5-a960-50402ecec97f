<template>
  <div class="yz-steps-box" ref='steps'>
    <!-- 步骤器 -->
    <slot></slot>
  </div>
</template>

<script>
export default {
  data () {
    return {
    };
  },
  props: {
    active: { // 代表第几个完成 从1开始
      type: Number,
      default: 0, // 0是第一个都没完成
    },
  },
  computed: {
    children() {
      return this.$refs.steps.children;
    },
  },
  mounted () {
    this.setStep();
  },
  watch: {
    active() {
      this.setStep();
    },
  },
  methods: {
    setStep() {
      if (this.active == 0) {
        for (let i = 0; i < this.children.length; i++) {
          this.children[i].classList = 'yz-step-item';
        }
        this.children[0].classList.add('doing');
      } else {
        for (let i = 0; i < this.children.length; i++) {
          if (i < this.active) {
            this.children[i].classList.add('done');
          } else if (i == this.active) {
            this.children[i].classList.add('doing');
          } else {
            this.children[i].classList = 'yz-step-item';
          }
        }
      }
    },
  },
};
</script>

<style lang="less">
  .yz-steps-box{
    display: flex;
    justify-content: space-between;
    position: relative;
  }
</style>
