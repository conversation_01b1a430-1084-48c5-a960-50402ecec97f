<template>
  <div class="yz-new-userMessage"  >
    <div class="textContent">
      <div class="userMessageContentList" :class="{anim:animatePraise==true}">
        <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
          <div class="fl">
            <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2" alt>
          </div>
          <div class="fr">
            <p class="userName">{{(item.realName|| item.nickname)|hideName}}</p>
            <div class="bottom" v-if="item.msgReply">
              <p class="uesrQuestion">{{item.msgContent}}</p>
              <div class="answer-content">
                <div class="msg">
                  <p class="answer" :style='{background: answerBgColor}'>
                    <span>回复:&nbsp;</span>
                    {{item.msgReply}}
                  </p>
                </div>
                <!--<div class="line"></div>-->
              </div>
            </div>
            <div class="bottom2" v-if="!item.msgReply" >
              <p class="uesrQuestion">{{item.msgContent}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="userMessageContent">
      <div class="fl">
        <img :src="userImg?userImg+headLimit:userImg|defaultAvatar2" alt>
      </div>
      <div class="fr">
        <p class="userName">{{userName}}</p>
        <textarea maxlength="50" v-model.trim="message" placeholder="说上两句吧..."></textarea>
        <span>{{message.length}}/50</span>

      </div>
    </div>
    <div class="button-box">
      <button :class='{pink: pinkBtn}' @click="enrollMsg">提交留言</button>
    </div>
  </div>
</template>

<script>
  import { toLogin, isEmployee ,filterEmoji } from "@/common";
    export default {
      name: "userMessage",
      props:{
        scholarship: {
          type: String,
          default: '164'
        },
        answerBgColor: {
          type: String,
          default: 'rgba(145, 145, 145, 0.05);',
        },
        pinkBtn: {
          type: Boolean,
          default: false,
        },
      },
      data(){
        return{
          headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
          userImg:'',
          userName:'',
          message:'',
          animatePraise:false,
          isLogin:false,
          isEmployee:isEmployee(),
          enrollMsgList:[]
        }
      },
      watch: {
        scholarship() {
          this.getEnrollMsgList();
        },
      },

      created(){
        this.isLogin = !!this.storage.getItem("authToken");
        this.userImg = this.storage.getItem("headImg") || "";
        setInterval(this.scrollNext, 5000);
        this.getEnrollMsgList();
        // 兼容app没有头像的bug
        if (this.isLogin) {
          this.getUserInfo();
        }
      },
      methods:{
        scrollNext(){
          if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
            return;
          }
          this.animatePraise = true;
          setTimeout(() => {
            this.enrollMsgList.push(this.enrollMsgList[0]);
            this.enrollMsgList.shift();
            this.animatePraise = false;
          }, 500);
        },
        // 评论
        enrollMsg: function() {
          if (!this.isLogin) {
            toLogin.call(this, null);
            return;
          }
          if (this.isEmployee) {
            this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
            return;
          }
          if (!this.message) {
            this.$modal({ message: "请输入评论内容", icon: "warning" });
            return;
          }
          this.$http
            .post("/mkt/enrollMsg/1.0/", {
              scholarship: this.scholarship,
              msgContent: filterEmoji(this.message)
            })
            .then(res => {
              let { code, body } = res;
              if (code === "00") {
                this.$modal({
                  message: "提交成功",
                  beforeClose: (action, instance, done) => {
                    done();
                    this.message = "";
                    this.getEnrollMsgList();
                  }
                });
              }
            });
        },
        getEnrollMsgList: function() {
          this.$http
            .post("/mkt/getEnrollMsgList/1.0/", { scholarship: this.scholarship })
            .then(res => {
              let { code, body } = res;
              if (code === "00") {
                  this.enrollMsgList=body;
              }
            });
        },
        async getUserInfo() {
          const { code, body } = await this.$http.post('/us/getUserInfoById/1.0/');
          if (code == '00') {
            this.userName=body.realName || body.zmcName || body.mobile;
            this.userImg = body.headImg || "";
          }
        },
      }
    }
</script>

<style scoped lang="less">
  .yz-new-userMessage {
    border-radius: 0.1rem;
    width: 100%;
    margin: 0 auto;
    height: auto;
    overflow: hidden;
    animation: slide .5s ;
    .headBox {
      width: .6rem;
      height: 0.3rem;
      text-align: center;
      img {
        width: 2.46rem;
        margin-top: 0.19rem;
        margin-bottom: 0.15rem;
      }
    }
    .textContent {
      //background: url("../../../assets/image/active/newDreamBuild/dreamFive/userMessageBg.png")
      //no-repeat center;
      background-size: 100%;
      overflow: hidden;
      height: 4.1rem;
      .userMessageContentList {
        width: 100%;
        &.anim {
          transition: all 1s;
          margin-top: -1.7rem;
        }
        .content {
          overflow: hidden;
          height: 1.33rem;
          position: relative;
          display: flex;

          .fl {
            width: 0.52rem;
            img {
              width: 0.38rem;
              height: 0.38rem;
              float: right;
              border-radius: 50%;
              margin-top: 0.11rem;
            }
          }
          .fr {
            flex: 1;
            height: 1.14rem;
            padding-right: 0.15rem;
            padding-left: 0.06rem;
            .userName {
              margin-top: 0.12rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }
            .uesrQuestion {
              display: inline-block;
              // width: 2.76rem;
              margin-top: 0.03rem;
              font-size: 0.14rem;
              color: rgba(54, 54, 54, .8);
            }
            .line {
              position: absolute;
              display: inline-block;
              width: 0.02rem;
              height: 0.14rem;
              background-color:rgba(239, 186, 6, 1);
              top: 0.17rem;
              left: 0.01rem;
            }
            .answer-content {
              margin-top: 0.1rem;
              height: auto;
              width: 100%;
              .answer {
                border-radius: 0.05rem;
                font-size: 0.14rem;
                color: rgba(0, 0, 0, 0.6);
                padding: 0.1rem;
                background: rgba(145, 145, 145, 0.05);
              }
            }
          }
        }
      }
    }

    .userMessageContent {
      display: flex;
      .fl {
        width: 0.52rem;
        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }
      .fr {
        flex: 1;
        position: relative;
        padding-left: 0.06rem;
        padding-right: 0.15rem;
        .userName {
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
          display: inline-block;
          width: 2.48rem;
          height: 0.24rem;
        }
        textarea {
          width: 100%;
          height: 0.86rem;
          border: 1px solid rgba(240, 86, 10, 1);
          padding: 0.13rem 0.1rem 0.13rem 0.1rem;
          margin-top: 0.03rem;
          border-radius: .05rem;
        }
        span {
          position: absolute;
          font-size: 0.09rem;
          color: rgba(23, 6, 6, 0.4);
          top: 1.07rem;
          right: 0.2rem;
        }

      }

    }
    .button-box{
      text-align: center;
      margin-top: 0.2rem;
      button {
        width: 1.36rem;
        height: 0.4rem;
        border-radius: .4rem;
        background: linear-gradient(0deg,  #A50609,#DB3135);
        color:#fff;
        font-size: 0.17rem;
        font-weight: 600;
        border-bottom: 0.04rem solid #511001;
        &.pink{
          background: linear-gradient(180deg, #F286A9 0%, #E93570 100%);
        }
      }
    }
  }
</style>
