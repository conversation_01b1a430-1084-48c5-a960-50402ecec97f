<template>
  <dialog-container v-model="show" @close='close'>
    <div class="yz-ck-getChance">
      <div class="t1"></div>
      <div class="bg-box">
        <p class="t2">以下方式可获得抽签机会</p>
        <div class="box">
          <span>每日签到 *1</span>
          <!-- <button v-if="count.drawCount==='0'">去签到</button> -->
          <span class="right">今日已获取</span>
        </div>
        <div class="box">
          <span>每日学习 *1</span>
          <span class="right" v-if="count.studyCount==='1'">今日已获取</span>
          <button @click='toStudy' v-else>去学习</button>
        </div>
        <div class="box">
          <span>每日分享 *1</span>
          <span class="right" v-if="count.shareCount==='1'||isShare">今日已获取</span>
          <button @click='openShare' v-else>去分享</button>
        </div>
        <p class="t3">
          越努力越幸运，<br />学习可以提高抽好签的运气值喔～
        </p>
        <img src="../../../../assets/image/active/ckSprint/yellow-y.png" class="quotation-marks" alt="">
        <img src="../../../../assets/image/active/ckSprint/yellow-y.png" class="quotation-marks2" alt="">
      </div>
    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container.vue';

export default {
  components: { DialogContainer },
  data () {
    return {
      show: false,
      count: {
        drawCount: '1'
      }
    };
  },
  props: {
    scrollTop: {
      type: [Number, String],
      default: 0,
    },
    value: {
      type: Boolean,
      default: false,
    },
    isShare: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value (val) {
      this.show = val;
      this.getCount()

    },
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '21', '抽签机会不足');
    this.show = this.value
  },
  methods: {
    close () {
      this.show = false;
      this.$emit('input', false);
      this.$emit('close', false);
    },
    toStudy () {
      this.$yzStatistic('sprintAct.base.click', '23', '抽签机会不足-去学习');
      this.close();
      document.documentElement.scrollTop = this.scrollTop;
    },
    openShare () {
      this.$yzStatistic('sprintAct.base.click', '24', '抽签机会不足-去分享');

      this.close();
      this.$emit('openShare');
    },
    async getCount () {
      const { code, body } = await this.$http.post("/us/drawAndStudyAndShareCount/1.0/", { scholarship: 2522 });
      if (code === '00') {
        this.count = body
      }
    }
  },
};
</script>

<style lang="less">
.yz-ck-getChance {
  padding-top: 0.45rem;
  line-height: 1.2;
  .t1 {
    background: url(../../../../assets/image/active/ckSprint/no-chance.png) no-repeat;
    background-size: 100% 100%;
    width: 1.44rem;
    height: 0.24rem;
    margin: 0 auto;
  }
  .bg-box {
    background: url(../../../../assets/image/active/ckSprint/h1.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 2.89rem;
    font-weight: 600;
    padding-top: 0.32rem;
    position: relative;
    color: #fff;
  }
  .t2 {
    padding-left: 0.31rem;
    margin-bottom: 0.13rem;
  }
  .box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 0.31rem;
    padding-right: 0.26rem;
    margin-bottom: 0.06rem;
    .right {
      width: 0.86rem;
      text-align: center;
      font-size: 0.12rem;
    }
    button {
      width: 0.85rem;
      height: 0.24rem;
      background: #ffaf00;
      font-size: 0.12rem;
      border-radius: 30px;
      text-align: center;
    }
  }
  .t3 {
    padding-left: 0.38rem;
    line-height: 1.2;
    position: absolute;
    width: 100%;
    bottom: 0.61rem;
    left: 0;
  }
  .quotation-marks {
    position: absolute;
    width: 0.17rem;
    height: 0.15rem;
    bottom: 0.97rem;
    left: 0.22rem;
  }
  .quotation-marks2 {
    position: absolute;
    width: 0.17rem;
    height: 0.15rem;
    bottom: 0.43rem;
    right: 0.17rem;
    transform: rotate(180deg);
  }
}
</style>
