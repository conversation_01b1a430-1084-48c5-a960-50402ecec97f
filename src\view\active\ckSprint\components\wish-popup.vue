<template>
  <div>
    <div class="bg" @click="$emit('cancel')" v-if="wishShow"></div>
    <div v-if="wishShow" class="edit">
      <div>
        <div class="edit-box">
          <div class="cl">
            <div class="edit-box-top">
              <span @click="checked=!checked">
                <img src="../../../../assets/image/active/ckSprint/ic_not-select.png" alt="" v-if="!checked">
                <img src="../../../../assets/image/active/ckSprint/ic_select.png" alt="" v-else>
                <span>同步发送到圈子</span>
              </span>
              <button class="fr btn" :disabled="!content" @click="submit">发表</button>
            </div>
            <textarea placeholder="2021成考我必胜，一起加油！" v-model.trim="content" class="wish-input" ref="wistTextarea"
                      @focus="inputFocus"></textarea>
          </div>
        </div>
      </div>
    </div>

  </div>

</template>

<script>

export default {
  props: {
    wishShow: {
      type: Boolean,
      default: false
    },
    isSocketConnect: {
      type: Boolean,
      default: false
    },

  },
  data () {
    return {
      content: '',
      checked: true
    }
  },
  methods: {

    async submit () {
      // let content = this.content;
      if (!this.content) {
        this.$modal({ message: '请输入许愿内容', icon: 'warning' });
        return;
      }
      if (this.content.length > 100) {
        this.$modal({ message: '最多可发送100个字', icon: 'warning' });
        return;
      }
      if (this.checked) this.$yzStatistic('sprintAct.base.click', '27', '弹幕发送');

      const params = {
        scholarship: '2522',
        wishComment: this.content,
        // scholarshipVideoId: this.videoId,
        synCircleStatus: this.checked
      }
      const { code, body } = await this.$http.post("/us/drawWish/1.0/", params);
      if (code === '00') {
        if (this.checked) this.$yzStatistic('sprintAct.base.click', '13', '同步圈子');
        if (body == '2') {
          this.$modal({ message: '发送失败，包含敏感词汇！', icon: 'warning' });
        } else if (body == '1') {
          this.$modal({ message: '发送成功，成考加油吖～' });
          this.content = '';
          this.checked = true;
          this.$emit('load')
          this.$emit('cancel')

        } else {
          this.$modal({ message: '发送失败，网络问题请重试', icon: 'warning' });
        }
      } else {
      }
    },
    inputFocus: function () {
      setTimeout(function () {
        window.scrollTo(0, document.getElementsByTagName('body')[0].offsetHeight);
      }, 600);
    },
    editFocus () {
      this.$refs.wistTextarea.focus();
    },
  },
}
</script>

<style lang="less" scoped>
@import '../../../../assets/less/variable';

.edit-box {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  > .cl {
    max-width: @maxWidth;
    margin: 0 auto;
    padding: 0.07rem 0.15rem;
    background-color: #fff;
    .edit-box-top {
      height: 0.36rem;
      line-height: 0.3rem;
      border-bottom: 1px #e7e7e7 solid;
      margin-bottom: 0.06rem;
    }
  }
  .btn {
    border: none;
    font-size: 0.14rem;
    color: #fff;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    border-radius: 0.05rem;
    padding: 0.06rem 0.08rem;
    &:disabled {
      background: #bfbfbf;
    }
  }

  .wish-input {
    width: 100%;
    height: 0.8rem;
    padding: 0.08rem 0.1rem;
    line-height: 0.2rem;
    font-size: 0.14rem;
    color: #333;
    border-radius: 0.04rem;
    background: #f3f2f2;
    font-weight: 400;
    border: 1px #f3f2f2 solid;
  }
}

.bg {
  position: fixed;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  top: 0;
  opacity: 0.3;
  z-index: 99;
  background-color: rgb(0, 0, 0);
}
.edit {
  position: fixed;
  bottom: 0;
  height: 0.5rem;
  z-index: 999;
  width: 100%;
  max-width: 640px;
}
</style>
