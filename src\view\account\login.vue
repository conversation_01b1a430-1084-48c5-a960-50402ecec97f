<template>
  <div v-if="isShow">
    <div class="login-wrap register" v-if="action === 'bindMobile'">
      <top-bar title="绑定" :showBackButton="false"></top-bar>
      <div class="loginBox">
        <div class="login-item">
          <input type="text" autocomplete="off" placeholder="您的姓名" v-model.trim="realName" />
        </div>
        <div class="login-item">
          <input type="text" autocomplete="off" placeholder="您的手机号" v-model.trim="mobile" style="margin-top: 0.2rem"
            maxlength="13" />
        </div>
        <div class="login-item group">
          <div class="input-wrap">
            <input type="text" autocomplete="off" placeholder="请输入验证码" v-model.trim="valicode" />
          </div>
          <Captcha class="dtn-Captcha" v-on:run="getValicode" ref="btnCaptcha" style="top: 56%" />
        </div>
        <div class="login-item item-btn">
          <button class="btn-submit" :disabled="!realName || !mobile || !valicode" @click="bindMobile">
            绑定
          </button>
        </div>
      </div>
      <img src="../../assets/image/ic_register_logo.png" alt class="loginIcon" />
      <img src="../../assets/image/ic_register_iphone_sel.png" alt class="phoneIcon" style="top: 41.6%" />
      <img src="../../assets/image/ic_register_verify.png" alt class="verifyIcon" style="top: 49.5%" />
      <img src="../../assets/image/ic_register_iphone_sel.png" alt class="verifyIconText" />
    </div>
    <div class="content" v-else>
      <div class="bg-cont" ref="content">
        <div ref="piccont" class="rqpic" id="piccont">
          <img @load="rotation" :src="item" alt v-for="(item, index) in pic" :key="index" class="pic" />
        </div>
      </div>
      <div class="login-m">
        <div v-if="show" class="start">
          <div class="title">
            <img v-if="inviteId" class="head-img" :src="invite.headImg
    ? invite.headImg + headLimit
    : invite.headImg | defaultAvatar
    " alt="" />
            <p class="username" v-if="inviteId">
              您的好友 {{ invite.nickName }}
            </p>
            <span v-else>登录后更精彩</span>
          </div>
          <div class="dec" v-if="inviteId">
            邀请您登录，与60万学员一起来提升学历
          </div>
          <div class="dec" v-else>
            60万学员的上进故事都在期待与你的相遇
          </div>
          <div class="phone-write">
            <input v-model="mobile" class="set" maxlength="13" autofocus="autofocus" placeholder="请输入手机号" />
            <img src="../../assets/image/upGradelogin/phone.png" alt class="phone" />
            <img src="../../assets/image/upGradelogin/close.png" alt class="close" v-if="mobile" @click="empty" />
          </div>
          <div class="btn" @click="getValicode">
            获取验证码
          </div>
        </div>
        <div v-show="!show" class="next">
          <div class="next-title">已发送至手机{{ mobile }}</div>
          <div class="next-cat" @click="goblack">
            换个手机号进入
            <img src="../../assets/image/upGradelogin/zuo.png" alt class="zuo" />
          </div>
          <div class="tost">
            <btn-captcha class="btn-captcha" v-on:run="getValicode" ref="btnCaptcha" />
          </div>
          <div class="Verification">
            <input id="valicode" v-model.trim="valicode" type="number" ref="valicode" />
            <img src="../../assets/image/upGradelogin/close.png" alt class="close" v-if="valicode"
              @click="remvalicode" />
          </div>
          <div class="agreement" :class="{ isAgree: isAgree }">
            <span @click="isAgree = !isAgree" v-if="login">
              <i class="icons radio1"></i>勾选即视为同意
            </span>
            <span v-else>登录即视为同意</span>
            <router-link class="name" :to="{ name: 'newAgreement' }">《远智教育用户服务协议》</router-link>
            <router-link class="name" :to="{ name: 'privacyAgreement' }">《用户隐私协议》</router-link>
          </div>
          <div class="btn" v-show="login" @click="goLogin">
            注册
          </div>
        </div>
      </div>
    </div>
    <!-- 绑定身份证 -->
    <div class="bind-idcard" v-if="isBindIdCard">
      <div class="inner">
        <div class="hd">绑定身份证</div>
        <div class="bd">
          <div class="login-item">
            <input type="text" autocomplete="off" placeholder="请输入您的身份证号" v-model.trim="idCard" />
            <i class="icons i-del" @click="idCard = ''"></i>
          </div>
          <div class="login-item item-btn">
            <button class="btn-submit" :disabled="!idCard" @click="bindIdCard">
              完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <load-bar :isLoading="true" v-else></load-bar>
</template>

<script>
import config from '../../config'
import { isWeixin, isphone, isIDCard, isWeibo, isWxwork } from '../../common'
import { getUrlParam } from '@/utils'
import btnCaptcha from '@/components/loginTime'
import Captcha from '@/components/btnCaptcha'
import topBar from '@/components/topBar'
import loadBar from '@/components/loadBar'
import storage from '../../plugins/storage'
import { setRawCookie, getRawCookie } from 'tiny-cookie'

export default {
  data() {
    return {
      appid: config.appid,
      code: null, // 微信授权得到的code
      inviteId: '',
      scope: '',
      realName: '', // 姓名
      mobile: '', // 手机号
      valicode: '', // 手机验证码
      isShow: false, // 是否显示登录、注册界面
      token: '', // 微信授权后生成的token
      bindType: '1', // 绑定类型：1-手机注册、2-微信授权、3-QQ授权、4-微博授权、5-其他开发平台
      redirect: '', // 回调地址
      action: 'login', // 用来判断登录还是注册
      scholarship: '', // 注册入口，如果用户是通过分享链接进行注册则填入该值（分享链接后面会接上这个参数）
      idCard: '', // 身份证号码
      isBindIdCard: false, // 是否需要绑定身份证
      oldUrl: '',
      invitedUrl: '',
      captcha: '',
      timestamp: '',
      regChannel: 3,
      registerPath: '',
      paln: '',
      regOrigin: '',
      isAgree: false,
      startTime: 0,
      pic: [
        require('../../assets/image/upGradelogin/bgrivers/1.png'),
        require('../../assets/image/upGradelogin/bgrivers/2.png'),
        require('../../assets/image/upGradelogin/bgrivers/3.png'),
        require('../../assets/image/upGradelogin/bgrivers/4.png'),
        require('../../assets/image/upGradelogin/bgrivers/5.png'),
        require('../../assets/image/upGradelogin/bgrivers/6.png'),
        require('../../assets/image/upGradelogin/bgrivers/7.png'),
        require('../../assets/image/upGradelogin/bgrivers/8.png'),
        require('../../assets/image/upGradelogin/bgrivers/9.png'),
        require('../../assets/image/upGradelogin/bgrivers/10.png'),
        require('../../assets/image/upGradelogin/bgrivers/11.png'),
        require('../../assets/image/upGradelogin/bgrivers/12.png'),
        require('../../assets/image/upGradelogin/bgrivers/13.png'),
        require('../../assets/image/upGradelogin/bgrivers/14.png'),
        require('../../assets/image/upGradelogin/bgrivers/15.png'),
        require('../../assets/image/upGradelogin/bgrivers/16.png'),
      ],
      show: true,
      login: false,
      bgtimer: null,
      bgHeight: 0,
      ViewHeight: 0,
      agreement: '',
      swiperOption: {
        direction: 'vertical',
        speed: 6500,
        freeMode: true,
        loop: true,
        autoplay: {
          delay: 0,
          disableOnInteraction: false,
        },
      },
      timer: '',
      repettition: true,
      headLimit: '?x-oss-process=image/resize,m_fixed,h_76,w_76',
      invite: {},
      flag: false,
      regExtParam: '',
      birthdayUserId: '',
      recruitType: '',
      recruitWxCode: ''
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      // 通过 `vm` 访问组件实例,将值传入oldUrl
      if (from.path) {
        vm.oldUrl = from.path
        storage.setItem('registerPath', from.path)
      }
    })
  },
  created() {
    this.recruitType = this.$route.query.recruitType
    this.startTime = Date.now()
    this.redirect = decodeURIComponent(this.$route.query.redirect || '')

    this.inviteId = (
      window.sessionStorage.getItem('inviteId') ||
      decodeURIComponent(
        this.$route.query.inviteId ||
        this.getQueryString(this.redirect, 'inviteId') ||
        ''
      )
    ).replace(/ /g, '+')
    this.getInviteInfo()
    this.agreement = window.sessionStorage.getItem('agreement') || ''
    if (this.agreement) {
      this.show = false
    }
    this.scholarship = this.$route.query.scholarship || ''
    // this.regOrigin = this.$route.query.regOrigin || '';
    if (this.$route.query.regOrigin) {
      if (Array.isArray(this.$route.query.regOrigin)) {
        this.regOrigin = this.$route.query.regOrigin[0]
      } else {
        this.regOrigin = this.$route.query.regOrigin || ''
      }
    } else {
      this.regOrigin = window.sessionStorage.getItem('regOrigin') || ''
    }
    this.regChannel = this.$route.query.regChannel
      ? this.$route.query.regChannel
      : window.sessionStorage.getItem('regChannel')
        ? window.sessionStorage.getItem('regChannel')
        : 3

    this.plan = this.$route.query.plan || ''
    this.registerPath = this.$route.query.registerPath || ''
    this.Advertisement = this.$route.query.plan || ''
    this.action = this.$route.query.action || 'login'
    this.regExtParam = this.$route.query.regExtParam || ''
    this.birthdayUserId = this.$route.query.birthdayUserId || ''
    this.timestamp = Date.now()
    this.mobile = this.storage.getItem('mobile') || ''
    let noAuth = this.$route.query.noAuth || '0'
    if (this.redirect.indexOf('recruitType=1') == -1 || this.redirect.indexOf('recruitType=2') == -1 || this.redirect.indexOf('recruitType=3') == -1 || this.redirect.indexOf('recruitType=4') == -1 || this.redirect.indexOf('recruitType=5') == -1) {
      if (isWxwork() && !this.recruitType) {
        // 企业微信环境下直接手机登陆
        this.isShow = true
      } else if (isWeixin() && this.action !== 'bindMobile' && noAuth != '1' && this.redirect.indexOf('321Activity') == -1 && !this.recruitType) {
        // 微信授权登录
        //if (isWeixin() && this.action !== 'bindMobile'&&noAuth!='1') { // 微信授权登录
        this.code = this.$route.query.code || null
        this.scope = this.$route.query.state || 'snsapi_base'
        if (!this.code) {
          this.gotoAuth();
        } else {
          if (!!this.storage.getItem('authToken')) {
            // 已登录
            if (this.redirect) {
              this.$router.replace({ path: decodeURIComponent(this.redirect) })
            } else {
              if (window.history.length > 2) {
                this.$router.go(-2)
              } else {
                this.$router.go(1)
              }
            }
          } else {
            this.userAuth()
          }
        }
      } else if (isWeixin() && noAuth != '1' && this.redirect.indexOf('321Activity') != -1 && !this.recruitType) {
        this.code = this.$route.query.code || null
        this.scope = this.$route.query.state || 'snsapi_base'
        if (!this.code) {
          this.gotoAuth()
        } else {
          if (!!this.storage.getItem('ykAuthtoken')) {
            // 已登录
            if (this.redirect) {
              this.$router.replace({ path: decodeURIComponent(this.redirect) })
            } else {
              if (window.history.length > 2) {
                this.$router.go(-2)
              } else {
                this.$router.go(1)
              }
            }
          } else {
            this.userAuth2()
          }
        }
      } else if (isWeibo() && noAuth != '1' && !this.recruitType) {
        this.code = this.$route.query.code || null
        if (!this.code) {
          this.gotoWeiboAuth()
        } else {
          if (!!this.storage.getItem('authToken')) {
            // 已登录
            if (this.redirect) {
              this.$router.replace({ path: decodeURIComponent(this.redirect) })
            } else {
              if (window.history.length > 2) {
                this.$router.go(-2)
              } else {
                this.$router.go(1)
              }
            }
          } else {
            this.weiboLogin()
          }
        }
      } else {
        // 手机号码登录
        this.isShow = true
      }
    } else {
      // 手机号码登录
      this.isShow = true
    }

    // 获取授权码，如果Code为空，则重新获取
    this.recruitWxCode = getUrlParam()['code']
    console.log('created--初始化--获取Code', getUrlParam())
    if (!this.recruitWxCode) {
      this.getWxAppId()
    } else {
      const recruitType = getUrlParam()['recruitType']
      if (recruitType) {
        this.recruitType = recruitType
        this.redirect = `/student?recruitType=${recruitType}`
      }
    }
    // if (this.mobile) {
    //   this.getReg()
    // }
  },
  mounted() {
    // if (this.action != "bindMobile") {
    //   this.$nextTick(() => {
    //     this.rotation();
    //   });
    // }
  },
  beforeDestroy() {
    this.bgclear()
    this.storage.removeItem('agreement')
    window.sessionStorage.removeItem('agreement')
  },
  methods: {
    // 重定向获取微信appid
    getWxAppId() {
      const recruitType = getUrlParam()['recruitType']
      // 如果没有学籍信息，则不请求
      if (!recruitType) return
      // 反之，请求
      this.$http.post('/us/getMpAppId/1.0/', { recruitType })
        .then((res) => {
          const url = window.location.origin + `/login?recruitType=${recruitType}&redirect=/student?recruitType=${recruitType}`
          console.log('重定向获取参数', res?.body, url)
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res?.body || ''}&redirect_uri=${url}&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect`
        })
    },
    remvalicode() {
      this.valicode = ''
    },
    phone(item) {
      var pre = item.substring(0, 3)
      var next = item.substring(3, 7)
      var last = item.substr(7, item.length)
      item = pre + ' ' + next + ' ' + last
      return item
    },
    goblack() {
      this.mobile = this.phone(this.mobile)
      if (this.isAgree) {
        this.isAgree = false
      }
      this.$refs.btnCaptcha.clearTimer()
      this.show = true
      this.getWxAppId()
    },
    bgclear() {
      clearInterval(this.bgtimer)
    },
    getReg() {
      this.mobile = this.mobile.trim()
      this.mobile = this.mobile.replace(/\s+/g, '')
      if (!isphone(this.mobile)) {
        this.$modal({
          message: '请输入正确的手机号码',
          icon: 'warning',
          duration: 1000,
        })
        this.mobile = ''
        return
      }
      this.storage.setItem('mobile', this.mobile)
      this.$http
        .post('/us/checkLoginOrRegister/1.0/', { mobile: this.mobile })
        .then((res) => {
          if (res.code != '00') {
            return
          }
          // 兼容原有逻辑处理
          const obs = getUrlParam()
          if(obs.recruitType && !res.body){
            this.$router.push({ path: '/student/guidePageLogin', query: obs })
            return
          }
          // const recruitType = this.$route.query?.redirect?.indexOf('recruitType=1')
          // let redirect = recruitType != -1 && recruitType !== undefined
          // // 不存在，则通过search取值
          // if (!redirect) {
          //   redirect = obs['recruitType'] === 1
          // } else {
          //   obs['recruitType'] = 2
          // }
          
          // if (redirect && !res.body) {
          //   this.$router.push({ path: '/student/guidePageLogin', query: obs })
          //   return
          // }
          if (!res.body) {
            this.login = true
          } else {
            this.login = false
          }

          // setTimeout(function(){ this.datemassage=false,this.show = false;},1000)

          if (this.show) {
            this.mobile = this.phone(this.mobile)
          }
        })
    },
    empty() {
      this.mobile = ''
    },
    rotation() {
      if (this.action != 'bindMobile' && !this.flag) {
        this.flag = true
        setTimeout(() => {
          const differenceHeight = 3510
          this.bgtimer = setInterval(() => {
            this.bgHeight = this.bgHeight - 0.3
            this.$refs.piccont.style.top = this.bgHeight + 'px'
            if (-this.bgHeight > differenceHeight - 2) {
              this.bgHeight = 0
            }
          }, 16)
        }, 400)
      }
    },
    weiboLogin() {
      this.$http
        .post('/us/loginByWeibo/1.0/', { code: this.code })
        .then((res) => {
          if (res.code === '00') {
            // 授权成功
            this.cacheLoginInfo(res.body)
            this.redirection()
          } /*else if (res.code === 'E00011') { // 没有绑定手机号的老用户，需要绑定手机号
            this.isShow = true;
            this.action = 'bindMobile';
          }*/ else {
            this.$modal({ message: res.message, icon: 'warning' })
          }
        })
    },
    userAuth2: function () {
      let data = {
        code: this.code,
        scope: this.scope,
        notPrompt: 1,
      }
      this.$http.post('/us/authYK/1.0/', data).then((res) => {
        if (res.body.code === 'E30003' || res.body.code === 'E30006') {
          // E30003:无用户信息记录，需要重新获取新的code再次请求；  // E30006:用户授权失败
          this.scope = 'snsapi_userinfo'
          this.gotoAuth()
          return
        }
        if (res.code === '00') {
          // 授权成功
          // alert(JSON.stringify(res.body))
          this.cacheLoginInfo(res.body)
          this.redirection()
        } /*else if (res.code === 'E00011') { // 没有绑定手机号的老用户，需要绑定手机号
            this.isShow = true;
            this.action = 'bindMobile';
          }*/ else {
          this.$modal({ message: res.message, icon: 'warning' })
        }
      })
    },
    // 微信授权登录
    userAuth: function () {
      // scope默认上送snsapi_base 后台判断用户是否授权，如果未授权则会返回错误码【E30003】，需要前端重新获取code并上送snsapi_userinfo
      // scope:snsapi_base  授权后者关注公众号后使用(静默授权，只能获取openid)
      // scope:snsapi_userinfo  用户从未授权过时使用
      let data = {
        code: this.code,
        scope: this.scope,
        inviteId: this.inviteId,
        notPrompt: 1,
      }
      this.$http.post('/us/auth/1.0/', data).then((res) => {
        if (res.code === '00') {
          // 授权成功
          if (res.body.code === 'E30003' || res.body.code === 'E30006') {
            // E30003:无用户信息记录，需要重新获取新的code再次请求；  // E30006:用户授权失败
            this.scope = 'snsapi_userinfo'
            this.gotoAuth()
            return
          }
          this.cacheLoginInfo(res.body)
          if (res.body.needBind === '1') {
            // 需要绑定手机号
            this.isShow = true
            if (res.body.token) {
              // 全新用户才会返回此参数
              this.action = 'login'
              this.bindType = '2'
              this.token = res.body.token
            } else {
              // 没有绑定手机的老用户
              this.action = 'bindMobile'
            }
            return
          }
          this.redirection()
        } else {
          window.location.href = `${window.location.origin}/login?inviteId=${this.inviteId}&regOrigin=${this.$route.query.regOrigin}`
          //  this.$router.replace({ name: 'login'})
          this.$modal({ message: res.message, icon: 'warning' })
        }
      })
    },

    // 手机号登录
    goLogin: function () {
      if (this.valicode.length <= 5) {
        if (this.login) {
          this.$modal({ message: '请输入验证码！', icon: 'warning' })
        }
        return
      }
      if (this.login) {
        if (!this.isAgree) {
          this.$modal({ message: '请阅读后勾选协议！', icon: 'warning' })
          return
        }
      }
      this.$indicator.open()
      this.mobile = this.mobile.trim()
      this.mobile = this.mobile.replace(/\s+/g, '')
      //广告推广记录URL
      if (this.plan) {
        var invitedUrl = `${window.location.origin}${this.registerPath
          }?action=${this.action}&scholarship=${this.scholarship}&inviteId=${this.inviteId
          }&source=GDT&plan=${this.$route.query.plan}&name=${decodeURIComponent(
            this.$route.query.shareName
          )}`
      } else {
        var invitedUrl = `${window.location.origin}${this.registerPath
          }?action=${this.action}&scholarship=${this.scholarship}&inviteId=${this.inviteId
          }&regOrigin=${this.regOrigin}&name=${decodeURIComponent(
            this.$route.query.shareName
          )}`
      }
      // 判断是否是从直播分享页而来
      var redirect = this.redirect.startsWith('/app/liveStream')
      if (redirect) {
        invitedUrl = `${window.location.origin}${this.redirect}\n${this.$route.query.liveName}`
      }
      const datas = {
        mobile: this.mobile,
        valicode: this.valicode,
        notPrompt: 1,
        token: this.token,
        regChannel: this.regChannel,
        registerUrl: invitedUrl,
        bindType: this.bindType,
        inviteToken: this.inviteId,
        scholarship: this.scholarship,
        idCard: this.idCard,
        regOrigin: this.regOrigin,
        channelId: window.sessionStorage.getItem('channelId') || '', // 用于统计来源
        TimeOnPage: Date.now() - this.startTime,
        regExtParam: this.regExtParam,
        birthdayUserId: this.birthdayUserId,
      }
      if (this.scholarship) {
        datas.sg =
          (
            (dictJson.scholarship || []).find(
              (val) => val.dictValue === this.scholarship
            ) || {}
          ).ext1 || ''
      }
      this.$http
        .post('/us/login/1.0/', datas)
        .then((res) => {
          this.$indicator.close()
          if (res.code === '00') {
            // 2026级城建奖学金邀约注册绑定判断
            if (getRawCookie('onceRefresh') == 1) {
              setRawCookie('onceRefresh', 2)
            }
            if (getRawCookie('ucEnrollment') == 1) {
              setRawCookie('ucEnrollment', 2)
            }
            this.cacheLoginInfo(res.body)
            if (res.body.userInfo) {
              this.storage.setItem('userId', res.body.userInfo.userId)
            }
            if (res.body.isLogin == '0') {
              const query = this.$route.query
              this.$router.push({
                name: 'joinus',
                query,
              })
            } else {
              this.redirection()

              this.storage.setItem('bindStudent', '0')
            }
          } else if (res.code === 'E30017') {
            // 需要绑定身份证
            this.isBindIdCard = true
            // 2026级城建奖学金邀约注册绑定判断
            if (getRawCookie('onceRefresh') == 1) {
              setRawCookie('onceRefresh', 2)
            }
            if (getRawCookie('ucEnrollment') == 1) {
              setRawCookie('ucEnrollment', 2)
            }
          } else {
            this.valicode = ''
            this.timer = null
            this.$modal({ message: res.message, icon: 'warning' })
          }
        })
        .catch(() => {
          this.$indicator.close()
        })
    },
    recruitTypeLogin() {
      let data = {
        mobile: this.mobile,
        valicode: this.valicode,
        recruitType: this.recruitType,
        code: this.recruitWxCode,
      }
      this.$http.post('/us/loginForMp/1.0/', data)
        .then((res) => {
          this.$indicator.close()
          if (res.code === '00') {
            this.cacheLoginInfo(res.body)
            if (res.body.userInfo) {
              this.storage.setItem('userId', res.body.userInfo.userId)
            }
            if (res.body.isLogin == '0') {
              const query = this.$route.query
              this.$router.push({
                name: 'joinus',
                query,
              })
            } else {
              this.redirection()

              this.storage.setItem('bindStudent', '0')
            }
          } else if (res.code === 'E30017') {
            // 需要绑定身份证
            this.isBindIdCard = true
          } else if (res.code === 'E00098') {
            this.$router.push({ path: '/student/guidePageLogin', query: { recruitType: 1, newCode: '98' } })
          } else {
            this.valicode = ''
            this.timer = null
            this.$modal({ message: res.message, icon: 'warning' })
          }
        })
        .catch(() => {
          this.$indicator.close()
        })
    },
    // 注册
    register: function () {
      this.mobile = this.mobile.trim()
      this.mobile = this.mobile.replace(/\s+/g, '')
      this.$indicator.open()
      var invitedUrl = `${window.location.origin}${this.registerPath
        }?action=${this.action}&scholarship=${this.scholarship}&inviteId=${this.inviteId
        }$name=${decodeURIComponent(this.$route.query.shareName)}`
      const data = {
        regChannel: this.regChannel,
        registerUrl: invitedUrl,
        realName: this.realName,
        mobile: this.mobile,
        valicode: this.valicode,
        bindType: this.bindType,
        token: this.token,
        inviteToken: this.inviteId,
        scholarship: this.scholarship,
        notPrompt: 1,
        idCard: this.idCard,
        channelId: window.sessionStorage.getItem('channelId') || '', // 用于统计来源
      }
      if (this.scholarship) {
        data.sg =
          (
            (dictJson.scholarship || []).find(
              (val) => val.dictValue === this.scholarship
            ) || {}
          ).ext1 || ''
      }
      this.$http.post('/us/register/1.0/', data).then((res) => {
        this.$indicator.close()
        if (res.code === '00') {
          this.$modal({ message: '恭喜您注册成功！' })
          this.cacheLoginInfo(res.body)
          this.storage.setItem('bindStudent', '0')
          this.redirection()
        } else {
          this.$modal({ message: res.message, icon: 'warning' })
        }
        if (res.code === 'E30017') {
          // 需要绑定身份证
          this.isBindIdCard = true
        }
      })
    },
    // 缓存登录信息
    cacheLoginInfo: function (data) {
      if (!data.auth_token && !data.ykAuthtoken) return
      this.storage.clear()
      this.storage.setItem('authToken', data.auth_token || '')
      this.storage.setItem('relation', data.relation)
      this.storage.setItem('headImg', data.userInfo.headImg || '')
      this.storage.setItem('yzCode', data.userInfo.yzCode || '')
      this.storage.setItem('zmcName', data.userInfo.nickname || '未完善')
      this.storage.setItem('realName', data.userInfo.realName || '')
      this.storage.setItem('isBindMobile', data.userInfo.mobile ? '1' : '0')
      this.storage.setItem('mobile', data.userInfo.mobile)
      this.storage.setItem('empId', data.userInfo.pEmpId || '')
      this.storage.setItem('ykAuthtoken', data.ykAuthtoken || '')
      this.storage.setItem('userId', data.userId || '')
      // this.storage.setItem('bindStudent', (data.userInfo.bindStudent));
      // 是否绑定身份证
      this.storage.setItem('oldAuthToken', data.auth_token || '') // 用于在没登录时分享出去的链接没有邀约id
    },
    // 重定向
    redirection: function () {
      let redirect = decodeURIComponent(this.redirect || '/home')
      //redirect = redirect.replace(/&?inviteId=[^&]*/g, '').replace(/\?&/g, '?');
      redirect = redirect
        .replace(/(&?inviteId=[^&]*)|(&?authToken=[^&]*)/g, '')
        .replace(/\?&+/g, '?')
      const { query } = this.$route
      let queryStr = ''
      for (const key in query) {
        if (Object.hasOwnProperty.call(query, key)) {
          // 检测是否有这个key
          const item = query[key]
          if (key != 'redirect' && key != 'inviteId' && (item || item == 0)) {
            if (redirect.includes(key + "=")) break //  避免重复添加参数
            queryStr += `&${key}=${item}`
          }
        }
      }
      if (
        this.storage.getItem('isBindMobile') !== '1' &&
        (redirect.startsWith('/invite') ||
          redirect.startsWith('/active/adultExam') ||
          redirect.startsWith('/active/scholarship'))
      ) {
        // 未绑定手机号老用户访问报读邀约页时要先绑定手机
        this.$modal({ message: '尚未绑定手机，请先绑定', icon: 'warning' })
        this.isShow = true
        this.action = 'bindMobile'
      } else if (
        redirect.startsWith('/app/adExtend') ||
        redirect.startsWith('/app/signShare') ||
        redirect.startsWith('/app/circleAd')
      ) {
        window.location.replace(
          `${window.location.origin}${this.$route.query.redirect}`
        )
      } else {
        if (redirect.indexOf('?') == -1) {
          window.location.replace(
            `${window.location.origin}${redirect}?inviteId=${this.inviteId}${queryStr}`
          )
        } else {
          window.location.replace(
            `${window.location.origin}${redirect}&inviteId=${this.inviteId}${queryStr}`
          )
        }
      }
    },
    randomStr() {
      let non = ''
      for (let i = 0; i < 8; i++) {
        non += String.fromCharCode(Math.floor(Math.random() * 27) + 65)
      }
      return non
    },
    // 获取手机验证码
    getValicode: function () {
      this.getReg()
      this.mobile = this.mobile.trim()
      this.mobile = this.mobile.replace(/\s+/g, '')
      if (!isphone(this.mobile)) {
        this.$modal({ message: '请输入正确的手机号码', icon: 'warning' })
        return
      }
      this.valicode = ''
      this.$refs.btnCaptcha.start()
      if (this.repettition == false) {
        return
      }
      this.repettition = false
      if (this.$route.query.recruitType) {

      }
      this.$http
        .post('/us/authCode/1.0/', { mobile: this.mobile, notPrompt: 1 })
        .then((res) => {
          this.repettition = true
          res.code !== '00' && this.$refs.btnCaptcha.clearTimer()
          if (res.code == 'E30015') {
            this.valicode = ''
            this.action = 'register'
          } else if (res.code == 'E30022') {
            this.$modal({ message: res.message, icon: 'warning' })
            return
          }
          this.datemassage = true
          const that = this
          this.$modal({ message: '短信已发送', duration: 1000 })
          that.show = false
          setTimeout(() => {
            this.$refs.valicode.focus()
          }, 1000)
          this.getReg()
        })
    },
    // 登录、注册切换
    toggle: function (action) {
      this.valicode = ''
      this.action = action
    },
    // 老用户绑定手机号
    bindMobile: function () {
      this.$indicator.open()
      this.mobile = this.mobile.trim()
      this.mobile = this.mobile.replace(/\s+/g, '')
      const data = {
        realName: this.realName,
        mobile: this.mobile,
        valicode: this.valicode,
        inviteToken: this.inviteId,
        scholarship: this.scholarship,
        notPrompt: 1,
        idCard: this.idCard,
      }
      this.$http.post('/us/bindMobile/1.0/', data).then((res) => {
        this.$indicator.close()
        if (res.code === '00') {
          //this.$modal({message: '恭喜您注册成功！'});
          this.cacheLoginInfo(res.body)
          this.redirection()
        } else {
          this.$modal({ message: res.message, icon: 'warning' })
        }
        if (res.code === 'E30017') {
          // 需要绑定身份证
          this.isBindIdCard = true
        }
      })
    },
    // 绑定身份证
    bindIdCard: function () {
      if (!isIDCard(this.idCard)) {
        this.$modal({ message: '请输入正确的身份证号码', icon: 'warning' })
        return
      }
      if (this.action === 'register') {
        this.register()
      } else if (this.action === 'bindMobile') {
        this.bindMobile()
      }
    },
    // 获取url参数
    getQueryString: function (url, name) {
      let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
      let r = url.substr(1).match(reg)
      if (r != null) return decodeURI(r[2])
      return null
    },
    // 微信静默授权
    gotoAuth: function () {
      let redirectUri = window.location.href
        .replace(/(&?code=[^&]*)|(&?state=[^&]*)/g, '')
        .replace(/\?&+/g, '?')
      window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.appid
        }&redirect_uri=${encodeURIComponent(
          redirectUri
        )}&response_type=code&scope=${this.scope}&state=${this.scope
        }#wechat_redirect`
    },
    //微博授权
    gotoWeiboAuth: function () {
      //https://api.weibo.com/oauth2/authorize?client_id=3102704802&response_type=code&redirect_uri=http://test0-zm.yzwill.cn/login
      let redirectUri = window.location.href
        .replace(/(&?code=[^&]*)|(&?state=[^&]*)/g, '')
        .replace(/\?&+/g, '?')
      window.location.href = `https://api.weibo.com/oauth2/authorize?client_id=${config.appKey
        }&response_type=code&redirect_uri=${encodeURIComponent(redirectUri)}`
    },
    getWeiboToken: function () {
      let redirectUri = window.location.href
        .replace(/(&?code=[^&]*)|(&?state=[^&]*)/g, '')
        .replace(/\?&+/g, '?')
      this.$http
        .post('https://api.weibo.com/oauth2/access_token', {
          client_id: config.appKey,
          client_secret: config.client_secret,
          grant_type: 'authorization_code',
          code: this.code,
          rediect_url: redirectUri,
        })
        .then((res) => {
          if (res.code == 200) {
            this.getUseInfo(res.body)
          }
        })
    },
    //获取微博用户信息
    getUseInfo(data) {
      this.$http
        .get(
          `https://api.weibo.com/2/users/show.json?access_token=${data.access_token}&uid=${data.uid}`
        )
        .then((res) => { })
    },
    getInviteInfo() {
      if (!this.inviteId) {
        return
      }
      this.$http
        .post('/us/getInviteInfo/1.0/', { inviteToken: this.inviteId })
        .then((res) => {
          const { code, body } = res
          if (code !== '00') return
          this.invite = body || {}
        })
    },
  },
  components: { btnCaptcha, loadBar, topBar, Captcha },
  watch: {
    mobile: function (newValue) {
      if (newValue.trim().length > 11) {
        this.mobile = newValue.substr(0, 11)
      }
    },
    valicode: function (newValue) {
      if (newValue.trim().length == 6 && !this.login) {
        // if (!this.login&&this.frist) {
        //   this.frist=false
        //   this.goLogin();
        // }
        if (this.timer) {
          clearTimer(this.timer)
          return
        } else {
          this.timer = setTimeout(() => {
            const redirectRecruitType = this.redirect?.split('recruitType=')?.[1]?.split('&')?.[0]
            if (this.recruitType || [1, 2, 3, 4, 5].includes(redirectRecruitType*1)) {
              // 简化判断，同时 this.redirect 中的 recruitType 兜底赋值 this.recruitType
              this.recruitType = this.recruitType ?? redirectRecruitType
              this.recruitTypeLogin()
            } else {
              this.goLogin()
            }
          }, 500)
        }
      } else if (newValue.trim().length > 6) {
        this.valicode = newValue.substr(0, 6)
      }
    },

    mobile(newValue, oldValue) {
      if (newValue > oldValue) {
        if (newValue.length === 4 || newValue.length === 9) {
          var pre = newValue.substring(0, newValue.length - 1)
          var last = newValue.substr(newValue.length - 1, 1)
          this.mobile = pre + ' ' + last
        } else {
          this.mobile = newValue
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
@import '../../assets/less/variable';

.checkinput {
  border: 0.01rem solid rgba(255, 255, 255, 1) !important;
}

.h1 {
  text-align: center;
  margin: 0.11rem 0 0.2rem;
  color: #444;
  font-size: 0.25rem;
}

.login-wrap {
  // position:relative; min-height:100vh; padding:.44rem 0 3.33rem; background:#fff url(../../assets/image/bg-login.png) no-repeat bottom center; background-size:100%;
  position: relative;
  min-height: 100vh;
  padding: 0.44rem 0 3.33rem;
  background: #fff url(../../assets/image/ic_register_bg.png) no-repeat top center;
  background-size: 100%;

  .loginBox {
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 0px 2px 14px 0px rgba(0, 0, 0, 0.15);
    margin: 1.8rem 0.15rem 0 0.15rem;
    position: relative;

    .captcha {
      width: 1.28rem;
      height: 0.44rem;
    }

    .ps {
      padding-top: 0.2rem;
      text-align: center;
      color: #666;
      font-size: 0.13rem;
    }

    // 注册
    &.register {
      padding-top: 0;
    }

    &.active {
      background-image: none;

      .btn-tologin {
        color: #198381;
      }

      .btn-captcha {
        color: #198381;
        background-color: #fff;
      }

      .btn-submit {
        background-color: #efe142;
        color: #333;
        background-image: linear-gradient(to bottom right, #efe142, #ffd100);
      }
    }
  }

  .dtn-Captcha {
    position: absolute;
    text-align: center;
    top: 19.8%;
    right: 0%;
    width: 1.25rem;
    height: 0.44rem;
    color: #f0465d;
    font-size: 0.16rem;
    border: none;
    border-radius: 0.02rem;
    background: none;
    font-weight: 600;
    line-height: 0.44rem;

    &:disabled {
      opacity: 0.8;
      color: gray;
    }
  }

  .loginIcon {
    position: absolute;
    width: 0.8rem;
    top: 22%;
    left: 40%;
  }

  .phoneIcon {
    position: absolute;
    width: 0.32rem;
    top: 33%;
    left: 8%;
  }

  .verifyIcon {
    position: absolute;
    width: 0.32rem;
    top: 41.1%;
    left: 8%;
  }

  .verifyIconText {
    position: absolute;
    width: 0.32rem;
    top: 42.2%;
    left: 8%;
    display: none;
  }
}

.content {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  input::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 1);
    transform: translate(0%, 8%);
  }

  .bg-cont {
    width: 100%;
    height: 100vh;
    overflow-y: hidden;
  }

  .rqpic {
    width: 100%;
    position: absolute;
    z-index: 3;
    // overflow-y: auto;
    //     animation: myfrist 40s;
    //     animation-timing-function: linear;
    //     animation-iteration-count: infinite;
    //      @keyframes myfrist{
    //    0%{top: 0rem};
    //    100%{top: -35.10rem;}
    //  }
  }

  .pic {
    width: 100%;
  }

  .login-m {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 100vh;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9;

    .btn {
      width: 3.34rem;
      height: 0.5rem;
      background: rgba(240, 110, 108, 1);
      border-radius: 0.25rem;
      text-align: center;
      line-height: 0.5rem;
      font-size: 0.17rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(255, 255, 255, 1);
    }

    .start {
      width: 100%;
      height: 100vh;
      padding: 0 0.2rem;
      position: relative;

      .title {
        font-size: 0.2rem;
        font-weight: 600;
        margin-top: 0.2rem;
        color: rgba(255, 255, 255, 1);
        display: flex;
        align-items: center;

        .head-img {
          width: 0.3rem;
          height: 0.3rem;
          border-radius: 50%;
          margin-right: 0.05rem;
        }

        .username {
          width: 3.1rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .dec {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        padding: 0.05rem 0;
        margin-bottom: 1.14rem;
      }

      .phone-write {
        position: relative;
        margin-bottom: 0.3rem;
      }

      .set {
        width: 3.34rem;
        height: 0.5rem;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 0.25rem;
        border: 1px solid rgba(255, 255, 255, 1);
        padding-left: 0.54rem;
        font-size: 0.15rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
      }

      .phone {
        width: 0.32rem;
        height: 0.32rem;
        position: absolute;
        top: 50%;
        left: 0.11rem;
        transform: translate(0, -50%);
      }

      .close {
        width: 0.32rem;
        height: 0.32rem;
        position: absolute;
        top: 50%;
        right: 0.1rem;
        transform: translate(0, -50%);
      }
    }

    .next {
      width: 100%;
      height: 100vh;
      padding: 0 0.2rem;

      .black {
        margin-top: 0.38rem;
        width: 0.32rem;
        height: 0.32rem;
        position: relative;
        left: -0.1rem;
      }

      .next-title {
        margin-top: 0.2rem;
        font-size: 0.24rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
      }

      .next-cat {
        width: 1.24rem;
        margin-top: 0.1rem;
        font-size: 0.15rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);

        .zuo {
          width: 0.08rem;
          height: 0.13rem;
          position: relative;
          top: 0.05rem;
          left: 0.04rem;
        }
      }

      .tost {
        height: 0.21rem;
        margin-top: 1.01rem;
        margin-bottom: 0.1rem;

        div {
          font-size: 0.15rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }

      .Verification {
        position: relative;

        #valicode {
          height: 0.5rem;
          background: none;
          z-index: 9;
          border: none;
          width: 3.45rem;
          border-bottom: 0.01rem solid rgba(255, 255, 255, 1);
          font-size: 0.15rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          color: rgba(255, 255, 255, 1);
        }

        .close {
          width: 0.32rem;
          height: 0.32rem;
          position: absolute;
          right: 0;
          top: 0.1rem;
        }

        margin-bottom: 0.1rem;
      }

      .agreement {
        font-size: 0.12rem;
        padding: 0.05rem 0;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);

        .name {
          color: #f06e6c;
          font-size: 0.12rem;
        }

        margin-bottom: 0.29rem;
      }
    }
  }
}

.login-item {
  margin: 0 0.25rem;
  background-color: #fff;

  &:not(:first-child) {
    margin-top: 0.06rem;
  }

  &.item-btn {
    margin-top: 0.35rem;
  }

  input {
    width: 100%;
    height: 0.44rem;
    padding: 0 0.2rem;
    color: #444;
    font-size: 0.14rem;
    border: none;
    border-bottom: 1px solid #efefef;
    margin-top: 0.6rem;

    &:focus {
      border-color: @color;
    }
  }

  &.group {
    display: flex;
    align-items: center;

    .input-wrap {
      flex: 1;
      margin-right: 0.1rem;

      input {
        margin-top: 0.2rem;
      }
    }
  }
}

.protocal {
  padding-top: 0.08rem;
  line-height: 1;
  text-align: center;
  color: #888;
  display: inline-block;
  margin-left: 0.25rem;
  font-size: 0.1rem;

  .name {
    color: #f06e6c;
    font-size: 0.12rem;
  }

  white-space: nowrap;
}

.radio1 {
  width: 0.12rem;
  height: 0.12rem;
  margin-top: 0.03rem;
  margin-right: 0.05rem;
  background-image: url('../../../src/assets/image/upGradelogin/quan.png');
  margin-bottom: 0.15rem
}

.isAgree {
  .radio1 {
    background-image: url('../../assets/image/upGradelogin/check.png');
  }
}

.btn-submit {
  width: 100%;
  height: 0.49rem;
  color: #fff;
  font-size: 0.18rem;
  border: none;
  background-color: #f26662;
  background-image: @bgColor;
  border-radius: 0.02rem;
  margin-bottom: 0.4rem;

  &:disabled {
    opacity: 0.6;
  }
}

.btn-captcha {
  float: left;
  color: white;
  font-size: 0.15rem;
  border: none;
  background: none;
  line-height: 0.21rem;

  &:disabled {
    opacity: 0.8;
    color: gray;
  }
}

.top {
  position: absolute;
  top: 0.09rem;
  font-size: 0.15rem;
  padding-left: 0.32rem;

  .red {
    display: inline-block;
    width: 0.17rem;
    height: 0.17rem;
    vertical-align: middle;
    margin-right: 0.05rem;
    margin-bottom: 0.02rem;

    img {
      width: 100%;
    }
  }

  .btn-toggle {
    position: static;
  }
}

.btn-toggle {
  position: absolute;
  top: 0.09rem;
  right: 0.19rem;
  color: #5aa4d9;
  font-size: 0.15rem;
  font-weight: 700;
}

.btn-tologin {
  margin-right: 0.12rem;
  color: #5aa4d9;
  font-size: 0.15rem;
}

.bind-idcard {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10000;
  padding: 0 0.16rem;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);

  &:after {
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
    content: '';
  }

  >.inner {
    display: inline-block;
    width: 100%;
    margin-top: -0.4rem;
    padding: 0.2rem 0;
    vertical-align: middle;
    text-align: left;
    background-color: #fff;
    border-radius: 0.04rem;
  }

  .hd {
    padding-bottom: 0.2rem;
    text-align: center;
    font-size: 0.16rem;
  }

  .login-item {
    position: relative;
    margin: 0 0.14rem;

    input {
      height: 0.36rem;
    }

    &.item-btn {
      margin-top: 0.4rem;
    }
  }

  .i-del {
    position: absolute;
    top: 0;
    right: 0;
    width: 0.36rem;
    height: 0.36rem;
    background-image: url(../../assets/image/public_ico_delete.png);
  }
}

.hr {
  border-top: 0.08rem solid #f5f6f8;
}

.mainBox /deep/ .swiper-container-free-mode>.swiper-wrapper {
  -webkit-transition-timing-function: linear;
  /*之前是ease-out*/
  -moz-transition-timing-function: linear;
  -ms-transition-timing-function: linear;
  -o-transition-timing-function: linear;
  transition-timing-function: linear;
  margin: 0 auto;
}

.swiper-item {
  position: relative;
  background: transparent;
  width: 100%;
  height: auto;

  .item {
    width: 100%;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}
</style>
