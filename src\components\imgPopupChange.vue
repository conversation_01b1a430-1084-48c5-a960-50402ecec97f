<template>
  <div class="imgPopup" v-if="show" @click="close">
    <div class="inner" @click.stop>
      <div class="bd">
        <img :src="imgSrcs|basePath" alt="">
      </div>
    </div>
    <div class="delete" v-if="showChange" @click.stop>
      <!--<file-upload v-model="pic">-->
      <!--<div class="btn-upload">更换图片</div>-->
      <!--</file-upload>-->
      <webuploader :is2M='true' @upload="uploadImg" :server="server" :picker="'filePickerChange'+type" :type="type">
      </webuploader>
      <div class="btn-upload">更换图片</div>
    </div>
  </div>
</template>

<script>
import config from '../config'
import fileUpload from '@/components/fileUpload';
import webuploader from "@/components/uploader"
export default {
  filters: {
    basePath: function (url) {
      if (!url) return url;
      if (url.indexOf('std/') != '-1') {
        return config.imgBaseURL + url
      } else {
        return config.imgBasePath + url;
      }
    }
  },
  props: {
    server: {
      type: String
    },
    isShow: {
      type: Boolean,
      default: false
    },
    showChange: {
      type: Boolean,
      default: true
    },
    imgSrc: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      show: this.isShow,
      showPic: false,
      imgSrcs: '',
      fileType: '',
      pic: '',
      type: '',
      materialType: ''
    }
  },
  methods: {
    open: function () {
      this.show = true;
    },
    close: function () {
      this.show = false;
    },
    changeImg: function () {
      this.$emit('changeImg', this.fileType, this.pic)
    },
    uploadImg: function (body, type) {
      this.imgSrcs = body;
      this.$emit('changeImg', type, body, this.materialType)
    }
  },
  watch: {
    pic: function (val) {
      this.imgSrcs = val;
      this.$emit('changeImg', this.fileType, this.pic)
    }
  },
  components: { fileUpload, webuploader }
}
</script>

<style lang="less" scoped>
.imgPopup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.7);
  > .inner {
    position: absolute;
    top: 50%;
    right: 0rem;
    left: 0rem;
    max-height: 100%;
    overflow-y: auto;
    transform: translateY(-50%);
    border-radius: 0.04rem;
  }
  > .inner > .bd {
    padding: 0;
    max-height: 70vh;
    overflow: scroll;
    img {
      width: 100%;
    }
  }
  .delete {
    position: absolute;
    bottom: 0.8rem;
    left: 50%;
    transform: translateX(-50%);
    i {
      display: inline-block;
      width: 0.44rem;
      height: 0.44rem;
      background-image: url('../assets/image/ver_ico_del.png');
      background-size: 100%;
    }
  }
}
.btn-upload {
  width: 2.08rem;
line-height: .4rem;
text-align: center;
cursor: pointer;
color: #473A3A;
font-weight: 600;
font-size: 15px;
background: #FFFFFF;
border-radius: 20px;
}
</style>
