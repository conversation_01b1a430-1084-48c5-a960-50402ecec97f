<template>
  <div class="yz-newMain-second">
    <active-container
      ref="activeContainer"
      isNeedScroll
      :link="shareLink"
      :shareTitle="shareTitle"
      :shareDesc="shareDesc"
      :regOrigin="shareRegOrigin"
      :scholarship="scholarship"
      :rollCount="count"
      :levelId="pfsnLevel"
      :headerTabs="headerTabs"
      :initHeadActive="activeIndex"
      :inviteFrom="inviteFrom"
      :bannerResource="bannerImg"
      :bannerHeight="bannerHeight"
      :questionType="activeIndex == 0 ? 'dreamBuild' : 'openUniversity'"
      @getInviteId="getInviteId"
      @tabChange="tabChange"
      @scroll="scroll"
      @footerMethod="footerMethod"
      @enroll="footerEnroll"
      @singleClick="footerEnroll"
    >
      <template v-if="activeIndex == 0">
        <div
          slot="banner"
          class="adult-education-banner"
          :class="{ 'fixed-mt': headFixed }"
        >
          <img
            src="../../../../assets/image/active/enrollAggregate/dreamBuild.png"
            class="dreambuild-img"
            alt=""
          />
          <!-- <num-roll :count='count' class="num-roll" /> -->
          <swiper-enroll right=".1" top="0.16" />
          <banner-list
            :Expired="expired"
            :inviteId="inviteId"
            :pfsnLevel="pfsnLevel"
            :zmBannerType="pfsnLevel == 1 ? 9 : 8"
            @toEnroll="toEnroll"
          />
        </div>
        <div class="gift-box">
          <img
            src="../../../../assets/image/active/2021NewMain/gift.png"
            class="gift"
            alt=""
          />
          <p class="grey">考前辅导课（礼包价199）</p>
          <p class="grey">三本教材（礼包价100）</p>
          <img
            src="../../../../assets/image/active/2021NewMain/299.png"
            class="img299"
            alt=""
          />
          <button class="gift-btn" @click="toPayment">抢购领取</button>
        </div>
      </template>
      <div
        class="white-tabs"
        ref="whiteTabs"
        :class="{ fixed: whiteTabsFixed }"
      >
        <div
          class="item"
          :class="{ active: whiteTabIndex == 0 }"
          @click="whiteTabClick(0)"
        >
          <span v-if="activeIndex == 0">报读方法</span>
          <span v-if="activeIndex == 1">国家开放大学</span>
          <span v-if="activeIndex == 2">招生主页</span>
        </div>
        <div
          class="item"
          :class="{ active: whiteTabIndex == 1 }"
          @click="whiteTabClick(1)"
        >
          {{ whiteTab2Text }}
        </div>
      </div>
      <div :class="{ 'fixed-mt': whiteTabsFixed }">
        <adult-education
          v-if="activeIndex == 0"
          :pfsnLevel="pfsnLevel"
          :whiteTabActive="whiteTabIndex"
          :inviteId="inviteId"
          :scholarship="scholarship"
          :actName="actName"
          :regOrigin="regOrigin"
          :regChannel="regChannel"
        />
        <open-university
          v-if="activeIndex == 1"
          :pfsnLevel="pfsnLevel"
          :whiteTabActive="whiteTabIndex"
          :actName="actName"
          @lookMore="resetBarTop"
          :activeIndex="activeIndex"
        />
        <self-touch
          v-if="activeIndex == 2"
          :inviteId="inviteId"
          :pfsnLevel="pfsnLevel"
          :whiteTabActive="whiteTabIndex"
          :actName="actName"
          :regOrigin="regOrigin"
          :regChannel="regChannel"
        />
      </div>
      <div v-if="activeIndex == 2" slot="question">
        <self-question />
      </div>

      <van-popup v-model="showGuide" class="guide-pop">
        <p class="title">欢迎进入【{{ popText }}】展示页面</p>
        <p class="t2">您可在此选择自己喜欢的院校、专业进行报读哦！</p>
        <div class="btn-box">
          <button @click="showGuide = false">我知道了</button>
        </div>
      </van-popup>
    </active-container>
  </div>
</template>

<script>
import { isStudent, isLogin, toLogin, getIsAppOpen } from '@/common';
import { getActivityInfo } from '@/common/request';
import { toAppLogin } from '@/common/jump';
import openBanner from '@/assets/image/active/2021NewMain/open-banner.png';
import openBanner2 from '@/assets/image/active/2021NewMain/open-banner2.png';
import selfBanner from '@/assets/image/active/2021NewMain/self-banner.png';
import SwiperEnroll from '@/view/active/enrollAggregate/components/swiper-enroll';
import SelfQuestion from '@/view/active/selfTought/components/question';
import ActiveContainer from '../components/active-container';
import NumRoll from '../components/num-roll';
import bannerList from '../components/banner-list';
import AdultEducation from './adult-education';
import OpenUniversity from './open-university';
import SelfTouch from './self-touch';
import statistic from '../statistic.json';
import { Toast } from 'vant';

export default {
  components: {
    ActiveContainer,
    bannerList,
    SwiperEnroll,
    NumRoll,
    AdultEducation,
    OpenUniversity,
    SelfTouch,
    SelfQuestion,
  },
  data() {
    return {
      inviteId: '',
      headerTabs: ['成人高考', '开放教育', '自学考试'],
      activeIndex: 0,
      actName: '',
      count: 0,
      expired: false,
      headFixed: false,
      whiteTabIndex: 0,
      whiteTabsFixed: false,
      whiteTabsTop: 533, // 第一个tab的高度
      pfsnLevel: 5, // 默认专科
      showGuide: false,
      tabFromRouter: null,
      isLogin: isLogin(),
      isAppOpen: false,
      regOrigin: '',
      isShow:false,
    };
  },
  computed: {
    regChannel() {
      // 邀约渠道
      return this.$route.query.regChannel || '';
    },
    shareRegOrigin() {
      // 渠道
      const type = {
        0: '51',
        1: '47',
        2: '45',
      };
      return type[this.activeIndex];
    },
    scholarship() {
      // 优惠类型
      const ship = {
        0: '219', // --changeEnroll--
        1: '148', // 1
        2: '66',
      };
      return ship[this.activeIndex];
    },
    shareLink() {
      return `/active/newMain/newSecondMain?tab=${this.activeIndex}&pfsnLevel=${this.pfsnLevel}`;
    },
    shareTitle() {
      const title = {
        0: '在线报读，名校录取，学信网可查，和我一起提升学历！',
        1: '来国家开放大学读学历，权威有保证！',
        2: '自考精英计划，靠谱的教学质量，学历提升无忧虑。',
      };
      return title[this.activeIndex];
    },
    shareDesc() {
      const desc = {
        0: '远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！',
        1: '报读缴费即可入学，不用参加全国成考，证书学信网可查！',
        2: '自考精英计划是远智教育精心打造的学历提升产品，旨在帮助更多上进青年提升学历，拥抱更好的未来。',
      };
      return desc[this.activeIndex];
    },
    inviteFrom() {
      const type = {
        0: 'dreamBuild',
        1: 'openUniversity',
        2: 'selfTought',
      };
      return type[this.activeIndex];
    },
    bannerImg() {
      console.log(this.pfsnLevel,'this.pfsnLevel')
      const banner = {
        0: '', // 成教是轮播
        1: openBanner,
        2: selfBanner,
      };
      if(this.whiteTabIndex == 1){
        banner[1] = openBanner2
      }
      return banner[this.activeIndex];
    },
    bannerHeight() {
      const height = {
        0: '3.2rem',
        1: '2.98rem',
        2: '3.95rem',
      };
      return height[this.activeIndex];
    },
    whiteTab2Text() {
      const text = {
        0: '成考简介',
        1: '广东开放大学',
        2: '自考简介',
      };
      return text[this.activeIndex];
    },
    recruitType() {
      // 招生类型
      const recruitType = {
        0: '1', // 成教
        1: '2', // 国开
        2: '4', // 自考
      };
      return recruitType[this.activeIndex];
    },
    popText() {
      const text = {
        0: '成考专科', // 成教
        1: '国开专科', // 国开
        2: '自考本科', // 自考
      };
      return text[this.tabFromRouter] || '';
    },
  },
  created() {
    this.pfsnLevel = Array.isArray(this.$route.query.pfsnLevel)
      ? parseInt(this.$route.query.pfsnLevel[1])
      : parseInt(this.$route.query.pfsnLevel); // 必须放这里
    this.regOrigin = this.$route.query.regOrigin || '';
    this.showPopFormTab();
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.getActivityInfo();
    this.getWhiteTabsTop();
    this.setStatistic('browse', 'main');
  },
  watch: {
    activeIndex() {
      this.getActivityInfo();
      this.getWhiteTabsTop();
      this.setStatistic('browse', 'main');
    },
  },

  methods: {
    getInviteId(id) {
      this.inviteId = id;
    },
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    showPopFormTab() {
      const { tab, from } = this.$route.query;
      if (tab >= 0) {
        this.activeIndex = parseInt(tab);
      }
      if (from == 'serialReading') {
        // 来源是高起本
        this.showGuide = true;
        this.tabFromRouter = tab;
      }
    },
    setStatistic(type, key) {
      const pfsnLevel = this.pfsnLevel;
      const secondStatistic = statistic.secondMain[pfsnLevel][this.activeIndex];
      this.$yzStatistic(
        `marketing.base.${type}`,
        secondStatistic[key].id,
        secondStatistic[key].name
      );
    },
    scroll(scrollTop) {
      if (this.activeIndex == 0) {
        this.headFixed = scrollTop > 120; // 邀约头的高度
      }
      this.whiteTabsFixed = scrollTop > this.whiteTabsTop - 25; // - 一半容器高度
    },
    // 去报读页
    toPayment() {
      this.setStatistic('click', '299');
      if (this.expired) {
        this.tips();
        return;
      }
      this.toEnroll();
    },
    footerMethod(method) {
      setTimeout(() => {
        this.setStatistic('click', method);
      }, 500);
    },
    // 脚部按钮的报读
    footerEnroll() {
      if (!this.login()) {
        return;
      }
      if (this.activeIndex != 2) {
        this.toEnroll();
        return;
      }
      // 自考
      const url = window.location.pathname;
      const bindStudent = this.storage.getItem('bindStudent');
      if ((isStudent() && bindStudent == 0) || bindStudent == 1) {
        this.$router.push({ name: 'roleAuth', query: { redirect: url } });
        return;
      }
      const currentRecruitType = this.storage.getItem('recruitType');
      if (currentRecruitType != 4) {
        this.$router.push({
          name: 'selfTought.form',
          query: {
            pfsnLevel: this.pfsnLevel,
            regChannel: this.regChannel,
            regOrigin: this.regOrigin,
          },
        });
      } else {
        this.$router.push({ name: 'stuInfo' });
      }
    },
    toEnroll() {
      const isGk = true && this.scholarship == 148 && this.whiteTabIndex == 1; // 是否是广东开放大学
      if(this.activeIndex == 1) {
        Toast('系统更新中，暂不支持自主报读，请联系助学老师协助报读。')
        return;
      }
      this.$router.push({
        name: 'adultExamEnrollCheck',
        query: {
          activityName: 'scholarship',
          inviteId: this.inviteId,
          action: 'login',
          scholarship: this.scholarship,
          actName: isGk ? '普通全额' : this.actName,
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
          regOrigin: this.regOrigin,
          // unvs: JSON.stringify(unvs),
          recruitType: this.recruitType,
          isGk,
        },
      });
    },
    tips() {
      this.$modal({ message: '活动暂已结束！', icon: 'warning' });
    },
    tabChange(i) {
      this.activeIndex = i;
      // if (i === 1 && this.pfsnLevel === 5) {
      //   this.whiteTabIndex = 1;
      // } else {
      //   this.whiteTabIndex = 0;
      // }

      if (this.whiteTabIndex === 1) {
        this.whiteTabIndex = this.pfsnLevel === 5 ? 1 : 0;
        this.setStatistic('click', 'tab2');
      }
      window.scrollTo(0, 0);
    },
    getWhiteTabsTop() {
      if (!this.whiteTabIndex && !this.pfsnLevel === 5) {
        this.$nextTick(() => {
          const top = this.$refs.whiteTabs.offsetTop;
          this.whiteTabsTop = top;
        });
      }
    },
    whiteTabClick(index) {
      this.whiteTabIndex = index;
      this.isShow =true
      if (index == 1) {
        this.setStatistic('click', 'tab2');
      }
      // 重新获取留言tabbar距离顶部的高度
      this.resetBarTop();
    },
    resetBarTop() {
      this.$nextTick(() => {
        this.$refs.activeContainer.getMsgBarScrollTop();
      });
    },
    async getActivityInfo() {
      this.count = 0; // 需要先重置
      const { code, body } = await getActivityInfo(this.scholarship);
      if (code == '00') {
        this.expired = Date.now() > body.EndTime;
        this.actName = body.actName;
        this.count = parseInt(body.learnCount);
      }
    },
  },
};
</script>

<style lang="less">
.yz-newMain-second {
  min-height: 100vh;
  background: #fff;
  .adult-education-banner {
    position: relative;
  }
  .dreambuild-img {
    position: absolute;
    width: 1.1rem;
    top: 0.1rem;
    left: 0.1rem;
    z-index: 2;
  }
  .fixed-mt {
    margin-top: 0.5rem;
  }
  .num-roll {
    color: #c12500;
    position: absolute;
    top: 0.1rem;
    width: auto;
    right: 0.1rem;
    z-index: 2;
  }
  .gift-box {
    padding: 0.19rem 0.15rem 0.14rem;
    background: #fff;
    border-radius: 0.1rem 0.1rem 0 0;
    color: rgba(0, 0, 0, 0.6);
    font-size: 0.12rem;
    position: relative;
    z-index: 1;
    margin-top: -0.1rem;
    border-bottom: 0.1rem solid #f2f2f2;
    .gift {
      width: 1.55rem;
      height: 0.18rem;
      margin-bottom: 0.1rem;
    }
    .grey {
      width: 2.17rem;
      height: 0.19rem;
      line-height: 0.19rem;
      background: rgba(0, 0, 0, 0.05);
      margin-top: 0.06rem;
      border-radius: 100px;
      padding-left: 0.1rem;
    }
    .img299 {
      position: absolute;
      top: 0.23rem;
      right: 0.16rem;
      width: 1.03rem;
      height: 0.19rem;
    }
  }
  .gift-btn {
    position: absolute;
    bottom: 0.15rem;
    right: 0.15rem;
    width: 0.8rem;
    height: 0.36rem;
    text-align: center;
    color: #fff;
    font-size: 0.14rem;
    font-weight: 500;
    border-radius: 0.05rem;
    background: linear-gradient(180deg, #ffbf3c 0%, #f88215 100%);
  }
  .white-tabs {
    color: #000;
    font-size: 0.14rem;
    background: #fff;
    display: flex;
    position: relative;
    z-index: 5;
    border-bottom: 1px solid rgba(23, 6, 6, 0.08);
    &.fixed {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }
    .item {
      height: 0.5rem;
      line-height: 0.5rem;
      flex: 1;
      text-align: center;
      position: relative;
      z-index: 3;
      &.active {
        color: #b6161b;
        &::after {
          content: '';
          height: 0.02rem;
          width: 0.4rem;
          border-radius: 10px;
          background: #b6161b;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }
  .guide-pop {
    border-radius: 0.05rem;
    padding: 0.3rem 0.16rem 0.2rem;
    width: 2.86rem;
    text-align: center;
    .title {
      font-size: 0.16rem;
      margin-bottom: 0.15rem;
    }
    .t2 {
      color: rgba(23, 6, 6, 0.8);
      margin-bottom: 0.23rem;
    }
    button {
      width: 0.9rem;
      height: 0.3rem;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      border-radius: 100px;
      color: #fff;
      font-weight: 600;
    }
  }
}
</style>
