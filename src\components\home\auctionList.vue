<template>
  <div class="home-list">
      <div class="bd clearfix" v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
      <router-link class="item h1"
                   v-for="item in list"
                   :key="item.courseId"
                   :to="{name:'vocationalCourseDetail',params:{id:item.courseId},query:{salesType:createResource,inviteId:inviteId}}">
        <!--<status class="static" :status="item.salesStatus"></status>           -->
        <div class="pic"><img class="img" :src="item.listPicUrl|imgBaseURL"></div>
        <div class="txt">
          <div class="tit1 row2"> <span v-if="item.courseTab">{{item.courseTab?item.courseTab.split(",")[0]:''}}</span>{{item.courseName}}</div>
          <div class="desc">{{item.listPageDesc}}</div>
          <div style="position: absolute;bottom:0;width:100%">
            <div style="float: left;margin-top: 0rem;">
              <!-- :big="true" -->
              <price class="mt" :salesPrice="createResource=='2'?item.zhimi:item.price" :originalPrice="createResource=='2'?'':item.cost"  :nozhimi="createResource!= 2"></price>
            </div>
            <div style="float: right;margin-top: .07rem;margin-right: .11rem">
              <!--<div class="fsc gap1" v-if="item.salesStatus==='2'">开始时间:{{item.startTime|formatDate('yyyy.MM.dd hh:mm')}}</div>-->
              <div class="fsc gap1" v-if="createResource=='2'"><i><img src="../../assets/image/joinman.png" alt=""></i><span class="fc">{{item.purchaseNum}}</span>人兑换</div>
              <div class="fsc gap1" v-else><i><img src="../../assets/image/joinman.png" alt=""></i><span class="fc">{{item.purchaseNum}}</span>人购买</div>
            </div>
          </div>
          <!--<div class="fsc" v-else>库存:{{item.goodsCount}}个</div>-->
        </div>
      </router-link>
      <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="list.length"></load-bar>
    </div>
  </div>
</template>

<script>
  import status from '@/components/status'
  import price from '@/components/price'
  import loadBar from '@/components/loadBarTwo'

  export default {
    props: ['salesType','nozhimi','createResource','inviteId'],
    data() {
      return {
        pageNum: 0,
        pageSize: 10,
        goodsType: '2',
        hotGoosType:'1',
        list: [],
        // list: [],
        hotGoodsList:[],
        isLoading: false,
        allLoaded: false,
        isNoData:true,
      }
    },
    created() {
      this.loadMore();
    },
    methods: {
      getCourseList(){
        this.isLoading = true;
        let data = {
          createResource: this.createResource,
          pageNum:this.pageNum,
          pageSize:this.pageSize,
        };
        this.$http.post('/bds/getVocationalCourseList/1.0/',data).then(res=>{
          if (res.code !== '00') return;
          const datas = res.body || {};
          this.list.push(...(datas || []));
          if(datas.length < 1) {
            this.isNoData = false;
          }
          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        })
      },

      getGoodsList: function () {
        this.isLoading = true;
        let data = {
          salesType: this.salesType,
          pageNum:this.pageNum,
          pageSize:this.pageSize,
          goodsType: this.goodsType
        };

        this.$http.post('/gs/goodsList/1.0/', data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || {};
          this.list.push(...(datas.list || []));
          if(datas.list.length < 1) {
            this.isNoData = false;
          }
          this.$nextTick(() => {
            this.allLoaded = datas.list.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        this.pageNum++;
        this.getCourseList();
      },
      reset: function () {
        this.pageNum = 1;
        this.allLoaded = false;
        this.list = [];
        this.getCourseList();
      }
    },
    watch: {
      goodsType: function () {
        this.reset();
      },
    },
    components: {status, price, loadBar}
  }
</script>
<style lang="less" scoped>
  @import "../../assets/less/variable.less";
  .home-list{
    background-color: white;
    min-height: 62vh;
    padding-bottom: .1rem;
    .hd{
      position:relative; line-height:.3rem; font-size:0;
      &:after{ .borderBottom }
      a{
        display:inline-block; width:25%; text-align:center; color:#999; font-size:.12rem;
        &.active{ color:@color; }
      }
    }
    //.bd{ min-height:50vh; }
    .headBox {
      margin-top:.12rem;
      text-align:center;
      font-weight:600;
      color:rgba(68,68,68,1);
      font-size:.14rem;
      height: .4rem;
      line-height:.4rem;
      .headIcon {
        vertical-align: middle;
        width: .24rem;
        margin-top: -.06rem;
      }
    }
    .item{
      background-color:#fff;
      margin-left:.1rem;
      margin-top:.15rem;
      display:block; position:relative; float:left; width:3.55rem;
      &:before{ display:none }
      &:after{ display:none }
      &.h1{ height:.87rem; }
      &.h2{ height:2.65rem; }
      .pic{ width:1.4rem; height:.87rem; margin:0 auto;float: left;border-radius: .05rem;overflow: hidden;
        img{   object-fit: cover;}
      }
      .txt{width:2.15rem ; padding:0 0rem .12rem .1rem;float: left ;height: .87rem;
        position: relative;}
      .tit1{ max-height:.36rem;  line-height:.18rem;color: rgba(23, 6, 6, 1);font-weight: bold ;font-size: .14rem;
      span{
        padding: 0 .06rem;
        margin-right: .05rem;
        background-image: linear-gradient(to right,rgba(250, 196, 168, 1),rgba(255, 146, 86, 1));
        border-radius: .04rem;
        color: white;
        font-size: .12rem;
        height: .18rem;
        line-height: .18rem;
      }}
      .desc{font-size: .12rem;color: rgba(23, 6, 6, .6);height: .15rem;overflow: hidden}
    }
    .gap1{ margin-top:.04rem; }
    .fsc{ color:rgba(23, 6, 6, .6); font-size:.11rem;line-height: .2rem;
      i{
        display: inline-block;
        width: .2rem;
        height: .2rem;
        img{width: 100%}
      }
    }
    .fc{ color:rgba(23, 6, 6, .6); }
  }
</style>
