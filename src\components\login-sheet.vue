<template>
  <van-action-sheet class='yz-login-sheet' v-model="loginShow">
    <!-- 页内登录 -->
    <div class="login-close" @click="close">
        <img src="@/assets/image/app/applive/close.png" alt />
    </div>
    <div class="logo-box">
      <img class="logo-img" src="@/assets/image/app/applive/logo.png" alt />
    </div>
    <div class="login-text">
      <div class="login-title">
        登录/注册
      </div>
      <div class="login-input">
        <span class="input-title">手机号码</span>
        <input type="value" id="mobile" ref="mobile" v-model="mobile" class="input-content"  placeholder="输入有效手机号" maxlength="13" @focus="focusMobile" @blur="blurMobile">
        <span class="get-code" @click="getValicode()" v-if="!isCountdown">获取验证码</span>
        <van-count-down
          v-else 
          :time="countTime" 
          class="get-code"
          format="sss"
          @finish="isCountdown = false"
          />
      </div>
      <div class="line" ref="mobileLine"></div>
      <div class="login-input">
        <span class="input-title">验证码</span>
        <input type="value" id="valicode" ref="valicode" v-model="valicode" class="input-content"  placeholder="输入6位验证码" maxlength="6" @focus="focusValicode" @blur="blurValicode">
      </div>
      <div class="line" ref="valicodeLine"></div>
      <div class="login-box" @click="goLogin">
        <button class="login-submit">提交</button>
      </div>
    </div>

  </van-action-sheet>
</template>

<script>
import { isLogin, isphone } from '@/common';

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  
  data() {
    return {
      loginShow: false,
      mobile: '',
      valicode: '',
      countTime: 60 * 1000,
      isCountdown:false,
      isLogin: isLogin(),
    }
  },
  watch: {
    value(val) {
      this.loginShow = val;
    },
  },
  mounted() {
    this.inviteId = this.$route.query.inviteId || "";
    this.loginShow = this.value;
  },
  methods: {
    close() {
      this.$emit('input', false);
      this.loginShow = false;
    },
    focusMobile() {
      this.$refs.mobileLine.style.background = '#FF6467'
    },
    blurMobile() {
      this.$refs.mobileLine.style.background = '#EBEBEB'
    },
    focusValicode() {
      this.$refs.valicodeLine.style.background = '#FF6467'
    },
    blurValicode() {
      this.$refs.valicodeLine.style.background = '#EBEBEB'
    },
    toLogin(){
      if(!this.isLogin){
        // var liveName = this.detail.name
        var inviteId = this.inviteId
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            scholarship: this.$route.query.scholarship || "",
            regChannel: this.$route.query.regChannel || "",
            // liveName: liveName,
            regOrigin: this.$route.query.regOrigin || '',
            inviteId: inviteId || "",
            action: "",
          },
        });
      }
      return;
    },
    // 获取验证码
    getValicode() {
      var mobile = this.mobile.trim();
      mobile = mobile.replace(/\s+/g, "");
      if (!isphone(mobile)) {
        this.$modal({ message: "请输入正确的手机号码", icon: "warning" });
        return;
      }
      this.valicode = "";
        // 判断是否为老用户
      this.$http
          .post("/us/checkLoginOrRegister/1.0/", { mobile: mobile })
          .then((res) => {
            if (res.code != "00") {
              return;
            }
            if (res.body) {
              this.login = true;
              this.isCountdown = true
            } else {
              this.login = false;
              this.storage.setItem("mobile", mobile);
              this.toLogin()
              return
      }
      this.$http
      .post("/us/authCode/1.0/", { mobile: mobile, notPrompt: 1 })
      .then((res) => {
          this.$modal({ message: "短信已发送", duration: 1000 });
      })
      setTimeout(() => {
          this.$refs.valicode.focus();
      }, 1000);
      this.storage.setItem("mobile", mobile);
          // setTimeout(function(){ this.datemassage=false,this.show = false;},1000)
      if (this.show) {
        this.mobile = this.phone(mobile);
      }
      });
    },
    // 登录
    goLogin() {
      if (this.valicode.length <= 5) {
        if (this.login) {
          this.$modal({ message: "请输入验证码！", icon: "warning" });
        }
        return;
      }
      var mobile = this.mobile.trim();
      mobile = mobile.replace(/\s+/g, "");
      var registerUrl = `${window.location.origin}${this.$route.fullPath}` ;
      const datas = { 
        mobile: mobile,
        valicode: this.valicode, 
        notPrompt: 1,
        token: '',
        regChannel: this.$route.query.regChannel || "",
        registerUrl: registerUrl,
        bindType: "1",
        inviteToken: this.inviteId,
        scholarship: this.$route.query.scholarship,
        idCard: '',
        regOrigin: this.$route.query.regOrigin || '',
        channeId: this.channelId,
        regExtParam: this.$route.query.regExtParam || "",
        birthdayUserId: '',
      }
      this.$http
        .post("/us/login/1.0/", datas)
        .then((res) => {
          if(res.code === "00"){
              this.isAudition = false
              if(res.body.auth_token){
                this.storage.setItem("authToken", res.body.auth_token);
                this.isLogin = !!this.storage.getItem("authToken");
                this.storage.setItem("relation", res.body.relation);
              }
              if (res.body.userInfo) {
              this.storage.setItem("userId", res.body.userInfo.userId);
              this.storage.setItem("headImg", res.body.userInfo.headImg);
              this.storage.setItem("zmcName", res.body.userInfo.nickname);
              this.storage.setItem("mobile", res.body.userInfo.mobile);
              this.storage.setItem("realName", res.body.userInfo.realName);
              this.storage.setItem("yzCode", res.body.userInfo.yzCode);
              this.storage.setItem("bindStudent", res.body.userInfo.bindStudent);
              }       
            this.loginShow = false
            location.reload();
          }
        })
    },
  },
}
</script>

<style lang="less">
  .yz-login-sheet{
    .login-close {
      width: 100%;
      height: .16rem;
      text-align: right;
      margin-top: .19rem;
      margin-bottom: .04rem;
      padding: 0 .16rem;
    }
    .logo-box {
      width: 100%;
      height: .79rem;
      text-align: center;
      .logo-img {
        width: 1.11rem;
        height: .79rem;
      }
    }
    .login-text {
      width: 100%;
      padding: 0 .25rem;
      margin-top: .42rem;
      .login-title {
        font-size: .22rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: .3rem;
        margin-bottom: .16rem;
      }
      .login-input {
        width: 100%;
        height: .5rem;
        display: flex;
        .input-title {
          margin-right: .23rem;
          width: .6rem;
          font-size: .15rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #333333;
          line-height: .5rem;
        }
        .input-content {
          width: 1.05rem;
          font-size: .15rem;
          // margin-right: .57rem;
          caret-color:#F06E6C;
          border: none;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: .5rem;
        }
        .get-code {
          margin-left: auto;
          margin-right: 0;
          font-size: .15rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FF6467;
          line-height: .5rem;
        }
      }
      .line {
        width: 3.25rem;
        height: .01rem;
        background: #EBEBEB;
      }
      .login-box {
        margin-top: .4rem;
        margin-bottom: .61rem;
        width: 100%;
        height: .4rem;
        text-align: center;
        .login-submit {
          width: 1.2rem;
          height: .4rem;
          background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
          border-radius: .2rem;
          font-size: .17rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #FFFFFF;
          line-height: .4rem;
        }
      }
    }
  }
  
</style>