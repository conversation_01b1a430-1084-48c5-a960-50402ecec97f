<template>
  <div class="bc-w">
    <div class="no-data" v-if="records.length===0">这是最新发起的抽奖，暂无往期记录</div>
    <review-item v-else
                 v-for="(item,index) in records"
                 :key="index"
                 :item="item"
                 :planCount="item.planCount"
                 :time="item.runTime"
                 :headImgUrl="item.headImgUrl">
      <div class="fsc1">中奖人：{{item.userName}}({{item.mobile | hidePhone}})</div>
    </review-item>
  </div>
</template>

<script>
  import reviewItem from '@/components/reviewItem'

  export default {
    props: {
      records: {
        type: Array,
        default: []
      }
    },
    components: {reviewItem}
  }
</script>
