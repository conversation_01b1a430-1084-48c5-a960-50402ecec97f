<!--
 * @Descripttion: 
 * @Author: yanh<PERSON>
 * @Date: 2022-10-20 13:24:55
 * @LastEditors: yanhui
 * @LastEditTime: 2023-07-20 16:37:57
-->
<template>
  <div class='yz-newMain-four'>
    <div class="item" @click='go("secondMain", { pfsnLevel: 5 })'>
      <img class="icon" src="../../../../assets/image/active/2021NewMain/ic_5.png" alt="">
      <p>读大专</p>
    </div>
    <div class="item" @click='go("secondMain", { pfsnLevel: 1 })'>
      <img class="icon" src="../../../../assets/image/active/2021NewMain/ic_1.png" alt="">
      <p>升本科</p>
    </div>
    <!-- <div class="item" @click='go("serialReading")'>
      <img class="icon" src="../../../../assets/image/active/2021NewMain/ic_15.png" alt="">
      <p>高起本</p>
    </div> -->
    <div class="item" @click='go("graduate")'>
      <img class="icon" src="../../../../assets/image/active/2021NewMain/ic_6.png" alt="">
      <p>研究生</p>
    </div>
    <div class="item" @click='major'>
      <img class="icon" src="../../../../assets/image/active/2021NewMain/ic_15.png" alt="">
      <p>职业技能</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    inviteId: String,
    regChannel: String,
    regOrigin: String,
  },
  methods: {
    go(name, query = {}) {
      // 埋点相关
      const obj = {
        pfsn5: { id: 6, name: '首页-读大专' },
        pfsn1: { id: 7, name: '首页-升本科' },
        serialReading: { id: 8, name: '首页-高起本' },
        graduate: { id: 9, name: '首页-在职研究生' },
      };
      const key = name == 'secondMain' ? (query.pfsnLevel == 5 ? 'pfsn5' : 'pfsn1') : name;
      this.$yzStatistic('marketing.base.click', obj[key].id, obj[key].name);
      // 路由相关
      const routeQuery = this.$route.query;
      const final = {
        ...routeQuery,
        ...query,
        inviteId: this.inviteId,
        regChannel: this.regChannel,
        regOrigin: this.regOrigin,
      };
      this.$router.push({
        name,
        query: final,
      });
    },
    major(){
      this.$router.push({ name: "classSelect", query: {goodsTypeId: '34'} })
    }
  },
};
</script>

<style lang="less" scoped>
  .yz-newMain-four{
    margin: -0.75rem 0.1rem 0;
    background: #fff;
    border-radius: 0.1rem;
    box-shadow: 0px 0px 10px 0px rgba(23, 6, 6, 0.1);
    padding: 0.2rem 0.2rem 0.1rem;
    display: flex;
    justify-content: space-between;
    background: #fff;
    position: relative;
    z-index: 2;
    .item{
      text-align: center;
      line-height: 1;
      color: #000;
      font-size: 0.14rem;

    }
    .icon{
      width: 0.5rem;
      height: 0.5rem;
      vertical-align: middle;
      margin-bottom: 0.08rem;
    }
  }
</style>
