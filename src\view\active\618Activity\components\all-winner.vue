<template>
  <dialog-container
    v-model="show"
    title='全部中奖名单'
    @close='close'
    close-on-click-overlay
  >
    <div class="yz-winner-list" :class='{nodata: list.length == 0}'>
      <no-data v-if='list.length == 0' />
      <template v-else >
        <div class="table-head">
          <span>姓名</span>
          <span>尾号</span>
          <span class='w1'>奖品</span>
          <span class='w2'>时间</span>
        </div>
        <ul class="yellow-table table-scroll" v-infinite-scroll="loadMore" :infinite-scroll-disabled="loading" infinite-scroll-distance="10">
          <li v-for="(item, index) in list" :key="index">
            <span class='span'>{{item.userName | hideNickname}}</span>
            <span class='span'>{{item.mobile | subPhone}}</span>
            <span class="w1 span">{{item.prizeVO.prizeName}}</span>
            <span class="w2 span">{{item.lotteryTime | formatTimeByMoment('MM.DD HH:mm')}}</span>
          </li>
        </ul>
      </template>
    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container';

export default {
  components: { DialogContainer },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
    }
  },
  filters: {
    subPhone(val) {
      if (!val) {
        return '';
      }
      return '*' + val.toString().substr(-4);
    },
  },
  watch: {
    value() {
      this.show = this.value;
    },
  },
  methods: {
    close() {
      this.$emit('input', false);
    },
    loadMore() {
      this.$emit('loadMore');
    },
  },
};
</script>

<style lang="less">
  .yz-winner-list{
    padding: 0 0.15rem 0.2rem;
    position: relative;
    &.nodata{
      padding: 0.33rem 0 0.68rem 0;
    }
    .table-head{
      background: rgb(254, 247, 223);
      color: #453838;
      font-size: 0.14rem;
      font-weight: 500;
      display: flex;
      justify-content: space-between;
      span{
        width: 0.55rem;
        height: 0.4rem;
        line-height: 0.4rem;
        display: inline-block;
        text-align: center;
        &:not(:last-child){
          border-right: 1px solid #fff;
        }
        &.w1{
          flex: 1;
        }
        &.w2{
          width: 0.9rem;
        }
      }

    }
    .table-scroll{
      max-height: 3rem;
      overflow: auto;
    }
  }
</style>
