<template>
  <div class="commentBox">
    <div class="comment" v-if="postInfo.showText2">
      <div v-html="postInfo.scText2"></div>...<span @click="toPostApp?toDown():postInfo.showText2 = false">全文</span>
    </div>
    <div class="comment" v-else v-html="postInfo.scText"></div>
    <img-box :scPicUrl="postInfo.scPicUrl" />
    <canvas-video 
      :picUrlsImgWHObj="postInfo.picUrlsImgWHObj" :scVideoUrl="postInfo.scVideoUrl"
      :videoSign="postInfo.videoSign"
      :videoDesc="postInfo.videoDesc"
      :videoCover="postInfo.videoCover"
      :vid="postInfo.id"
      :mtop="mtop"
      :scrollTop="scrollTop"
      ref='canvasVideo'
    />
    <div class="comment-bg run">
      <div :style="{background: 'url(' +imgbaseurl+ postInfo.extContent.runImg + ') repeat-x bottom'}"
           @click.stop="showImgClick(0,[postInfo.extContent.runImg])" class="comment-bg-run"></div>
      <template v-if="postInfo.extContent.historyRun==1">
        <div class="comment-bg-word">
          <div class="comment-bg-word-title">历史跑步·{{Number(postInfo.extContent.distance).toFixed(2)}}KM</div>
          <div class="comment-bg-word-time">
            历史时长·{{postInfo.extContent.runTime}}H </div>
          <div class="comment-bg-word-subtitle">
            历史跑量{{Number(postInfo.extContent.totalRunNum).toFixed(2)}}KM超越了{{postInfo.extContent.transcendNum}}%的上进青年
          </div>
        </div>
      </template>
      <template v-else>
        <div class="comment-bg-word">
          <div class="comment-bg-word-title">今日跑步·{{postInfo.extContent.distance}}KM</div>
          <div class="comment-bg-word-time">
            <div>
              <p>用时</p>
              <p class="t1">{{postInfo.extContent.runTime}}</p>
            </div>
            <div>
              <p>配速</p>
              <p class="t1">{{postInfo.extContent.spendDesc}}</p>
            </div>
            <div>
              <p>步数</p>
              <p class="t1">{{postInfo.extContent.totalSteps || 0}}</p>
            </div>
          </div>
        </div>
        <div class="comment-bg-subtitle">今日超越了{{postInfo.extContent.transcendNum}}%的上进青年，继续加油！</div>
        <img src="../../assets/image/app/circleAd/run-j.png" class="comment-bg-runj" alt="">
      </template>
      <div class="comment-bg-run-total" v-if="postInfo.extContent.historyRun!=1">
        <!-- <img src="../../assets/image/app/circleAd/run-total.png" alt="" srcset=""> -->
        <div class="total-right">
          <img src="../../assets/image/app/circleAd/t-r.png" class="img-tr" alt="">
          <div class="history-run-icon"></div>
          <span>{{Number(postInfo.extContent.totalRunNum).toFixed(2)}}km</span>
        </div>
        <img src="../../assets/image/app/circleAd/t-l.png" class="img-tl" alt="">
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from "vant";
import imgBox from '@/components/post/imgBox';
import { imgBaseURL } from "@/config";
import { downloadApp } from '@/common'
import CanvasVideo from '@/components/post/canvas-video'


export default {
  components: {
    imgBox,
    CanvasVideo
  },
  data () {
    return {
      autoLimit: "?x-oss-process=image/auto-orient,1",
      imgbaseurl: imgBaseURL
    };
  },
  props: {
    postInfo: {
      type: Object,
      default: () => ({})
    },

    toPostApp: {
      type: Boolean,
      default: false
    },
    scrollTop: {
      type: Number,
      default: 0
    },
    mtop: {
      type: Number,
      default: 0
    },
  },
  computed: {},
  watch: {},
  methods: {
    showImgClick (index, arr) {
      arr = arr.map(item => (imgBaseURL + item))
      ImagePreview({
        images: arr,
        startPosition: index,
        closeable: true,
        closeOnPopstate: true
      })
    },
    toDown () {
      if (!!this.storage.getItem("authToken")) {
        downloadApp()
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }
    },

  },
  created () { },
  mounted () {
  },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.commentBox {
  padding: 0.15rem;
  background: #fff;
  /deep/ .emotionimg {
    width: 0.24rem;
    height: 0.24rem;
    vertical-align: bottom;
  }
  .comment {
    font-size: 0.15rem;
    color: rgba(23, 6, 6, 1);
    overflow: hidden;
    text-overflow: ellipsis;
    span {
      color: #f06e6c;
    }

    &-bg {
      &.run {
        background-image: url('../../assets/image/app/circleAd/run-bg.png');
      }
      &.book {
        background-image: url('../../assets/image/app/circleAd/read-bg.png');
        height: 1.05rem;
      }
      // width: 3.43rem;
      height: 1.1rem;
      margin-top: 0.08rem;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-radius: 0.06rem;
      &-subtitle {
        font-size: 0.11rem;
        font-weight: 400;
        color: #F27006;
        // margin-top: 0.14rem;
        background: rgba(219, 219, 219, 0.49);
        text-align: center;
        height: 0.2rem;
        line-height: 0.2rem;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        border-radius: 0 0 0.06rem 0.06rem;
      }
      &-runj{
        position: absolute;
        width: 0.47rem;
        height: 0.43rem;
        opacity: 0.9;
        bottom: 0.08rem;
        right: 0;
      }
      &-run {
        width: 0.7rem;
        height: 0.7rem;
        position: absolute;
        left: 0.14rem;
        top: 0.14rem;
        background-size: 100% !important;
        &-total {
          // width: 1rem;
          // background: linear-gradient(180deg, #e8854b 0%, #cc2725 100%);
          // border-radius: 0 0.05rem 0 0.05rem;
          position: absolute;
          top: -0.08rem;
          right: -0.05rem;
          left: 0;
          padding: 0.04rem;
          font-size: 0.1rem;
          font-weight: 400;
          color: #ffffff;
          line-height: 0.1rem;
          .img-tl{
            float: right;
            width: 0.5rem;
            height: 0.21rem;
          }
          .total-right{
            position: relative;
            color: #fff;
            font-size: 0.1rem;
            float: right;
            padding-right: 0.05rem;
            height: 0.21rem;
            line-height: 0.21rem;
            padding-left: 0.24rem;
            .img-tr{
              position: absolute;
              left: 0;
              top: 0;
              height: 0.21rem;
              width: 100%;
            }
            &>span{
              padding-left: 0.04rem;
              border-left: 1px solid #fff;
              font-weight: 600;
              position: relative;
              z-index: 2;
            }
          }
          .history-run-icon {
            position: absolute;
            left: -0.14rem;
            background: url('../../assets/image/app/circleAd/run-icon.png');
            width: 0.33rem;
            height: 0.1rem;
            background-size: contain;
            top: 50%;
            z-index: 2;
            transform: translateY(-50%);
          }
          span {
            opacity: 0.9;
          }
        }
      }
      &-book {
        height: 0.7rem;
        width: 0.49rem;
        position: absolute;
        left: 0.16rem;
        top: 0.17rem;
        &-total {
          // width: 1rem;
          background: linear-gradient(180deg, #e8854b 0%, #cc2725 100%);
          border-radius: 0 0.05rem 0 0.05rem;
          position: absolute;
          top: 0;
          right: 0;
          padding: 0.04rem;
          font-size: 0.1rem;
          font-weight: 400;
          color: #ffffff;
          line-height: 0.1rem;
          img {
            float: left;
          }
          i {
            float: left;
            width: 0.02rem;
            height: 0.1rem;
            background-color: #fff;
            margin: 0 0.02rem;
            opacity: 0.5;
          }
          span {
            opacity: 0.9;
            margin-left: 0.04rem;
          }
        }
      }
      &-word {
        padding-top: 0.17rem;

        &.pl80 {
          padding-left: 0.8rem;
          padding-top: 0.28rem;
        }
        &-title {
          font-size: 0.14rem;
          font-weight: 600;
          color: #333333;
          line-height: 0.2rem;
          margin-bottom: 0.06rem;
          padding-left: 1.04rem;
          padding-top: 0.03rem;
        }
        &-time {
          font-size: 0.12rem;
          font-weight: 400;
          color: #333333;
          line-height: 0.16rem;
          padding-left: 0.93rem;
          display: flex;
          align-items: center;
          margin-top: 0.05rem;
          &>div{
            width: 0.68rem;
            text-align: center;
            position: relative;
            .t1{
              font-weight: 500;
            }
            &:not(:last-child) {
              &::after{
                width: 1px;
                height: 0.29rem;
                position: absolute;
                right: 0;
                top: 0.03rem;
                content: '';
                background: #DDD8D8;
              }

            }
          }
        }

      }
    }
  }
}
</style>
