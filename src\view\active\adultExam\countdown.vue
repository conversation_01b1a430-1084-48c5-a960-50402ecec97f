<template>
  <div class="countdown">
    <span class="num day">{{day}}</span>
    <span class="num hour">{{hour}}</span>
    <span class="num minute">{{minute}}</span>
    <span class="num second">{{second}}</span>
    <span class="num big millisecond">{{millisecond}}</span>
  </div>
</template>

<script>
  export default {
    name: 'countdown',
    data() {
      return {
        timer: null,
        day: '--',
        hour: '--',
        minute: '--',
        second: '--',
        millisecond: '--',
        endTime: '1519833600000'  // 结束时间：2018/03/01 00:00:00
      }
    },
    created() {
      if (this.endTime) {
        this.timer = setInterval(this.countdown, 100);
      }
    },
    beforeDestroy() {
      this.clearTimer();
    },
    methods: {
      countdown: function () {
        const now = new Date().getTime();
        const surplus = this.endTime - now;
        
        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;
  
        this.day = this.complement(parseInt(s / oneDay));
        this.hour = this.complement(parseInt(s % oneDay / oneHour));
        this.minute = this.complement(parseInt(s % oneDay % oneHour / 60));
        this.second = this.complement(parseInt(s % oneDay % oneHour % 60));
        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      complement: function (num) {
        return num < 10 ? '0' + num : num;
      },
      clearTimer: function () {
        clearInterval(this.timer)
      }
    }
  }
</script>

<style lang="less" scoped>
  .countdown{
    height:.36rem;margin:.3rem .36rem 0;text-align:left;background:url(../../../assets/image/active/adultExam/cd-bg_new.png) no-repeat;background-size:100% 100%;
    .num {
      display:inline-block;width:.27rem;height:.27rem;line-height:.27rem;vertical-align:middle;color:#e40312;font-size:.16rem;
      &.big{width:.34rem;height:.34rem;line-height:.34rem;font-size:.24rem;}
      &.day{margin-left:.16rem;}
      &.hour{margin-left:.18rem;}
      &.minute{margin-left:.22rem;}
      &.second{margin-left:.14rem;}
      &.millisecond{margin-left:.29rem;}
    }
  }
</style>
