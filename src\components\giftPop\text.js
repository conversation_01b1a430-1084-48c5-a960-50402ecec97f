const base = [
  '考前辅导课(礼包价199)',
  '三本辅导教材(礼包价100)',
];
const base2 = [
  '考前辅导课(礼包价199)',
  '三本辅导教材(礼包价100)',
  '成考分过295分,奖励三年全额奖学金劵',
];
const base3 = [
  '考前辅导课(礼包价199)',
  '三本辅导教材(礼包价100)',
  '成考分过295分,奖励三年全额学费奖学金劵',
];
const base4 = [
  '考前辅导课(礼包价199)',
  '三本辅导教材(礼包价100)',
  '成考过295分，奖3年学费奖学金(上述考分不含政策性加分)',
];
const base5 = [
  '考前辅导课(礼包价199)',
  '三本辅导教材(礼包价100)',
  '成考分过295分，免三年学费（考分不含政策性加分）',
]
export default function ({ pfsnLevel, scholarship, unvsId }){
  if (!scholarship) {
    return '';
  }
  if((scholarship==108 || scholarship==141 || scholarship==164) && (unvsId=='52'||unvsId=='55'||unvsId=='153180146776432500'||unvsId=='152989436245450072'||unvsId=='154564027670030437')){
    return base4;
  }
  let text74 = ''
  if (pfsnLevel) {
    if (scholarship == 74) { // 广州商
      text74 = '购买上进礼包，即可获得500元本科奖学金，用于抵扣第三年等值学费';
    }
  }
  const textObj = {
    50: base,
    71: base,
    72: ['领取9000元奖学金后，【购买上进礼包】并【完成见证】可激活使用券（仅限抵扣第二、第三年等值学费）', ...base],
    74: [text74, ...base],
    78: ['报读即可获得补贴，无需缴纳第三年学费；', ...base2, '上述考分不含政策性加分'],
    100: ['购买上进辅导礼包，即可获得1000元上进奖学金，可抵第三年等值学费；', ...base3, '上述考分不含政策性加分'],
    108:[...base],
    141:[...base],
    164:[...base],
    103:['领取9000元奖学金后，【购买上进礼包】并【完成见证】可激活使用券（仅限抵扣第二、第三年等值学费）', ...base4],
    121:['领取3000元奖学金后，【购买上进礼包】并【完成见证】可激活使用券（仅限抵扣第二、第三年等值学费）',...base5],
    123: ['就读后，奖励第二、第三年等值学费', '成考分过295分，就读后，奖励三年学费', ...base, '上述考分不含政策性加分', '凭高校缴费发票申请奖励学费'],
  }
  return textObj[scholarship] || '';
}
