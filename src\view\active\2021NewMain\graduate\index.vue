<template>
  <div class="yz-graduate-main">
    <active-container
    :showQuestion="false"
      mibanPink
      isSlotTabBar
      isNeedScroll
      :isNeedMsgBar='false'
      answerBgColor='#E2DEEA'
      :rollCount='count'
      :bannerResource='bannnerImg'
      :shareTitle="shareTitle"
      :shareDesc="shareDesc"
      :link="shareLink"
      :scholarship="scholarship"
      regOrigin='43'
      @getInviteId='getInviteId'
      @footerMethod='footerMethod'
      @scroll='scroll'
      @singleClick='enroll'
    >
      <!-- 头部栏 -->
      <div slot="tab-bar" class="graduate-top-tab" :class='{fixed: topFixed}'>
        <div
          class="item"
          :class="{active: activeIndex == index}"
          v-for="(item, index) in tabs"
          :key="index"
          @click.prevent="tabBarClick(index)"
        >
          <span>{{item}}</span>
        </div>
        <div class="line" :class="lineClass" ></div>
      </div>
      <!-- 学校和专业 -->
      <g-school v-show='activeIndex == 0' />
      <!-- 报考流程 -->
      <div class="tab-content-2" v-show="activeIndex == 1">
        <img src="../../../../assets/image/active/enrollAggregate/graduate/tqpc.png">
        <img class="img2" src="../../../../assets/image/active/enrollAggregate/graduate/zcpc.png">
      </div>
      <!-- 八大优势 -->
      <div class="tab-content-3" v-show="activeIndex == 2">
        <ul>
          <li v-for="(item, index) in tabContent3ImgList" :key='index'>
            <img :src="item.imgUrl">
            <p class="text-1" >{{item.text1}}</p>
            <p class="text-2" v-html="item.text2"></p>
          </li>
        </ul>
      </div>
      <!-- 问题 -->
      <graduateQuestion v-show='activeIndex == 3' />
      <!-- 留言区 -->
      <!-- <div class="messageHead">
        <img src="../../../../assets/image/active/enrollAggregate/graduate/message.png">
        <span>留言区</span>
      </div> -->
    </active-container>
  </div>
</template>

<script>
import { toAppLogin } from '@/common/jump';
import { isStudent, isLogin, toLogin, getIsAppOpen } from '@/common';
import { getActivityInfo } from '@/common/request';
import appShare from '@/mixins/appShare';
import SwiperEnroll from "@/view/active/enrollAggregate/components/swiper-enroll";
import graduateQuestion from "@/components/activePage/graduateQuestion";
import ActiveContainer from "../components/active-container";
import NumRoll from "../components/num-roll";
import GSchool from "./school";
import bannner from '../../../../assets/image/active/enrollAggregate/graduate/banner.png';
import statistic from '../statistic.json';

export default {
  mixins: [appShare],
  components: {
    NumRoll,
    SwiperEnroll,
    GSchool,
    graduateQuestion,
    ActiveContainer,
  },
  data() {
    return {
      scholarship: '137',
      shareTitle: '上班族在职也不怕，利用空闲时间一起来拿名校双证硕士！',
      shareDesc: '本科毕业三年，专科毕业五年，现在报考，多重优惠，点击了解',
      shareLink: '/active/graduate',
      activeIndex: 0,
      tabs: ['院校专业', '报考流程', '八大优势', '政策咨询'],
      recruitType: '',
      inviteId: '',
      count: 0,
      topFixed: false,
      bannnerImg: bannner,
      isLogin: isLogin(),
      isAppOpen: false,
      regOrigin: '',
      tabContent3ImgList:[
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/ssxw.png"),text1:'硕士学位',text2:`双证毕业，国家学位网可查，<br>含金量最高`},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/szgl.png"),text1:'实战管理',text2:`提升综合管理能力、强化企业<br>运作效率`},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/jyrm.png"),text1:'精英人脉',text2:'汇聚各行各业中高层管理者，<br>优质人脉圈'},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/zyzh.png"),text1:'资源整合',text2:'利用校友资源、名校资源实现<br>1+1＞2效果'},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/ktsy.png"),text1:'开拓视野',text2:'系统学习MBA课程，实战案<br>例剖析，提升格局'},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/szjx.png"),text1:'升职加薪',text2:'起点高、人脉广、资源多、<br>机会自然也多'},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/xymr.png"),text1:'校友名人',text2:'同各知名企业名人高管、政<br>府事业单位领导成为校友'},
        {imgUrl: require("../../../../assets/image/active/enrollAggregate/graduate/tab-3/slbj.png"),text1:'实力背景',text2:'公司上市用于对外学历共识，<br>强化个人背景光环'},
      ],
    };
  },
  computed: {
    lineClass() {
      const cls = {
        1: 'active',
        2: 'active2',
        3: 'active3',
      };
      return cls[this.activeIndex] || '';
    },
  },
  created() {
    this.regOrigin = this.$route.query.regOrigin || '';
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.recruitType = this.storage.getItem('recruitType');
    this.getActivityInfo();
    this.$yzStatistic('marketing.base.browse', statistic.graduate[this.activeIndex].id, statistic.graduate[this.activeIndex].name);
  },
  methods: {
    getInviteId(id) {
      this.inviteId = id;
    },
    tabBarClick(index) {
      this.activeIndex = index;
      this.$yzStatistic('marketing.base.browse', statistic.graduate[index].id, statistic.graduate[index].name);
    },
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    scroll(scrollTop) {
      this.topFixed = scrollTop > 120; // 邀约头
    },
    footerMethod(method) {
      setTimeout(() => {
        this.$yzStatistic('marketing.base.click', statistic.graduate[method].id, statistic.graduate[method].name);
      }, 500);
      if (method == 'enroll') {
        this.enroll();
      }
    },
    enroll() {
      if (!this.login()) {
        return;
      }
      if (this.recruitType != 5) {
        const bindStudent = this.storage.getItem('bindStudent');
        if(['0', '1'].includes(bindStudent) && isStudent()){
          const url = window.location.pathname;
          this.$router.push({ name: 'roleAuth', query: { redirect:url} })
          return
        }
        const { query } = this.$route;
        this.$router.push({ name: "graduateForm", query: { ...query, regOrigin: this.regOrigin } });
        return;
      }
      this.$router.push({ path:"/student" });
    },
    async getActivityInfo() {
      const res = await getActivityInfo(106);
      if (res.code == '00') {
        this.count = parseInt(res.body.learnCount);
      }
    },

  },
};
</script>

<style lang="less" scoped>
  @import './index.less';
</style>
