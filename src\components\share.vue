<template>
  <transition name="fade">
    <div class="share" v-if="isShow&&showShare" @click="close">
      <img class="tips" :src="bgShare" v-if="bgShare"/>
      <img class="tips" src="../assets/image/bg-share.png" v-else/>
    </div>
  </transition>
</template>

<script>
  import config from '../config';
  import {isIOS, isWeixin, toLogin} from '../common';
  import wx from "weixin-js-sdk"
  export default {
    props: {
      title: {
        type: String,
        default: ''
      },
      desc: {
        type: String,
        default: ''
      },
      link: {
        type: String,
        default: ''
      },
      imgUrl: {
        type: String,
        default: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png'
      },
      type: {
        type: String,
        default: 'link'
      },
      dataUrl: {
        type: String,
        default: ''
      },
      regOrigin: {
        type: [String, Number],
        default: '16'
      },
      scholarship: {
        type: String,
        default: ''
      },
      replaceInviteId: {
        type: Boolean,
        default: true
      },
      hideCopyBtn: {
        type: Boolean,
        default: false
      },
      showShare: {
        type: Boolean,
        default: true
      },
      bgShare: null,
      begin:{
        type:Boolean,
        default:true
      },
      isActivity:{
        type:Boolean,
        default:false
      },
      // 未登陆是否可配置分享信息
      notLoginShare: {
        type: Boolean,
        default: false
      },
      // 层次ID
      levelId:{
        type:[String, Number],
        default: ""
      }
    },
    data() {
      return {
        isShow: false,
        action: '',
        isReady: false,
        wxConfig: null,
        isLogin: null
      }
    },
    created() {
      //this.isLogin = !!(this.storage.getItem('authToken'));
      this.isLogin = !!((this.storage.getItem('authToken')||this.storage.getItem('ykAuthtoken')))
      this.action = this.$route.query.action || '';
      if(this.begin){
        this.getWxScript();
      }
    },
    watch: {
      title() {
        this.init();
      },
      desc() {
        this.init();
      },
      link() {
        this.init();
      },
    },
    methods: {
      openNO: function (newReady) {
        if (!isWeixin()) {
          return;
        }
        if(newReady){
          this.isReady=false;
          this.$nextTick(()=>{
            this.isShow = true;
          })
        }
        if (this.isReady) {
          this.isShow = true;
          fn();
        } else {
          this.getWxScript();
        }
      },
      openTwo() {
        this.getWxScript();
      },
      open: function (msg, action, redirect,fn) {
        // 如果是未登录
        if (!this.isLogin) {
          toLogin.call(this, msg, action, redirect);
        } else {
          this.isBindMobile(redirect, () => {
            if (!isWeixin()) {
              this.$modal({message: '请在微信客户端打开链接进行分享', icon: 'warning'});
              return;
            }
            if (this.isReady) {
              this.isShow = true;
              fn();
            } else {
              this.getWxScript();
            }
          });
        }
      },
      close: function () {
        this.isShow = false;
      },
      getWxScript: function (action) {
        if (!isWeixin()) return;
        if (!window.wx) {
          let $script = document.createElement('script');
          $script.type = 'text/javascript';
          $script.charset = 'utf-8';
          $script.src = config.jweixin;
          $script.onload = () => {
            this.wxJsapiSign(action);
          };
          global.document.body.appendChild($script);
        } else {
          this.wxJsapiSign(action);
        }
      },
      wxJsapiSign: function (action) {
        if (!this.notLoginShare) {
          if (!this.isLogin) return;
        }
        if (this.wxConfig && this.wxConfig.appid) {
          this.wxAuth(this.wxConfig, action);
          return;
        }
        const url = isIOS()?window.entryUrl:window.location.origin + this.$route.fullPath.split('#')[0];

        this.$http.post('/bds/jsapiSign/1.0/', {url: url}).then(res => {
          if (res.code !== '00') return;
          this.wxConfig = res.body;
          if (this.wxConfig && this.wxConfig.appid) {
            this.wxAuth(this.wxConfig, action);
          } else {
            this.$modal({message: '微信签名失败，请重试', icon: 'warning'});
          }
        });
      },
      // 通过config接口注入权限验证配置
      wxAuth: function (data, action) {
        wx.config({
          debug: false,
          appId: data.appid,
          timestamp: data.timestamp,
          nonceStr: data.noncestr,
          signature: data.signature,
          jsApiList: ['onMenuShareTimeline', 'onMenuShareAppMessage', 'onMenuShareQQ', 'onMenuShareQZone','hideMenuItems','showMenuItems']
        });
        wx.ready(() => {
          this.init();
          this.isReady = true;
          if (this.action === 'share' || action === 'share') this.open();
        });
      },
      init: function () {
        if (!this.notLoginShare) {
          if (!this.isLogin) return;
        }

        let shareLink = this.link || `${window.location.origin}/invite`;
        if (this.replaceInviteId && this.isActivity) {
          shareLink = shareLink.replace(/(&?action=[^&]*)|(&?scholarship=[^&]*)|(&?inviteId=[^&]*)/g, '').replace(/\?&+/g, '?');
          shareLink += (shareLink.includes('?') ? '&' : '?') + `action=login&scholarship=${this.scholarship}&inviteId=${encodeURIComponent(this.storage.getItem('authToken') || '')}&regOrigin=${this.regOrigin}&pfsnLevel=${this.levelId}`;
        }
        else if (this.replaceInviteId) {
          shareLink = shareLink.replace(/(&?action=[^&]*)|(&?scholarship=[^&]*)|(&?inviteId=[^&]*)/g, '').replace(/\?&+/g, '?');
          shareLink += (shareLink.includes('?') ? '&' : '?') + `action=login&scholarship=${this.scholarship}&inviteId=${encodeURIComponent(this.storage.getItem('authToken') || '')}&regOrigin=${this.regOrigin}&pfsnLevel=${this.levelId}`;
        } else {
          shareLink = shareLink.replace(/(&?action=[^&]*)|(&?scholarship=[^&]*)/g, '').replace(/\?&+/g, '?');
          shareLink += (shareLink.includes('?') ? '&' : '?') + `action=login&scholarship=${this.scholarship}&pfsnLevel=${this.levelId}`;
        }
        const imgUrl = this.imgUrl.includes('http')?this.imgUrl:'https:'+this.imgUrl
        // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
        wx.onMenuShareTimeline({
          title: this.title,
          link: shareLink,
          imgUrl
        });
        // 获取“分享给朋友”按钮点击状态及自定义分享内容接口
        wx.onMenuShareAppMessage({
          title: this.title,
          desc: this.desc,
          link: shareLink,
          imgUrl,
          type: this.type,
          dataUrl: this.dataUrl,
          success: () => {
            this.$emit('appMessageShared', true);
          },
          cancel: () => {
            this.$emit('appMessageShared', false);
          }
        });

        // 获取“分享到QQ”按钮点击状态及自定义分享内容接口
        wx.onMenuShareQQ({
          title: this.title,
          desc: this.desc,
          link: shareLink,
          imgUrl,
          success: function () {
          },
          cancel: function () {
          }
        });

        // 获取“分享到QQ空间”按钮点击状态及自定义分享内容接口
        wx.onMenuShareQZone({
          title: this.title,
          desc: this.desc,
          link: shareLink,
          imgUrl,
          success: function () {
          },
          cancel: function () {
          }
        });

        // 隐藏复制链接按钮
        if (this.hideCopyBtn) {
          wx.hideMenuItems({
            menuList: ['copyUrl']
          });
        } else {
          wx.showMenuItems({
            menuList: ['copyUrl']
          });
        }
      },
      // 判断是否绑定手机
      isBindMobile: function (redirect, callback) {
        if (this.storage.getItem('isBindMobile') === '1') {
          callback();
          return;
        }

        this.$http.post('/us/isBindMobile/1.0/').then(res => {
          if (res.code !== '00') return;

          if (res.body === '1') {
            this.storage.setItem('isBindMobile', '1');
            callback();
          } else {
            toLogin.call(this, '尚未绑定手机，请先绑定', 'bindMobile', redirect);
          }
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  .share{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; background-color:rgba(0, 0, 0, .8);
    .tips{ position:absolute; top:0; right:0; width:3.75rem; height:2.14rem; }
  }
  .fade-enter-active, .fade-leave-active{
    transition:opacity .4s
  }
  .fade-enter, .fade-leave-to{
    opacity:0
  }
</style>
