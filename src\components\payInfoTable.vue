<template >
<div class="box">
  <table class="tables bc-w" border='1' frame='void' width="100%" v-if="isLoaded&&scholarship=='14'">
    <tr class="tr" >
      <td class="td txt-c" width="10%"></td>
      <td class="td" width="24%">名称</td>
      <td class="td" width="14%">标准学费</td>
      <td class="td" width="12%">助学金</td>
      <td class="td" width="14%">应缴</td>
      <td class="td txt-c" width="8%">
        <i class="icons i-checkbox" :class="{checked:selected.length===payInfos.length}" @click="select('ALL')"></i>
      </td>
    </tr>
    <template v-for="(val,key) in payInfos" v-if="val.detail.length>0">
      <tr class="tr" v-for="(item,index) in val.detail">
        <td class="td txt-c" v-if="index===0" :rowspan="val.detail.length">{{val.title}}</td>
        <td class="td">{{item.itemCode + ':' + item.itemName}}</td>
        <td class="td">{{item.feeAmount}}</td>
        <td class="td" v-if="~~item.offerAmount>0" style="color: red">{{item.offerAmount}}</td>
        <td class="td" v-else>{{item.offerAmount}}</td>
        <td class="td">{{item.payable}}</td>
        <td class="td txt-c" v-if="index===0" :rowspan="val.detail.length">
          <i class="icons i-checkbox" :class="{checked:selected.includes(val.detail)}" @click="select(val.detail)"></i>
        </td>
      </tr>
    </template>
  </table>
  <table class="tables bc-w" border='1' frame='void' width="100%" v-else-if="isLoaded&&type=='wxFriendPay'">
    <tr class="tr" >
      <td class="td txt-c" width="10%"></td>
      <td class="td" width="24%">科目名称</td>
      <td class="td" width="14%">标准学费</td>
      <td class="td" width="12%" v-if="hasOfferAmount">助学金</td>
      <td class="td" width="14%">应缴</td>
    </tr>
    <template v-for="(val,key) in payInfos" v-if="val.detail.length>0">
      <tr class="tr" v-for="(item,index) in val.detail">
        <td class="td txt-c" v-if="index===0" :rowspan="val.detail.length">{{val.title}}</td>
        <td class="td">{{item.itemCode + ':' + item.itemName}}</td>
        <td class="td">{{item.feeAmount}}</td>
        <td class="td" v-if="hasOfferAmount" style="color: red">{{item.offerAmount||'0.00'}}</td>
        <td class="td">{{item.amount.toFixed(2)}}</td>
      </tr>
    </template>
  </table>

  <table class="tables bc-w" width="100%" border='1' frame='void' v-else-if="isLoaded&&recruitType=='2'&&new Date(enrollTime).getTime()>new Date(2019,3,30,18,0,0).getTime()">
    <tr class="tr" >
      <td class="td txt-c" width="20%"></td>
      <td class="td" width="43%">名称</td>
      <td class="td" width="25%">应缴</td>
      <td class="td txt-c" width="18%">
        <i class="icons i-checkbox" :class="{checked:selected.length===payInfos.length}" @click="select('ALL')"></i>
      </td>
    </tr>
    <template v-for="(val,key) in payInfos" v-if="val.detail.length>0">
    <tr class="tr" v-for="(item,index) in val.detail"  v-if="item.itemCode.indexOf('W')==-1">
      <td class="td txt-c" v-if="index===0" :rowspan="JSON.stringify(val.detail).indexOf('W')!=-1?val.detail.length-1:val.detail.length">{{val.title}}</td>
      <td class="td">{{item.itemCode + ':' }}{{ item.itemCode.indexOf("Y")!=-1?item.itemName.substr(0,6)+'学费':item.itemName}}</td>
      <td class="td">{{item.itemCode.indexOf("Y")!=-1&&val.detail[2]?(parseFloat(item.payable)+parseFloat(val.detail[2].payable)).toFixed(2):item.payable}}</td>
      <td class="td txt-c" v-if="index===0" :rowspan="JSON.stringify(val.detail).indexOf('W')!=-1?val.detail.length-1:val.detail.length">
        <i class="icons i-checkbox" :class="{checked:selected.includes(val.detail)}" @click="select(val.detail)"></i>
      </td>
    </tr>
  </template>
  </table>
  <table class="tables bc-w" border='1' frame='void' width="100%" v-else-if="isLoaded">
    <tr class="tr" >
      <td class="td txt-c" width="20%">类型</td>
      <td class="td" width="43%">名称</td>
      <td class="td" width="25%">应缴</td>
      <td class="td txt-c" width="18%">
        <i class="icons i-checkbox" :class="{checked:selected.length===payInfos.length}" @click="select('ALL')"></i>
      </td>
    </tr>
    <template v-for="(val,key) in payInfos" v-if="val.detail.length>0">
      <tr class="tr" v-for="(item,index) in val.detail" >
        <td class="td txt-c" v-if="index===0" :rowspan="val.detail.length">{{val.title}}</td>
        <td class="td" v-if="item.itemCode!='P'&&item.itemCode!='J'">{{item.itemName}}</td>
        <td class="td" v-else>{{item.itemName}}</td>
        <td class="td">{{item.payable}}</td>
        <td class="td txt-c" v-if="index===0" :rowspan="val.detail.length">
          <i class="icons i-checkbox" :class="{checked:selected.includes(val.detail)}" @click="select(val.detail)"></i>
        </td>
      </tr>
    </template>
  </table>
</div>

</template>

<script>
  export default {
    props: ['payInfos', 'scholarship','isLoaded','type','hasOfferAmount','recruitType','enrollTime'],
    data() {
      return {
        selected: []
      }
    },
    created(){
    },
    methods: {
      select: function (item) {
        if (item === 'ALL') {
          if (this.selected.length === this.payInfos.length) {
            this.selected = [];
          } else {
            this.selected = this.payInfos.map(item => item.detail);
          }
        } else {
          let payInfos = this.payInfos.map(item => item.detail);
          let n = this.selected.includes(item) ? 0 : 1;
          this.selected = payInfos.splice(0, payInfos.indexOf(item) + n);
        }
        this.$emit('input', this.selected);
      },
    },
    watch: {
      // 默认勾选辅导费
      payInfos: function () {
        // console.log(this.payInfos);
        let tutorPayInfos = this.payInfos.find(item => item.type === 'tutorPayInfos');
        if (tutorPayInfos && tutorPayInfos.detail.length) {
          this.selected = [tutorPayInfos.detail];
          this.$emit('input', this.selected);
        }
      },
      enrollTime:{
        handler(newVal,oldVal){},
        deep:true,
        immediate:true
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable.less";
  .box{
    border-radius: 0.1rem;
    overflow: hidden;
  }
  .tables{
    position:relative; width:100%; font-size:.14rem;border-radius: 0.1rem;border-color: #EDEBEB;
    overflow: hidden;
    color:rgba(23,6,6,0.8);
    // &:before{ .borderTop }
    // &:after{ .borderLeft }
    .td{
      position:relative; padding:.1rem .06rem; vertical-align:middle;
      // &:before{ .borderBottom }
      // &:after{ .borderRight }
    }
  }
  .i-checkbox{
    width:.14rem; height:.14rem; border-radius: 50%;border:1px solid rgba(23,6,6,0.4);margin-top: 0.04rem;
    &.checked{ background-image:url(../assets/image/radio1-2.png);border: 0 }
  }
</style>
