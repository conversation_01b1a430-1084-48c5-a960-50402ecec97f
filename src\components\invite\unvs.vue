<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.unvsId===item.unvsId}" @click="selected(item)">{{item.unvsName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';
  import dataJson from '../../view/active/scholarship/dataJson.json';
  import unvsData from '../../view/active/scholarship/unvs.json';
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data() {
      return {
        options: [],
        type: 'U',
        pageNum: 0,
        pageSize: 20,
        unvsName: '',
        pfsnLevel: '',
        cityCode: '',
        unvsIds: '',
        isLoading: false,
        allLoaded: false,
        activityName: '',
        grade:'',
        recruitType:'',
        isGk: false // 是否是广东开放大学
      }
    },
    created() {
      this.activityName = this.datas.activityName;
      this.grade = this.datas.grade.dictValue;
      this.getCity();
      this.pfsnLevel = this.datas.pfsnLevel.dictValue;
      this.recruitType =this.datas.recruitType.dictValue;
      this.unvsName = this.$route.query.unvsName || '';
      this.isGk = true && this.datas.isGk;
    },
    methods: {
      getUnvsList: function () {
        if (this.datas.scholarship == 148 && this.isGk) {
          this.options.push({unvsId: "164627569224717108", unvsName: "广东开放大学"});
           return ;
        }
        if(this.datas.scholarship==1 || this.datas.scholarship==148)
        {
           this.options.push({unvsId: "46", unvsName: "国家开放大学"});
           return ;
        }
        this.isLoading = true;
        let data = {
          grade:this.grade,
          type: this.type,
          unvsName: this.unvsName,
          pfsnLevel: this.pfsnLevel,
          scholarship:typeof(this.datas.scholarship)==='string'?[this.datas.scholarship]:this.datas.scholarship,
          cityCode: this.cityCode,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        let url = '/mkt/enrollInfo/1.0/';
        let headers = {};
        if (this.isDingding) {
          data.recruitType = this.recruitType;
          url = `${domain}/newStudentChange/getEnrollInfo.do`;
          data = qs.stringify(data);
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
          };
        }
        this.$http.post(url, data, {
          headers: headers,
        }).then(res => {
          if (res.code !== '00') return;
          const datas =(res.body || []);
          this.options.push(...datas);
          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      // 筛选院校
      /*filterUnvs: function (datas) {
        if (datas.length === 0) return datas;
        if (this.unvsIds) {
          const unvsIds = this.unvsIds.split(',');
          datas = datas.filter(val => unvsIds.includes(val.unvsId));
        }
        // 限时促销/奖学金活动筛选院校
        if (this.activityName === 'scholarship'||this.activityName === 'lottery') {
          datas = datas.filter(val => unvsData[this.pfsnLevel].includes(val.unvsId));
        }
        return datas;
      },*/
      loadMore: function () {
        this.pageNum++;
        this.getUnvsList();
      },
      selected: function (val) {
        this.$emit('input', val);
      },
      getCity: function () {
        const city = this.datas.city.dictValue;
        const datas = dataJson;
        if (city) {
          this.cityCode = datas.cityCode[city] || '';
          this.unvsIds = datas.unvsId[city] || '';
        }
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
