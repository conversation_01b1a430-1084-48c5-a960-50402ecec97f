<!-- 成教协议 -->
<template>
  <div class="agreement-wrapper">
    <pdf-preview :fileUrl="pdfUrl" @startInterval='handlePdfFinished'/>
    <div class="btn" v-if="!agreen && pdfFinished" :disabled="show" @click="goAgreem">{{time?`阅读倒计时${time}秒`:"同意签署协议"}}</div>
    <van-icon v-if="agreen" name="close" class="close" @click="closeAgreement" size="20px"/>
  </div>
</template>

<script>
import pdfPreview from "@/components/pdfPreview/index.vue";

export default {
  props: ['selectType','agreen', 'pdfUrl'],
  components: {
    pdfPreview,
  },
  data() {
    return {
      time: 5,
      show: true,
      learnId: "",
      pdfFinished: false, // pdf加载完成标志
    };
  },
  created() {
    this.learnId = this.$route.query.learnId || ""
  },
  methods: {
    handlePdfFinished() {
      if (!this.agreen) {
        this.pdfFinished = true;
        this.count();
      }
    },
    closeAgreement(){
      this.$emit("closeMain", false);
    },
    // 点击同意协议按钮
    async goAgreem() {
      if (this.time != 0) {
        return;
      }
      this.$emit("closeMain", false);
      if(this.selectType){
        this.$emit("payment", this.selectType);
      }
    },
    count() {
      let start = setInterval(() => {
        if (this.time > 1) {
          this.time = this.time - 1;
        } else {
          this.time = this.time - 1;
          clearInterval(start);
          this.show = false;
          return;
        }
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.agreement-wrapper {
  position: relative;
  padding-bottom: 0.4rem;
  .close {
    position: fixed;
    top: 0.1rem;
    right: 0.1rem;
  }
  .btn {
    width: 100%;
    height: 0.4rem;
    position: fixed;
    bottom: 0rem;
    text-align: center;
    line-height: 0.4rem;
    background: linear-gradient(
      135deg,
      rgba(240, 145, 144, 1) 0%,
      rgba(240, 120, 119, 1) 66%,
      rgba(240, 110, 108, 1) 100%
    );
    font-size: 0.15rem;
    font-weight: bold;
    color: rgba(255, 255, 255, 1);
    &[disabled] {
      background: white;
      background: rgba(187, 182, 182, 1);
    }
  }
}

</style>
