<template>
  <!-- 自动打开小程序页面 -->
  <div class="transitPage-wrapper">
    <!-- <wx-open-launch-weapp id="launch-btn" appid="wx9b6c444edb3f466c" path="url" env-version="trial">
      <script type="text/wxtag-template">
        <style>.btn { padding: 12px }</style>
        <button class="btn">前往APP</button>
      </script>
    </wx-open-launch-weapp> -->
  </div>
</template>

<script>
import openApp from '@/mixins/openApp'

export default {
  mixins: [openApp],
  data() {
    return {}
  },
  mounted() {
    this.wxCallAppInit() // 微信标签唤起app
    this.toActivePage()
  },
  methods: {
    async toActivePage() {
      const res = await this.getLink()
      console.log('自动打开小程序页面-res2：', res)
      if (res.body) {
        location.href = res.body
      }
    },
    getLink() {
      const { inviteId, mappingId, circleId, shareType } = this.$route.query
      console.log('自动打开小程序页面-query2：', this.$route.query)
      const inviteToken = inviteId ? inviteId.replace(/\ +/g, '+') : ''
      return this.$http.post('/us/getAppletsToLink/1.0/', {
        appletsUrl: '/pages/active/20230619/index',
        query: `inviteToken=${inviteToken}&circleId=${
          mappingId || circleId
        }&shareType=${shareType}`
      })
    }
  }
}
</script>

<style lang="less" scoped>
.transitPage-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: hidden;
}
</style>