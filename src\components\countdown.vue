<template>
  <div class="countdown" v-if="page==='detail'">
    距结束: <span>{{fullDate}}</span>
  </div>
  <span v-else>{{miniDate}}</span>
</template>

<script>
  export default {
    name: 'countdown',
    props: ['page', 'endTime'],
    data() {
      return {
        timer: null,
        day: '--',
        hour: '--',
        minute: '--',
        second: '--',
        millisecond: '--'
      }
    },
    computed: {
      fullDate: function () {
        return `${this.day}天${this.hour}时${this.minute}分${this.second}秒${this.millisecond}`;
      },
      miniDate: function () {
        return `${this.day}:${this.hour}:${this.minute}:${this.second}:${this.millisecond}`;
      }
    },
    mounted() {
      if (this.endTime) {
        this.timer = setInterval(this.countdown, 100);
      }
    },
    beforeDestroy() {
      this.clearTimer();
    },
    methods: {
      countdown: function () {
        const now = new Date().getTime();
        const endTime = new Date(this.endTime.replace(/-/g, '/')).getTime();
        this.getTime(endTime - now);
      },
      getTime: function (surplus) {
        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;
  
        this.day = this.complement(parseInt(s / oneDay));
        this.hour = this.complement(parseInt(s % oneDay / oneHour));
        this.minute = this.complement(parseInt(s % oneDay % oneHour / 60));
        this.second = this.complement(parseInt(s % oneDay % oneHour % 60));
        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      complement: function (num) {
        return num < 10 ? '0' + num : num;
      },
      clearTimer: function () {
        clearInterval(this.timer)
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .countdown{
    margin:.08rem .12rem; padding:.2rem; text-align:center; color:#fff; font-size:.12rem; background-color:#f26662; background-image:@bgColor; border-radius:.04rem;
    > span{ font-weight:bold; font-size:.14rem; }
  }
</style>
