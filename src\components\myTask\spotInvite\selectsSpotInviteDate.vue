<template>
  <picker :slots="slots" :isSetDefaultVal="isSetDefaultVal" v-model="val" ref="picker"/>
</template>

<script>
  import picker from '@/components/picker';

  export default {
    props: {
      dictName: String,
      options: Array,
      ext1: String,
      isSetDefaultVal: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      defaultIndex: {
        type: Number,
        default: 0
      },
      filterValue: Array
    },
    data() {
      return {
        slots: [],
        val: [{}],
        vals: []
      }
    },
    created() {
      // this.init();
    },
    methods: {
      init: function () {
        // this.$nextTick(() => {
          this.slots = [{flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex}];
        // })
      },
      getOptions: function () {
        let options = [];
        if (this.options.length >= '1') {
          // this.vals = this.options || [];
          let vals = [];
          for (let item of this.options) {
            let mydate = new Date(item.replace(/-/g,'/')).getDay();
            let weekday = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            vals.push({
              date: item,
              dateTxt: item + " " + weekday[mydate]
            })
            options.push(item + " " + weekday[mydate]);
          }
          this.vals = vals;
        }
        return options;
      },
      open: function () {
        if (this.disabled) return;
        this.$refs.picker.open();
      },
      setValues: function (value) {
        if (value) {
          const name = (this.vals.find(val => val.dictValue === value) || {}).dictName;
          if (name) {
            this.$refs.picker.setValues([name]);
          }
        }
      }
    },
    watch: {
      val: function () {
        this.$emit('input', this.vals[this.val[0].index] || {});
      }
    },
    components: {picker}
  }
</script>
