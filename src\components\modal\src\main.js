import Vue from 'vue';
import {isVNode} from '../../../common'

// const ModalConstructor = Vue.extend(require('./main.vue'));
import main from './main.vue';
const ModalConstructor = Vue.extend(main);
let seed = 10000;
let instances = [];

let Modal = (function (options) {
  Modal.open(options);
});

Modal.open = (options) => {
  if ('string' === typeof options) {
    options = {message: options};
  }
  if (!options.message) return;
  options.onClose = function (id) {
    Modal.close(id);
  }

  let id = `modal_${seed++}`;
  let instance = new ModalConstructor({
    data: options
  });
  instance.id = id;
  if (isVNode(instance.message)) {
    instance.$slots.default = [instance.message];
    instance.message = null;
  } /*else {
    delete instance.$slots.default;
  }*/
  instance.vm = instance.$mount();
  document.body.appendChild(instance.vm.$el);
  instance.vm.visible = true;
  instance.vm.$el.style.zIndex = seed;
  instances.push(instance);
  return instance.vm;
}

Modal.confirm = function (message, title, option) {
  return new Promise((resolve, reject) => {
    const defaults = {
      customClass: 'confirm',
      title: title,
      message: message,
      icon: false,
      showConfirmButton: true,
      showCancelButton: true,
      closeOnClickModal: false,
      duration: 0,
      yes: resolve,
      cancel: reject
    };

    Modal.open(Object.assign(defaults, option));
  });
}

Modal.close = function (id) {
  for (let i = 0, len = instances.length; i < len; i++) {
    if (instances[i] && instances[i].id === id) {
      instances.splice(i, 1);
    }
  }
}

Modal.closeAll = function () {
  for (let item of instances) {
    item.close();
  }
}

export default Modal;
