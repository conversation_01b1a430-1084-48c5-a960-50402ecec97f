import { isIOS, isAndroid,isWeixin } from '@/common';

/**
 * 调用原生方法
 * @param {String} methodName 原生方法名
 * @param {Object} params 传给原生的参数
 */
const callHandler = (methodName, params = {}) => {
  const data = { methodName, params };
  return new Promise((resolve, reject) => {
    if (isAndroid() && !isWeixin()) {
      const jsonString = window.android.nativeMethodExecute(JSON.stringify(data));
      const response = JSON.parse(jsonString);
      resolve(response);
    } else if (isIOS() && !isWeixin()) {
      setupWebViewJavascriptBridge(function (bridge) {
        bridge.callHandler('nativeMethodExecute', data, (jsonString) => {
          const response = JSON.parse(jsonString);
          resolve(response);
        });
      });
    } else {
      reject();
    }
  });
}

/**
 * 我们注册方法给原生调用
 * @param {String} methodName 我们组册的方法名
 * @param {Function} cb 回调函数，需要执行的业务
 */
const registerHandler = (methodName, cb) => {
  window[methodName] = () => {
    cb && cb();
  };
};


/**
 * ios与h5通信方法
 * @param {Function} callback 回调函数
 */
function setupWebViewJavascriptBridge(callback) {
  if (window.WebViewJavascriptBridge) {
    return callback(window.WebViewJavascriptBridge)
  }
  if (window.WVJBCallbacks) {
    return window.WVJBCallbacks.push(callback)
  }
  const isFromMini = sessionStorage.getItem('isFromMini');
  if (!isFromMini) {
    window.WVJBCallbacks = [callback]
    let WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none'
    WVJBIframe.src = 'https://__bridge_loaded__'
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(() => {
      document.documentElement.removeChild(WVJBIframe)
    }, 0)
  }
}
/**
 * registerhandler(name, callback) { // ios注册方法
    setupWebViewJavascriptBridge(function (bridge) {
      bridge.registerHandler(name, function (data, responseCallback) {
        callback(data, responseCallback)
      })
    })
  }
 */

export default {
  callHandler,
  registerHandler,
}
