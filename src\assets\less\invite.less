@import "variable";

.widget-list{
  position:relative;background-color:#fff;
  .scholarship-info{ color:#69a5da; margin-right:.1rem; position:absolute; right:.8rem; z-index:999;top:.12rem; font-size:.12rem; }
  > .item{
    display:flex; align-items:center; position:relative; padding:.1rem 0;
    &:after{ .borderBottom }
    &:last-child{
      &:after{ border-bottom:none; }
    }
    &.disabled{
      pointer-events:none;
      > .rt{ padding-right:.13rem; }
      .icons{ display:none; }
    }
    > .lf{ flex:1; padding:0 .1rem 0 .12rem; white-space:nowrap; }
    > .rt{ color:#999; }
    .arr-r{ width:.36rem; height:.36rem; background-image:url(../image/public_ico_open_right.png); }
  }
}
.form-enroll{
  > .item{
    position:relative; background-color:#fff;display:flex; align-items:center;  padding:.1rem 0;
    > .lf{ flex:1; padding:0 .1rem 0 .12rem; white-space:nowrap; }
    > .rt{
      .input{ width:100%; height:.36rem; padding:0 .12rem; border:none; font-size:.14rem;text-align: right;color: #888 }
    }

    &:after{ .borderBottom }
  }
}
.school{
  color:#666;
  > .hd{
    display:flex; align-items:center; justify-content:space-between; position:relative; margin-top:.15rem; padding:.08rem .12rem; border-top:2px solid #04a0d4; background-color:#ececec;
    &:after{ .borderBottom }
  }
  > .ft{ padding:.08rem .08rem .2rem; color:#555; font-size:.11rem; background-color:#f9fafb; }
  .fs1{ font-size:.17rem; }
  .fs2{ font-size:.12rem; }
  .fs3{ font-size:.11rem; }
}
.table{
  width:100%;
  tr{ background-color:#f9fafb; }
  td{
    position:relative; padding:.08rem; vertical-align:middle; font-size:.12rem;
    &:after{ .borderBottom }
    &:before{ .borderRight }
    &:last-child{
      &:before{ border-right:none; }
    }
  }
  > .hd{ text-align:center; font-size:.14rem; font-weight:bold; background-color:rgba(236, 236, 236, .5); }
}
.btn-enroll{ position:fixed; right:.2rem; bottom:.2rem; width:.56rem; height:.56rem; line-height:.56rem; text-align:center; color:#fff; font-size:.17rem; box-shadow:0 2px 2px #bbb; border-radius:50%; background-color:#f26662; background-image:@bgColor; }

.select-item{
  position:relative; padding:0 .12rem; line-height:.44rem;
  &:after{ .borderBottom }
  &.active{
    &:before{ position:absolute; top:.04rem; right:0; width:.36rem; height:.36rem; background-image:url(../../assets/image/selected.png); background-size:100% 100%; content:''; }
  }
}
