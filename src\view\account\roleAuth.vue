<template>
  <div class="full">
    <van-popup v-model="showPop" :close-on-click-overlay='false'>
      <div class="popupBox">
        <p class="topText">系统检测到您已通过助学老师录入以下身份信息，请进行确认：</p>
        <p class="bottomText">身份证号码</p>
        <p class="idCardNumber">{{ this.idCardNumber }}</p>
        <button class="topBtn" @click="submitClick()">确认无误</button>
        <button class="bottomBtn" @click="errorClick()">信息有误，联系助学老师</button>
        <p class="phoneText">如有疑问，请拨打远智热线：************</p>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { isStudent } from '../../common';
import storage from '../../plugins/storage'
import axios from 'axios'
import { Popup, Toast } from 'vant';

export default {
  // beforeRouteEnter(to, from, next) {
  //   let phone = storage.getItem('mobile') || storage.getItem('phone');
  //   axios.post('/us/getUserIdCard/1.0/',{mobile:phone}).then(res =>{
  //     let {code,body} = res;
  //     if(body == null) {
  //       storage.setItem('bindStudent', '0');
  //     }else if(body.bindStudent == '1') {
  //       storage.setItem('bindStudent', '1');
  //     }else {
  //       storage.setItem('bindStudent', '2');
  //     }

  //     const redirect = to.query.redirect;
  //     if (isStudent() && storage.getItem('bindStudent')=='2') {
  //       next(redirect || {name: 'stuInfo'});
  //     } else {
  //       axios.post('/us/userTypes/1.0/').then(res => {
  //         const {code, body} = res;
  //         if (code === '00') {
  //           storage.setItem('relation', body);
  //           if (isStudent() && storage.getItem('bindStudent')=='2') {
  //             next(redirect || {name: 'stuInfo'});
  //           } else {
  //             next();
  //           }
  //         }
  //       });
  //     }
  //   })
  // },
  data() {
    return {
      userType: '',
      idCardNumber: '',
      showPop: false,
      fromView: '',
      phone: '',
      teacherPhone: '',
      isclick: true,
      stdId: '',
      learnId: '',
      learnInfo: {},
      bindStudentid: '',
    }
  },
  async created() {
    this.phone = storage.getItem('mobile') || storage.getItem('phone');
    this.fromView = this.$route.query.redirect;
    // this.getUserIdCard();
    await this.getNewrelation();
    // await 

  },
  methods: {
    //获取最新的relation值
    getNewrelation() {
      let phone = storage.getItem('mobile') || storage.getItem('phone');
      this.$http.post('/us/userTypes/1.0/').then(res => {
        let { code, body } = res;
        if (code === '00') {
          storage.setItem('relation', body);
        }
        this.getUserIdCard();
        this.getStdLearnInfo()
      })
    },

    // 正常返回0
    defaultClick() {
      this.$modal.confirm(' ', undefined, {
        customClass: 'confirm authModal',
        icon: 'ketangrukou',
        confirmButtonText: '尚未报读，现在去报读',
        cancelButtonText: '我已报读，去找回学业'
      }).then(action => {
        this.$router.push({ name: 'newDreamBuild' });
      }).catch(() => {
        this.$router.push({ name: 'idcardBind', query: { redirect: this.$route.query.redirect } });
      });
    },
    //获取学员身份
    getUserIdCard() {
      this.$http.post('/us/getUserIdCard/1.0/', { mobile: this.phone }).then(res => {
        let { code, body } = res;
        const [path, queryString] = this.$route.query.redirect.split('?');
        let queryParams = ''
        // 解析查询参数
        const searchParams = new URLSearchParams(queryString);
        for (const [key, value] of searchParams.entries()) {
          if (key == 'recruitType') {
            queryParams = value
          }
        }
        this.fromView = this.fromView.split("?")[0];
        if (body == null || body.bindStudent == '0') {
          this.storage.setItem('bindStudent', '0');
          if (this.fromView == '/' || this.fromView == '/home') {
            this.$router.push({ name: 'home' });
          }
          if (this.fromView == '/settings') {
            this.$router.push({ name: 'settings' });
          }
          if (this.fromView == '/student' || this.fromView == '/student/mytask' || this.fromView == '/student/myCurriculum' || this.fromView == '/student/more' || this.fromView == '/student') {
            if(this.$route.query.redirect.indexOf('recruitType=1')!=-1){
              this.$router.push({path:'/student/guidePageLogin',query:{ recruitType: 1 }})
            } else if(this.$route.query.redirect.indexOf('recruitType=2')!=-1){
              this.$router.push({path:'/student/guidePageLogin',query:{ recruitType: 2 }})
            } else if(this.$route.query.redirect.indexOf('recruitType=3')!=-1){
              this.$router.push({path:'/student/guidePageLogin',query:{ recruitType: 3 }})
            } else if(this.$route.query.redirect.indexOf('recruitType=4')!=-1){
              this.$router.push({path:'/student/guidePageLogin',query:{ recruitType: 4 }})
            } else if(this.$route.query.redirect.indexOf('recruitType=5')!=-1){
              this.$router.push({path:'/student/guidePageLogin',query:{ recruitType: 5 }})
            }else{
              this.defaultClick();
            }
          }
        } else if (body.bindStudent == '1') {
          this.bindStudentid = '1'
          console.log(this.bindStudentid, 'this.bindStudentid');
          this.storage.setItem('bindStudent', '1');
          this.showPop = true;
          this.teacherPhone = body.eMobile;
        } else {
          this.storage.setItem('bindStudent', '2');
          if (this.fromView == '/' || this.fromView == '/home') {
            this.$router.push({ name: 'home' });
            return;
          }
          if (this.fromView == '/settings') {
            this.$router.push({ name: 'settings' });
            return;
          }
          if (this.fromView == '/student/mytask') {
            this.$router.push({ name: 'myTask', query: { recruitType: queryParams } });
            return;
          }
          if (this.fromView == '/student/more') {
            this.$router.push({ name: 'more' });
            return;
          }
          if (this.fromView == '/student/myCurriculum') {
            this.$router.push({ name: 'myCurriculum' });
            return;
          }
          if (this.learnInfo.stdStage && !['5', '12', '6', '7', '8', '9', '10'].includes(this.learnInfo.stdStage)) {
            if (this.learnInfo.grade == '2021' && this.learnInfo.paidCount > 0) {
              this.$router.push({ name: 'tutorialClass' });
              return;
            }
          }
          this.storage.setItem('queryString', queryString)
          this.$router.push({ name: 'stuInfo', query: { recruitType: queryParams } });
        }
      })

    },
    //获取学员信息
    getStdLearnInfo() {
      this.$http.post('/mkt/stdLearnInfo/1.0/').then(res => {
        let { code, body } = res;
        if (body) {
          //过滤退学信息
          body.learnInfos = body.learnInfos.filter((item) => {
            if (item.stdStage !== '10') {
              return item;
            }
          });
          this.learnInfo = body.learnInfos.filter((item) => {
            if (item.recruitType == '1') {
              return item
            }
          })
          this.learnId = body.learnInfos[0].learnId;
          if (this.bindStudentid == '1') {
            this.idCardNumber = body.idCard;
          }
        }
      })
    },
    submitClick() {
      // if(this.isclick= true){
      //   this.isclick = false;
      this.$indicator.open();
      //调用确认无误接口
      this.$http.post('/us/updateUserIdCard/1.0/', { learnId: this.learnId, idCard: this.idCardNumber, mobile: this.phone }).then(res => {
        let { code, body } = res;
        if (body == '2') {
          storage.setItem('bindStudent', body);
          if (this.fromView == '/student' || this.fromView == '/student?v=20181027') {
            this.$indicator.close();
            this.$router.push({ name: 'stuInfo' });
          } else if (this.fromView == '/') {
            this.$indicator.close();
            this.$router.push({ name: 'home' });
          }
          else if (this.fromView == '/student/mytask') {
            this.$indicator.close();
            console.log(5);
            this.$router.push({ name: 'myTask' });
          }
          else if (this.fromView == '/student/myCurriculum') {
            this.$indicator.close();
            this.$router.push({ name: 'myCurriculum' });
          }
          else if (this.fromView == '/student/more') {
            this.$indicator.close();
            this.$router.push({ name: 'more' });
          }
          else {
            this.$indicator.close();
            this.$router.push({ name: 'stuInfo' });
          }
        } else {
          this.$indicator.close();
          Toast('信息有误，请联系招生老师！');
        }
      })
      // }
      // setTimeout(()=>{
      //     this.isclick = true;
      // },200)
    },
    errorClick() {
      //跳转至查询老师
      if (this.fromView == '/') {
        storage.clear();
      }
      this.$router.push({ name: 'teacherSearch', query: { eMobile: this.teacherPhone } });

    },
  },
  components: { Popup, Toast }
}
</script>

<style lang="less" scoped>
.full {
  height: 100vh;
  background-color: #fff;
}

.full /deep/ .van-popup {
  border-radius: .04rem;
}

.popupBox {
  width: 3.43rem;
  height: 3.22rem;
  text-align: center;
  overflow: hidden;

  .topText {
    font-size: .14rem;
    // text-align: left;
    width: 2.1rem;
    margin: 0 auto;
    margin-top: .22rem;
    color: rgba(102, 102, 102, 1);
  }

  .bottomText {
    font-size: .17rem;
    font-weight: 500;
    color: rgba(68, 68, 68, 1);
    // margin: 0 auto;
    margin-top: .25rem;
  }

  .idCardNumber {
    margin-top: .1rem;
    font-weight: 500;
    color: rgba(68, 68, 68, 1);
    font-size: .19rem;
  }

  button {
    border: none;
    width: 2.28rem;
    height: .45rem;
    border: .04rem;
    font-size: .16rem;
  }

  .topBtn {
    background: linear-gradient(215deg, rgba(239, 66, 92, 1) 0%, rgba(248, 111, 107, 1) 100%);
    color: rgba(255, 255, 255, 1);
    font-weight: 500;
    margin-top: .25rem;
  }

  .bottomBtn {
    color: #e85b57;
    margin-top: .14rem;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(242, 102, 98, 1);
    font-weight: 500;
    color: rgba(242, 102, 98, 1);
  }

  .phoneText {
    margin-top: .12rem;
    font-size: .14rem;
    color: rgba(102, 102, 102, 1);
  }
}
</style>
<style lang="less">
@import "../../assets/less/variable";

.modal.confirm.authModal {
  padding: 0 11.5% !important;

  .icon {
    width: 1.12rem;
    height: .72rem;
    margin: .72rem 0 .51rem;
  }

  .ft {
    flex-direction: column-reverse;

    .btn {
      color: @color;
      border-color: @color;
      flex: auto;

      &.primary {
        color: #fff;
      }
    }
  }
}

.modal.confirm .txt {
  text-align: left;
}
</style>
