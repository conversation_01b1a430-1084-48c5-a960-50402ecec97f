<template>
  <div class="comment-item">
    <div class="cover"><img class="img" :src="headImgUrl|defaultAvatar" alt=""/></div>
    <div class="cont">
      <div class="name">
        <p>{{name}}</p>
        <p class="time">{{time|formatDate('yyyy.MM.dd hh:mm:ss')}}</p>
      </div>
      <div class="fs content">{{content}}</div>
      <slot name="sub"></slot>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['name', 'headImgUrl', 'time', 'content']
  }
</script>

<style lang="less" scoped>
  .comment-item{
    margin:0 .1rem; padding:.1rem 0; overflow:hidden; font-size:.14rem; word-break:break-all; border-bottom:1px solid rgba(23,6,6,0.08);
    &:last-child{ border-bottom:none; }
    &.sub{ margin:.15rem 0 0; border-bottom:none; background: rgba(23,6,6,0.03); border-radius: 0.1rem; padding: 0.1rem}
    .cover{ float:left; width:.4rem; height:.4rem; overflow:hidden; border-radius:50%; }
    .cont{ margin-left:.4rem; padding-left: 0.05rem; }
    .content{ line-height: 1; }
    .name{ padding-bottom:.1rem;  }
    .time{ color:rgba(23,6,6,0.4); font-size:.11rem; line-height: 1; padding-top: 0.03rem }
  }
</style>
