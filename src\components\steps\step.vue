<template>
  <div class="yz-step-item">
    <!-- 步骤item -->
    <div class="icon-box">
      <span class="default"></span>
      <div class="active">
        <i></i>
      </div>
      <img src="../../assets/image/radio1-2.png" class="finish" alt="">
    </div>
    <p class="title">{{title}}</p>
  </div>
</template>

<script>
export default {
  data () {
    return {
    };
  },
  props: {
    title: String,
  },
  methods: {
  },
};
</script>

<style lang="less">
  .yz-step-item{
    position: relative;
    flex: 1;
    text-align: center;

    &:not(:last-child)::after{
      content: '';
      position: absolute;
      width: 100%;
      left: 50%;
      top: 7px;
      height: 1px;
      background: #EDEBEB;
      z-index: 1;
    }
    &.doing{
      .active{
        display: inline-block;
      }
      .default, .finish{
        display: none;
      }
    }
    &.done{
      .finish{
        display: inline-block;
      }
      .default, .active{
        display: none;
      }
      &::after{
        background: #FAD3D2;
      }
    }
    .icon-box{
      width: 15px;
      height: 15px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 2;
    }
    .default{
      border-radius: 50%;
      width:7px;
      height: 7px;
      display: inline-block;
      background: rgb(171, 165, 165);
    }
    .active{
      background: rgb(274, 191, 192);
      position: relative;
      z-index: 2;
      border-radius: 50%;
      width: 100%;
      height:100%;
      display: none;
      &::after{
        content: '';
        background: rgb(233, 86, 90);
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        width: 7px;
        height: 7px;
        z-index: 2;
        vertical-align: middle;
      }
    }
    .finish{
      width: 100%;
      display: none;
    }
    .title{
      margin-top: 0.07rem;
      font-size: 0.13rem;
    }
  }
</style>
