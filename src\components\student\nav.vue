<template>
  <div class="footer nav-bar">
    <div class="wrap">
      <div class="inner plr">
        <router-link class="item" :to="{ path: '/student',  query: { recruitType: $route.query.recruitType } }" activeClass="active" :exact="true">
          <i class="icons i-home"></i>
          <span class="label">我的信息</span>
        </router-link>
        <router-link class="item" :to="{ path: '/student/mytask', query: { recruitType: $route.query.recruitType } }" activeClass="active" :exact="true">
          <i class="icons i-order"></i>
          <span class="label">我的任务</span>
        </router-link>
        <router-link
          class="item"
          :to="{ name: 'audition', query: { learnId: learnInfo.learnId, from: 1 } }"
          activeClass="active"
          :exact="true"
          v-if="isAuditionCourse && !$route.query.recruitType"
        >
          <i class="icons i-integral"></i>
          <span class="label">试听课程</span>
        </router-link>
        <div v-if="!tutor && !isAuditionCourse && !$route.query.recruitType">
          <router-link class="item" :to="{ name: routerName, query: routerQuery }" activeClass="active" :exact="true" v-if="!adult" >
            <i class="icons i-integral"></i>
            <span class="label">我的课表</span>
          </router-link>
          <!-- <router-link class="item" to="/student/myCurriculum" activeClass="active" :exact="true" v-if="!adult" >
            <i class="icons i-integral"></i>
            <span class="label">我的课表</span>
          </router-link> -->
          <!-- <router-link class="item" :to="{path:'/student/newCurriculum',query:{unvsName: learnInfos.unvsName,unvsCode: learnInfos.unvsCode,pfsnName:learnInfos.pfsnName}}" activeClass="active" :exact="true" v-if="!tutor">
            <i class="icons i-integral"></i>
            <span class="label">成的课表</span>
          </router-link> -->
          <div class="item"  activeClass="active" :exact="true" v-if="adult" @click="togoclass">
            <i class="icons i-integral"></i>
            <span class="label">我的课表</span>
          </div>
        </div>
        <router-link class="item" to="/tutorialClass/optimzeHome"  activeClass="active" :exact="true" v-else-if='tutor && !$route.query.recruitType'>
          <i class="icons i-integral"></i>
          <span class="label">辅导课程</span>
        </router-link>
        <router-link class="item" to="/student/more" activeClass="active" :exact="true" v-if="!$route.query.recruitType">
          <i class="icons i-set"></i>
          <span class="label">更多</span>
        </router-link>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        tutor:false,
        learnInfos:[],
        learnInfo: {},
        isShowMyCurriculum: true,
        adult:false,
        recruitType:null,
        isAuditionCourse: false, // 是否是试听课
        routerName: 'myCurriculum',
        routerQuery: {},
      }
    },
    created(){
      this.getStdLearnInfo();
    },
    methods:{
      togoclass(){
        this.recruitType=this.storage.getItem('recruitType');
        this.getStdLearnInfo().then(res=>{
          if(this.recruitType==1||this.recruitType==3){
            this.$router.replace({
              path:'/student/newCurriculum',
              query:{ pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName }
            });
          }else if(this.recruitType==5){
            this.$router.push({
              name:'tutorialClass.graduateClasslist',
              query:{ pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName,pfsnLevel:this.learnInfo.pfsnLevel,grade:this.learnInfo.grade }
            });
          }
        })

      },
      // 获取学员基础信息
      getStdLearnInfo: function () {
        return new Promise((resolve,reject)=>{
          this.$http.post('/mkt/stdLearnInfo/1.0/').then(res => {
            this.$indicator.close();
            let {code, body} = res;
            let learnId = this.storage.getItem('learnId');
            if (code !== '00') return;
            // 过滤学员
            this.learnInfos = body.learnInfos.filter((item) => {
              // ||前面是旧判断 成教同时不是退学才返回数据, 全日制/自考所有状态都能看
              if ((item.recruitType=='1' && item.stdStage !== '10') || item.recruitType=='3' || item.recruitType=='4' || item.recruitType=='5') {
                return item;
              }
            });

            if(this.learnInfos.length == 0){
              return;
            }

            let learnInfosFrist = learnId ? this.learnInfos.find((item)=> item.learnId == learnId) : this.learnInfos[0];
            this.learnInfo = learnInfosFrist;
            this.setMenus(learnInfosFrist);
            // if(learnId){
            //   this.isShowTutor(
            //     learnInfosFrist.payTime,
            //     learnInfosFrist.grade,
            //     learnInfosFrist.stdStage ,
            //     learnInfosFrist.recruitType,
            //     learnInfosFrist
            //   );
            // }
            // this.setAudition();
            // this.adult = learnInfosFrist.recruitType != 2 && learnInfosFrist.recruitType != 4;
            // this.recruitType=learnInfosFrist.recruitType;
            resolve(res);
          }).catch(err => {
            this.$indicator.close();
          });
        })
      },
      setMenus({ stdStage, recruitType, payTime, grade }) {
        let learnId = this.storage.getItem('learnId');
        // let learnInfo = learnId ? this.learnInfos.find((item)=> item.learnId == learnId) : this.learnInfos[0];
        this.isAuditionCourse = recruitType == 1 && !payTime && ['1','2','3'].includes(stdStage);
        if (this.isAuditionCourse) {
          return;
        }
        if(learnId){
          this.tutor = false;
          this.isShowTutor(
            grade,
            stdStage ,
            recruitType,
          );
        }
        this.adult = recruitType != 2 && recruitType != 4;
        // console.log(this.adult);
      },
      isShowTutor(grade,stdStage, recruitType){
        // console.log(recruitType);
        switch (recruitType) {
          case '1':
          case '2':
            if (!['7', '8', '10'].includes(stdStage)) {
              this.tutor = true;
              this.routerName = 'optimzeHome';
              this.routerQuery = {};
              // if(new Date().getFullYear() + 1 == grade){
              //   this.tutor = true;
              //   this.routerName = 'optimzeHome';
              //   this.routerQuery = {};
              //   // if(this.$route.path=='/student/myCurriculum'){
              //   //   this.$router.replace({
              //   //     name:'optimzeHome'
              //   //   })
              //   // }
              // }
            }
          break;
          case '3':
            this.routerName = 'myCurriculum';
            this.routerQuery = {};
          break;
          // 全日制不变 走课表
          case '4': // 自考走辅导课
            this.routerName = 'tutorialClass.newself';
            this.routerQuery = { pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName };
            // if(this.$route.path == '/student/myCurriculum'){
            //   this.$router.replace({
            //     name:'tutorialClass.newself',
            //     query:{pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName,}
            //   })
            // }
          break;
          case '5':
            this.routerName = 'tutorialClass.graduateClasslist';
            this.routerQuery = { pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName,pfsnLevel:this.learnInfo.pfsnLevel,grade:this.learnInfo.grade };
            // if(this.$route.path == '/student/myCurriculum'){
            //   this.$router.replace({
            //     name:'tutorialClass.graduateClasslist',
            //     query:{ pfsnName:this.learnInfo.pfsnName,unvsCode:this.learnInfo.unvsCode,unvsName:this.learnInfo.unvsName,pfsnLevel:this.learnInfo.pfsnLevel,grade:this.learnInfo.grade }
            //   });
            // }
          break;
          default:
            break;
        }
        // if (recruitType == '1' || recruitType == '2') { // 国开成教走这个
        // } else {
        // }
      },
    }
  }
</script>
<style lang="less" scoped>
  @import "../../assets/less/variable";

  .nav-bar{
    .item{ float:left; width:25%; height:100%; text-align:center; }
    .label{ display:block; padding-top:.03rem; line-height:1; font-size:.1rem; color:#999; }

    .icons{ width:.32rem; height:.32rem;  }
    .i-home{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
    .i-order{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
    .i-integral{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
    .i-set{ background-image:url(../../assets/image/student/icon/<EMAIL>); }

    .active{
      .label{ color:@color; }
      .i-home{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
      .i-order{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
      .i-integral{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
      .i-set{ background-image:url(../../assets/image/student/icon/<EMAIL>); }
    }
  }
</style>
