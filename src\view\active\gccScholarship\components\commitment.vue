<template>
  <div  class='commitment'>
    <h2 class="title">上进承诺书</h2>
    <div class="content">
      <p> 本人(姓名: {{userName > 6? userName.substr(0,6)+ '...' : userName}})正在申请远智教育2022级广州商学院本科奖学金，本人承诺将秉承上进精神，爱国、爱家、爱企业，在工作中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。</p>
    </div>

    <!-- 未见证 isReceive && !witnessStatus -->
    <div v-cloak class="not-witnessed hide" :class="{show:notWitnessed}" >
      <div class="note">
        <p>奖学金激活需要企业领导在线进行见证上进点评，完成后才能激活使用奖学金。</p>
        <p>*活动结束后的7天内，您还可以在「远智教育」公众号-「远智学堂」-「学堂首页」内进行见证</p>
      </div>
      <div class="btns">
        <button class="activation-btn" @click="open"></button>
        <router-link class="link" :to="{ name: 'sprintProcess' }">如何见证激活?</router-link>
      </div>
    </div>

    <!-- 见证成功 isReceive && witnessStatus -->
    <div v-cloak class="witness-success hide" :class="{show: witnessStatus}"  >
      <div class="user-info">
        <p>见证人：{{recording.witnessName}}</p>
        <p>职务：{{recording.workUnit}}</p>
        <p>见证时间：{{recording.createTime | formatDate('yyyy-MM-dd')}}</p>
        <div class="reviews">
          上进点评：<span>{{recording.comment}}</span>
        </div>
        <div class="stamp">
          <img src="../../../../assets/image/active/gccScholarship/stamp.png" alt="">
        </div>
      </div>
    </div>

    <van-popup v-model="shareShow" get-container="body" class="popupBoxShare-pop">
      <div class="popupBoxShare">
        <img src="../../../../assets/image/active/oneYear/share.png" class="icon" alt />
        <p class="text1">点击右上角，发送给领导，让领导进行上进点评</p>
        <p @click="shareShow=false" class="title">我知道了</p>
      </div>
    </van-popup>

    <share
      :title="wxShare.title"
      :desc="wxShare.desc"
      :link="wxShare.shareLink"
      :scholarship="wxShare.scholarship"
      :imgUrl="wxShare.imgUrlL"
      :isActivity="true"
      ref="share"
    />

  </div>
</template>
<script>
import share from "@/components/share";
import mixin from '../mixin'
import bridge from '@/plugins/bridge';
import { AppShareUrl } from '../config'
import { isAndroid, isIOS } from "@/common";
export default {
  props:{
    isReceive: {
      type:Boolean,
      default: false,
    }
  },
  mixins:[ mixin ],
  components:{
    share
  },
  data(){
    return {
      scholarship:'121',//活动类型
      witnessStatus: false, //是否见证
      recording:{},//见证记录
      isAppOpen: false,
      notWitnessed:false,
      userName:'',
      wxShare:{
        title:"读本科报广州商学院，奖3000元奖学金，限时领取！",
        desc: '为鼓励更多上进青年加入学习队伍，远智教育推出“广州商学院本科奖学金”活动，名额有限，快来领取，提升学历吧！',
        shareLink: window.location.origin + '/active/gccScholarship/index',
        scholarship:'121',
        imgUrlL: "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png",
      },
      shareShow:false,
    }
  },
  created(){
    this.userName = this.storage.getItem("realName") || this.storage.getItem("zmcName");
    this.setIsAppOpen();
    this.getWitnessStatus();
  },
  methods:{
    setIsAppOpen() {
      bridge.callHandler('isAppOpen').then((res) => {
        if (res.appOpen) {
          this.isAppOpen = true;
          this.getUserInfo();
        }
      });
    },
    getCookie(name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return false;
      }
    },
    //获取用户信息
    getUserInfo(){
      let params = {
        authToken: this.getCookie("authToken")
      };
      this.$http.post('/us/userInfo/1.0/', params).then(res=>{
        let { code, body } = res;
        if(code == '00'){
          this.userName = body.realName;
        }
      })
    },
    // 获取用户当前是否被认证
    async getWitnessStatus(){
      let api = '/mkt/isAdvanceByUserId/1.0/';
      let params ={
        scholarship:this.scholarship,
        coverToken:this.storage.getItem("authToken")
      }

      await this.$http.post(api,params).then(res=>{
        const { code } = res;
        if(code == '00'){
          // true 见证 false 未见证
          this.witnessStatus = res.body;
          if(res.body == false){
            this.notWitnessed = true;
          };
          if(res.body){
            this.getIswitness();
          }
        }
      })
    },
    // 获取个人被上进认证记录
    getIswitness(){
      let api = '/mkt/getAdvanceWitnessByCoverToken/1.0/';
      let params = {
        coverToken: this.storage.getItem("authToken"),
        scholarship: this.scholarship,
      };
      this.$http.post(api, params).then((res) => {
        let { code, body} = res;
          if (code == "00") {
            if (body) {
              this.recording = body;
            }
          }
      })
    },
    open(){
      let ShareUrl = window.location.origin; //域名地址
      let InvitePeopleToken = encodeURIComponent(this.storage.getItem("authToken")); //邀请人token
      let regChannel = '3';

      if(this.isAppOpen){

        ShareUrl = AppShareUrl;
        InvitePeopleToken = this.getCookie("authToken");

        if(isAndroid()){
          regChannel = '5';
        }
        if(isIOS()){
          regChannel = '6';
        }

      }

      this.wxShare.title = `求助！我是上进青年${this.userName}，邀请您为我上进承诺见证助威！`;
      this.wxShare.desc = '我正在申请远智教育2022级广州商学院本科奖学金，需要您的见证才可以激活3000元奖学金，快来帮帮我！'
      this.wxShare.shareLink = `${ShareUrl}/active/gccScholarship/witness?sprint_token=${InvitePeopleToken}&stdName=${encodeURIComponent(this.userName)}&regOrigin=14&regChannel=${regChannel}&inviteId=${InvitePeopleToken}`;

      this.$nextTick(() => {
        const now = new Date().getTime();

        if(now < this.AC_INFO.StartTime){
          this.$modal({
            message: "活动还未开始~请耐心等待噢！",
            icon: "warning",
          });
          return;
        }

        if(now > this.AFTER7DAYS) {
          this.$modal({
            message: "活动已结束~下次赶早哦！",
            icon: "warning",
          });
          return;
        }

        if(this.isAppOpen){
          let params = {
            title: this.wxShare.title,
            content: this.wxShare.desc,
            image: this.wxShare.imgUrlL,
            url: this.wxShare.shareLink + "&scholarship=121",
            channel: '1'
          }
          bridge.callHandler('shareWebPage',params);
          return;
        }

        this.shareShow = true;
        this.$refs.share.openTwo();
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.commitment{
  width: 3.55rem;
  padding-bottom: 0.28rem;
  background: #FFFFFF;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.11);
  border-radius: 10px;
  margin:0 0.1rem;
  .title{
    font-size: 0.18rem;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #170606;
    text-align: center;
    padding: 0.18rem 0;
  }
  .content{
    border-bottom: 1px dashed #EF464C;
    margin: 0 0.1rem;
    padding-bottom: 0.16rem;
    p{
      font-size: 0.14rem;
      text-indent: 2em;
    }
  }
  .note{
    padding:0 0.16rem;
    margin-top: 0.1rem;
    p{
      font-size: 0.12rem;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #C13935;
      line-height: 17px;
    }
    p + p {
      margin-top: 0.18rem;
    }
  }
  .btns{
    margin-top: 0.24rem;
    text-align: center;
    .activation-btn{
      width: 2.52rem;
      height: 0.44rem;
      background: url('../../../../assets/image/active/gccScholarship/activationBtn.png') no-repeat;
      background-size:100%;
      margin: 0 auto;
      display: block;
    }
    .link{
      margin-top: 0.14rem;
      display: inline-block;
      // text-decoration:inherit;
      text-decoration: underline;
      color:#B9B4B4;
      font-size: 0.14rem;
    }
  }

  .witness-success{
    position: relative;
    .user-info{
      padding: 0 0.16rem;
      margin: 0.16rem 0;
      p {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: .2rem;
        margin-bottom: 0.1rem;
      }
      .reviews{
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        margin-top: 0.18rem;
        word-wrap: break-word;

        span{
          font-size: 0.14rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #6E1002;

        }

      }
      .stamp{
        width: 1.36rem;
        height: 0.68rem;
        left: 1.78rem;
        top: 0.1rem;
        position: absolute;
        img{
          width: 100%;
        }

      }
    }
  }
}

.hide {
  display:none;
}
.show {
  display:block !important;
}

.popupBoxShare-pop {
    border-radius: 0.1rem;
  }
.popupBoxShare {
  width: 2.83rem;
  height: 1.93rem;
  background: #fff;
  border-radius: 10px;
  text-align: center;
  border-radius: 0.1rem;
  position: relative;
  .icon {
    height: 0.32rem;
    width: 0.32rem;
    margin-top: 0.3rem;
    border-radius: 50%;
  }
  .title {
    position: absolute;
    color: rgba(23, 6, 6, 1);
    font-size: 0.14rem;
    margin-top: 0.14rem;
    bottom: 0;
    line-height: 0.5rem;
    width: 100%;
    color: #f06e6c;
    border-top: 1px solid rgba(23, 6, 6, 0.08);
  }

  .text1 {
    margin-top: 0.2rem;
    color: #170606;
    font-size: 0.14rem;
    padding: 0 0.2rem 0.2rem;
  }
}

[v-cloak] {
  display: none !important;
}

</style>
