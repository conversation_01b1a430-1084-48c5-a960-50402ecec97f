<template>
  <div class="pay-success">
    <img class="bg" src="../../../assets/image/active/adultExam/<EMAIL>" alt=""/>
    <div class="inner">
      <div class="title">
        <h1 class="h1">缴费成功！</h1>
        <p class="subtitle">招生老师会联系您，为您提供帮助！</p>
      </div>
      <div class="cont">
        <div class="hd">获得奖励</div>
        <div class="bd">
          <div class="item-wrap">
            <div class="item cl" v-if="discounts===0">
              <img class="ico" src="../../../assets/image/active/adultExam/<EMAIL>" alt=""/>
              <div class="txt">第三年学费<span class="fc1">全免</span></div>
            </div>
            <div class="item cl" v-else>
              <img class="ico" src="../../../assets/image/active/adultExam/<EMAIL>" alt=""/>
              <div class="txt">第三年学费<span class="fc1">优惠1500元</span></div>
            </div>
            <div class="item cl">
              <img class="ico" src="../../../assets/image/active/adultExam/<EMAIL>" alt=""/>
              <div class="txt">辅导材料</div>
            </div>
            <div class="item cl">
              <img class="ico" src="../../../assets/image/active/adultExam/<EMAIL>" alt=""/>
              <div class="txt">全年辅导课免费上</div>
            </div>
          </div>
        </div>
        <div class="ft">
          <p class="txt">邀请好友与你一起参加活动拿奖励，提升学历。</p>
          <button class="btn mt-12" @click="openShare">邀请好友报读</button>
        </div>
      </div>
    </div>
    <share title="2018成考开始啦！现在报读可享全额助学金(三年学费全免)" desc="提前开启2018成考报名，邀请好友入学，上万智米疯狂送" :link="shareLink" scholarship="14" ref="share"/>
  </div>
</template>

<script>
  import share from '@/components/share'
  export default {
    data() {
      return {
        shareLink: '',
        unvsId: '',
        freeIds: ['52', '51', '31', '1']
      }
    },
    computed: {
      discounts: function () {
        return this.freeIds.includes(this.unvsId) ? 0 : 1500;
      }
    },
    created() {
      this.unvsId = this.$route.query.unvsId;
      this.shareLink = `${window.location.origin}/active/adultExam`;
    },
    methods: {
      openShare: function () {
        this.$refs.share.open(null, 'login');
      }
    },
    components: {share}
  }
</script>

<style lang="less" scoped>
  .pay-success{
    position:relative; height:100vh; background-color:#fa5355;
    > .bg{ width:100%; }
    .inner{ position:absolute; top:0; right:0; left:0; }
    .title{ height:1.56rem; }
    .h1{ padding-top:.43rem; padding-left:.13rem; text-align:center; color:#fff; font-size:.26rem; }
    .subtitle{ margin-top:.04rem; text-align:center; color:#fff; font-size:.13rem; }
    .cont{
      .hd{ line-height:.28rem; text-align:center; color:#fff; font-size:.18rem; }
      .bd{
        padding-top:.1rem; text-align:center;
        .item-wrap{ display:inline-block; }
      }
      .ft{
        margin-top:.8rem; text-align:center;
        .txt{ color:#fff; font-size:.13rem; }
        .btn{ width:1.8rem; height:.44rem; line-height:.44rem; color:#fa5355; font-size:.17rem; border:none; background-color:#fff; border-radius:.05rem; }
      }
      .item{
        margin-top:.2rem; text-align:left;
        .ico{ float:left; width:.5rem; height:.5rem; }
        .txt{ margin-left:.7rem; line-height:.5rem; color:#5a5a5a; font-size:.2rem; }
      }
    }
    .fc1{ color:#fa5355; }
  }
</style>
