<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.cityCode===item.cityCode}" @click="selected(item)">{{item.cityName}}</div>
    <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="options.length"></load-bar>
  </div>
</template>

<script>
  import { isNewSelf } from '@/common';
  import loadBar from '@/components/loadBar';
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data() {
      return {
        options: [],
        unvsId:'',
        grade:'',
        scholarship:'',
        pageNum: 0,
        pageSize: 20,
        type: 'C',
        isLoading:false,
        allLoaded:false,
        pfsnLevel:'',
        pfsnId:'',
        recruitType:''
      }
    },
    created() {
      this.pfsnId = this.datas.pfsn.pfsnId;
      this.activityName = this.datas.activityName;
      this.grade = this.datas.grade.dictValue;
      this.unvsId = this.datas.unvs.unvsId;
      this.scholarship= this.datas.unvs.scholarship;
      this.pfsnLevel= this.datas.pfsnLevel.pfsnLevel;
      this.recruitType=this.datas.recruitType.dictValue;
    },
    methods: {
      getCity(){
        // if(this.unvsId==='46'){ // 原本的代码
        //   this.isLoading = true;
        //   const data = {
        //     type: this.type,
        //     pfsnName: '',
        //     grade: this.grade,
        //     pfsnLevel: this.pfsnLevel,
        //     unvsId: this.unvsId,
        //     pfsnId: this.pfsnId,
        //     recruitType:'2',
        //     scholarship:this.datas.scholarship,
        //     pageNum: this.pageNum,
        //     pageSize: this.pageSize
        //   }
        //   this.$http.post('/mkt/getGkOpenEnrollCityInfo/1.0/', data).then(res => {
        //     if (res.code !== '00') return;
        //     const datas = (res.body || []);
        //     this.options.push(...datas);

        //     this.$nextTick(() => {
        //       this.allLoaded = datas.length === 0;
        //       this.isLoading = this.allLoaded;
        //     });
        //   });
        //   return;
        // }
        let url = '/mkt/enrollInfo/1.0/'; // 请求url
        let data = {
          type: this.type,
          pfsnName: '',
          grade: this.grade,
          pfsnLevel: this.pfsnLevel,
          unvsId: this.unvsId,
          pfsnId: this.pfsnId,
          scholarship:typeof(this.datas.scholarship)==='string'?[this.datas.scholarship]:this.datas.scholarship,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          recruitType:this.recruitType
        }
        if (this.unvsId == '46') {
          url = '/mkt/getGkOpenEnrollCityInfo/1.0/';
          data.recruitType = 2;
        }
        if (this.recruitType == 4 && !isNewSelf) { // 自考获取城市跟别的不一样
          url = '/mkt/selectSelfExamCity/1.0/';
        }
        let headers = {};
        if (this.isDingding) {
          url = `${domain}/newStudentChange/getEnrollInfo.do`;
          data = qs.stringify(data);
          headers = {
            'content-type': 'application/x-www-form-urlencoded',
          };
        }
        this.isLoading = true;
        this.$http.post(url, data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || [];
          this.options.push(...datas);

          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        this.pageNum++;
        this.getCity();
      },
      selected: function (val) {
        this.$emit('input', val);
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>

