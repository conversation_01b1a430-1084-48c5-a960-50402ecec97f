<template>
  <div class="yz-ckSprint">
    <inviteTop text='邀请你一起上进学习' @getInviteId='getInviteId' />
    <!-- 分享组件 -->
    <share :title="shareTitle" :desc="shareDesc" :link="link" :isActivity="true" :scholarship="scholarship"
           :imgUrl="shareImage" :regOrigin='regOrigin' ref="share" />

    <!-- 抽签结果 -->
    <draw v-model="drawShow" :drawType="drawType" @toDraw="toDraw" @changShare="changShare" />
    <!-- 抽签中 -->
    <drawing v-model="drawingShow" />
    <!-- 主页 -->
    <active-main :lastDate='lastDate' :loginMethod='login' @toTestFate='toTestFate' />
    <text-countdown :isAppOpen='isAppOpen' :lastDate='lastDate' @getOffsetTop='getOffsetTop' />
    <!-- 休闲区 -->
    <leisure-area />

    <!-- 上进圈子 -->
    <forward-circle />

    <!-- 弹窗相关 -->
    <growth-dialog v-model="growthShow" @comeTogether='comeTogether' />
    <!-- 分享落地 -->
    <test-dialog v-model="showTest" :showRecord='gotList.length > 0' :drawCount='drawCount' @openList='openGotList'
                 @startDraw='startDraw' />
    <!-- 学习获得抽奖机会 -->
    <had-change v-model="showHadChance" @startDraw='startDraw' />
    <!-- 抽奖记录 -->
    <got-list v-model="showGotList" :loading='loading' :list='gotList' @loadMore='gotListLoadMore'
              @changShare='changShare' />
    <!-- 获取机会 -->
    <get-chance v-model="showGetChance" :scroll-top='textOffsetTop' @openShare='getChangeOpenShare'
                :isShare="isShare" />
    <!-- 唤醒App -->
    <wx-open-app v-if='!isAppOpen' />
  </div>
</template>

<script>
import appShare from '@/mixins/appShare';
import share from "@/components/share";
import inviteTop from "@/view/active/enrollAggregate/components/invite-top";
import LeisureArea from './components/leisure-area.vue';
import TextCountdown from './components/text-countdown.vue';
import { isLogin, toLogin } from '@/common';
import { toAppLogin } from '@/common/jump';
import ForwardCircle from './components/forward-circle.vue';
import GrowthDialog from './components/growth-dialog.vue';
import TestDialog from './components/test-dialog.vue';
import HadChange from './components/had-change.vue';
import GotList from './components/got-list.vue';
import GetChance from './components/get-chance.vue';
import ActiveMain from './components/active-main.vue';
import Draw from './components/draw.vue';
import Drawing from './components/drawing.vue';
import WxOpenApp from './components/wx-open-app.vue';

const defaultShareParams = {
  shareTitle: '2021成人高考 通关大秘籍',
  shareDesc: '最全面、最高效的备考攻略，跟着节奏走，让你学习休闲两不误！',
  link: '/active/ckSprint',
  shareImage: '',
};

export default {
  mixins: [appShare],
  components: {
    share,
    inviteTop,
    LeisureArea,
    TextCountdown,
    ForwardCircle,
    ActiveMain,
    GrowthDialog,
    TestDialog,
    HadChange,
    GotList,
    GetChance,
    Draw,
    Drawing,
    WxOpenApp
  },
  data () {
    return {
      inviteId: '',
      shareTitle: '2021成人高考 通关大秘籍',
      shareDesc: '最全面、最高效的备考攻略，跟着节奏走，让你学习休闲两不误！',
      shareImage: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/ae4a6056-1905-47b5-a7bb-81b1452c55b4.png',
      scholarship: '2522',
      regOrigin: '52',
      link: '/active/ckSprint',
      endTimeStamp: new Date(2021, 9, 24).getTime(), // 结束时间
      show: false,
      drawShow: false, // 抽签弹窗
      growthShow: false,
      drawType: -1,
      drawingShow: false,
      isLogin: isLogin(),
      showGotList: false,
      showTest: false,
      showDialog: 0, // 1 学习记录 2
      drawCount: 0,
      showGetChance: false,
      showHadChance: false,
      textOffsetTop: 0,
      loading: false,
      pageNum: 1,
      pageSize: 15,
      gotList: [],
      gotTotal: 0,
      isShare: false,//是否分享
      isDrawing: false,
    };
  },
  computed: {
    appShareParams () {
      return {
        title: this.shareTitle,
        content: this.shareDesc,
        url: this.link,
        image: this.shareImage,
        scholarship: this.scholarship,
        regOrigin: this.regOrigin,
      };
    },
    lastDate () {
      const cha = this.endTimeStamp - Date.now();
      const date = cha / (1000 * 60 * 60 * 24);
      return parseInt(date);
    },

  },
  watch: {
    appShareParams () {
      this.setShareParams(this.appShareParams);
    },
  },
  created () {
    this.$yzStatistic('sprintAct.base.browse', '1', '首页');
    const drawType = this.$route.query.drawType || '';
    if (drawType) {
      this.drawType = Number(drawType);
      this.drawShow = true;
      this.$yzStatistic('sprintAct.base.browse', '31', '分享抽签页');

    }
  },
  mounted () {
    this.initAppShare(() => {
      this.setShareParams(this.appShareParams);
      this.drawShow = false;
      // this.getLearningRecords();
    });
    if (this.isLogin) {
      this.dailySign();
      this.drawRecord(1);
    }
  },
  methods: {
    getInviteId (id) {
      this.inviteId = id;
    },
    // 设置分享参数
    setShare (isDefault = true, ofterParams = {}) {
      if (isDefault) {
        this.link = defaultShareParams.link;
        this.shareTitle = defaultShareParams.shareTitle;
        this.shareDesc = defaultShareParams.shareDesc;
        return;
      }
      this.link = ofterParams.link;
      this.shareTitle = ofterParams.title;
      this.shareDesc = ofterParams.shareDesc;
    },
    comeTogether () {
      this.$http.post('/us/drawCount/1.0/', {
        scholarship: 2522,
        type: 1
      }).then(res => {
        if (res.body > 0) {
          this.showHadChance = true
        }
      })

    },
    // 有抽签次数 抽签
    toDraw () {
      this.$yzStatistic('sprintAct.base.click', '20', '再抽一次');

      this.drawShow = false;
      this.startDraw();
    },
    // 测考运 抽签
    toTestFate () {
      // 打开测试页面
      this.$yzStatistic('sprintAct.base.click', '3', '首页-测考运');
      if (!this.login()) {
        return;
      }
      // 先获取次数 再打开
      this.getDrawCount();
      this.showTest = true;
    },
    openGotList () {
      this.showGotList = true;
      this.drawRecord(1);
    },
    getGotList (list) {
      this.gotList = list;
    },
    login () {
      if (!this.isLogin) {
        if (this.isAppOpen) {
          toAppLogin();
          return false;
        }
        toLogin.call(this, null);
        return false;
      }
      return true;
    },
    getOffsetTop (num) {
      this.textOffsetTop = num;
    },
    getChangeOpenShare () {
      this.setShare();
      this.openShareMethod();
    },
    gotListLoadMore () {
      if (!this.isLogin) {
        return;
      }
      this.drawRecord();
    },
    // 签到
    async dailySign () {
      this.$yzStatistic('sprintAct.base.click', '22', '签到');

      const res = await this.$http.post('/us/dailySign/1.0/', { type: 4, scholarship: this.scholarship });
      if (res.code == '00') {
        if (!this.growthShow && res.body) {
          this.showHadChance = true;
          this.getDrawCount();
        }
      }
    },
    // 抽奖
    async startDraw (type = 1) {
      if (this.isDrawing) {
        return;
      }
      if (!this.login()) {
        return;
      }
      if (this.drawCount <= 0) {
        this.showGetChance = true;
        return;
      }
      this.isDrawing = true;
      const res = await this.$http.post('/us/startDraw/1.0/', { scholarship: this.scholarship, type });
      setTimeout(() => {
        this.isDrawing = false;
      }, 200);
      if (res.code == '00') {
        if (type == 1) {
          this.drawType = Number(res.body.drawType);
          this.drawingShow = true;
          this.getDrawCount();
          this.drawRecord(1);
          setTimeout(() => {
            this.drawingShow = false;
            this.drawShow = true;
          }, 2500);
        }
      }
    },
    // 获取抽奖次数
    async getDrawCount () {
      const res = await this.$http.post('/us/drawCount/1.0/', {
        scholarship: 2522,
        type: 1
      });
      if (res.code == '00') {
        this.drawCount = res.body || 0;
      }
    },
    // 读书记录
    async getLearningRecords () {
      const { code, body } = await this.$http.post("/us/learningRecords/1.0/", {
        scholarship: 2522,
        type: 2
      });
      if (code === '00') {
        if (body > 0) {
          this.growthShow = true;
        }
      }
    },
    // 抽奖记录
    async drawRecord (pageNum = this.pageNum) {
      if (this.loading) {
        return;
      }
      if (this.gotTotal == this.gotList.length && this.pageNum > 1) {
        return;
      }
      this.loading = true;
      this.pageNum = pageNum || this.pageNum;
      const res = await this.$http.post('/us/drawRecord/1.0/', {
        scholarship: 2522,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: 1,
      });
      if (res.code == '00') {
        this.gotList = this.pageNum == 1 ? res.body.data : this.gotList.concat(res.body.data);
        this.gotTotal = res.body.recordsTotal;
        this.pageNum += 1;
      }
      setTimeout(() => {
        this.loading = false;
      }, 100);
    },
    openShareMethod () {
      this.$yzStatistic('sprintAct.base.click', '12', '分享');
      this.drawShare();
      if (!this.isAppOpen) {
        this.$nextTick(() => {
          this.$refs.share.openNO(true);
        });
        return;
      }
      setTimeout(() => {
        this.openAppShare();
      }, 200);
    },
    // 抽签分享
    changShare (isShare, shareObj = {}) {
      this.setShare(!isShare, shareObj);
      if (isShare) {
        this.openShareMethod();
      }
    },
    async drawShare () {
      const { code, body } = await this.$http.post('/us/drawShare/1.0/', {
        scholarship: 2522,
        type: 3,
      });
      if (code === '00' && body) {
        this.isShare = true
      }
    }
  },


}
</script>
<style lang='less' scoped>
@import url(./index.less);
</style>
