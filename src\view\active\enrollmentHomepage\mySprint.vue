<template>
  <div class="sprint_list">
    <div class="list">
      <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50" >
        <ul>
          <li v-for="item in sprintList">
            <div class="detail">
              <p><span>活动类型:</span><span>{{item.scholarshipName}}</span></p>
              <p><span>见证人：</span><span>{{item.witnessName}}</span></p>
              <p><span>工作单位:</span><span>{{item.workUnit}}</span></p>
              <p><span>上进点评：</span><span>{{item.comment}}</span></p>
              <p><span>见证时间:</span><span>{{item.createTime | formatDate('yyyy.MM.dd')}}</span></p>
            </div>
            <img src="../../../assets/image/active/old/sprint_success.png" alt="" class="success">
          </li>
        </ul>
      </div>
        <div class="empty" v-if="sprintList.length==0">
          暂无见证
        </div>
      </div>
  </div>
</template>

<script>
  import loadBar from "@/components/loadBar"
  export default {
    name: "sprintNotes",
    data(){
      return{
        sprintList:[],
        pageNum:0,
        pageSize:10,
        isLoading:false,
        allLoaded:false
      }
    },
    created(){
      this.loadMore()
    },
    methods:{
      getSptintList(){
        this.$http.post('/mkt/getAdvanceWitnessListByCWUserId/1.0/',{type:'advance.witness.act.202001',pageNum:this.pageNum,pageSize:this.pageSize}).then(res=>{
          if(res.code=='00'){
            const datas = (res.body || []);
            this.sprintList.push(...res.body);
            this.$nextTick(()=>{
              this.allLoaded = datas.length === 0;
              this.isLoading = this.allLoaded;
            })
          }
        })
      },
      loadMore(){
        this.pageNum++;
        this.getSptintList()
      }
    },
    components:{loadBar}
  }
</script>

<style scoped lang="less">
  .sprint_list{
    background-color: white;
    height:auto;
    overflow: hidden;
    min-height: 100vh;
    .headBox_school{
      width: .92rem;
      height: 0.3rem;
      text-align: center;
      margin: 0.4rem auto 0rem;
      color:rgba(54, 54, 54, 1) ;
      font-size: .15rem;
      position: relative;
      &:before{
        content:"";
        position: absolute;
        left:-.4rem;
        top:0rem;
        width: .39rem;
        height:.24rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/icon_left.png");
        background-size: .39rem;
      }
      &:after{
        content:"";
        position: absolute;
        right:-.4rem;
        top:0rem;
        width: .39rem;
        height:.24rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
        background-size: .39rem;
      }
    }
    .list{
      width: 100%;
      height: auto;
      overflow: hidden;
      .empty{
        position: fixed;
        top:0;
        bottom:0;
        left:0;
        right: 0;
        margin: auto;
        width: 1rem;
        height: 1rem;
        padding-top: .83rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/no_sprint.png");
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        color: rgba(54, 54, 54, 1);
      }
      ul{
        li{
          margin: .1rem auto;
          width: 3.52rem;
          height: auto;
          overflow: hidden;
          background-color: white;
          box-shadow: 0px 0px 9px rgba(152, 78, 12, 0.2);
          border-radius: .05rem;
          position: relative;
          .head_info{
            .head{
              width: .38rem;
              height: .38rem;
              overflow: hidden;
              float: left;
              border-radius: 50%;
              img{
                width: 100%;
              }
            }
            .info{
              float: left;
              width: 2.8rem;
              margin-left: .05rem;
              margin-top: .03rem;
              p{
                clear: both;
                margin-bottom: .1rem;
                font-size: .14rem;
                .name{
                  float: left;
                }
                .time{
                  float: right;
                  color: rgba(54, 54, 54, .4);
                  font-size: .12rem;
                }
              }
            }
          }
          .success{
            position: absolute;
            width: 1.37rem;
            height: .68rem;
            top:0;
            right:0;
          }
          .detail{
            clear: both;
            width: 100%;
            padding: .15rem .13rem;
            /*margin-left: .3rem;*/
            font-size: .14rem;
            color: rgba(54, 54, 54, .8);
            line-height: .15rem;
            span{
              &:first-of-type{
                float: left;
                width:.75rem ;
                color: rgba(239, 186, 6, 1);
                margin-right: .03rem;
                line-height: .2rem;
              }
              &:last-of-type{
                max-width: 2.43rem;
                float: left;
                height: auto;
                overflow: hidden;
                line-height: .2rem;
              }
            }
            p{
              height: auto;
              overflow: hidden;
              margin-bottom: .05rem;
              &:last-of-type{
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
  .activity_rule{
    position: absolute;
    right: 0;
    text-align: center;
    height: .34rem;
    top:.25rem;
    width: 1.05rem;
    font-size: .14rem;
    z-index: 9999;
    padding-left: .03rem;
    background-image: url("../../../assets/image/active/sprintBeforeExam/activity_rule.png");
    background-size: 1.05rem .34rem;
    a{
      display: block;
      width: 100%;
      line-height: .3rem;
      padding-left: .02rem;
      text-align: center;
      color: rgba(130, 77, 9, 1);
    }
  }
</style>
