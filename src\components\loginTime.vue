<template>
  <div :disabled="isDisabled" @click="run" class="time">{{btnCaptchaName}}  <img v-if="!isDisabled" src="../assets/image/upGradelogin/zuo.png" alt class="zuo" /></div>
</template>

<script>
export default {
  props: {
    second: {
      type: Number,
      default: 60
    }
  },
  data() {
    return {
      isDisabled: false,
      time: 0,
      timer: null
    };
  },
  created() {
    // this.start();
  },
  computed: {
    btnCaptchaName: function() {
      return this.isDisabled ? `${this.time}s` : "重新获取验证码";
    }
  },
  beforeDestroy() {
    this.clearTimer();
  },
  methods: {
    start: function() {
      this.time = this.second;
      this.setDisabled(true);

      this.timer = setInterval(() => {
        this.time -= 1;
        if (this.time <= 0) {
          this.clearTimer();
        }
      }, 1000);
    },
    clearTimer: function() {
      this.timer && clearInterval(this.timer);
      this.setDisabled(false);
    },
    setDisabled: function(val) {
      this.isDisabled = val;
    },
    run: function() {
      this.$emit("run");
    }
  }
};
</script>
<style lang="less" scoped>
.time {
  font-size: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
}
 .zuo {
          width: 0.08rem;
          height: 0.13rem;
          position: relative;
          top: 0.04rem;
          left: 0.04rem;
        }
</style>
