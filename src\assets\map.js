
import {mapHTTPType} from '../../src/config'
// export function MP(ak) {
//     return new Promise(function(resolve, reject) {
//         window.onload = function() {
//             resolve(BMap)
//         }
//         if(!window.BMap){
//             var script = document.createElement("script");
//             script.type = "text/javascript";
//             script.src = mapHTTPType + "://api.map.baidu.com/api?v=3.0&ak=" + ak ;
//             script.onerror = reject;
//             document.head.appendChild(script);
//         }

//     })
// }


export  function loadBMap(ak) {
    return new Promise(function(resolve, reject) {
      if (typeof BMap !== 'undefined') {
        resolve(BMap)
        return true
      }
      window.onBMapCallback = function() {
        resolve(BMap)
      }
      let script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = mapHTTPType + "://api.map.baidu.com/api?v=3.0&ak=" + ak + '&callback=onBMapCallback';
      script.onerror = reject
      document.head.appendChild(script)
    })
  }
