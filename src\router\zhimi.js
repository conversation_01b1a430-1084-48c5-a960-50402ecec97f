import config from '../config';
import {loadPcdJson, inviteId} from '../common';

export default [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/newHome',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('../view/home/<USER>'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../view/account/login'),
    meta: {title: '登录/注册',loadDict:true,}
  },
  {
    path: '/privacy',
    name: 'privacyAgreement',
    component: () => import('../view/account/privacy'),
    meta: {title: '隐私协议',loadDict:true}
  },
  {
    path: '/joinus',
    name: 'joinus',
    component: () => import('../view/account/joinUs.vue'),
    meta: {title: '加入我们一起上进学习'}
  },
  {
    path: '/goods/exchange/:id',
    name: 'exchange',
    component: () => import('../view/goods/exchange'),
    children: [
      {
        path: 'introduction',
        name: 'exchangeIntroduction',
        component: () => import('../view/goods/introduction')
      }
    ],
    meta: {title: `${config.title}-兑换`, requiresAuth: true}
  },
  {
    path: '/goods/auction/:id',
    name: 'auction',
    component: () => import('../view/goods/auction'),
    children: [
      {
        path: 'rule',
        name: 'auctionRule',
        component: () => import('../view/goods/auctionRule'),
      },
      {
        path: 'introduction',
        name: 'auctionIntroduction',
        component: () => import('../view/goods/introduction')
      }
    ],
    meta: {title: `${config.title}-竞拍`, requiresAuth: true}
  },
  {
    path: '/goods/lottery/:id',
    name: 'lottery',
    component: () => import('../view/goods/lottery'),
    children: [
      {
        path: 'introduction',
        name: 'lotteryIntroduction',
        component: () => import('../view/goods/introduction')
      }
    ],
    meta: {title: `${config.title}-抽奖`, requiresAuth: true}
  },
  {
    path: '/pay/goodsSubmitOrder',
    name: 'submitOrder',
    component: () => import('../view/order/submitOrder'),
    beforeEnter: loadPcdJson,
    children:[
      {
        path: 'address',
        name: 'submitOrderAddress',
        component: () => import('../view/order/children/submitOrderAddress'),
        beforeEnter: loadPcdJson,
      },
      {
        path: 'alipay',
        name: 'alipaymentPost',
        component: () => import('../view/student/home/<USER>'),
        meta: {
          title: '支付宝支付',
          requiresAuth: false
        },
      }
    ],
    meta: {requiresAuth: false,title:'确认订单'}
  },
  {
    path: '/myorder',
    name: 'order',
    component: () => import('../view/order/list'),
    meta: {title: `我的订单`, requiresAuth: true,loadDict:true}
    // keepAlive: true,
  },
  {
    path: '/myorder/address',
    name: 'orderAddress',
    component: () => import('../view/order/orderAddress'),
    beforeEnter: loadPcdJson,
    meta: {title: `${config.title}-我的订单`, requiresAuth: true}
  },
  {
    path: '/vocationalCourse',
    name: 'vocationalCourse',
    component: () => import('../view/vocationalCourse/home'),
    meta: {title: `职业课程`}
  },
  {
    path: '/vocationalCourse/detail/:id',
    name: 'vocationalCourseDetail',
    component: () => import('../view/vocationalCourse/courseDetail'),
    meta: {title: `职业课程详情`}
  },
  {
    path: '/order/payDeatail',
    name: 'payDeatail',
    component: () => import('../view/order/payDeatail.vue'),
    meta: {title: `缴费详情`}
  },
  {
    path: '/vocationalCourse/myCourse',
    name: 'myCourse',
    component: () => import('../view/vocationalCourse/myCourse'),
    meta: {title: `职业课程`, requiresAuth: true}
  },
  {
    path: '/pay/Coursepayment',
    name: 'vocationalPayment',
    component: () => import('../view/vocationalCourse/payment'),
    meta: {title: `职业课程`, requiresAuth: true}
  },
  {
    path: '/myorder/courseDetail',
    name: 'orderCourseDetail',
    beforeEnter: loadPcdJson,
    component: () => import('../view/order/courseDetail'),
    meta: {title: `${config.title}-我的订单`, requiresAuth: true,loadDict:true}
  },
  {
    path: '/myorder/detail',
    name: 'orderDetail',
    beforeEnter: loadPcdJson,
    component: () => import('../view/order/detail'),
    meta: {title: `订单详情`, requiresAuth: true,loadDict:true}
  },
  {
    path: '/myorder/logistics',
    name: 'orderLogistics',
    component: () => import('../view/order/logistics'),
    meta: {title: `${config.title}-查看物流`, requiresAuth: true}
  },
  {
    path: '/map',
    name: 'map',
    component: () => import('../view/map/map'),
    meta: {requiresAuth: true}
  },
  {
    path: '/integral',
    name: 'integral',
    component: () => import('../view/integral/integral'),
    meta: {title: `智米记录`, requiresAuth: true, keepAlive: false,}
  },
  {
    path: '/zmRaiders',
    name: 'zmRaiders',
    component: () => import('../view/zmRaiders/zmRaiders'),
    meta: {title: `${config.title}-智米攻略`,}
  },
  {
    path: '/newStudentVideo',
    name: 'newStudentVideo',
    component: () => import('../view/zmRaiders/newStudentVideo'),
    meta: {title: `${config.title}-新生入学指导`}
    },
  // {
  //   path: '/settings',
  //   name: 'settings',
  //   component: resolve =>require(['../view/settings/home'],resolve),
  //   meta: {title: `${config.title}-个人中心`, requiresAuth: true, keepAlive: true,}
  //   // children:[
  //   //   {path:'sign',name:'sign',component: () => import('../view/settings/signshare')}
  //   // ]
  // },
  {
    path: '/settings/personalInfo',
    name: 'personalInfo',
    component: () => import('../view/settings/personalInfo'),
    meta: {requiresAuth: true,loadDict:true,title:'个人资料'}
  },
  {
    path: '/settings/name/edit',
    name: 'nameEdit',
    component: () => import('../view/settings/nameEdit'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/profession/edit',
    name: 'professionEdit',
    component: () => import('../view/settings/professionEdit'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/zhiiRecord',
    name: 'zhiiRecord',
    component: () => import('../view/settings/zhiiRecord'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/enrollList',
    name: 'enrollList',
    component: () => import('../view/settings/enrollList/home'),
    meta: {requiresAuth: true,title:'助学邀约，还能赚智米兑换礼品！'}
  },
  {
    path: '/settings/enrollList/myEnroll',
    name: 'myEnrollRecord',
    component: () => import('../view/settings/enrollList/myEnroll'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/enrollList/zmMethod',
    name: 'zmMethod',
    component: () => import('../view/settings/enrollList/zmMethod'),
    meta: {requiresAuth: true,title:'助学攻略'}
  },
  {
    path: '/settings/myFans',
    name: 'myFans',
    component: () => import('../view/settings/myFans'),
    meta: {requiresAuth: true,title:"我的邀约"}
  },
  {
    path: '/settings/myEnrollStu',
    name: 'myEnrollStu',
    component: () => import('../view/settings/myEnrollStu'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/coupon',
    name: 'coupon',
    component: () => import('../view/settings/coupon'),
    meta: {requiresAuth: true,loadDict:true,title:'奖学金'}
  },
  {
    path: '/settings/address',
    name: 'address',
    component: () => import('../view/settings/address'),
    beforeEnter: loadPcdJson,
    meta: {requiresAuth: true,title:'地址管理'}
  },
  {
    path: '/settings/address/edit',
    name: 'addressEdit',
    component: () => import('../view/settings/addressEdit'),
    beforeEnter: loadPcdJson,
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/address/editJD',
    name: 'addressEditJD',
    component: () => import('../view/settings/addressEditJD'),
    beforeEnter: loadPcdJson,
    meta: {requiresAuth: false}
  },
  {
    path: '/settings/address/payEdit',
    name: 'payEdit',
    component: () => import('../view/settings/payEdit'),
    meta: {requiresAuth: true}
  },
  {
    path: '/settings/idCard',
    name: 'idCard',
    component: () => import('../view/settings/idCard'),
    meta: {requiresAuth: true,title:'绑定身份证'}
  },
  {
    path: '/settings/questions',
    name: 'questions',
    component: () => import('../view/settings/questions'),
    meta: {requiresAuth: true}
  },
  {
    path: '/invite',
    name: 'invite',
    component: () => import('../view/invite/home'),
    beforeEnter: inviteId,
    meta: {title: '邀约报读'}
  },
  {
    path:'/invite/introduce',
    name: 'inviteIntroduce',
    component: () => import('../view/invite/introduce'),
    meta: {title: '邀约报读'}
  },
  {
    path:'/invite/enroll',
    name: 'inviteEnroll',
    // component: r => require.ensure([], () => require('../view/invite/enroll'), 'inviteEnroll'),
    component: () => import('../view/invite/enroll'),
    meta: {title: '', requiresAuth: true,loadDict:true},
    children: [
      {
        path:'infoConfirm',
        name: 'infoConfirm',
        beforeEnter:"",
        // component: r => require.ensure([], () => require('../view/invite/infoConfirm'), 'inviteEnroll'),
        component: () => import('../view/invite/infoConfirm'),
        meta: {title: '成考报名',loadDict:true}
      },
      {
        path: 'success',
        name: 'enrollSuccess',
        // component: r => require.ensure([], () => require('../view/invite/success'), 'inviteEnroll'),
        component: () => import('../view/invite/success'),
        beforeEnter: inviteId,
        meta: {title: '成考报名',loadDict:true}
      }
    ]
  },
  {
    path: '/invite/link',
    name: 'inviteLink',
    component: () => import('../view/invite/link'),
    meta: {title: '我的米瓣', requiresAuth: true, keepAlive: true,loadDict:true}
  },
  {
    path: '/invite/qr',
    name: 'inviteQR',
    component: () => import('../view/invite/qr'),
    meta: {title: '二维码生成'}
  },
  {
    path:'/settings/myEwm',
    name: 'myEwm',
    component: () => import('../view/settings/myEwm'),
    beforeEnter: inviteId,
    meta: {requiresAuth: true,title:'邀约二维码'}
  },
  /*{
    path:'/settings/signInfo',
    name: 'signInfo',
    component: () => import('../view/settings/signInfo'),
    meta: {requiresAuth: true}
  },*/
  {
    path: '/pay/recharge',
    name: 'zhimiPay',
    component: () => import('../view/settings/zhimiPay'),
    meta: {title: '智米充值', requiresAuth: true}
  },
  {
    path: '/training/course',
    name: 'trainingCourse',
    component: () => import('../view/settings/trainingCourse'),
    meta: {title: '内部培训', requiresAuth: true}
  },
  {
    path: '/settings/mobile',
    name: 'changeMobile',
    component: () => import('../view/settings/changeMobile'),
    meta: {title: '更换手机号', requiresAuth: true}
  },
  {
    path: '/settings/changeBindMobile',
    name: 'changeBindMobile',
    component: () => import('../view/settings/changeBindMobile'),
    meta: {title: '更绑手机号', requiresAuth: true}
  },
  {
    path: '/settings/verificationMobile',
    name: 'verificationMobile',
    component: () => import('../view/settings/verificationMobile'),
    meta: {title: '验证手机号', requiresAuth: true}
  },
  {
    path: '/settings/verificationIDCard',
    name: 'verificationIDCard',
    component: () => import('../view/settings/verificationIDCard'),
    meta: {title: '验证手机号和身份证号', requiresAuth: true}
  },
  {
      path: '/settings/taskCard',
    name: 'taskCard',
    component: () => import('../view/settings/taskCard/taskList'),
    meta: {title: '任务卡', requiresAuth: false}
  },
  {
    path: '/settings/taskCard/taskInvite',
    name: 'taskInvite',
    beforeEnter: inviteId,
    component: () => import('../view/settings/taskCard/taskInvite'),
    meta: {title: '任务卡', requiresAuth: false}
  },
  {
    path: '/scholarshipStory',
    name: 'scholarshipStory',
    beforeEnter: inviteId,
    component: () => import('../view/scholarshipStory/scholarshipStoryList'),
    meta: {title: '奖学金故事'}
  },
  {
    path: '/scholarshipStory/scholarshipStoryInfo',
    name: 'scholarshipStoryInfo',
    beforeEnter: inviteId,
    component: () => import('../view/scholarshipStory/scholarshipStoryInfo'),
    meta: {title: '奖学金故事'}
  },
  {
    path: '/scholarshipStory/visitorScholarshipStoryInfo',
    name: 'visitorScholarshipStoryInfo',
    beforeEnter: inviteId,
    component: () => import('../view/scholarshipStory/visitorScholarshipStoryInfo'),
    meta: {title: '远智教育'}
  },
  {
    path: '/scholarshipStory/comment',
    name: 'scholarshipStoryComment',
    beforeEnter: inviteId,
    component: () => import('../view/scholarshipStory/comment'),
    meta: {title: '奖学金故事'}
  },
  {
    path: '/enrollInNet',
    name: 'enrollInNet',
    component: () => import('../view/settings/enrollInNet'),
    meta: {title: '',}
  },
  {
    path:'/examineeCard',
    name:'examineeCard',
    component: () => import('../view/settings/examineeCard'),
    meta: {title: '准考证',requiresAuth:true}
  },
  {
    path:'/question',
    name:'question',
    component: () => import('../view/question/home'),
    meta: {title: '帮助与反馈',requiresAuth:true}
  },
  {
    path:'/question/questionList',
    name:'questionList',
    component: () => import('../view/question/questions'),
    meta: {title: '帮助与反馈',requiresAuth:true}
  },
  {
    path:'/question/detail',
    name:'questionDetail',
    component: () => import('../view/question/questionDetail'),
    meta: {title: '帮助与反馈',requiresAuth:true}
  },
  {
    path:'/tutorialClass/home',
    name:'tutorialClass',
    component: () => import('../view/tutorialClass/home'),
    meta: {title: '考前辅导课',requiresAuth:true}
  },
  {
    path:'/tutorialClass/optimzeHome',
    name:'optimzeHome',
    component: () => import('../view/tutorialClass/optimzeHome'),
    meta: {title: '考前辅导课'}
  },
  {
    path:'/tutorialClass/self',
    name:'tutorialClass.self',
    component: () => import('../view/tutorialClass/self'),
    meta: {title: '我的课表',requiresAuth:true}
  },
  {
    path:'/tutorialClass/graduateClasslist',
    name:'tutorialClass.graduateClasslist',
    component: () => import('../view/tutorialClass/graduateClasslist'),
    meta: {title: '我的课表',loadDict:true}
  },
  {
    path:'/tutorialClass/newself',
    name:'tutorialClass.newself',
    component: () => import('../view/tutorialClass/newself'),
    meta: {title: '我的课表',requiresAuth:true}
  },
  {
    path:'/tutorialClass/adultProcess',
    name:'adultProcess',
    component: () => import('../view/tutorialClass/adultProcess'),
    meta: {title: '成人教育报考流程',requiresAuth:true}
  },
  {
    path:'/tutorialClass/tutorClassPlay',
    name:'tutorClassPlay',
    component: () => import('../view/tutorialClass/courseDetail'),
    meta: {title: '',loadDict:true}
  },
  {
    path:'/tutorialClass/newCourseDetail',
    name:'newCourseDetail',
    component: () => import('../view/tutorialClass/newCourseDetail'),
    meta: {title: '辅导课详情',loadDict:true}
  },
  {
    path:'/settings/stuPayList',
    name:'stuPayList',
    component: () => import('../view/settings/payDetail/stuPaylist'),
    meta: {title: '缴费管理',loadDict:true,requiresAuth:true}
  },{
    path:'/settings/account',
    name:'account',
    component: () => import('../view/settings/payDetail/account'),
    meta: {title: '缴费管理',loadDict:true,requiresAuth:true}
  },{
  path:'/settings/applyCountRecord',
    name:'applyCountRecord',
    component: () => import('../view/settings/payDetail/myAccountRecord'),
    meta: {title: '提现记录',loadDict:true,requiresAuth:true}
},{
    path:'/settings/applyCount',
    name:'applyCount',
    beforeEnter: loadPcdJson,
    component: () => import('../view/settings/payDetail/myAccountApply'),
    meta: {title: '提现',loadDict:true,requiresAuth:true}
  },
  {
    path:'/Newsettings',
    name:'settings',
    component: () => import('../view/settings/serverGround'),
    meta: {title: '服务大厅',loadDict:true}
  },{
    path:'/tutorialClass/selfTutorClassPlay',
    name:'self-tutorClassPlay',
    component: () => import('../view/tutorialClass/selfDetail'),
    meta: {title: '',loadDict:true}
  },
  {
    path: '/tutorClass/check',
    name: 'tutorClassCheck',
    component: () => import('../view/tutorialClass/tutorCheck'),
    meta: {title: '加载中…', requiresAuth: true}
  },
  {
    path: '/order/paySuccess',
    name: 'paySuccess',
    component: () => import('../view/order/paySuccess'),
    meta: {title: '支付成功'}
  },
  {
    path: '/wallet',
    name: 'wallet',
    component: () => import('../view/wallet/index'),
    meta: {title: '我的钱包'}
  },
  {
    path: '/wallet/outCash',
    name: 'outCash',
    component: () => import('../view/wallet/outCash'),
    beforeEnter: loadPcdJson,
    meta: {title: '申请提现', loadDict: true,requiresAuth:true}
  },
  {
    path: '/wallet/outCashList',
    name: 'outCashList',
    component: () => import('../view/wallet/outCashList'),
    meta: {title: '提现记录'}
  },
  {
    path: '/wallet/outCashDetails',
    name: 'outCashDetails',
    component: () => import('../view/wallet/outCashDetails'),
    meta: {title: '提现详情'}
  },
];
