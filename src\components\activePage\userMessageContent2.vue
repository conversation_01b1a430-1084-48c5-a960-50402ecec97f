<template>
  <div class="main">
          <div class="textContent">
            <div class="userMessageContentList" :class="{anim:animatePraise==true}">
              <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                <div class="fl">
                  <img :src="item.headImg|defaultAvatar2" alt>
                  <p class="userName">{{item.realName|hideNickname}}</p>                  
                </div>
                <div class="fr">
                  <div class="bottom" v-if="item.msgReply">
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                    <div class="content">                       
                      <p class="answer">
                        <span>回复:&nbsp;</span>
                        {{item.msgReply}}
                      </p>
                        <div class="fl">
                        <img src="../../assets/image/xiaoyuan.png" alt>
                        <p class="userName">远智小妹</p>                  
                        </div>                          
                    </div>
                  </div>
                  <div class="bottom2" v-if="!item.msgReply">
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                  </div>
                </div>
                <div class="line"></div>
              </div>
            </div>
          </div>
          <div class="userMessageContent">
            <div class="fl">
              <img :src="userImg|defaultAvatar2" alt>
            </div>
            <div class="fr">
              <p class="userName">{{userName}}</p>
              <textarea maxlength="50" v-model.trim="message" placeholder="说点什么吧"></textarea>
              <span>{{message.length}}/50</span>
              <button @click="enrollMsg()" :class="{'noBackImg':btnColor}" :style="{'backgroundColor':btnColor}">提交留言</button>
            </div>
          </div>
        <van-popup v-model="showPop">
          <div class="popupBox">
            <img
              src="../../assets/image/active/openUniversity/<EMAIL>"
              class="icon"
              alt
            >
            <p class="title">发送成功</p>
            <p class="text1">感谢你的提问, 我们会尽快解决你的疑惑！</p>
          </div>
        </van-popup>
  </div>
</template>

<script>

import { toLogin,filterEmoji } from "../../common";
import { Popup } from "vant";
  export default {
    name: 'userMessageContent',
    props:[
      'scholarship','btnColor'
    ],

    data() {
      return {
        enrollMsgList:[],
        message:'',
        userImg:'',
        userName:'',
        animatePraise: false,
        isLogin:null,
        showPop:false,
        loadingFlag:true

      }
    },
    created() {
      this.isLogin = !!this.storage.getItem("authToken");
      this.userName = this.storage.getItem("realName") || "";
      this.userImg = this.storage.getItem("headImg") || "";
      setInterval(this.scroll, 3000);
    },
  mounted: function() {
    window.addEventListener("scroll", this.handleScroll, true);
  },
    methods:{
    // 获取留言列表
    getEnrollMsgList: function() {
      this.$http
        .post("/mkt/getEnrollMsgList/1.0/", { scholarship: this.scholarship })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.enrollMsgList = body;
          }
        });
    },
    // 评论
    enrollMsg: function() {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      if (this.storage.getItem('relation')=='2' || this.storage.getItem('relation') =='6') {
        this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
        return;
      }
      if (!this.message) {
        this.$modal({ message: "请输入评论内容", icon: "warning" });
        return;
      }

      this.$http
        .post("/mkt/enrollMsg/1.0/", {
          scholarship: this.scholarship,
          msgContent: filterEmoji(this.message)
        })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.showPop = true;
            setTimeout(() => {
              this.showPop = false;
            }, 5000);
            this.message = "";
            this.getEnrollMsgList();
          }
        });
    },
    // 轮播用户评论
    scroll() {
      if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
        return;
      }
      this.animatePraise = true;
      setTimeout(() => {
        this.enrollMsgList.push(this.enrollMsgList[0]);
        this.enrollMsgList.shift();
        this.animatePraise = false;
      }, 500);
    },
    //监听滑动的距离
    handleScroll() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (this.loadingFlag) {
        if (scrollTop > 400) {
          this.getEnrollMsgList();
          this.loadingFlag = false;
        }
      }
    },
    },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll, true);
  }
  }
</script>

<style lang="less" scoped>
.main /deep/ .van-popup {
  top: 45%;
  text-align: center;
  background-color: rgba(246, 246, 246, 0);
}
    .textContent {
      overflow: hidden;
      height: 5.4rem;
      .userMessageContentList {
        width: 3.75rem;
        &.anim {
          transition: all 1s;
          margin-top: -1.7rem;
        }
        .content {
          overflow: hidden;
          height: 1.8rem;
          position: relative;
          .line {
            position: absolute;
            bottom: 0.01rem;
            height: 1px;
            width: 2.76rem;
            left: 0.64rem;
            background-color: rgba(23, 6, 6, 0.08);
          }
          .fl {
            width: 0.64rem;
            text-align: right;
            padding-right: .1rem;
            img {
              width: 0.38rem;
              height: 0.38rem;
              border-radius: 50%;
              margin-top: 0.11rem;
            }
             .userName {
                 color: #A29B9B;
                 font-size: .12rem;
                 margin-top: .06rem;
             }
          }
          .fr {
            width: 3.11rem;
            height: 1.14rem;
            .uesrQuestion {
              position: relative; 
              display: inline-block;
              width: 2.5rem;
              margin-top: 0.2rem;
              margin-left: 0.05rem;
              font-size: 0.14rem;
              color: #453838;
              background: #FFEFD3;
              padding: .06rem .1rem;
            }
            .uesrQuestion:after, .uesrQuestion:before {
                border: solid transparent;
                content: ' ';
                height: 0;
                right: 100%;    //根据三角形的位置，可以随意更改。
                position: absolute;
                width: 0;
            } 

            .uesrQuestion:after {
                border-width: .08rem;
                border-right-color: #FFEFD3;
                top: .08rem;//根据三角的位置改变
            }


            .content {
              position: relative;
              margin-bottom: 0.09rem;
              height: auto;
              overflow: hidden;

              .answer {
                float: left;
                margin-top: .25rem;
                background:#FBC464;
                border-radius: 3px 3px 3px 0px;
                width: 2.44rem;
                font-size: 0.14rem;
                color: #5B3D09;
                padding: 0.05rem .1rem;
          
                position: relative;

              }
            .answer:after, .answer:before {
                border: solid transparent;
                content: ' ';
                height: 0;
                left: 100%;    //根据三角形的位置，可以随意更改。
                position: absolute;
                width: 0;
            } 

            .answer:after {
                border-width: .08rem;
                border-left-color: #FBC464;
                top: .08rem;//根据三角的位置改变
            }


              .fl {
                  float: right;
                  text-align: center;
                  img {
                      margin-top: .21rem;
                  }
              }
            }
          }
        }
      }
    }
    .userMessageContent {
      width: 3.75rem;
      overflow: hidden;
      .fl {
        width: 0.64rem;
        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }
      .fr {
        width: 3.11rem;
        position: relative;
        .userName {
          margin-left: 0.1rem;
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
          display: inline-block;
          width: 2.48rem;
          height: 0.24rem;
        }
        textarea {
          width: 2.87rem;
          height: 0.86rem;
          border-radius: 5px;
          padding: 0.13rem 0.1rem 0.13rem 0.1rem;
          margin-top: 0.03rem;
          border-color: rgba(239, 186, 6, 1);
        }
        span {
          position: absolute;
          font-size: 0.09rem;
          color: rgba(23, 6, 6, 0.4);
          top: 1.07rem;
          right: 0.305rem;
        }
        button {
          width: 1rem;
          height: 0.35rem;
          border: 0;
          margin-top: 0.1rem;
          margin-left: 0.89rem;
          margin-bottom: 0.2rem;
          background-color: #D7D7D8;
          background: url("../../assets/image/active/gdLingNan/btn_msg.png") no-repeat;
          background-size: 100%;
          border-radius: 3px;
          color: #ffffff;
          font-size: 0.14rem;
          &.noBackImg{
            background-image: none;
          }
        }
      }
    }
      .popupBox {
    width: 2.83rem;
    height: 1.54rem;
    background: #fff;
    .icon {
      height: 0.32rem;
      width: 0.32rem;
      margin-top: 0.3rem;
      border-radius: 50%;
    }
    .title {
      color: rgba(23, 6, 6, 1);
      font-size: 0.17rem;
      margin-top: 0.14rem;
    }
    .text1 {
      margin-top: 0.05rem;
    }
    .text1 {
      margin-top: 0.04rem;
      color: rgba(23, 6, 6, 0.4);
      font-size: 0.12rem;
    }
  }
</style>
