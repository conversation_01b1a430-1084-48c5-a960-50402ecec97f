export default [
  {
    path: '/app/live/course',
    name: 'AppLiveCourse',
    component: () => import('../view/app/liveCourse'),
    meta: { title: '课程直播' }
  },
  {
    path: '/app/liveStream',
    name: 'liveStream',
    component: () => import('../view/app/index'),
    meta: { title: '直播分享页' }
  },
  {
    path: '/app/test',
    name: 'test',
    component: () => import('../view/app/test.vue'),
    meta: { title: '直播分享页' }
  },
  {
    path: '/app/live/course',
    name: 'AppLiveCourse',
    component: () => import('../view/app/liveCourse'),
    meta: { title: '课程直播' }
  },
  {
    path: '/app/signShare',
    name: 'AppLiveCourse',
    component: () => import('../view/app/signShare'),
    meta: { title: '打卡分享' }
  },
  {
    path: '/app/zhimiPay',
    name: 'appZhimiPay',
    component: () => import('../view/app/zhimiPay'),
    meta: { title: '打卡分享' }
  },
  {
    path: '/app/submitSuccess',
    name: 'submiSuccess',
    component: () => import('../view/app/submitSuccess'),
    meta: { title: '提交成功' }
  },
  {
    path: '/app/circleAd',
    name: 'appCircleShare',
    component: () => import('../view/app/appCircleShare.vue'),
  },
  {
    path: '/app/openAppText',
    name: 'openAppText',
    component: () => import('../view/app/openAppText.vue'),
  },
  {
    path: '/app/openApp',
    name: 'openApp',
    component: () => import('../view/app/openApp.vue'),
  },
  {
    path: '/app/adExtend',
    name: 'adExtend',
    component: () => import('../view/app/adExtend'),
  },
  {
    path: '/app/adExtend/moneyPay',
    name: 'adExtendMoneyPay',
    meta: { title: '缴费' },
    component: () => import('../view/app/moneyPay'),
    children: [{
        path: 'alipay',
        name: 'alipayment',
        component: () => import('../view/app/alipay'),
        meta: {
            title: '支付宝支付',
            requiresAuth: false
        },
    }]
  },
  {
    path: '/app/birthday',
    name: 'app.birthday',
    component: () => import('../view/app/birthday'),
    meta: {title: '生日欧气传递'}
  },
  {
    path: '/app/progressCertificate',
    name: 'progressCertificate',
    component: () => import('../view/app/progressCertificate/index'),
    meta: { title: '上进报告查询' }
  },
  {
    path: '/app/progressCertificate/result',
    name: 'progressCertificateResult',
    component: () => import('../view/app/progressCertificate/result/index'),
    meta: { title: '上进报告查询' }
  },
  {
    path: '/app/progressCertificate/detail',
    name: 'progressCertificateDetail',
    component: () => import('../view/app/progressCertificate/detail.vue'),
    meta: { title: '上进报告查询' }
  },
  {
    path: '/app/live2',
    name: 'live2',
    component: () => import('../view/app/live/index.vue'),
    meta: { title: ' ' }
  },
];
