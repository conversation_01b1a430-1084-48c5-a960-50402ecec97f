import { formatTimeByMoment } from '@/filters'

const sensors = require('sa-sdk-javascript')
const isProd = process.env.RUN_ENV === 'production'
const serverURL = `https://data.shangjinqingnian.com/${isProd ? 'collect' : 'debug'}`

sensors.init({
  server_url: serverURL,
  show_log: !isProd,
  name: 'sensors',
  use_client_time: true,
  send_type: 'beacon',
  heatmap: {
    clickmap: 'default',
    scroll_notice_map: 'not_collect'
  }
})

// 配置全局属性
sensors.registerPage({
  $platform: 'h5',
  $project: 'zmc',
  referer: window.location.origin,
  event_time: formatTimeByMoment(new Date().valueOf())
})

export default sensors
