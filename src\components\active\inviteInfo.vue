<template>
  <div class="invite invite-top" v-if="showInvite" >
    <img :src="invite.headImg|defaultAvatar" alt="">
    <div class="headTxt">
      <p>您的好友 <span>{{invite.nickName}}</span></p>
      <p>邀请您一起来提升学历</p>
    </div>
  </div>
</template>

<script>
  import {defaultAvatar} from "../../filters";

  export default {
        name: "inviteInfo",
    filters:{defaultAvatar},
        data(){
          return{
            invite:{},
            showInvite:false
          }
        },
    created(){
      this.inviteId = this.$route.query.inviteId || '';
      if (!!this.inviteId) {
        //获取邀约人信息
        this.getInviteInfo();
      }
    },
      methods:{
        getQueryString: function (url, name) {
          let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
          let r = url.substr(1).match(reg);
          if (r != null) return decodeURI(r[2]);
          return null;
        },
        getInviteInfo() {
          let inviteId = (window.sessionStorage.getItem('inviteId') ||this.$route.query.inviteId ||decodeURIComponent(this.$route.query.inviteId || this.getQueryString(this.redirect, 'inviteId') || '')).replace(/ /g, '+');
          if(inviteId)
          {
            this.$http.post('/us/getInviteInfo/1.0/', {inviteToken: inviteId}).then(res => {
              let {code, body} = res;
              if (code !== '00') return;
              this.invite = body||{};
              this.showInvite = true;
            });
          }
        },
      }
    }
</script>

<style scoped lang="less">

  .invite {
    padding: .16rem 0;
    background-image: url("../../assets/image/active/dreamBuild/content-bg.png");
    background-size: 100%;
  img {
    width: .48rem;
    height: .48rem;
    border-radius: 50%;
    float: left;
    margin-left: .24rem;
  }
  .headTxt{
    float: left;
    margin-left: .12rem;
  p {
    font-size: .18rem;
  span {
    color: #E15443;
    font-size: .14rem;
  }
  &:first-of-type {
     font-size: .14rem;
   }
  }
  }
  }
  .invite-top{
  // background-image: url("../../../assets/image/active/dreamBuild/contentBg2.jpg");
  // background-size: 100% 100%;
    background-color:#F3B422;
    height: .72rem;
    margin-bottom: -.11rem;
    position: relative;
    z-index: 10;
  }
</style>
