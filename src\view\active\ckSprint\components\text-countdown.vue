<template>
  <div class="yz-ck-textCountdown" ref='textCountdown'>
    <div class="title-img"></div>
    <p class="t1">跟着节奏走，一起去追梦</p>
    <div class="text-box">
      <img src="../../../../assets/image/active/ckSprint/mh.png" class="img1" alt="">
      <p class="t2">嘿！亲爱的同学，你好。欢迎进入成考备考旅程～</p>
      <p class="t2">
        知识大要点、答题小技巧、丰富多彩的校园活动、轻松解压又营养的好视频、能量满满的同学圈子交流…跟着节奏走，备考期间学习、休闲两不误，理想大学速速碗里来！</p>
      <img src="../../../../assets/image/active/ckSprint/mh.png" class="img2" alt="">
    </div>
    <div class="countdown-box" ref='countdown'>
      <p class="t3">记得按照「成考备考学习攻略」进行针对性复习哦！</p>
      <div class="num-box">
        <span class="num-bg" :class='{ml5: index > 0}' v-for="(item, index) in lastDate.toString()"
              :key="index">{{item}}</span>
      </div>
      <div class="btn-box">
        <red-btn @click='toCourse'>看课表</red-btn>
        <red-btn @click='toStudy'>去复习</red-btn>
        <red-btn @click='toTopic'>去刷题</red-btn>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import { isLogin } from '@/common';
import { examUrl } from '@/config/index';
import Crypto from "@/plugins/secret";
import { getStdLearnInfo } from '@/common/request';
// import { toAppHome } from '@/common/jump';
import RedBtn from './red-btn.vue';

export default {
  components: {
    RedBtn,
  },
  props: {
    lastDate: {
      type: [Number, String],
      default: 0,
    },
    isAppOpen: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      learnInfos: [],
      yzCode: '',
    }
  },
  mounted () {
    this.getOffsetTop();
    if (isLogin()) {
      getStdLearnInfo().then(res => {
        if (res.code == '00') {
          this.yzCode = res.body.yzCode;
          if (res.body.learnInfos) {
            this.learnInfos = res.body.learnInfos.filter((item) => {
              if (item.stdStage !== '10') {
                return item;
              }
            });
          }
        }
      });
    }
  },
  methods: {
    getOffsetTop () {
      const boxHeight = this.$refs.textCountdown.offsetHeight;
      const boxTop = this.$refs.textCountdown.offsetTop;
      const countdownHeight = this.$refs.countdown.offsetHeight;
      const sum = boxTop + (boxHeight - countdownHeight - 10);
      this.$emit('getOffsetTop', sum);
    },
    toCourse () {
      this.$yzStatistic('sprintAct.base.click', '5', '首页-看课表');
      if (this.learnInfos.length == 0) {
        Toast('同学你还没有学籍喔，请先报读～');
        return;
      }
      if (this.isAppOpen) {
        this.toStudyTime()
        window.location.href = 'yuanzhiapp://yzwill.cn/Activity/TrainClassList';
      }
    },
    toStudy () {
      this.$yzStatistic('sprintAct.base.click', '6', '首页-去复习');
      if (this.learnInfos.length == 0) {
        Toast('同学你还没有学籍喔，请先报读～');
        return;
      }
      if (this.isAppOpen) {
        this.toStudyTime()

        window.location.href = 'yuanzhiapp://yzwill.cn/Activity/LearnResource';
      }
    },
    toTopic () {
      this.$yzStatistic('sprintAct.base.click', '7', '首页-去刷题');
      if (this.learnInfos.length == 0) {
        Toast('同学你还没有学籍喔，请先报读～');
        return;
      }
      setTimeout(() => {
        this.toStudyTime()
        window.location.href = `${examUrl}/autoLogin?freeTag=${Crypto.encrypt(this.yzCode)}&type=siteExam`;
      }, 200);
    },

    async toStudyTime () {
      await this.$http.post('/us/goToStudy/1.0/', {
        scholarship: 2522,
        type: 2
      });

    },
  },
};
</script>

<style lang="less">
@import '../../../../assets/bebas/index.css';
.yz-ck-textCountdown {
  background: #e1f1f4;
  padding: 0.78rem 0 0.13rem;
  margin-top: -0.46rem;
  .title-img {
    background: url(../../../../assets/image/active/ckSprint/title-school.png) no-repeat;
    background-size: 100% 100%;
    width: 2.4rem;
    height: 0.22rem;
    margin: 0 auto 0.06rem;
  }
  .t1 {
    font-size: 0.12rem;
    text-align: center;
  }
  .text-box {
    position: relative;
    padding: 0.35rem 0.1rem 0.45rem;
    font-size: 0.14rem;
    color: #363636;
    .t2 {
      position: relative;
      z-index: 2;
      padding-left: 0.1rem;
      text-indent: 28px;
    }
    img {
      position: absolute;
      width: 0.45rem;
      height: 0.39rem;
      z-index: 1;
    }
    .img1 {
      top: 0.1rem;
      left: 0.1rem;
    }
    .img2 {
      right: 0.1rem;
      bottom: 0.18rem;
      transform: rotate(180deg);
    }
  }
  .countdown-box {
    background: url(../../../../assets/image/active/ckSprint/bg-read.png) no-repeat;
    background-size: 100% 100%;
    width: 3.6rem;
    height: 1.81rem;
    margin: 0 auto;
    position: relative;
    .t3 {
      font-size: 0.12rem;
      text-align: center;
      position: absolute;
      bottom: 0.6rem;
      width: 100%;
      left: 0;
    }
    .btn-box {
      position: absolute;
      text-align: center;
      width: 100%;
      left: 0;
      bottom: 0.15rem;
      button:not(:last-child) {
        margin-right: 0.07rem;
      }
    }
  }
  .num-box {
    padding-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: bebas;
    font-size: 0.3rem;
    color: #d92424;
    line-height: 1;
    .num-bg {
      display: inline-block;
      background: url(../../../../assets/image/active/ckSprint/num-bg.png) no-repeat;
      background-size: 100% 100%;
      width: 0.38rem;
      height: 0.45rem;
      line-height: 0.45rem;
      text-align: center;
      box-shadow: 0px 0.05rem 0.08rem 0px rgba(217, 36, 36, 0.2);
      &.ml5 {
        margin-left: 0.05rem;
      }
    }
  }
}
</style>

