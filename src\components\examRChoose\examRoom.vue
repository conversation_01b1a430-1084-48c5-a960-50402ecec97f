<template>
  <div>
    <div class="select-item" v-for="item in options" :class="{active:value.epId==item.epId}"
         @click="selected(item)">{{item.epName}}
    </div>
    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';

  export default {
    props: ['value', 'province', 'eyId'],
    data() {
      return {
        options: [],
        isLoading: true,
        allLoaded: false,
      }
    },
    created() {
      this.getExamPlace();
    },
    methods: {
      // 获取考场列表
      getExamPlace: function () {
        const datas = {
          eyId: this.eyId,
          provinceCode: this.province.provinceCode,
          cityCode: this.province.cityCode,
          districtCode: this.province.districtCode
        }
        this.$http.post('/mkt/getExamPlace/1.0/', datas).then(res => {
          const {code, body} = res;
          if (code === '00') {
            this.isLoading = false;
            this.allLoaded = true;
            this.options = body || [];
          }
        });
      },
      selected: function (val) {
        this.$emit('input', val);
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
