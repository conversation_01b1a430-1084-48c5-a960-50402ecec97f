<template>
  <div>
    <!-- 分享落地页 -->
    <div class="invite">
      <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" alt="">
      <div class="rightView">
        <p><span>您的好友</span> <span>{{invite.realName}}</span></p>
        <p>邀请您一起来抢新年红包</p>
      </div>
      <div class="look-more" v-if="ios||isWxwork" @click="downApp"  >查看更多</div>
      <div class="look-more" v-if="!ios&&!isWxwork" @click="downApp">
        查看更多
        <wx-open-launch-app class="launch-follow" @error="callAppError" :appid="OpenAppId" extinfo="extinfo" >
          <script type="text/wxtag-template">
              <style>
                .follow {
                      width: 1000px;
                      height: 1000px;
                  }
              </style>
              <div  class="follow"></div>
          </script>
        </wx-open-launch-app>
      </div>

    </div>
    <div class="contain">
      <div class="reward">
        <img src="../../../assets/image/aspirantUniversity/reward.png" alt="">
      </div>
      <p class="zm-value">价值（688.88元）</p>
      <p class="tips">每个用户都有1次机会领红包</p>
      <div class="gift">
        <img src="../../../assets/image/aspirantUniversity/red-gift.png" alt="">
      </div>
      <div class="footer">
        <div class="open-redgift" @click="downApp" v-if="ios||isWxwork">
          <h3>打开APP领红包</h3>
          <p>帮ta获得抽奖机会</p>
        </div>
        <div class="open-redgift"  v-if="!ios&&!isWxwork" @click="downApp">
          <h3>打开APP领红包</h3>
          <p>帮ta获得抽奖机会</p>
          <wx-open-launch-app class="launch-follow" @error="callAppError" :appid="OpenAppId" extinfo="extinfo" >
            <script type="text/wxtag-template">
                <style>
                  .follow {
                        width: 1000px;
                        height: 1000px;
                    }
                </style>
                <div  class="follow"></div>
            </script>
          </wx-open-launch-app>
        </div>
        <p class="active-time">活动时间：1月17日-1月23日</p>
        <div class="help-rules">
          助力须知：<br>
          1、通过此链接注册并前往app参与红包活动，赠送好友抽奖机会<br>
          2、必须是活动期间新注册的用户 <br>
          3、每个新用户仅可为1位好友助力<br>
        </div>
      </div>
    </div>
    <!-- 弹框 -->
    <van-popup v-model="showHelp" :close-on-click-overlay="false">
      <div class="pop-contain">
        <div class="pop-contain-in">
          <div class="pop-head">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" alt="">
          </div>
          <img class="head-bot" src="../../../assets/image/aspirantUniversity/head-bottm.png" alt="">
          <div class="help-txt">{{ invite.realName | ellipsis }}的助力邀请</div>
          <h3>邀请好友助力得抽奖机会</h3>
          <div class="pop-gift">
            <img src="../../../assets/image/aspirantUniversity/red-gift.png" alt="">
          </div>
          <div class="help-btn" @click="toHelp">
            为TA助力
          </div>
        </div>

      </div>
    </van-popup>
     <share
      :title="shareObj.title"
      :desc="shareObj.content"
      :link="shareObj.shareLink"
      :imgUrl="shareObj.image"
      regOrigin='61'
      ref="share"
    />
  </div>
</template>

<script>
import { isIOS, downloadApp, isLogin, isWxwork } from '@/common'
import openApp from "@/mixins/openApp";
import share from "@/components/share";
import { toAppLogin } from '@/common/jump';
export default {
  components:{share},
  mixins: [openApp,],
  data () {
    return {
      invite: {},
      showHelp: true,
      isLogin: isLogin(),
      ios: isIOS(),
      isWxwork: isWxwork(),
      headLimit:"?x-oss-process=image/resize,m_fixed,h_38,w_38",
      shareObj:{
        title:'邀你一起参加「远智教育」虎年红包活动',
        image: 'https://static.yzou.cn/spring/springshare.png',
        content: "荣耀笔记本电脑、荣耀手机、上进家族IP周边等礼品邀你来抽取！",
        shareLink: window.location.origin + "/active/2022Spring",
      }
    };
  },
  filters: {
    ellipsis (value) {
      if (!value) return ''
      if (value.length > 4) {
        return value.slice(0,4) + '...'
      }
      return value
    }
  },
  created () {
    this.inviteId = this.$route.query.inviteId || '';
    if (!!this.inviteId) {
      this.getInviteInfo();
    }
  },
  mounted(){
    this.wxCallAppInit();  // 微信标签唤起app
    this.$yzStatistic('activity.2022.spring.event', null, '助力弹窗');
    if(this.isLogin){
      const name = this.storage.getItem('realName')||this.storage.getItem('zmcName')||'同学'
      const authToken = this.storage.getItem('authToken');
       this.shareObj = {
        title: name+`邀你一起参加「远智教育」虎年红包活动`,
        content: '荣耀笔记本电脑、荣耀手机、上进家族IP周边等礼品邀你来抽取！',
        image: 'https://static.yzou.cn/spring/springshare.png',
        shareLink: `${window.location.origin}/active/2022Spring?inviteId=${encodeURIComponent(authToken || '')}`,
      }
    }
  },
  methods: {
    downApp () {
      this.$yzStatistic('activity.2022.spring.event', null, '活动引导下载app页面');
      if(this.ios||this.isWxwork){
        downloadApp()
      }
    },
    // 获取邀约人信息
    getInviteInfo () {
      let inviteId = decodeURIComponent(this.$route.query.inviteId);
      console.log('inviteId: '+ inviteId);
      if (inviteId) {
        this.$http.post('/us/getInviteInfo/1.0/', { inviteToken: inviteId }).then(res => {
          let { code, body } = res;
          if (code !== '00') return;
          this.invite = body || {};
          // if(this.$route.query.isSharePage){
          //   this.showInvite = true;
          // }

        });
      }
    },
    // 助力弹框
    toHelp () {
      this.$yzStatistic('activity.2022.spring.event', null, '助力弹窗-点击为他助力');
      this.showHelp = false;
      console.log("this.isLogin"+ this.isLogin);
      console.log(!this.isLogin);
      if (!this.isLogin) {
        console.log('denglu');
        this.$yzStatistic('activity.2022.spring.event', null, '助力弹窗-手机号登录');
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.inviteId || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "19",
          },
        });
      } 
    },
  }
};
</script>

<style lang = "less" scoped>
@import url('./index.less');
</style>

