<template>
  <!-- 组件说明: https://www.tapd.cn/44062861/markdown_wikis/show/#1144062861001004525 -->
  <div class="tencent-dot-player-wrap">
    <video
      id="tencent-dot-player"
      class="tencent-dot-player"
      preload="auto"
      playsinline
      webkit-playsinline
    ></video>
  </div>
</template>

<script>
import Vue from "vue";
import selectRate from "./selectRate";
import selectStudy from "./selectStudy";

import selectQuality from "./selectQuality";
import audioPopup from "./audioPopup";
import errorPopup from "./errorPopup";

let sliderBarEl; // 进度条Dom
let timeEl; // 时刻Dom

let totalWidth; // 进度条宽度
let startX; // 定义初始位置
let currentTime; // 当前时刻
let second; // 滑动的时候需要快进的时刻
let flag = false; // 是否滑动
export default {
  props: {
    // 播放器实例化的配置项
    playerOpts: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 视频资源的配置
    /**
     * opts参数如下:
     * videoList: [
     *   {
     *      src:      String  必填参数    视频地址
     *      text:     String  必填参数    清晰度文字说明
     *      coverUrl: String  非必填参数  封面图
     *    }
     * ]
     */
    opts: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      ratePopup: null, // 倍速选择框实例
      studyPopup: null, // 视频音频选择框实例
      qualityPopup: null, // 清晰度选择框实例
      audioPopup: null, // 音频弹框实例
      errorPopup: null, // 播放错误弹框实例

      tcPlayer: null,
      startPlayTime: 0, // 开始时间戳
      endPlayTime: 0, // 结束时间戳
      playTime: 0, // 播放时间
      duration: 0, // 视频时长
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    opts: {
      handler(nV, oV) {
        this.reset();
      },
      deep: true,
    },
  },
  methods: {
    // 给实例扩展方法
    addExampleFunc(player) {
      player.dotExtend = {
        getPlayTime: this.getPlayTime, // 获取播放时长(用户播放3秒, 暂停5秒, 再播放4秒, 调用这方法, 获取到的播放时长就是真实的观看时长3+4=7秒)
        resetPlayTime: this.resetPlayTime, // 重置播放时长
        // 下面添加不同的方法
      };
    },
    // 获取播放时间
    getPlayTime() {
      try {
        const newTime = this.isPlay ? new Date().getTime() : this.endPlayTime
        const playTime = parseInt((newTime - this.startPlayTime) / 1000)
        this.playTime = this.playTime + playTime
        this.startPlayTime = newTime
        return this.playTime
      } catch (error) {
        console.log(error)
        return 0
      }
    },
    // 重置播放时长
    resetPlayTime() {
      this.startPlayTime = 0;
      this.EndPlayTime = 0;
      this.playTime = 0;
      this.isPlay = false
    },
    // 初始化
    init() {
      this.loadPlayerScript(this.initTCPlayer);
    },
    // 重置
    reset() {
      // 重置错误弹框层
      this.errorPopup.show = false;
      // 重置清晰度
      const list = this.opts.videoList;
      this.qualityPopup.list = list;
      this.qualityPopup.currentIndex = 0;
      const item = list[0];
      document.querySelector(".quality-value").innerText = item.text;
      // 重置播放时长
      this.resetPlayTime()

      if (item.coverUrl) {
        this.tcPlayer.poster(item.coverUrl);
      }
      if (item.src) {
        this.tcPlayer.src(item.src);
      }
    },
    // 打开画面按钮, 关闭音频画面 (子组件调用该方法)
    openFrame() {
      this.audioPopup.show = false;
      this.studyPopup.currentIndex = 0;
      document.querySelector(".study-value").innerText = "视频";
    },
    // 切换线路 (子组件调用该方法)
    switchLine() {
      this.$emit("switchLine");
    },
    // 设置倍速 (子组件调用该方法)
    setRate(num) {
      this.tcPlayer.playbackRate(num);
      document.querySelector(".rate-value").innerText = num;
    },
    // 重置倍速
    resetRate() {
      this.ratePopup.currentIndex = 0
      document.querySelector(".rate-value").innerText = '倍速';
    },
    // 设置学习方式 (子组件调用该方法)
    setStudy(index, text) {
      if (index == 0) {
        this.audioPopup.show = false;
      } else {
        this.audioPopup.show = true;
      }
      document.querySelector(".study-value").innerText = text;
    },
    // 设置清晰度 (子组件调用该方法)
    setQuality(index) {
      const item = this.opts.videoList[index];
      if (item.coverUrl) {
        this.tcPlayer.poster(item.coverUrl);
      }
      this.resetRate();
      const time = this.tcPlayer.currentTime();
      this.tcPlayer.src(item.src);
      setTimeout(() => {
        this.tcPlayer.currentTime(time) // 回到上个清晰度的播放时刻
      },100);
      document.querySelector(".quality-value").innerText = item.text;
    },
    // 播放 (子组件也会调用该方法)
    play() {
      this.$emit("play");
      this.tcPlayer.play();
    },
    // 暂停播放
    pause() {
      this.$emit("pause");
      this.tcPlayer.pause();
    },
    // 处理控件实例
    handleControlExample(boxName, valueName, valueText) {
      const temp = Vue.extend({
        template: `
          <div class="${boxName} vjs-control">
            <div class="${valueName} common-control-value">${valueText}</div>
          </div>
        `,
      });
      const btnEl = new temp({
        el: document.createElement("div"),
      });
      return btnEl;
    },
    // 绘制控件按钮
    drawControlBtn() {
      const fullBtn = document.querySelector(".vjs-fullscreen-control");

      const qualityText = this.opts?.videoList?.[0].text; // 用可选链处理, 防止渲染的时候没有传入videoList而报错

      // 绘制清晰度按钮
      const qualityBtn = this.handleControlExample(
        "quality-btn",
        "quality-value",
        qualityText
      );
      document
        .querySelector(".vjs-control-bar")
        .insertBefore(qualityBtn.$el, fullBtn);
      qualityBtn.$el.onclick = () => {
        this.pause();
        this.qualityPopup.show = true;
      };

      // 绘制学习方式按钮 视频/音频
      const studyBtn = this.handleControlExample(
        "study-btn",
        "study-value",
        "视频"
      );
      document
        .querySelector(".vjs-control-bar")
        .insertBefore(studyBtn.$el, qualityBtn.$el);
      studyBtn.$el.onclick = () => {
        this.pause();
        this.studyPopup.show = true;
      };

      // 绘制倍速按钮
      const rateBtn = this.handleControlExample(
        "rate-btn",
        "rate-value",
        "倍速"
      );
      document
        .querySelector(".vjs-control-bar")
        .insertBefore(rateBtn.$el, studyBtn.$el);
      rateBtn.$el.onclick = () => {
        this.pause();
        this.ratePopup.show = true;
      };
    },
    // 绘制控件对应的弹框
    /**
     * 1.绘制的弹框要挂载tcplayer的容器内, 否则全屏的时候弹框层显示不出来
     * 2.挂载到tcplayer的容器后, 组件用$emit,bus中央事件,inject等方法无法传递信息,
     * 所以需要实例化的时候传入父组件的this, 通过父组件的this操作父组件的方法
     */
    drawSelectPopup() {
      const tcplayer = document.querySelector(".tcplayer");

      // 倍速弹框
      // 定义倍速组件构造函数
      const rateFunc = Vue.extend(selectRate); 
      // 创建倍速组件实例
      this.ratePopup = new rateFunc({
        propsData: {
          parentCom: this, // 传入组件的this, 在弹框层使用父组件的this操作组件方法
        },
        el: document.createElement("div"),
      });

      // 视频或音频弹框
      // 定义视频或音频组件构造函数
      const studyFunc = Vue.extend(selectStudy);
      // 创建视频或音频组件实例
      this.studyPopup = new studyFunc({ 
        propsData: {
          parentCom: this, // 传入组件的this, 在弹框层使用父组件的this操作组件方法
        },
        el: document.createElement("div"),
      });

      // 清晰度弹框
      // 定义清晰度组件构造函数
      const qualityFunc = Vue.extend(selectQuality);
      // 创建清晰度组件实例
      this.qualityPopup = new qualityFunc({
        propsData: {
          parentCom: this, // 传入组件的this, 在弹框层使用父组件的this操作组件方法
        },
        el: document.createElement("div"),
      });
      this.qualityPopup.list = this.opts?.videoList; // 用可选链处理, 防止渲染的时候没有传入videoList而报错

      // 音频播放弹框
      // 定义音频播放组件构造函数
      const audioFunc = Vue.extend(audioPopup);
      // 创建音频播放组件实例
      this.audioPopup = new audioFunc({
        propsData: {
          parentCom: this, // 传入组件的this, 在弹框层使用父组件的this操作组件方法
        },
        el: document.createElement("div"),
      });

      // 播放错误
      // 定义播放错误组件构造函数
      const errorFunc = Vue.extend(errorPopup);
      // 创建播放错误组件实例
      this.errorPopup = new errorFunc({
        propsData: {
          parentCom: this, // 传入组件的this, 在弹框层使用父组件的this操作组件方法
        },
        el: document.createElement("div"),
      });

      // 把这5个组件的dom元素添加到腾讯云播放器的容器内
      tcplayer.append(
        this.ratePopup.$el,
        this.studyPopup.$el,
        this.qualityPopup.$el,
        this.audioPopup.$el,
        this.errorPopup.$el
      );
    },
    // 加载腾讯云script
    loadPlayerScript(callback) {
      if (!window.TCPlayer) {
        /**
         * 1.没有使用npm 引入腾讯云, 是因为腾讯云npm包的css文件有个fill-available属性冲突了, 所以才用了script标签引入sdk, 
         * 2.将sdk下载后, 用script标签引入, 是无法避免fill-available属性冲突, 所以引入了一个string-replace-loader的loader来改变腾讯云css文件的属性
         */
        const myScript = document.createElement("script");
        myScript.type = "text/javascript";
        myScript.setAttribute(
          "src",
          "./../../../static/tcplayer/tcplayer.v4.8.0.min.js"
        );
        myScript.onload = callback;
        document.body.appendChild(myScript);
      } else {
        callback();
      }
    },
    // 初始化播放器
    initTCPlayer() {
      this.$nextTick(() => {
        this.tcPlayer = TCPlayer("tencent-dot-player", {
          controlBar: {
            volumePanel: false, // 是否显示音量控制
            playbackRateMenuButton: false, // 是否显示播放速率选择按钮
          },
          plugins: {
            ContinuePlay: {
              auto: true, // 是否在播放时自动续播
            },
          },
          ...this.playerOpts,
        });
        if (this.opts?.videoList?.length) {
          const item = this.opts.videoList[0];
          if (item.coverUrl) {
            this.tcPlayer.poster(item.coverUrl);
          }
          if (item.src) {
            this.tcPlayer.src(item.src);
          }
        }
        this.tcPlayer.on("error", () => {
          this.errorPopup.show = true;
        });
        this.tcPlayer.on("play", () => {
          this.isPlay = true
          this.startPlayTime = new Date().getTime(); // 记录开始播放的时间戳
          this.$emit("onPlay");
        });
        this.tcPlayer.on("pause", () => {
          this.isPlay = false
          this.endPlayTime = new Date().getTime() // 记录暂停或者结束播放的时间戳
          this.$emit("onPause");
        });
        this.tcPlayer.on("ended", () => {
          this.isPlay = false
          this.endPlayTime = new Date().getTime() // 记录暂停或者结束播放的时间戳
          this.$emit("onEnded");
        });
        this.tcPlayer.on('loadedmetadata',()=>{
          this.duration = parseInt(this.tcPlayer.duration())
        })
        this.listenSlide();
        this.addExampleFunc(this.tcPlayer);
        this.$emit("inited", this.tcPlayer);
        this.drawControlBtn();
        this.drawSelectPopup();
      });
    },
    // 监听滑动
    listenSlide() {
      // 播放器Dom
      const targetElement = document.getElementById(
        "tencent-dot-player_html5_api"
      );

      sliderBarEl = document.querySelector('.vjs-play-progress.vjs-slider-bar') // 进度条Dom
      timeEl = document.querySelector('.vjs-current-time-display') // 时刻Dom
      totalWidth = targetElement.offsetWidth - 20 // 进度条宽度
      
      // 监听click事件，即手指触碰屏幕时的事件
      // targetElement.addEventListener("click", (event) => {});
      
      // 监听touchstart事件，即手指触碰屏幕时的事件
      targetElement.addEventListener("touchstart", this.touchStart);

      // 监听touchmove事件，即手指在屏幕上滑动时的事件
      targetElement.addEventListener("touchmove", this.touchMove);

      // 监听touchend事件，即手指离开屏幕时的事件
      targetElement.addEventListener("touchend", this.touchEnd);
    },
    touchStart(event) {
      // 获取触摸点的初始位置
      let touch = event.touches[0];
      startX = touch.clientX;

      currentTime = parseInt(this.tcPlayer.currentTime())

      // 阻止默认滚动行为
      event.preventDefault();
    },
    touchMove(event) {
      flag = true
      // 获取触摸点的当前位置
      let touch = event.touches[0];
      let currentX = touch.clientX;
      this.tcPlayer.pause()
      let deltaX = currentX - startX; // 计算横向滑动距离
      // 距离相对于总宽度的比例, 并将其限制在-1到1之间
      const progress = Math.min(Math.max(deltaX / totalWidth, -1), 1);
      second = parseInt(currentTime + this.duration * progress) // 当前需要定位到哪个时刻
      second = Math.min(Math.max(second, 0), this.duration) // 处理临界值, 时刻不能少于0, 最大不能超过当前视频时长
      let width = Math.min(Math.max(second / this.duration, 0), 1)
      width = width * 100 // 转为百分比
      width = parseFloat(width.toFixed(2)) // 百分比保留两位小数
      sliderBarEl.style.width = width + '%' // 设置Dom进度条宽度
      timeEl.innerText = this.formatTime(second) // 设置Dom视频当前时间

      // 阻止默认滚动行为
      event.preventDefault();
    },
    touchEnd(event) {
      if (flag) {
        this.tcPlayer.currentTime(second)
        this.tcPlayer.play()
        startX = null; // 清空初始位置
        currentTime = null
        second = null
        flag = false
      }
    },
    // 将秒转为时分秒格式(按照腾讯云播放器的格式来改), 例如3737秒转为1:02:17格式, 65秒转为1:05
    /***
     * 1.拖动屏幕的时候播放时刻会一直变化, 为了拖动结束开始播放的那一刻无感知切换, 就按照腾讯云的格式来转换, 要不然就会有一闪的效果
     */
    formatTime(seconds) {
      let hours = Math.floor(seconds / 3600); // 计算小时数
      let minutes = Math.floor((seconds % 3600) / 60); // 计算分钟数
      let remainingSeconds = seconds % 60; // 计算剩余的秒数

      let timeString = "";

      if (hours > 0) {
        timeString += hours + ":";
        minutes = ("0" + minutes).slice(-2) // 如果小时数大于0且分数小于10, 分数就补零, 否则不补零
      }

      timeString += minutes + ":" + ("0" + remainingSeconds).slice(-2);

      return timeString;
    }
  },
  destroyed() {
    if (this.tcPlayer) {
      this.tcPlayer.dispose();
    }
    const targetElement = document.getElementById(
      "tencent-dot-player_html5_api"
    );
    if (targetElement) {
      targetElement.removeEventListener("touchstart", this.touchStart);
      targetElement.removeEventListener("touchmove", this.touchMove);
      targetElement.removeEventListener("touchend", this.touchEnd);
    }
  },
};
</script>

<!-- tcplayer.min.css文件中的fill-available属性有冲突, 已经在webpack中用string-replace-loader这个loader将fill-available替换成stretch -->
<style src="../../../static/tcplayer/tcplayer.min.css"></style>
<style lang="less" scoped>
.tencent-dot-player-wrap {
  width: 100%;
  height: 100%;
  .tencent-dot-player {
    width: 100%;
    height: 100%;
  }
}
/deep/.tcplayer {
  // .vjs-control {
  //   width: 3em;
  // }
  .common-control-value {
    font-size: 1.2em;
    line-height: 2.57;
  }
}
</style>
