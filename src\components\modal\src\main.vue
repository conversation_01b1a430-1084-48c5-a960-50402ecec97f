<template>
  <transition name="modal" v-on:after-leave="destroyElement">
    <div class="modal" :class="customClass" v-show="visible" @click="handleWrapperClick">
      <div class="inner">
        <div class="hd" v-if="title">{{title}}</div>
        <div class="bd">
          <img class="icon" :src="iconImg" alt="" v-if="icon"/>
          <div class="txt"><slot>{{message}}</slot></div>
          <p v-if="small_message.length>0" class="small_txt">{{small_message}}</p>
        </div>
        <template v-if="halfBtn">
          <div class="ft" v-if="showConfirmButton||showCancelButton">
            <button class="btn" @click="handleCancel" v-if="showCancelButton">{{cancelButtonText}}</button>
            <button class="btn primary" @click="handleConfirm('confirm')" v-if="showConfirmButton">{{confirmButtonText}}</button>
          </div>
        </template>
        <template v-else>
          <div class="ft btn-block" v-if="showConfirmButton||showCancelButton">
            <button class="btn primary" @click="handleConfirm('confirm')" v-if="showConfirmButton">{{confirmButtonText}}</button>
            <button class="btn" :class="{small:smallCancel}" @click="handleCancel" v-if="showCancelButton">{{cancelButtonText}}</button>
          </div>
        </template>
      </div>
    </div>
  </transition>
</template>

<script>
  export default {
    data() {
      return {
        visible: false,
        customClass: '',          // 自定义类名
        title: '',
        message: '',
        halfBtn:true,           //按钮是否为1/2宽
        icon: 'success',
        duration: 3000,
        yes: null,
        cancel: null,
        smallCancel: false,    //取消按钮大小
        beforeClose: null,
        timer: null,
        onClose: null,
        closeOnClickModal: true,  // 是否可通过点击遮罩关闭
        showConfirmButton: false, // 显示确定按钮
        showCancelButton: false,  // 显示取消按钮
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        small_message:''
      }
    },
    computed: {
      iconImg: function () {
        return require(`../../../assets/image/modal/${this.icon}.png`);
      }
    },
    mounted() {
      this.startTimer();
    },
    methods: {
      close: function () {
        this.clearTimer();
        this.visible = false;
        if (typeof this.onClose === 'function') this.onClose(this.id);
      },
      handleClose: function (action) {
        if ('function' === typeof this.beforeClose) {
          this.beforeClose(action, this, () => {
            this.close();
          });
        } else {
          this.close();
        }
      },
      handleWrapperClick: function () {
        if (this.closeOnClickModal) this.handleClose('cancel');
      },
      handleConfirm: function () {
        'function' === typeof this.yes && this.yes();
        this.handleClose('confirm');
      },
      handleCancel: function () {
        'function' === typeof this.cancel && this.cancel();
        this.handleClose('cancel');
      },
      clearTimer: function () {
        clearTimeout(this.timer);
      },
      startTimer: function () {
        if (this.duration > 0) {
          this.timer = setTimeout(() => {
            this.handleClose('cancel');
          }, this.duration);
        }
      },
      destroyElement: function (el) {
        this.$destroy(true);
        el.parentNode.removeChild(el);
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../../../assets/less/variable";
  .modal{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; text-align:center; padding:0 11.5%; overflow-y:auto; word-break:normal; background-color:rgba(0, 0, 0, .7); transition:opacity .3s ease;
    &:after{ display:inline-block; width:0; height:100%; vertical-align:middle; content:''; }
    > .inner{ display:inline-block; width:100%; margin-top:-.4rem; vertical-align:middle; text-align:left; background-color:#fff; border-radius:.04rem; transition:all .3s ease; }
    .bd{ display:flex; align-items:center; justify-content:center; flex-direction:column; min-height:1.7rem; padding:0 .12rem; font-size:.15rem; }
    .ft{
      display:flex; padding-right:.15rem;
      .btn{
        flex:1; height:.44rem; margin-left:.15rem; margin-bottom:.18rem; color:#666; font-size:.17rem; border:1px solid #efefef; background-color:#fff; border-radius:.02rem;
        &.primary{ color:#fff; border:none; background-color:#f26662; background-image:@bgColor; }
        &.small{ border:none;font-size: .12rem;height: .22rem;}
      }
      &.btn-block{
        display: block;
        padding: 0 .15rem 0;
        .btn{ display: inline-block;margin: 0 0 .18rem; width: 100%; }
      }
    }
    .icon{ width:.6rem; height:.6rem; }
    .txt{ margin-top:.05rem; min-height:.34rem; margin-bottom: .03rem;text-align: center;;max-width: 2.5rem;}
  }
  .modal-enter, .modal-leave-active{
    opacity:0;
  }
  .modal-enter .inner,
  .modal-leave-active .inner {
    transform: scale(.8);
  }

  // 确认消息框样式
  .modal.confirm{
    padding:0 .16rem;
    .hd{ margin:.2rem 0 -.2rem; text-align:center; font-size:.19rem; }
    .bd{ min-height:1.5rem; text-align:center; }
    .txt{ min-height:0; margin-top:0; }
  }
  .small_txt{
    font-size:.12rem;
    text-align: center;
    color:#444444
  }
  .ygzsk-modal {
    padding: 0 30%;
  }
</style>
