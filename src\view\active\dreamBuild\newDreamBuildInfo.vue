<template>
  <div id="infoWrapNew" :class="{ active: isIphnexX }">
    <inviteTop />
    <div class="Expired" v-if="Expired">
      <img src="../../../assets/image/active/adultExam/tiaozhuang_ico.png" alt="" />
      <p>当前奖学金活动已结束～</p>
    </div>
    <div v-else>
      <div class="advance">
        <!-- 8月30日屏蔽
          <div class="newBanner" v-if="started">
          <div @click='enroll' class="enrollBtn" :class="{active:isShowEnd==2}"></div>
          <img src="../../../assets/image/active/dreamBuild2021/dreamBuild.png" alt="" style="position: absolute;width: 1.78rem;top:.1rem;left:.1rem">
          <p class="enrollText">已报名人数</p>
          <div class="enrollCount">
            <DigitRoll :rollDigits="EnrolmentCount"/>
          </div>
          <div class="banner_swiper">
            <div class="gdBox" :class="{anim:animate==true}">
              <div class="gdBox_wrap" v-for="(item,index) in items" :key='index' :class="{anim1:index==1}">
                <div class="img">
                  <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar">
                </div>
                <div class="txt_fr">
                  <span>{{item.realName|hideName}}</span>
                  <span>报读成功</span>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <div class="content">
          <div class="bg">
            <div class="content">
              <div class="bd">
                <div class="school">
                  <div style="width: 100%">
                    <div class="headBox">
                      <!--<img class="titleImg" src="../../../assets/image/active/newDreamBuild/dreamFive/city_title_bg.png" alt="">-->
                      {{ cityTxt }}可报考院校
                    </div>
                  </div>
                  <!--<div class="item noc jndxItem" @click="goAnchor('#jndx')" v-if="school[cityName].citys.includes('jndx')">-->
                  <!-- <template v-if="cityCode != 'gz'">
                   <div class="item noc" @click="goAnchor('#jndx')" v-if="school[cityName].citys.includes('jndx')">
                    <i class="jndxNew"></i>
                    <span>暨南大学</span>
                  </div>
                  </template> -->
                  <!-- <template v-if="cityCode == 'gz' && pfsnLevel != 5">
                   <div class="item noc" @click="goAnchor('#jndx')" v-if="school[cityName].citys.includes('jndx')">
                    <i class="jndxNew"></i>
                    <span>暨南大学</span>
                  </div>
                  </template> -->
                  <template v-if="cityCode =='dg' && pfsnLevel!=5">
                    <div class="item noc" @click="goAnchor('#hnnydx')" v-if="school[cityName].citys.includes('hnnydx')">
                      <i class="hnnyNew"></i>
                      <span>华南农业大学</span>
                    </div>
                  </template>
                  <template v-if="cityCode !='dg'">
                    <div class="item noc" @click="goAnchor('#hnnydx')" v-if="school[cityName].citys.includes('hnnydx')">
                      <i class="hnnyNew"></i>
                      <span>华南农业大学</span>
                    </div>
                  </template>
                  <!--<div class="item noc hnnydxItem" @click="goAnchor('#hnnydx')" v-if="school[cityName].citys.includes('hnnydx')">-->
                  <div class="item noc" @click="goAnchor('#gzrjxy')" v-if="school[cityName].citys.includes('gzrjxy') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="gzrjNew"></i>
                    <span>广州软件学院</span>
                  </div>
                  <!--<div class="item noc stdxItem" @click="goAnchor('#stdx')" v-if="school[cityName].citys.includes('stdx')">-->
                  <!-- <div class="item noc" @click="goAnchor('#stdx')" v-if="school[cityName].citys.includes('stdx')">
                    <i class="stdxNew"></i>
                    <span>汕头大学</span>
                  </div> -->
                  <!--<div class="item noc gdjrItem" @click="goAnchor('#gdjrxy')" v-if="school[cityName].citys.includes('gdjrxy')">-->
                  <div class="item noc" @click="goAnchor('#gzdx')" v-if="school[cityName].citys.includes('gzdx')">
                    <i class="gzdxNew"></i>
                    <span>广州大学</span>
                  </div>
                  <!-- <template v-if="cityCode == 'hz' && pfsnLevel != 1">
                  <div class="item noc" @click="goAnchor('#gzdx')" v-if="school[cityName].citys.includes('gzdx')">
                    <i class="gzdxNew"></i>
                    <span>广州大学</span>
                  </div>
                  </template> -->
                  <!--<div class="item noc dglgItem" @click="goAnchor('#dglg')" v-if="school[cityName].citys.includes('dglg')">-->
                  <div class="item noc" @click="goAnchor('#dglg')" v-if="school[cityName].citys.includes('dglg')">
                    <i class="dglgNew"></i>
                    <span>东莞理工学院</span>
                  </div>
                  <!--<div class="item noc gzsxyItem" @click="goAnchor('#gzsxy')" v-if="school[cityName].citys.includes('gzsxy')">-->

                  <!--<div class="item noc lnsfItem" @click="goAnchor('#lnshxy')" v-if="school[cityName].citys.includes('lnshxy')">-->
                  <template v-if="pfsnLevel != 1">
                    <div class="item noc" @click="goAnchor('#lnshxy')" v-if="school[cityName].citys.includes('lnshxy')">
                      <i class="lnsfNew"></i>
                      <span>岭南师范学院</span>
                    </div>
                  </template>
                  <template v-if="(cityCode=='sw' && pfsnLevel == 1) || (cityCode=='zj' && pfsnLevel == 1)">
                    <div class="item noc" @click="goAnchor('#lnshxy')" v-if="school[cityName].citys.includes('lnshxy')">
                      <i class="lnsfNew"></i>
                      <span>岭南师范学院</span>
                    </div>
                  </template>
                  <!-- <template v-if="cityCode == 'sz'">
                    <div class="item noc" @click="goAnchor('#lnshxy')" v-if="school[cityName].citys.includes('lnshxy')">
                      <i class="lnsfNew"></i>
                      <span>岭南师范学院</span>
                    </div>
                  </template> -->
                  <!-- <div class="item noc" @click="goAnchor('#zknygc')" v-if="school[cityName].citys.includes('zknygc')">
                    <i class="zknyNew"></i>
                    <span>仲恺农业工程学院</span>
                  </div> -->
                  <div class="item noc" @click="goAnchor('#zqxy')" v-if="
                    school[cityName].citys.includes('zqxy') &&
                    (pfsnLevel == 1 || !pfsnLevel)
                  ">
                    <i class="zqxyNew"></i>
                    <span>肇庆学院</span>
                  </div>
                  <div class="item noc" @click="goAnchor('#jyxy')" v-if="school[cityName].citys.includes('jyxy')">
                    <i class="jyxyNew"></i>
                    <span>嘉应学院</span>
                  </div>
                  <!-- <div class="item noc" @click="goAnchor('#zyydx')" v-if="school[cityName].citys.includes('zyydx')">
                    <i class="gzzyyNew"></i>
                    <span>广州中医药大学</span>
                  </div> -->
                  <div class="item noc" @click="goAnchor('#gzsxy')" v-if="school[cityName].citys.includes('gzsxy') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="gzsxy"></i>
                    <span>广州商学院</span>
                  </div>
                  <div class="item noc" @click="goAnchor('#gzhl')" v-if="school[cityName].citys.includes('gzhl') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="gzhl"></i>
                    <span>广州华立学院</span>
                  </div>
                  <div class="item noc" @click="goAnchor('#dgcs')" v-if="school[cityName].citys.includes('dgcs') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="dgcs"></i>
                    <span> 东莞城市学院</span>
                  </div>
                  <div class="item noc" @click="goAnchor('#zhkj')" v-if="school[cityName].citys.includes('zhkj') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="zhkj-icon"></i>
                    <span>珠海科技学院</span>
                  </div>
                  <div class="item noc" @click="goAnchor('#gdxhxy')" v-if="school[cityName].citys.includes('gdxhxy') && (pfsnLevel == 1 || !pfsnLevel)">
                    <i class="gdxhxy-icon"></i>
                    <span>广州新华学院</span>
                  </div>
                    
                  <!-- <div class="item noc" @click="goAnchor('#gdjrxy')" v-if="school[cityName].citys.includes('gdjrxy')">
                    <i class="gdjrNew"></i>
                    <span>广东金融学院</span>
                  </div> -->
                  <template v-if="pfsnLevel == 5 || !pfsnLevel">
                    <!--<div class="item noc gzcjItem" @click="goAnchor('#gzcj')" v-if="school[cityName].citys.includes('gzcj')">-->
                    <div class="item noc" @click="goAnchor('#gzcj')" v-if="school[cityName].citys.includes('gzcj')">
                      <i class="gzcjNew"></i>
                      <span>广州城建职业学院</span>
                    </div>
                    <!--<div class="item noc gdzyjsItem" @click="goAnchor('#gzzyjs')" v-if="school[cityName].citys.includes('gzzyjs')">-->
                    <!-- <div class="item noc" @click="goAnchor('#gzzyjs')" v-if="school[cityName].citys.includes('gzzyjs')">
                      <i class="gdzyjsNew"></i>
                      <span>广东职业技术学院</span>
                    </div> -->
                    <!--<div class="item noc gzgbItem" @click="goAnchor('#gbdsdx')" v-if="school[cityName].citys.includes('gbdsdx')">-->
                    <div class="item noc" @click="goAnchor('#gbdsdx')" v-if="school[cityName].citys.includes('gbdsdx')">
                      <i class="gzgbNew"></i>
                      <span>广州开放大学</span>
                    </div>
                    <!--<div class="item noc zyydxItem" @click="goAnchor('#zyydx')" v-if="school[cityName].citys.includes('zyydx')">-->

                    <!--<div class="item noc zknyItem" @click="goAnchor('#zknygc')" v-if="school[cityName].citys.includes('zknygc')">-->

                    <!--<div class="item noc gdkmItem" @click="goAnchor('#gdkmzyxy')" v-if="school[cityName].citys.includes('gdkmzyxy')">-->
                    <div class="item noc" @click="goAnchor('#gdkmzyxy')"
                      v-if="school[cityName].citys.includes('gdkmzyxy')">
                      <i class="gdkmNew"></i>
                      <span>广东科贸职业学院</span>
                    </div>
                    <!--<div class="item noc gdstItem" @click="goAnchor('#gdst')" v-if="school[cityName].citys.includes('gdst')">-->
                    <div class="item noc" @click="goAnchor('#gdst')" v-if="school[cityName].citys.includes('gdst')">
                      <i class="gdstNew"></i>
                      <span>广东生态工程职业学院</span>
                    </div>
                    <!--<div class="item noc dgzyItem" @click="goAnchor('#dgzyjs')" v-if="school[cityName].citys.includes('dgzyjs')">-->
                    <div class="item noc" @click="goAnchor('#dgzyjs')" v-if="school[cityName].citys.includes('dgzyjs')">
                      <i class="dgzyNew"></i>
                      <span>东莞职业技术学院</span>
                    </div>
                    <!--<div class="item noc zqxyItem" @click="goAnchor('#zqxy')" v-if="school[cityName].citys.includes('zqxy')">-->

                    <!--<div class="item noc jyxyItem" @click="goAnchor('#jyxy')" v-if="school[cityName].citys.includes('jyxy')">-->

                    <!--<div class="item noc jyxyItem" @click="goAnchor('#gdln')" v-if="school[cityName].citys.includes('gdln')">-->
                    <!-- <div class="item noc" @click="goAnchor('#gdln')" v-if="school[cityName].citys.includes('gdln')">
                      <i class="gdlnNew"></i>
                      <span>广东岭南职业技术学院</span>
                    </div> -->
                    <!--<div class="item noc qyzyItem" @click="goAnchor('#qyzy')" v-if="school[cityName].citys.includes('qyzy')">-->
                    <div class="item noc" @click="goAnchor('#qyzy')" v-if="school[cityName].citys.includes('qyzy')">
                      <i class="qyzyNew"></i>
                      <span>清远职业技术学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gzhx')" v-if="school[cityName].citys.includes('gzhx')">
                      <i class="gzhxNew"></i>
                      <span>广州华夏职业学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#sszy')" v-if="school[cityName].citys.includes('sszy')">
                      <i class="sszyNew"></i>
                      <span>广东松山职业技术学院</span>
                    </div>
                    <!--<div class="item noc swzyItem" @click="goAnchor('#swzyjs')" v-if="school[cityName].citys.includes('swzyjs')">-->
                    <div class="item noc" @click="goAnchor('#swzyjs')" v-if="school[cityName].citys.includes('swzyjs')">
                      <i class="swzyNew"></i>
                      <span>汕尾职业技术学院</span>
                    </div>
                    <!--<div class="item noc nhcrItem" @click="goAnchor('#nhcr')" v-if="school[cityName].citys.includes('nhcr')">-->
                    <div class="item noc" @click="goAnchor('#nhcr')" v-if="school[cityName].citys.includes('nhcr')">
                      <i class="nhcrNew"></i>
                      <span>南海成人学院</span>
                    </div>
                    <!--<div class="item noc mmzyItem" @click="goAnchor('#mmzyjs')" v-if="school[cityName].citys.includes('mmzyjs')">-->
                    <!-- <div class="item noc" @click="goAnchor('#mmzyjs')" v-if="school[cityName].citys.includes('mmzyjs')">
                      <i class="mmzyNew"></i>
                      <span>茂名职业技术学院</span>
                    </div> -->
                    <div class="item noc" @click="goAnchor('#gdjtzy')" v-if="school[cityName].citys.includes('gdjtzy')">
                      <i class="gdjtzy-icon"></i>
                      <span>广东交通职业技术学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gdjdzy')" v-if="school[cityName].citys.includes('gdjdzy')">
                      <i class="gdjdzy-icon"></i>
                      <span>广东机电职业技术学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gzrp')" v-if="school[cityName].citys.includes('gzrp')">
                      <i class="gzrp-icon"></i>
                      <span>广州铁路职业技术学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gzhsvc')" v-if="school[cityName].citys.includes('gzhsvc')">
                      <i class="gzhsvc-icon"></i>
                      <span>广州华商职业学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gdiei')" v-if="school[cityName].citys.includes('gdiei')">
                      <i class="gdiei-icon"></i>
                      <span>广东信息工程职业学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gdposat')"
                      v-if="school[cityName].citys.includes('gdposat')">
                      <i class="gdposat-icon"></i>
                      <span>广东科学技术职业学院</span>
                    </div>
                    <div class="item noc" @click="goAnchor('#gzkjzy')" v-if="school[cityName].citys.includes('gzkjzy')">
                      <i class="gzkjzy-icon"></i>
                      <span>广州科技职业技术大学</span>
                    </div>
                  </template>

                  <!-- 占位DIV-->
                  <!--<div class="item noc" >-->
                  <!--<img  alt="">-->
                  <!--<span></span>-->
                  <!--</div>-->
                </div>
              </div>
              <div class="school-info">
                <template v-for="(item, index) in school[cityName].schoolInfo">
                  <div :id="item.name" v-if="psfnListFilter(item.detail.pfsnList, item)" class="info" :key="index">
                    <div class="table dsnm">
                      <div class="table-t">
                        <span class="fl">
                          {{ item.detail.unvsName }}
                        </span>
                        <span class="fr">
                          学制/年：{{ item.detail.schoolYear }}<br />书杂费:{{
                              item.detail.bookFee
                          }}
                        </span>
                      </div>
                      <div class="table-b">
                        <table>
                          <thead>
                            <tr>
                              <th width="60">招生层次</th>
                              <th width="150">招生专业</th>
                              <th width="80">学费<br />（元/学年）</th>
                            </tr>
                          </thead>
                          <tbody>
                            <template v-if="pfsnLevel == 5 || !pfsnLevel">
                              <tr v-for="(it, i) in item.detail.pfsnList['5']" :key="it.pfsn">
                                <td :rowspan="item.detail.pfsnList['5'].length" v-if="i == 0">
                                  高起专
                                </td>
                                <td class="text-l">
                                  {{ it.pfsn }}
                                </td>
                                <td>
                                  <span v-for="ele in it.fee" :key="ele">{{
                                      ele
                                  }}</span>
                                </td>
                              </tr>
                              <tr v-for="(it, i) in item.detail.pfsnList['2']" :key="it.pfsn">
                                <td :rowspan="item.detail.pfsnList['2'].length" v-if="i == 0">
                                  高起专<br />(2.5年学制)
                                </td>
                                <td class="text-l">
                                  {{ it.pfsn }}
                                </td>
                                <td>
                                  <span v-for="ele in it.fee" :key="ele">{{
                                      ele
                                  }}</span>
                                </td>
                              </tr>
                              <tr v-for="(it, i) in item.detail.pfsnList['3']" :key="it.pfsn">
                                <td :rowspan="item.detail.pfsnList['3'].length" v-if="i == 0">
                                  高起专<br />(3年学制)
                                </td>
                                <td class="text-l">
                                  {{ it.pfsn }}
                                </td>
                                <td>
                                  <span v-for="ele in it.fee" :key="ele">{{
                                      ele
                                  }}</span>
                                </td>
                              </tr>
                            </template>
                            <template v-if="pfsnLevel == 1 || !pfsnLevel">
                              <tr v-for="(it, i) in item.detail.pfsnList['1']" :key="it.pfsn">
                                <td :rowspan="item.detail.pfsnList['1'].length" v-if="i == 0">
                                  专升本
                                </td>
                                <td class="text-l">
                                  {{ it.pfsn }}
                                </td>
                                <td>
                                  <span v-for="ele in it.fee" :key="ele">{{
                                      ele
                                  }}</span>
                                </td>
                              </tr>
                            </template>
                          </tbody>
                        </table>
                      </div>
                    </div>
                    <span v-if="item.detail.unvsName=='东莞理工学院'" style="font-size:15px">注：东莞考区按300元/学年收取书杂费</span>
                  </div>
                </template>
              </div>
              <p class="bz">备注：报读专业最终以省考生院招生项目公布为准</p>
            </div>
          </div>
        </div>
      </div>
      <common-blank ref="process">
        <div class="process">
          <img src="../../../assets/image/active/dreamBuild/process-bg.png" alt="" />
          <div class="content">
            <div class="content-l">
              <p>2019.09</p>
              <p>2019.10</p>
              <p>2019.11</p>
            </div>
            <div class="content-m">
              <img src="../../../assets/image/active/dreamBuild/process.png" alt="" />
            </div>
            <div class="content-r">
              <p>报名</p>
              <p>做准考证<span>（缴纳111元考试费）</span></p>
              <p>参加2019年成人高考</p>
              <p>录取<span>（缴纳第一年费用）</span></p>
              <p>2.5-3年后高校颁发毕业证</p>
            </div>
            <div class="xz">
              <img src="../../../assets/image/active/dreamBuild/xz.png" alt="" />
            </div>
          </div>
          <div class="close" @click="s.process.close()">
            <img src="../../../assets/image/active/dreamBuild/close.png" alt="" />
          </div>
        </div>
      </common-blank>
      <!-- <div class="codeImg">
                      <img src="../../../assets/image/active/newDreamBuild/code.png" alt="">
                </div>
                <div class="bottomBox">
                  <a href="tel:4008336013" class="callUs">联系我们</a>
                  <span>|</span>
                  <a href="http://www.yzou.cn" class="official">访问官网</a>
        </div> -->
      <share :title="title" :desc="desc" :link="shareLink" regOrigin="48" :scholarship="scholarship" :levelId="pfsnLevel" ref="share" />
      <!-- <footer-bar @isIphnexX="isIphnexX=true" :inviteId="inviteId" :scholarship="scholarship" :actName="actName"  tabName="newintroduce" /> -->
      <second-footer @enroll="enroll" />
    </div>
  </div>
</template>

<script>
import appShare from "@/mixins/appShare";
import share from "@/components/share";
import commonBlank from "@/components/commonPopBlank";
import dataJson from "../scholarship/dataJson.json";
import newcountdown from "@/components/active/countdown5";
import school from "./dreambuildInfo.json";
import DigitRoll from "@huoyu/vue-digitroll";
import footerBar from "@/components/activePage/footer";
import SecondFooter from "@/view/active/2021NewMain/components/second-footer";
import inviteTop from "../enrollAggregate/components/invite-top";
import statistic from "@/view/active/2021NewMain/statistic.json";

let scholarship = "164";

export default {
  components: {
    share,
    commonBlank,
    newcountdown,
    DigitRoll,
    footerBar,
    SecondFooter,
    inviteTop,
  },
  mixins: [appShare],
  data() {
    return {
      isShowEnd: 0,
      EnrolmentCount: 0,
      Expired: false,
      started: true,
      shareLink: "",
      schoolIcon: "gzs",
      city: "hz",
      cityTxt: "惠州",
      // tabName: 'gqz',
      tabName: "",
      cityName: "",
      inviteId: "",
      isIphnexX: false,
      scholarship,
      pfsnLevel: "",
      nowTime: "",
      startTime: 1541595600000, // 倒计时开始时间：2018-11-11 21:00:00
      endTime: 1548691199000, //修改结束时间：2019年1月28日23时59分59秒,
      school: school,
      isChange: false,
      actName: "",
      endDay: "0",
      animate: false,
      items: [],
      headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
      title: "在线报读，名校录取，学信网可查，和我一起提升学历！",
      desc: "远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！",
      regOrigin: "",
      cityCode:'',
    };
  },
  computed: {
    regChannel() {
      return this.$route.query.regChannel || "";
    },
    jumpUrl: function () {
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          city: this.cityName,
          activityName: "scholarship",
          inviteId: this.inviteId,
          actName: this.actName,
          action: "login",
          scholarship: this.scholarship,
          recruitType: "1",
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
        },
      };
      return url;
    },
  },
  created() {
    this.cityCode = this.$route.query.city
    this.getNewRegList();
    //this.getEnrolmentCount()
    this.cityName = this.$route.params.name;
    this.inviteId = this.$route.query.inviteId || "";
    this.actName = this.$route.query.actName || "";
    this.cityTxt = dataJson.city[this.cityName];
    this.pfsnLevel = this.$route.query.pfsnLevel;
    const link = `/active/dreamBuild/newDreamBuildInfo/${this.cityName}?city=${this.cityCode}`;
    this.shareLink = window.location.origin + link;
    this.regOrigin = this.$route.query.regOrigin || "";

    this.timer = setInterval(() => {
      // this.scrollT();
      // this.countAdd();
    }, (Math.floor(Math.random() * 10) + 5) * 1000);
    // 获取活动开始和结束时间
    this.getServerTime();
    this.initAppShare(() => {
      this.setShareParams({
        title: this.title,
        content: this.desc,
        url: link + `&pfsnLevel=${this.pfsnLevel}`,
        regOrigin: "48",
      });
    });
    if (this.pfsnLevel) {
      this.$yzStatistic(
        "marketing.base.browse",
        statistic.city[this.pfsnLevel].main.id,
        statistic.city[this.pfsnLevel].main.name
      );
    }
  },
  mounted: function () {
    this.$nextTick(function () {
      this.isMounted = true;
    });
  },
  methods: {
    countAdd() {
      this.EnrolmentCount += 1;
    },
    enroll() {
      if (!this.isOpenAppLogin()) {
        // 该方法来自appShare.js
        return;
      }
      this.$router.push(this.jumpUrl);
    },
    phoneNumber() {
      window.location.href = "tel:4008336013";
    },
    psfnListFilter(list, item) {
      if (!this.pfsnLevel) {
        return true;
      }
      if (!list) {
        return false;
      }
      const bool3 = item.name === 'gdln' || item.name === 'zyydx' || item.name === 'zknygc' || item.name === 'gdjrxy' || item.name === 'gzhl';
      if(bool3) return false;
      
      const bool1 = Object.keys(list).includes(this.pfsnLevel.toString());
      const bool2 = this.pfsnLevel == 5 && Object.keys(list).some((item) => ["2", "3"].includes(item));
      return bool1 || bool2;
    },
    //筑梦信息
    getServerTime() {
      this.$http
        .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarship })
        .then((res) => {
          let { code, body } = res;
          if (code !== "00") return;
          this.startTime = body.StartTime;
          this.endTime = body.EndTime;
          this.actName = body.actName;
          this.isShowEnd = body.isShowEnd;
          this.EnrolmentCount = parseInt(body.learnCount);
          this.$nextTick(() => {
            this.getSystemDateTime();
          });
        });
    },
    turn: function () {
      this.showArea = true;
    },
    openShare: function () {
      this.$router.push({ name: "dreamBuildInvite" });
      // this.s.share.open(null, 'login', `${this.$route.fullPath}&action=share`);
    },
    chooseSchool: function (event) {
      if (event) event.cancelBubble = true;
      this.schoolIcon = "";
    },
    chooseSchoolIcon: function (event, city) {
      event.cancelBubble = true;
      if (this.schoolIcon == city) {
        this.schoolIcon = "";
      } else {
        this.schoolIcon = city;
      }
    },
    goAnchor: function (selector) {
      const anchor = this.$el.querySelector(selector);
      document.querySelector("html").scrollTop = anchor.offsetTop;
      document.getElementsByTagName("body")[0].scrollTop = anchor.offsetTop;
    },
    // 回到顶部
    backToTop: function () {
      document.getElementsByTagName("body")[0].scrollTop = 0;
    },
    getEnrolmentCount: function () {
      this.$http
        .post("/mkt/getEnrolmentCount/1.0/", { scholarship: scholarship })
        .then((res) => {
          let { code, body } = res;
          if (code === "00") {
            this.EnrolmentCount = parseInt(body.learnCount);
          }
        });
    },
    openShowMsgBox() {
      this.showMsgBox = true;
    },
    closeShowMsgBox() {
      this.showMsgBox = false;
    },
    // 获取最新注册用户列表
    getNewRegList: function () {
      this.$http.post("/us/getNewRegList/1.0/").then((res) => {
        let { code, body } = res;
        if (code === "00") {
          this.items = body;
        }
      });
    },
    toSchool(school) {
      if (school == "cj") {
        this.$router.push({ name: "enrollmentHomepage" });
      } else if (school == "gb") {
        this.$router.push({ name: "guangzhouOpenUniversity" });
      } else {
        this.$router.push({ name: "gdProfessionUniversity" });
      }
    },
    tips() {
      this.$modal({ message: "活动暂未开始！", icon: "warning" });
    },
    scrollT() {
      let oG = document.getElementsByClassName("gdBox_wrap");
      oG[0].style.opacity = 0;
      setTimeout(() => {
        this.animate = true;
      }, 100);
      setTimeout(() => {
        this.items.push(this.items[0]);
        this.items.shift();
        this.animate = false;
        oG[1].style.opacity = 0;
        oG[0].style.opacity = 1;
        this.animation(oG[1], { opacity: 1 });
      }, 500);
    },
    animation(oEle, obj, fn) {
      //1. 清除定时器
      clearInterval(oEle.timer);
      //2.开启定时器
      oEle.timer = setInterval(() => {
        var bflag = true;
        for (var attr in obj) {
          var current = 0;
          var target = 0;
          if (attr == "opacity") {
            current = this.getStyle(oEle, attr) * 100;
            target = obj[attr] * 100;
          } else {
            current = parseInt(getStyle(oEle, attr));
            target = parseInt(obj[attr]);
          }
          //2.1 步长=(目标-当前的位置)/10;
          var step = (target - current) / 10;
          //2.2 判断 步长
          step = step > 0 ? Math.ceil(step) : Math.floor(step);
          //2.3  新的位置=当前的位置+步长

          if (attr == "opacity") {
            oEle.style.opacity = (current + step) / 100;
            oEle.style.filter = `alpha(opacity=${current + step})`;
          } else if (attr == "zIndex") {
            oEle.style.zIndex = obj[attr];
          } else {
            oEle.style[attr] = current + step + "px";
          }
          if (current != target) {
            bflag = false;
          }
        }
        if (bflag) {
          clearInterval(oEle.timer);
          if (fn && typeof fn == "function") {
            // 如果 fn不等于 undefined ,就是 true
            fn.bind(oEle)(oEle);
          }
        }
      }, 40);
    },
    getStyle(oEle, attr) {
      if (window.getComputedStyle) {
        return window.getComputedStyle(oEle, null)[attr];
      }
      return oEle.currentStyle[attr];
    },
    // 获取服务器时间，判断活动是否已结束
    getSystemDateTime: function () {
      const now = new Date().getTime();
      this.nowTime = now;
      this.started = now > this.startTime;
      this.Expired = now > this.endTime;
      this.$refs.countdown && this.$refs.countdown.getSystemDateTime(now);
    },
  },
};
</script>

<style lang="less" scoped>
#infoWrapNew,
#infoWrapOld {
  &.active {
    padding-bottom: 34px;
  }

  .no-pop {
    pointer-events: none;

    img {
      pointer-events: none;
    }
  }

  .Expired {
    text-align: center;

    img {
      margin-top: 1.38rem;
      width: 1.6rem;
    }

    p {
      color: #999;
      margin-top: 0.5rem;
    }

    a {
      background: linear-gradient(to bottom right, #f86f6b, #ef425c);
      display: inline-block;
      width: 2.14rem;
      height: 0.44rem;
      line-height: 0.44rem;
      border-radius: 1rem;
      color: #fff;
      font-size: 0.16rem;
      margin-top: 0.2rem;
    }
  }

  .four {
    text-align: center;
    padding: 0 0.1rem;

    img {
      width: 100%;
    }
  }

  .isPay {
    position: fixed;
    top: 0.4rem;
    left: 0.1rem;

    img {
      width: 0.6rem;
    }
  }

  .fixBottom {
    position: fixed;
    bottom: 0;
    height: 0.6rem;
    z-index: 9999;

    .leftBox {
      display: inline-block;
      width: 1.96rem;
      height: 0.6rem;
      float: left;
      background: #fff;
      position: relative;
      border-top: 1px solid #2759a4;

      img {
        width: 0.39rem;
        margin-left: 0.1rem;
        margin-top: 0.12rem;
      }

      span {
        display: inline-block;
      }

      .textOne {
        margin-top: 0.12rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
      }

      .textTwo {
        position: absolute;
        left: 0.52rem;
        top: 0.3rem;
        font-size: 0.14rem;
        color: rgba(54, 54, 54, 0.6);
      }
    }

    .rightBox {
      display: inline-block;
      width: 1.79rem;
      height: 0.6rem;
      background: #2759a4;
      float: left;
      position: relative;

      .line {
        position: absolute;
        display: inline-block;
        width: 1px;
        height: 0.25rem;
        background: rgba(255, 255, 255, 0.4);
        top: 0.16rem;
        left: 0.88rem;
      }

      .phoneIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #ffffff;
          font-size: 0.14rem;
        }
      }

      .signUpIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #ffffff;
          font-size: 0.14rem;
        }
      }
    }
  }

  .codeImg {
    img {
      width: 3.75rem;
      height: 1.66rem;
    }
  }

  .bottomBox {
    width: 3.75rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    margin-bottom: 0.6rem;

    a {
      color: #f3b422;
      font-size: 0.17rem;
    }

    span {
      color: #f3b422;
      display: inline-block;
      margin-top: 0.01rem;
    }

    .callUs {
      margin-right: 0.1rem;
    }

    .official {
      margin-left: 0.1rem;
    }
  }

  .advance {
    .newBanner {
      height: 3.25rem;
      background: url("../../../assets/image/active/sprintBeforeExam/banner_bg.png") no-repeat center center;
      background-size: 3.75rem;

      .endday {
        position: absolute;
        color: rgba(130, 77, 9, 1);
        font-size: 0.29rem;
        font-style: normal;
        top: 1.4rem;
        left: 2.14rem;

        span:last-of-type {
          margin-left: 0.17rem;
        }

        .enrollCount {
          width: 0.68rem;
          height: 0.22rem;
          position: absolute;
          top: 3%;
          text-align: center;
          color: rgba(54, 54, 54, 1);
          font-size: 0.18rem;
          right: 2.5%;
          padding-left: 1.2%;
          font-weight: 900;
        }

        .banner_swiper {
          position: absolute;
          z-index: 50;
          width: 1.25rem;
          height: 0.25rem;
          left: 0.1rem;
          top: 0.1rem;
          overflow: hidden;

          .gdBox {
            &.anim {
              transition: all 1s;
              margin-top: -0.5rem;
            }
          }

          .gdBox_wrap {
            margin-bottom: 0.09rem;
            width: 100%;
            height: 0.25rem;
            clear: both;
            opacity: 0;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 0.25rem;

            .img {
              float: left;
              width: 0.25rem;
              height: 0.25rem;
              border-radius: 50%;

              img {
                border-radius: 50%;
              }
            }
          }

          .gdBox_wrap:first-of-type,
          .gdBox_wrap:nth-of-type(2) {
            opacity: 1;
          }

          .txt_fr {
            float: left;
            margin-left: 0.05rem;
            width: 0.85rem;
            height: 0.25rem;
            color: white;
            font-size: 0.12rem;
            margin-top: 0.02rem;

            span {
              display: inline-block;
              line-height: 0.25rem;
            }

            span:last-child {
              margin-left: 0.05rem;
            }
          }
        }
      }

      .activityText {
        position: absolute;
        left: 0.57rem;
        top: 3.16rem;
        font-size: 0.12rem;
        /*color: rgba(255, 255, 255, 1);*/
        color: #363636;
      }

      .content {
        position: absolute;
        top: 3.14rem;
        left: 1.21rem;
      }

      img {
        width: 100%;
      }
    }

    >.content {
      background-color: #319f9b;
      background-size: 100%;
      background-repeat: no-repeat;

      .tab {
        height: 0.5rem;
        margin-top: -0.02rem;

        p {
          float: left;
          height: 0.5rem;
          width: 1.875rem;
          background-color: #198381;
          text-align: center;
          padding: 0.1rem 0;

          span {
            display: inline-block;
            width: 100%;
            font-size: 0.18rem;
            color: #fff;
          }

          &.active {
            background-color: #fff;
            padding: 0;
            line-height: 0.46rem;

            span {
              position: relative;
              /*border-bottom: .04rem solid #198381;*/
              width: auto;
              border-right: none;
              color: #177d7b;
              border-radius: 0.02rem;

              &:before {
                content: "";
                height: 0.04rem;
                background-color: #198381;
                width: 0.3rem;
                bottom: -0.05rem;
                transform: translateX(-50%);
                left: 50%;
                position: absolute;
                border-radius: 2px;
              }
            }
          }

          &:last-of-type {
            span {
              border-right: none;
            }
          }
        }
      }

      .lcfy {
        text-align: right;

        p {
          margin-top: 0.18rem;
          font-size: 0.16rem;
          color: #198381;
          padding-right: 0.1rem;
        }
      }

      .bg {
        background-color: #fff;
        padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
        padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

        .content {
          width: 3.75rem;
          margin: 0 auto;

          .bd {
            /*border: 1px solid #4da8a3;*/
            border-top: none;

            p {
              text-align: center;
              font-size: 0.1rem;
              padding: 0.1rem 0 0;
              color: #fff;
            }

            .school {
              /*margin-top:.06rem;*/
              background-color: #fff;
              border-radius: 0.1rem;
              position: relative;
              display: flex;
              display: -webkit-flex;
              flex-wrap: wrap;
              padding: 0.25rem 0.1rem 0;
              -webkit-flex-wrap: wrap;
              justify-content: flex-start;

              .headBox {
                text-align: center;
                position: relative;
                margin-bottom: 0.2rem;

                .titleImg {
                  width: 3.5rem;
                }

                .cityNameText {
                  position: absolute;
                  top: 30%;
                  left: 26%;
                  font-size: 0.18rem;
                  font-weight: 900;
                  color: rgba(39, 89, 164, 1);
                }
              }

              .school-border {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #a68463;
                border: 1px solid #755637;
                border-radius: 0.16rem;
                top: -0.05rem;
                right: -0.05rem;
              }

              .item {
                width: 0.84rem;
                height: 0.84rem;
                margin-bottom: 0.15rem;
                position: relative;
                margin-right: 0.04rem;

                i {
                  display: block;
                  margin: 0 auto;
                  width: 0.42rem;
                  height: 0.42rem;
                  margin-top: 0.05rem;
                  object-fit: cover;
                }

                span {
                  display: block;
                  width: 0.6rem;
                  font-size: 0.1rem;
                  margin: 0 auto;
                  text-align: center;
                  color: #333;
                  font-weight: bold;
                }

                &.dsnm:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-image: url("../../../assets/image/active/adultExam/dsnm_new.png");
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }

                &.dsnj:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }
              }

              .hnnydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/bg_hnny.png");
                background-size: 105%;
              }

              .zyydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/gzzyy.png");
                background-size: 100%;
              }
            }
          }

          .bz {
            text-align: left;
            color: red;
            font-size: 0.1rem;
            margin-top: -0.1rem;
            margin-bottom: 0.2rem;
            // color: #000;
            // padding: 0.1rem;
          }
        }

        .school-info {
          padding-top: 0.1rem;
          width: 3.5rem;
          margin: 0 auto;

          .info {
            margin-bottom: 0.4rem;
            background-color: #fff;
            margin-left: -0.02rem;
            border-radius: 0.1rem;

            &:before {
              content: "";
              position: absolute;
              width: 0.2rem;
              height: 0.1rem;
              background-image: url("../../../assets/image/active/scholarship/sjx2.png");
              background-repeat: no-repeat;
              background-size: 100%;
              left: 0.3rem;
              top: -0.1rem;
            }

            &.to {
              left: -1rem;

              &:before {
                left: 1.25rem;
              }
            }

            &.tt {
              left: -1.4rem;

              &:before {
                left: 1.65rem;
              }
            }

            &.ttr {
              left: -2.72rem;

              &:before {
                left: 2.97rem;
              }
            }

            &.tf {
              left: -2.6rem;

              &:before {
                left: 2.75rem;
              }
            }

            .table {
              position: relative;
              border-radius: 0.1rem;
              background-color: #fff;
              border-left: 1px solid rgba(162, 192, 221, 1);
              border-right: 1px solid rgba(162, 192, 221, 1);
              border-top: 1px solid rgba(162, 192, 221, 1);
              overflow: hidden;

              .table-t {
                height: 0.52rem;
                line-height: 0.52rem;
                padding: 0 0.01rem;

                background: #e6eff7;
                border-radius: 5px 5px 0px 0px;

                .fl {
                  position: relative;
                  font-weight: 600;
                  padding-left: 0.05rem;
                }

                span {
                  font-size: 0.11rem;
                }

                .fr {
                  line-height: 0.22rem;
                  text-align: left;
                  margin-top: 0.07rem;
                }
              }

              .table-b {
                table {
                  width: 100%;
                  border-top: 1px solid rgba(162, 192, 221, 1);

                  th,
                  td {
                    font-size: 0.12rem;
                    font-weight: 400;
                    vertical-align: middle;
                    border-bottom: 1px solid rgba(162, 192, 221, 1);
                    border-right: 1px solid rgba(162, 192, 221, 1);
                    background-color: #fff;
                    /*border-right: 1px solid #72dfff;*/
                    padding: 0.04rem;
                    text-align: center;

                    &:last-of-type {
                      border-right: none;
                    }

                    &.text-l {
                      text-align: left;
                    }

                    span {
                      display: block;
                      line-height: 0.28rem;
                    }
                  }
                }
              }

              &:after {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #ffd100;
                right: -0.07rem;
                top: -0.1rem;
                z-index: -1;
              }
            }
          }
        }
      }

      .bottom {
        padding: 0 0.1rem;
        padding-bottom: 0.3rem;

        .lxwm {
          width: 100%;
          text-align: center;
          padding-bottom: 0.25rem;
          font-size: 0.18rem;
          color: #fff;
          background-image: url("../../../assets/image/active/dreamBuild/line.png");
          background-repeat: no-repeat;
          background-position-y: 0.05rem;
          background-size: 100%;
        }

        .pic {
          i {
            display: inline-block;
            vertical-align: bottom;
            margin-right: 0.05rem;
            width: 0.32rem;
            height: 0.32rem;
            background-image: url("../../../assets/image/active/dreamBuild/phone.png");
            background-size: 100%;

            &.www {
              background-image: url("../../../assets/image/active/dreamBuild/www.png");
            }
          }

          span {
            margin-right: 0.35rem;
            color: #fff;
            font-size: 0.18rem;

            &:last-of-type {
              margin-right: 0;
            }
          }
        }

        .ewm {
          text-align: center;
          padding: 0.16rem 0;
          margin: 0.3rem 0;
          border: 1px dashed #198381;

          img {
            width: 1.35rem;
          }

          p {
            font-size: 18px;
            margin-top: 0.1rem;
            color: #fff;
          }
        }
      }
    }
  }

  .process {
    position: relative;
    height: 4rem;

    >img {
      width: 3.1rem;
    }

    .content {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      padding: 0.9rem 0.1rem 0;

      .xz {
        position: absolute;
        bottom: 0.05rem;
        right: 0.27rem;

        img {
          width: 0.53rem;
        }
      }

      .content-l,
      .content-m,
      .content-r {
        float: left;

        img {
          width: 0.14rem;
          margin: 0.05rem;
        }

        p {
          padding-bottom: 0.24rem;

          span {
            color: #e85b57;
          }
        }
      }

      .content-l {
        padding-top: 0.48rem;
      }

      .content-r {
        p {
          font-size: 0.15rem;
        }
      }
    }

    .close {
      position: absolute;
      top: -0.1rem;
      right: -0.1rem;

      img {
        width: 0.32rem;
      }
    }
  }

  .chooseArea {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.8);

    .chooseCity {
      background-color: transparent;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 3rem;
      transform: translate(-50%, -50%);
      /*padding: .2rem;*/
      /*padding-bottom: 0;*/
      text-align: center;
      border-radius: 0.05rem;

      img {
        width: 100%;
      }

      .title {
        color: #ffd300;
      }

      .citys {
        width: 100%;
        height: 3.5rem;
        margin: 0 auto 0.1rem;
        text-align: center;
        font-size: 0;
        background-image: url("../../../assets/image/active/dreamBuild/city-bg.png");
        background-size: 100%;
        background-repeat: no-repeat;

        .citys-txt {
          text-align: center;
          padding: 0.2rem 0;
          margin-bottom: 0.2rem;
          font-size: 0.16rem;
          color: #ffd300;
        }

        a {
          display: inline-block;
          width: 0.7rem;
          height: 0.3rem;
          margin: 0 0.1rem 0.22rem;
          line-height: 0.3rem;
          text-align: center;
          color: #333;
          font-size: 0.13rem;
          background: #319f9b;
          border-radius: 4px;
          position: relative;

          &:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            border: 1px solid #319f9b;
            top: 0.01rem;
            right: 0.01rem;
            border-radius: 0.04rem;
          }

          img {
            width: 0.7rem;
          }
        }
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.4s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .slideDown {
    transform: translate3d(0, 0, 0);
    animation-name: slideDown;
    animation-duration: 0.6s;
    animation-delay: 0s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }

  @keyframes slideDown {
    0% {
      transform-origin: 0 0;
      transform: translateY(-10%);
    }

    100% {
      transform-origin: 0 0;
      transform: translateY(35%);
    }
  }

  .bounceIn {
    transform: translate3d(0, 0, 0);
    animation-name: bounceIn;
    animation-duration: 0.6s;
    animation-delay: 1s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.95);
    }

    100% {
      transform: scale(1.05);
    }
  }

  .bounceOut {
    transform: translate3d(0, 0, 0);
    animation-name: bounceOut;
    animation-duration: 0.8s;
    animation-delay: 0.5s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  @keyframes bounceOut {
    0% {
      transform: scale(0.2);
      top: 0.8rem;
      left: 1.35rem;
    }

    100% {
      transform: scale(1);
      top: 0;
      left: 0.3rem;
    }
  }

  .bounceOut2 {
    transform: translate3d(0, 0, 0);
    animation-name: bounceOut2;
    animation-duration: 0.8s;
    animation-delay: 0.8s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  @keyframes bounceOut2 {
    0% {
      transform: scale(0.2);
      top: 0.8rem;
      left: 1.35rem;
    }

    100% {
      transform: scale(1);
      top: 0;
      left: 1.35rem;
    }
  }

  .bounceOut3 {
    transform: translate3d(0, 0, 0);
    animation-name: bounceOut3;
    animation-duration: 0.8s;
    animation-delay: 1.1s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  @keyframes bounceOut3 {
    0% {
      transform: scale(0.2);
      top: 0.8rem;
      left: 1.35rem;
    }

    100% {
      transform: scale(1);
      top: 0;
      left: 2.4rem;
    }
  }

  .nextSlide {
    transform: translate3d(0, 0, 0);
    animation-name: nextSlide;
    animation-duration: 0.8s;
    animation-delay: 1s;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-fill-mode: backwards;
    animation-timing-function: linear;
  }

  @keyframes nextSlide {
    0% {
      top: 3.8rem;
    }

    100% {
      top: 3.9rem;
    }
  }

  .newBanner /deep/.countdown {
    .num {
      /*color:rgba(61,91,244,1);*/
      color: #e64b23;
      width: 0.13rem;
      height: 0.22rem;
      background: rgba(255, 255, 255, 1);
      border-radius: 8px;
    }
  }
}

#infoWrapOld {
  .advance {
    .newBanner {
      height: 3.26rem;
      background: url("../../../assets/image/active/old/group.png") no-repeat center center;
      background-size: 3.75rem;
      position: relative;

      .enrollText {
        position: absolute;
        top: 0.22rem;
        right: 20%;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-family: STSongti-SC-Black;
        font-size: 0.12rem;
        opacity: 0.9;
      }

      .enrollCount {
        width: 0.68rem;
        height: 0.22rem;
        position: absolute;
        top: 0.15rem;
        text-align: center;
        color: rgba(54, 54, 54, 1);
        font-size: 0.18rem;
        right: 0.1rem;
        padding-left: 1.2%;
        font-weight: 900;
      }

      .banner_swiper {
        position: absolute;
        z-index: 50;
        width: 1.25rem;
        height: 0.25rem;
        left: 0.1rem;
        top: 0.16rem;
        overflow: hidden;

        .gdBox {
          &.anim {
            transition: all 1s;
            margin-top: -0.5rem;
          }
        }

        .gdBox_wrap {
          margin-bottom: 0.09rem;
          width: 100%;
          height: 0.25rem;
          clear: both;
          opacity: 0;
          background-color: rgba(0, 0, 0, 0.3);
          border-radius: 0.25rem;

          .img {
            float: left;
            width: 0.25rem;
            height: 0.25rem;
            border-radius: 50%;

            img {
              border-radius: 50%;
            }
          }
        }

        .gdBox_wrap:first-of-type,
        .gdBox_wrap:nth-of-type(2) {
          opacity: 1;
        }

        .txt_fr {
          float: left;
          margin-left: 0.05rem;
          width: 0.85rem;
          height: 0.25rem;
          color: white;
          font-size: 0.12rem;
          margin-top: 0.02rem;

          span {
            display: inline-block;
            line-height: 0.25rem;
          }

          span:last-child {
            margin-left: 0.05rem;
          }
        }
      }

      .activityText {
        position: absolute;
        left: 0.57rem;
        top: 2.78rem;
        font-size: 0.12rem;
        color: rgba(255, 255, 255, 1);
      }

      .content {
        position: absolute;
        top: 2.76rem;
        left: 1.21rem;
      }

      img {
        width: 100%;
      }

      .enrollBtn {
        position: absolute;
        width: 2.3rem;
        height: 0.65rem;
        left: 0.72rem;
        top: 1.99rem;
        background-image: url("../../../assets/image/active/old/<EMAIL>");
        background-size: 100% 100%;
        color: white;
        font-size: 0.22rem;
        text-align: center;
        line-height: 0.58rem;

        &.active {
          top: 2.29rem;
        }
      }

      .enrolled {
        width: 100%;
        position: absolute;
        color: rgba(252, 66, 27, 1);
        font-size: 0.14rem;
        font-style: normal;
        top: 2.65rem;
        text-align: center;
      }
    }

    >.content {
      background-color: #319f9b;
      background-size: 100%;
      background-repeat: no-repeat;

      .tab {
        height: 0.5rem;
        margin-top: -0.02rem;

        p {
          float: left;
          height: 0.5rem;
          width: 1.875rem;
          background-color: #198381;
          text-align: center;
          padding: 0.1rem 0;

          span {
            display: inline-block;
            width: 100%;
            font-size: 0.18rem;
            color: #fff;
          }

          &.active {
            background-color: #fff;
            padding: 0;
            line-height: 0.46rem;

            span {
              position: relative;
              /*border-bottom: .04rem solid #198381;*/
              width: auto;
              border-right: none;
              color: #177d7b;
              border-radius: 0.02rem;

              &:before {
                content: "";
                height: 0.04rem;
                background-color: #198381;
                width: 0.3rem;
                bottom: -0.05rem;
                transform: translateX(-50%);
                left: 50%;
                position: absolute;
                border-radius: 2px;
              }
            }
          }

          &:last-of-type {
            span {
              border-right: none;
            }
          }
        }
      }

      .lcfy {
        text-align: right;

        p {
          margin-top: 0.18rem;
          font-size: 0.16rem;
          color: #198381;
          padding-right: 0.1rem;
        }
      }

      .bg {
        background-color: #fff;
        padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
        padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

        .content {
          width: 3.75rem;
          margin: 0 auto;

          .bd {
            /*border: 1px solid #4da8a3;*/
            border-top: none;

            p {
              text-align: center;
              font-size: 0.1rem;
              padding: 0.1rem 0 0;
              color: #fff;
            }

            .school {
              /*margin-top:.06rem;*/
              background-color: #fff;
              border-radius: 0.1rem;
              position: relative;
              display: flex;
              display: -webkit-flex;
              flex-wrap: wrap;
              padding: 0rem 0.1rem 0;
              -webkit-flex-wrap: wrap;
              justify-content: flex-start;

              .headBox {
                text-align: center;
                position: relative;
                margin-bottom: 0.2rem;
                background-position: 0.5rem 0;

                /*.titleImg {*/
                /*width: 3.5rem;*/
                /*}*/
                /*.cityNameText {*/
                /*position:absolute;*/
                /*top: 30%;*/
                /*left: 26%;*/
                /*font-size: .18rem;*/
                /*font-weight:900;*/
                /*color:rgba(39,89,164,1);*/
                /*}*/
              }

              .school-border {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #a68463;
                border: 1px solid #755637;
                border-radius: 0.16rem;
                top: -0.05rem;
                right: -0.05rem;
              }

              .item {
                width: 0.84rem;
                height: 0.84rem;
                margin-bottom: 0.15rem;
                position: relative;
                margin-right: 0.04rem;

                i {
                  display: block;
                  margin: 0 auto;
                  width: 0.42rem;
                  height: 0.42rem;
                  margin-top: 0.05rem;
                  object-fit: cover;
                }

                span {
                  display: block;
                  width: 0.6rem;
                  font-size: 0.1rem;
                  margin: 0 auto;
                  text-align: center;
                  color: #333;
                  font-weight: bold;
                }

                &.dsnm:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-image: url("../../../assets/image/active/adultExam/dsnm_new.png");
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }

                &.dsnj:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }
              }

              .hnnydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/bg_hnny.png");
                background-size: 105%;
              }

              .zyydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/gzzyy.png");
                background-size: 100%;
              }
            }
          }

          .bz {
            text-align: left;
            color: #fff;
            font-size: 0.1rem;
            margin-top: -0.1rem;
            margin-bottom: 0.2rem;
            // color: #000;
            // padding: 0.1rem;
          }
        }

        .school-info {
          padding-top: 0.1rem;
          width: 3.5rem;
          margin: 0 auto;

          .info {
            margin-bottom: 0.4rem;
            background-color: #fff;
            margin-left: -0.02rem;
            border-radius: 0.1rem;

            &:before {
              content: "";
              position: absolute;
              width: 0.2rem;
              height: 0.1rem;
              background-image: url("../../../assets/image/active/scholarship/sjx2.png");
              background-repeat: no-repeat;
              background-size: 100%;
              left: 0.3rem;
              top: -0.1rem;
            }

            &.to {
              left: -1rem;

              &:before {
                left: 1.25rem;
              }
            }

            &.tt {
              left: -1.4rem;

              &:before {
                left: 1.65rem;
              }
            }

            &.ttr {
              left: -2.72rem;

              &:before {
                left: 2.97rem;
              }
            }

            &.tf {
              left: -2.6rem;

              &:before {
                left: 2.75rem;
              }
            }

            .table {
              position: relative;
              border-radius: 0.1rem;
              background-color: #fff;
              border-left: 1px solid rgba(162, 192, 221, 1);
              border-right: 1px solid rgba(162, 192, 221, 1);
              border-top: 1px solid rgba(162, 192, 221, 1);
              overflow: hidden;

              .table-t {
                height: 0.52rem;
                line-height: 0.52rem;
                padding: 0 0.01rem;

                background: #e6eff7;
                border-radius: 5px 5px 0px 0px;

                .fl {
                  position: relative;
                  font-weight: 600;
                  padding-left: 0.05rem;
                }

                span {
                  font-size: 0.11rem;
                }

                .fr {
                  line-height: 0.22rem;
                  text-align: left;
                  margin-top: 0.07rem;
                }
              }

              .table-b {
                table {
                  width: 100%;
                  border-top: 1px solid rgba(162, 192, 221, 1);

                  th,
                  td {
                    font-size: 0.12rem;
                    font-weight: 400;
                    vertical-align: middle;
                    border-bottom: 1px solid rgba(162, 192, 221, 1);
                    border-right: 1px solid rgba(162, 192, 221, 1);
                    background-color: #fff;
                    /*border-right: 1px solid #72dfff;*/
                    padding: 0.04rem;
                    text-align: center;

                    &:last-of-type {
                      border-right: none;
                    }

                    &.text-l {
                      text-align: left;
                    }

                    span {
                      display: block;
                      line-height: 0.28rem;
                    }
                  }
                }
              }

              &:after {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #ffd100;
                right: -0.07rem;
                top: -0.1rem;
                z-index: -1;
              }
            }
          }
        }
      }

      .bottom {
        padding: 0 0.1rem;
        padding-bottom: 0.3rem;

        .lxwm {
          width: 100%;
          text-align: center;
          padding-bottom: 0.25rem;
          font-size: 0.18rem;
          color: #fff;
          background-image: url("../../../assets/image/active/dreamBuild/line.png");
          background-repeat: no-repeat;
          background-position-y: 0.05rem;
          background-size: 100%;
        }

        .pic {
          i {
            display: inline-block;
            vertical-align: bottom;
            margin-right: 0.05rem;
            width: 0.32rem;
            height: 0.32rem;
            background-image: url("../../../assets/image/active/dreamBuild/phone.png");
            background-size: 100%;

            &.www {
              background-image: url("../../../assets/image/active/dreamBuild/www.png");
            }
          }

          span {
            margin-right: 0.35rem;
            color: #fff;
            font-size: 0.18rem;

            &:last-of-type {
              margin-right: 0;
            }
          }
        }

        .ewm {
          text-align: center;
          padding: 0.16rem 0;
          margin: 0.3rem 0;
          border: 1px dashed #198381;

          img {
            width: 1.35rem;
          }

          p {
            font-size: 18px;
            margin-top: 0.1rem;
            color: #fff;
          }
        }
      }
    }
  }

  .fixBottom {
    position: fixed;
    bottom: 0;
    height: 0.6rem;
    z-index: 9999;

    .leftBox {
      display: inline-block;
      width: 1.96rem;
      height: 0.6rem;
      float: left;
      background: #fff;
      position: relative;
      border-top: 1px solid rgba(251, 103, 5, 1);

      img {
        width: 0.39rem;
        margin-left: 0.1rem;
        margin-top: 0.12rem;
      }

      span {
        display: inline-block;
      }

      .textOne {
        margin-top: 0.12rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
      }

      .textTwo {
        position: absolute;
        left: 0.52rem;
        top: 0.3rem;
        font-size: 0.14rem;
        color: rgba(54, 54, 54, 0.6);
      }
    }

    .rightBox {
      display: inline-block;
      width: 1.79rem;
      height: 0.6rem;
      /*background-color:rgba(39, 89, 164, 1);*/
      background-image: linear-gradient(to bottom,
          rgba(255, 177, 56, 1),
          rgba(251, 106, 13, 1));
      float: left;
      position: relative;

      .line {
        position: absolute;
        display: inline-block;
        width: 1px;
        height: 0.25rem;
        background: rgba(255, 255, 255, 0.4);
        top: 0.16rem;
        left: 0.88rem;
      }

      .phoneIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #fff;
          font-size: 0.14rem;
        }
      }

      .signUpIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #fff;
          font-size: 0.14rem;
        }
      }
    }
  }

  .headBox {
    clear: both;
    width: 1.5rem;
    height: 0.47rem;
    text-align: center;
    margin: 0.16rem auto 0;
    color: rgba(54, 54, 54, 1);
    font-size: 0.15rem;
    padding: 0.3rem 0;
    position: relative;
    background-image: url("../../../assets/image/active/old/title_build.png");
    background-size: 0.5rem 0.47rem;
    background-repeat: no-repeat;

    &:before {
      content: "";
      position: absolute;
      left: -0.3rem;
      top: 0.44rem;
      margin-top: -0.02rem;
      width: 0.2rem;
      height: 0.02rem;
      background-color: rgba(251, 103, 5, 1);
    }

    &:after {
      content: "";
      position: absolute;
      right: -0.3rem;
      top: 0.44rem;
      margin-top: -0.02rem;
      width: 0.2rem;
      height: 0.02rem;
      background-color: rgba(251, 103, 5, 1);
    }
  }
}

#infoWrapNew {
  .advance {
    .newBanner {
      height: 3.58rem;
      background: url("../../../assets/image/active/enrollAggregate/banner2.png") no-repeat center center;
      background-size: 3.75rem;
      position: relative;

      .enrollText {
        position: absolute;
        top: 0.11rem;
        right: 0.65rem;
        font-weight: bold;
        color: rgba(143, 69, 0, 1);
        font-family: STSongti-SC-Black;
        font-size: 0.13rem;
        opacity: 0.9;
      }

      .enrollCount {
        width: 0.55rem;
        height: 0.22rem;
        position: absolute;
        top: 0.11rem;
        text-align: center;
        color: rgba(143, 69, 0, 1);
        font-size: 0.15rem;
        right: 0.1rem;
        padding-left: 1.2%;
        font-weight: 900;
      }

      .banner_swiper {
        position: absolute;
        z-index: 50;
        width: 1.25rem;
        height: 0.25rem;
        right: 0.1rem;
        top: 0.32rem;
        overflow: hidden;

        .gdBox {
          &.anim {
            transition: all 1s;
            margin-top: -0.5rem;
          }
        }

        .gdBox_wrap {
          margin-bottom: 0.09rem;
          width: 100%;
          height: 0.25rem;
          clear: both;
          opacity: 0;
          background-color: rgba(143, 69, 0, 0.4);
          border-radius: 0.25rem;

          .img {
            float: left;
            width: 0.25rem;
            height: 0.25rem;
            border-radius: 50%;

            img {
              border-radius: 50%;
            }
          }
        }

        .gdBox_wrap:first-of-type,
        .gdBox_wrap:nth-of-type(2) {
          opacity: 1;
        }

        .txt_fr {
          float: left;
          margin-left: 0.05rem;
          width: 0.85rem;
          height: 0.25rem;
          color: white;
          font-size: 0.11rem;
          line-height: 0.25rem;

          span {
            float: left;
            line-height: 0.25rem;
          }

          span:last-child {
            margin-left: 0.05rem;
          }
        }
      }

      .activityText {
        position: absolute;
        left: 0.67rem;
        top: 3.85rem;
        font-size: 0.12rem;
        color: rgba(255, 255, 255, 1);
      }

      .content {
        position: absolute;
        top: 3.85rem;
        left: 1.21rem;
      }

      img {
        width: 100%;
      }

      .enrollBtn {
        position: absolute;
        width: 1.65rem;
        height: 0.41rem;
        left: 1.05rem;
        top: 2.74rem;
        background-image: url("../../../assets/image/active/enrollAggregate/btn_enroll.png");
        background-size: 100% 100%;
        color: white;
        font-size: 0.22rem;
        text-align: center;
        line-height: 0.58rem;
      }

      .enrolled {
        width: 100%;
        position: absolute;
        color: rgba(252, 66, 27, 1);
        font-size: 0.14rem;
        font-style: normal;
        top: 2.65rem;
        text-align: center;
      }
    }

    >.content {
      background-color: #319f9b;
      background-size: 100%;
      background-repeat: no-repeat;
      margin-bottom: 0.6rem;

      .tab {
        height: 0.5rem;
        margin-top: -0.02rem;

        p {
          float: left;
          height: 0.5rem;
          width: 1.875rem;
          background-color: #198381;
          text-align: center;
          padding: 0.1rem 0;

          span {
            display: inline-block;
            width: 100%;
            font-size: 0.18rem;
            color: #fff;
          }

          &.active {
            background-color: #fff;
            padding: 0;
            line-height: 0.46rem;

            span {
              position: relative;
              /*border-bottom: .04rem solid #198381;*/
              width: auto;
              border-right: none;
              color: #177d7b;
              border-radius: 0.02rem;

              &:before {
                content: "";
                height: 0.04rem;
                background-color: #198381;
                width: 0.3rem;
                bottom: -0.05rem;
                transform: translateX(-50%);
                left: 50%;
                position: absolute;
                border-radius: 2px;
              }
            }
          }

          &:last-of-type {
            span {
              border-right: none;
            }
          }
        }
      }

      .lcfy {
        text-align: right;

        p {
          margin-top: 0.18rem;
          font-size: 0.16rem;
          color: #198381;
          padding-right: 0.1rem;
        }
      }

      .bg {
        background-color: #fff;
        padding-bottom: constant(safe-area-inset-bottom); /*兼容 IOS<11.2*/
        padding-bottom: env(safe-area-inset-bottom); /*兼容 IOS>11.2*/

        .content {
          width: 3.75rem;
          margin: 0 auto;

          .bd {
            /*border: 1px solid #4da8a3;*/
            border-top: none;

            p {
              text-align: center;
              font-size: 0.1rem;
              padding: 0.1rem 0 0;
              color: #fff;
            }

            .school {
              /*margin-top:.06rem;*/
              background-color: #fff;
              border-radius: 0.1rem;
              position: relative;
              display: flex;
              display: -webkit-flex;
              flex-wrap: wrap;
              padding: 0rem 0.1rem 0;
              -webkit-flex-wrap: wrap;
              justify-content: flex-start;

              .headBox {
                /*width: 3.2rem;*/
                width: 100%;
                text-align: center;
                position: relative;
                margin: 0.29rem auto 0.13rem;
                font-size: 0.15rem;
                background-position: 0rem 0.06rem;
                color: rgba(54, 54, 54, 1);
                background-image: url("../../../assets/image/active/dreamBuild2021/title_cityDetail.png");
                background-size: 100%;
                background-repeat: no-repeat;
                /*.titleImg {*/
                /*width: 3.5rem;*/
                /*}*/
                /*.cityNameText {*/
                /*position:absolute;*/
                /*top: 30%;*/
                /*left: 26%;*/
                /*font-size: .18rem;*/
                /*font-weight:900;*/
                /*color:rgba(39,89,164,1);*/
                /*}*/
              }

              .school-border {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #a68463;
                border: 1px solid #755637;
                border-radius: 0.16rem;
                top: -0.05rem;
                right: -0.05rem;
              }

              .item {
                width: 0.84rem;
                height: auto;
                overflow: hidden;
                margin-bottom: 0.1rem;
                position: relative;
                margin-right: 0.04rem;

                i {
                  display: block;
                  margin: 0 auto;
                  width: 0.42rem;
                  height: 0.42rem;
                  margin-top: 0.05rem;
                  object-fit: cover;
                }

                span {
                  display: block;
                  width: 0.6rem;
                  font-size: 0.1rem;
                  margin: 0 auto;
                  text-align: center;
                  color: #333;
                  font-weight: bold;
                }

                &.dsnm:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-image: url("../../../assets/image/active/adultExam/dsnm_new.png");
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }

                &.dsnj:after {
                  content: "";
                  position: absolute;
                  width: 0.405rem;
                  height: 0.28rem;
                  background-repeat: no-repeat;
                  background-size: 100%;
                  right: -0.1rem;
                  top: -0.1rem;
                }
              }

              .hnnydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/bg_hnny.png");
                background-size: 105%;
              }

              .zyydxItem {
                // width: 1.73rem;
                background: url("../../../assets/image/active/newDreamBuild/dreamFive/gzzyy.png");
                background-size: 100%;
              }
            }
          }

          .bz {
            text-align: left;
            color: #fff;
            font-size: 0.1rem;
            margin-top: -0.1rem;
            margin-bottom: 0.2rem;
            // color: #000;
            // padding: 0.1rem;
          }
        }

        .school-info {
          padding-top: 0.1rem;
          width: 3.5rem;
          margin: 0 auto;

          .info {
            margin-bottom: 0.1rem;
            background-color: #fff;
            margin-left: -0.02rem;
            border-radius: 0.1rem;

            &:before {
              content: "";
              position: absolute;
              width: 0.2rem;
              height: 0.1rem;
              background-image: url("../../../assets/image/active/scholarship/sjx2.png");
              background-repeat: no-repeat;
              background-size: 100%;
              left: 0.3rem;
              top: -0.1rem;
            }

            &.to {
              left: -1rem;

              &:before {
                left: 1.25rem;
              }
            }

            &.tt {
              left: -1.4rem;

              &:before {
                left: 1.65rem;
              }
            }

            &.ttr {
              left: -2.72rem;

              &:before {
                left: 2.97rem;
              }
            }

            &.tf {
              left: -2.6rem;

              &:before {
                left: 2.75rem;
              }
            }

            .table {
              position: relative;
              border-radius: 0.1rem;
              background-color: #fff;
              border-left: 1px solid rgb(255, 216, 178);
              border-right: 1px solid rgb(255, 216, 178);
              border-top: 1px solid rgb(255, 216, 178);
              overflow: hidden;

              .table-t {
                height: 0.52rem;
                line-height: 0.52rem;
                padding: 0 0.01rem;

                /*background:#e6eff7;*/
                /*background-color: rgb(255,216,178);*/
                background-color: #fffbf6;
                border-radius: 5px 5px 0px 0px;

                .fl {
                  position: relative;
                  font-weight: 800;
                  padding-left: 0.05rem;
                }

                span {
                  font-size: 0.11rem;
                }

                .fr {
                  line-height: 0.22rem;
                  text-align: left;
                  margin-top: 0.07rem;
                }
              }

              .table-b {
                table {
                  width: 100%;
                  border-top: 1px solid rgb(255, 216, 178);

                  th,
                  td {
                    font-size: 0.12rem;
                    font-weight: 400;
                    vertical-align: middle;
                    border-bottom: 1px solid rgb(255, 216, 178);
                    border-right: 1px solid rgb(255, 216, 178);
                    background-color: #fff;
                    /*border-right: 1px solid #72dfff;*/
                    padding: 0.04rem;
                    text-align: center;

                    &:last-of-type {
                      border-right: none;
                    }

                    &.text-l {
                      text-align: left;
                    }

                    span {
                      display: block;
                      line-height: 0.28rem;
                    }
                  }
                }
              }

              &:after {
                content: "";
                position: absolute;
                width: 100%;
                height: 100%;
                background-color: #ffd100;
                right: -0.07rem;
                top: -0.1rem;
                z-index: -1;
              }
            }
          }
        }
      }

      .bottom {
        padding: 0 0.1rem;
        padding-bottom: 0.3rem;

        .lxwm {
          width: 100%;
          text-align: center;
          padding-bottom: 0.25rem;
          font-size: 0.18rem;
          color: #fff;
          background-image: url("../../../assets/image/active/dreamBuild/line.png");
          background-repeat: no-repeat;
          background-position-y: 0.05rem;
          background-size: 100%;
        }

        .pic {
          i {
            display: inline-block;
            vertical-align: bottom;
            margin-right: 0.05rem;
            width: 0.32rem;
            height: 0.32rem;
            background-image: url("../../../assets/image/active/dreamBuild/phone.png");
            background-size: 100%;

            &.www {
              background-image: url("../../../assets/image/active/dreamBuild/www.png");
            }
          }

          span {
            margin-right: 0.35rem;
            color: #fff;
            font-size: 0.18rem;

            &:last-of-type {
              margin-right: 0;
            }
          }
        }

        .ewm {
          text-align: center;
          padding: 0.16rem 0;
          margin: 0.3rem 0;
          border: 1px dashed #198381;

          img {
            width: 1.35rem;
          }

          p {
            font-size: 18px;
            margin-top: 0.1rem;
            color: #fff;
          }
        }
      }
    }
  }

  .fixBottom {
    position: fixed;
    bottom: 0;
    height: 0.6rem;
    z-index: 9999;

    .leftBox {
      display: inline-block;
      width: 1.96rem;
      height: 0.6rem;
      float: left;
      background: #fff;
      position: relative;
      border-top: 1px solid rgba(230, 75, 35, 1);

      img {
        width: 0.39rem;
        margin-left: 0.1rem;
        margin-top: 0.12rem;
      }

      span {
        display: inline-block;
      }

      .textOne {
        margin-top: 0.12rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
      }

      .textTwo {
        position: absolute;
        left: 0.52rem;
        top: 0.3rem;
        font-size: 0.14rem;
        color: rgba(54, 54, 54, 0.6);
      }
    }

    .rightBox {
      display: inline-block;
      width: 1.79rem;
      height: 0.6rem;
      background-color: rgba(230, 75, 35, 1);
      /*background-image: linear-gradient(to bottom,rgba(255, 177, 56, 1),rgba(251, 106, 13, 1));*/
      float: left;
      position: relative;

      .line {
        position: absolute;
        display: inline-block;
        width: 1px;
        height: 0.25rem;
        background: rgba(255, 255, 255, 0.4);
        top: 0.16rem;
        left: 0.88rem;
      }

      .phoneIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #fff;
          font-size: 0.14rem;
        }
      }

      .signUpIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;

        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }

        p {
          color: #fff;
          font-size: 0.14rem;
        }
      }
    }
  }
}

#infoWrapOld /deep/ .d-roll-wrapper {
  margin: 0 auto;
}

#infoWrapNew /deep/ .d-roll-list {
  margin-top: -0.04rem;
}

#infoWrapNew /deep/ .d-roll-item {
  height: 0.25rem !important;
  flex-grow: 0.03;
}

#infoWrapNew /deep/ .van-collapse-item__content {
  background-color: #e5eff7;
}

#infoWrapNew /deep/ .swiper-container {
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
}

#infoWrapNew /deep/ .d-roll-wrapper {
  margin: 0 auto;
}

#infoWrapNew /deep/ .van-cell {
  line-height: 0.4rem !important;
}
</style>
<style lang="scss" scoped>
@mixin bgPosition($spriteWidth,
  $spriteHeight,
  $iconWidth,
  $iconHeight,
  $iconX,
  $iconY) {
  background-position: (($iconX / ($spriteWidth - $iconWidth)) * 100% ($iconY / ($spriteHeight - $iconHeight)) * 100%);
}

.hn {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1547);
}

.gzzyy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 206);
}

.gdjr {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0 no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1341);
}

.dglg {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 928);
}

.gzcj {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0 no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1238);
}

.gdzyjs {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 413);
}

.gzgb {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1134);
}

.lnsf {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 825);
}

.zkny {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 516);
}

.gdkm {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1444);
}

.gdst {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 619);
}

.dgzy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1031);
}

.zqxy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 722);
}

.jyxy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 1650);
}

.qyzy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0 no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 0);
}

.nhcr {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 309);
}

.swzy {
  background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png") 0 0rem no-repeat;
  background-size: 0.5rem 8.5rem;
  @include bgPosition(100, 1700, 50, 50, 0, 103);
}

.gdjrNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 1214);
}

.hnnyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/hnnyLogo.png");
  background-size: 0.42rem;
}

.gzrjNew {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gzrj.png');
  background-size: 0.42rem;
}

.gdlnNew {
  background: url("../../../assets/image/active/old/<EMAIL>");
  background-size: 0.42rem;
}

.gzzyyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/zyylogo.png");
  background-size: 0.42rem;
}

.dglgNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 84);
}

.gzdxNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/<EMAIL>") no-repeat center;
  background-size: 0.42rem;
}

.gzcjNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 170);
}

.gdzyjsNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 256);
}

.gzgbNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 344);
}
.zhkj-icon {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/zhkj.png") 0 0 no-repeat;
  background-size: 100% 100%;
}
.gdxhxy-icon {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/gdxhxy.png") 0 0 no-repeat;
  background-size: 100% 100%;
}
.lnsfNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 430);
}

.zknyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 518);
}

.gdkmNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 606);
}

.gdstNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 692);
}

.dgzyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 780);
}

.zqxyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 866);
}

.jyxyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 954);
}

.qyzyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  @include bgPosition(84, 1256, 42, 42, 0, 1040);
}

.sszyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/sszy.png") 0 0 no-repeat;
  background-size: 0.42rem 0.3rem;
  @include bgPosition(84, 1256, 42, 42, 0, 1040);
}

.nhcrNew {
  background: url("../../../assets/image/schoolLogo/nhcr.png") 0 0 no-repeat;
  background-size: 100%;
  //@include bgPosition(84, 1256, 42, 42, 0, -2);
  // @include bgPosition(84, 1256, 42, 42, 0, 1126);
}

.swzyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png") 0 0 no-repeat;
  background-size: 0.42rem 6.28rem;
  // @include bgPosition(84, 1256, 42, 42, 0, 1214);
  @include bgPosition(84, 1256, 42, 42, 0, 1126);
}

.stdxNew {
  background: url("../../../assets/image/active/newDreamBuild/stdx.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gzhxNew {
  background: url("../../../assets/image/active/dreamBuild2021/gzhx.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.jndxNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/jndx.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.mmzyNew {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/mmzyjs.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.swzy76 {
  background: url("../../../assets/image/active/shanweiInstituteTechnology/logo.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gdjrItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gdjr.png");
  background-size: 100%;
}

.dglgItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_dglg.png");
  background-size: 100%;
}

.gzcjItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gzcj.png");
  background-size: 100%;
}

.gzgbItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gzgb.png");
  background-size: 100%;
}

.lnsfItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_lnsf.png");
  background-size: 100%;
}

.zknyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_zkny.png");
  background-size: 100%;
}

.gdkmItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gdkm.png");
  background-size: 100%;
}

.gdstItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gdst.png");
  background-size: 100%;
}

.dgzyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_dgzy.png");
  background-size: 100%;
}

.gdzyjsItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gdzy.png");
  background-size: 100%;
}

.zqxyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_zqxy.png");
  background-size: 100%;
}

.jyxyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_jy.png");
  background-size: 100%;
}

.qyzyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_qyzy.png");
  background-size: 100%;
}

.nhcrItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_nhcr.png");
  background-size: 100%;
}

.swzyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_swzy.png");
  background-size: 100%;
}

.gzsxy {
  background: url("../../../assets/image/active/newDreamBuild/gzsxy.png");
  background-size: 100%;
}

.gzsxyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_gzsxy.png");
  background-size: 100%;
}
.gzhl {
  background: url('../../../assets/image/schoolLogo/gzhl.png');
  background-size: 100%;
}

.dgcs {
  background: url('../../../assets/image/schoolLogo/dgcs.png');
  background-size: 100%;
}
.stdxItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_stdx.png");
  background-size: 100%;
}

.mmzyItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_mmzyjs.png");
  background-size: 100%;
}

.jndxItem {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/bg_jndx.png");
  background-size: 100%;
}

.gdjtzy-icon {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/jtlogo.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gdjdzy-icon {
  background: url("../../../assets/image/active/newDreamBuild/dreamFive/gdjdzyLogo.png") no-repeat;
  background-size: 0.42rem 0.42rem;
}
.gdiei-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gdiei.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gdposat-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gdposat.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}
.gzkjzy-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gzkjzy.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gzcoaat-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gzcoaat.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gzhsvc-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gzhsvc.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}

.gzrp-icon {
  background: url('../../../assets/image/active/newDreamBuild/dreamFive/gzrp.png') no-repeat;
  background-size: 0.42rem 0.42rem;
}
</style>
