<template>
  <div class="yz-new-main" :class='{pb: isIphoneX}'>
    <inviteTop @getInviteId='getInviteId' />
    <!-- 分享组件 -->
    <share
      title="在线报读，名校录取，学信网可查，助你提升专本、研究生学历！"
      desc="远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！"
      :link="shareLink"
      :isActivity="true"
      regOrigin='44'
      ref="share"
    />
    <van-notice-bar
    @click="toDes"
  left-icon="volume-o"
  text="远智教育防诈提醒：近日有不法分子假冒我司工作人员实施诈骗，我司郑重声明：从未委托任何个人和机构进行“收退费”。如有疑问，请拨打4008336013"
/>
    <!-- 轮播 -->
    <div class="swiper-box">
      <y-swiper :inviteId='inviteId' :regChannel='regChannel' :regOrigin='regOrigin' />
    </div>
    <!-- 4个菜单 -->
    <four-menu :inviteId='inviteId' :regChannel='regChannel' :regOrigin='regOrigin' />
    <!-- 活动入口 -->
    <activity-entrance :inviteId='inviteId' :regOrigin='regOrigin' />
    <!--背书 -->
    <div class="white-box">
      <div class="video-content">
        <img class="img-title" src="../../../assets/image/active/2021NewMain/t1.png" alt="">
        <video v-show='isFirstPlay' src="https://static.yzou.cn/website/video/057A40359D814DC9B8B6F0B937E4CEED.mp4" loop controls="controls" ref="video" @play="videoChange" @pause="videoChange"></video>
        <div v-if='!isFirstPlay' class="video-bg" @click='playVideo'>
          <img class="img-play" src="../../../assets/image/active/2021NewMain/ic_play.png" alt="">
        </div>
      </div>
      <!-- 荣耀授权 -->
      <div class="title-swiper">
        <img class="img-title" src="../../../assets/image/active/2021NewMain/t2.png" alt="">
        <certificate-swiper />
      </div>
      <!-- 名校合作 -->
      <div class="title-swiper">
        <img class="img-title" src="../../../assets/image/active/2021NewMain/t3.png" alt="">
        <school-swiper />
      </div>
      <!-- 学员风采 -->
      <student-swiper />
    </div>
    <!-- copyright -->
    <div class="copy-right">
      承办单位: 广州远智教育科技有限公司<br />邮编: 516600 粤ICP05084359
    </div>
    <!-- 底部通栏 -->
    <y-footer :inviteId='inviteId'></y-footer>
  </div>
</template>

<script>
import { isIphoneX } from '@/common';
import share from "@/components/share";
import inviteTop from "../enrollAggregate/components/invite-top";
import YFooter from './components/footer';
import FourMenu from './components/four-menu';
import ActivityEntrance from './components/activity-entrance';
import YSwiper from './components/swiper';
import CertificateSwiper from './components/certificate-swiper';
import StudentSwiper from './components/student-swiper';
import SchoolSwiper from './components/school-swiper';

export default {
  components: {
    share,
    inviteTop,
    YFooter,
    FourMenu,
    ActivityEntrance,
    YSwiper,
    CertificateSwiper,
    StudentSwiper,
    SchoolSwiper,
  },
  data() {
    return {
      shareLink: window.location.origin + "/active/newMain",
      isIphoneX: isIphoneX(),
      inviteId: '',
      isFirstPlay: false,
    };
  },
  created() {
    this.$yzStatistic('marketing.base.browse', '2', '首页');
  },
  computed: {

    regChannel() {
      return this.$route.query.regChannel || '';
    },
    regOrigin() {
      return this.$route.query.regOrigin || '';
    },
  },
  methods: {
            toDes() {
      window.location.href='https://mp.weixin.qq.com/s/wspHS8DbGpnpNfTvu4D3Hw'
    },
    getInviteId(id) {
      this.inviteId = id;
    },
    videoChange() {
      this.$yzStatistic('marketing.base.click', '10', '首页-视频');
    },
    playVideo() {
      this.$refs.video.play();
      this.isFirstPlay = true;
    },
  },
};
</script>

<style lang="less">
  @import './index.less';
</style>
