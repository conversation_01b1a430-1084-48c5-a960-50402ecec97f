<template>
  <mt-popup v-model="isShow" position="bottom">
    <div class="commonPop-box">
      <div class="commonPop-content">
        <mt-picker :slots="slots" @change="onValuesChange" ref="picker"></mt-picker>
      </div>
      <div class="commonPop-title">
        <span @click="close">取消</span>
        <span @click="confirm">确定</span>
      </div>
    </div>
  </mt-popup>
</template>

<script>
  import {
    provinceName,
    cityName,
    districtName,
    provinceCode,
    cityCode,
    districtCode,
    provinceList,
    cityList,
    districtList
  } from '../common';
  import picker from '@/components/picker';

  export default {
    props: {
      types: {
        type: Number,
        default: 3
      },
      defaults: {
        type: Object,
        default: () => {
          return {
            // provinceCode: '440000',
            provinceCode: '110000',
            // cityCode: '440100',
            cityCode: '110100',
            // districtCode: '440101',
            districtCode: '110101',
          }
        }
      },
      // 省列表参数
      provinceList:{
        type: Array,
        default: () => {
          return provinceList();
        }
      }
    },
    data() {
      return {
        isShow: false,
        slots: [
          {flex: 1, values: this.provinceList},
          {divider: true, content: '-'},
          {flex: 1, values: []}
        ],
        provinceName: '',
        provinceCode: '',
        cityName: '',
        cityCode: '',
        districtName: '',
        districtCode: ''
      }
    },
    created() {
      this.init();
    },
    methods: {
      init: function () {
        this.setCitySlotValue(this.defaults.provinceCode);
        if (this.types > 2) {
          this.slots.push({divider: true, content: '-'});
          this.slots.push({flex: 1, values: []});
          this.setDistrictValue(this.defaults.provinceCode, this.defaults.cityCode);
        }
        this.$nextTick(() => {
          let vals = [provinceName(this.defaults.provinceCode), cityName(this.defaults.cityCode, this.defaults.provinceCode)];
          if (this.types > 2) {
            vals.push(districtName(this.defaults.districtCode, this.defaults.provinceCode, this.defaults.cityCode));
          }
          this.$refs.picker.setValues(vals);
        });
      },
      open: function () {
        this.isShow = true;
      },
      close(){
        this.isShow = false;
      },
      // 设置市的备选值数组
      setCitySlotValue: function () {
        if (!this.provinceCode) return;

        let citys = cityList(this.provinceCode);
        citys = citys.map((item) => {
          return item.cityName;
        });
        this.$refs.picker.setSlotValues(1, citys);
      },
      // 设置区的备选值数组
      setDistrictValue: function () {
        if (this.types < 3 || !this.provinceCode || !this.cityCode) return;

        let districts = districtList(this.provinceCode, this.cityCode);
        districts = districts.map((item) => {
          return item.districtName;
        });
        this.$refs.picker.setSlotValues(2, districts);
      },
      onValuesChange: function (picker, values) {
        if (values[0] && this.provinceName !== values[0]) {
          this.provinceName = values[0];
          this.provinceCode = provinceCode(this.provinceName);
        }
        if (values[1] && this.cityName !== values[1]) {
          this.cityName = values[1];
          this.cityCode = cityCode(this.cityName, this.provinceCode);
        }
        if (values[2]) {
          this.districtName = values[2];
          this.districtCode = districtCode(this.districtName, this.provinceCode, this.cityCode);
        }
      },
      confirm() {
        const val = {
          provinceCode: this.provinceCode,
          provinceName: this.provinceName,
          cityCode: this.cityCode,
          cityName: this.cityName,
          districtCode: this.districtCode,
          districtName: this.districtName
        };
        this.$emit('input', val);
        this.isShow = false;
      },
      setValues: function (provinceCode, cityCode, districtCode) {
        this.$refs.picker.setValues([provinceName(provinceCode), cityName(cityCode, provinceCode), districtName(districtCode, provinceCode, cityCode)]);
        this.$nextTick(() => {
          this.confirm();
        });
      }
    },
    watch: {
      provinceCode: function () {
        this.setCitySlotValue();
      },
      cityCode: function () {
        this.setDistrictValue();
      }
    },
    components: {picker}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/commonPopBox";
</style>
<style lang="less">
  @import "../assets/less/variable";

  .mint-popup-bottom{ width:100%; }
  .picker-items{
    .picker-slot{ font-size:.16rem; }
  }
  .picker-toolbar{ height:.5rem; line-height:.5rem; text-align:center; font-size:.16rem; }
</style>
