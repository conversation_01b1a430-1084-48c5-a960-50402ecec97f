<template>
  <!-- 客服联系-手动下载APP页面 -->
  <div class="landingPage-wrapper">
    <wx-open-launch-app id="launch-btn" @error="callAppError(inforObj.point)" @launch="callAppLaunch" :appid="OpenAppId" :extinfo="extinfo">
      <script type="text/wxtag-template">
        <style>
    .down{
        width: 1000px;
        height: 1000px;
      z-index: 999;
    }
  </style>
  <div class='down' id="down"></div>
      </script>
    </wx-open-launch-app>
  </div>
</template>

<script>
import openApp from '@/mixins/openApp'
import { isIOS } from '@/common'
import { getUrlParam } from '@/utils'

export default {
  mixins: [openApp],
  data() {
    return {
      inforObj: {},
      extinfo: 'yuanzhiapp://yzwill.cn'
    }
  },
  beforeRouteEnter(to, from, next) {
    // 修复iOS版微信HTML5 History兼容性问题
    if (isIOS() && to.path !== location.pathname) {
      location.assign(to.fullPath)
    } else {
      next()
    }
  },
  created() {
    this.inforObj = getUrlParam()
    if (this.inforObj) {
      this.extinfo = `yuanzhiapp://yzwill.cn${
        this.inforObj.pageName || ''
      }?params=${JSON.stringify(this.inforObj)}`
    }
    console.log('手动下载APP页面-h5-解码：', this.extinfo, this.inforObj)
    this.callAppLaunch('小程序活动下载页')
  },
  mounted() {
    this.wxCallAppInit()
  },
  methods: {
    // 是否执行埋点
    callAppLaunch(text) {
      console.log('手动下载APP页面-h5-callAppLaunch')
      text = text || '小程序活动下载页-点击前往APP'
      switch (Number(this.inforObj?.point)) {
        case 1: // 活动
          this.$yzStatistic('applent.upward.act.event', '2', text)
          break
        case 2: // 帖子
          break
        default:
          break
      }
    }
  }
}
</script>

<style lang="less" scoped>
.landingPage-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background: url('https://yzpres.oss-cn-guangzhou.aliyuncs.com/toApp-bg.png') no-repeat center;
  background-size: 100%;
}
</style>

