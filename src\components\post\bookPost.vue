<template>
  <div class="commentBox">
    <div class="comment" v-if="postInfo.showText2">
      <div v-html="postInfo.scText2"></div>...<span @click="toPostApp?toDown():postInfo.showText2 = false">全文</span>
    </div>
    <div class="comment" v-else v-html="postInfo.scText"></div>
    <img-box 
      v-if="postInfo.scPicUrl"
      :scPicUrl="postInfo.scPicUrl" 
    />
    <canvas-video 
      :picUrlsImgWHObj="postInfo.picUrlsImgWHObj" :scVideoUrl="postInfo.scVideoUrl"
      :videoSign="postInfo.videoSign"
      :videoDesc="postInfo.videoDesc"
      :videoCover="postInfo.videoCover"
      :vid="postInfo.id"
      :mtop="mtop"
      :scrollTop="scrollTop"
      ref='canvasVideo'
    />
    <div class="comment-bg book" v-if="!bookRead">
      <img :src="postInfo.extContent.bookUrl | imgBaseURL" alt class="comment-bg-book" />
      <div class="comment-bg-word pl80 pt28">
        <div class="comment-bg-word-title">阅读·{{postInfo.extContent.bookName}}</div>
        <div class="comment-bg-word-subtitle"
             v-if="postInfo.extContent.readPersonNum&&postInfo.extContent.readPersonNum!=='0'">
          累计发布读书笔记{{postInfo.extContent.totalReadNum}}篇，超越了{{postInfo.extContent.transcendNum}}%的上进青年</div>
      </div>
      <div class="comment-bg-book-total">
        <img src="../../assets/image/app/circleAd/ic_book.png" alt="" srcset="">
        <span>
          {{postInfo.extContent.readPersonNum}}名书友在看这本书
        </span>
      </div>
    </div>
    <div v-else class="comment-bg book">
      <img :src="postInfo.extContent.bookUrl | imgBaseURL" alt class="comment-bg-book" />
      <div class="comment-bg-word pl80">
        <div class="comment-bg-word-title">阅读·{{postInfo.extContent.bookName}}</div>
        <div class="comment-bg-word-subtitle mt5"
             v-if="postInfo.extContent.readPersonNum&&postInfo.extContent.readPersonNum!=='0'">
          累计发布读书笔记{{postInfo.extContent.totalReadNum}}篇，超越了{{postInfo.extContent.transcendNum}}%的上进青年</div>
      </div>
      <div class="comment-bg-book-total">
        <img src="../../assets/image/app/circleAd/ic_book.png" alt="" srcset="">
        <span>
          {{postInfo.extContent.readPersonNum}}名书友在看这本书
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import imgBox from '@/components/post/imgBox';
import { imgBaseURL } from "@/config";
import { downloadApp } from '@/common'
import CanvasVideo from '@/components/post/canvas-video'

export default {
  components: {
    imgBox,
    CanvasVideo
  },
  data () {
    return {
      autoLimit: "?x-oss-process=image/auto-orient,1",
      imgbaseurl: imgBaseURL
    };
  },
  props: {
    postInfo: {
      type: Object,
      default: () => ({})
    },
    toPostApp: {
      type: Boolean,
      default: false
    },
    bookRead: {
      type: Boolean,
      default: false
    },
    scrollTop: {
      type: Number,
      default: 0
    },
    mtop: {
      type: Number,
      default: 0
    },
  },
  computed: {},
  watch: {},
  methods: {
    toDown () {
      if (!!this.storage.getItem("authToken")) {
        downloadApp()
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }
    },

  },
  created () { },
  mounted () { },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.commentBox {
  padding: 0.16rem;
  background: #fff;
  /deep/ .emotionimg {
    width: 0.24rem;
    height: 0.24rem;
    vertical-align: bottom;
  }
  .comment {
    font-size: 0.15rem;
    color: rgba(23, 6, 6, 1);
    overflow: hidden;
    text-overflow: ellipsis;
    span {
      color: #f06e6c;
    }

    &-bg {
      &.run {
        background-image: url('../../assets/image/app/circleAd/run-bg.png');
      }
      &.book {
        background-image: url('../../assets/image/app/circleAd/read-bg.png');
        height: 1.05rem;
      }
      width: 3.43rem;
      height: 0.97rem;
      margin-top: 0.08rem;
      position: relative;
      background-repeat: no-repeat;
      background-size: 100%;

      &-book {
        height: 0.7rem;
        width: 0.49rem;
        position: absolute;
        left: 0.16rem;
        top: 0.17rem;
        &-total {
          // width: 1rem;

          background: #cc2725;
          border-radius: 0.06rem 0 0.05rem 0;
          position: absolute;
          bottom: 0;
          right: 0;
          padding: 0.04rem;
          font-size: 0.1rem;
          font-weight: 400;
          color: #ffffff;
          line-height: 0.1rem;
          img {
            float: left;
          }
          i {
            float: left;
            width: 0.02rem;
            height: 0.1rem;
            background-color: #fff;
            margin: 0 0.02rem;
            opacity: 0.5;
          }
          span {
            opacity: 0.9;
            margin-left: 0.04rem;
          }
        }
      }
      &-word {
        padding-left: 1rem;
        padding-top: 0.14rem;
        &.pl80 {
          padding-left: 0.8rem;
        }
        &.pt28 {
          padding-top: 0.28rem;
        }
        &-title {
          font-size: 0.14rem;
          font-weight: 600;
          color: #333333;
          line-height: 0.2rem;
          margin-bottom: 0.06rem;
        }
        &-time {
          font-size: 0.1rem;
          font-weight: 400;
          color: #333333;
          line-height: 0.16rem;
        }
        &-subtitle {
          font-size: 0.11rem;
          font-weight: 400;
          color: #f27006;
          margin-top: 0.14rem;
          &.mt5 {
            margin-top: 0.05rem;
          }
        }
      }
    }
  }
}
</style>