<template>
  <div class="status" :class="[statusClass, { new: isNew, allRadius: allRadius }]">{{statusName}}</div>
</template>

<script>
  export default {
    // props: ['status', 'isNew', 'allRadius'],
    props: {
      status: String | Number,
      isNew: {
        type: Boolean,
        default: false,
      },
      allRadius: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      statusName: function () {
        const statusObj = {
          '1': '已结束',
          '2': '即将开始',
          '3': '进行中'
        };
        return statusObj[this.status];
      },
      statusClass: function () {
        const classObj = {
          '1': 'grey',
          '2': 'yellow',
          '3': ''
        };
        return classObj[this.status];
      }
    }
  }
</script>

<style lang="less">
  @import "../assets/less/variable";

  .status{
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    line-height: .2rem;
    padding: 0 0.06rem;
    text-align: center;
    color: #fff;
    font-size: .12rem;
    background: linear-gradient(135deg, #73CCFA 0%, #3F9DF2 100%);
    border-radius: 0.05rem 0 0.05rem 0;
    &.allRadius{
      border-radius: 0.02rem;
    }
    &.new{
      border-radius: 0 0 0.05rem 0;
    }
    &.yellow{
      background: linear-gradient(135deg, #69D1C1 0%, #33B89A 100%);
    }
    &.grey{
      background: #B9B4B4;
    }
  }
</style>
