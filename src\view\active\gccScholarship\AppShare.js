import bridge from '@/plugins/bridge';
import { AppShareUrl } from './config'
import { isAndroid, isIOS } from "@/common";
export default {
  mounted(){
    let self = this;
    //全局挂在
    bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        window['$appShare'] = () =>{
          self._SHARE();
        };
        self.$setShareMenus();
      }
    });

  },
  methods:{
    //设置h5在app内右上角的菜单
    $setShareMenus(){
      let params = [
        {
          btnString: "",
          btnImage:"http://yzims.oss-cn-shenzhen.aliyuncs.com/fakerappshare.png",
          callback: "$appShare"
        }
      ]
      bridge.callHandler('setMenus',params);
    },
    _getCookie(name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return false;
      }
    },
    //调用app分享
    _SHARE(){
      let inviteId = this._getCookie('authToken');
      let regChannel = '';

      if(isAndroid()){
        regChannel = '5';
      }
      if(isIOS()){
        regChannel = '6';
      }

      let params = {
        title: '读本科报广州商学院，奖3000元奖学金，限时领取！',
        content: '为鼓励更多上进青年加入学习队伍，远智教育推出“广州商学院本科奖学金”活动，名额有限，快来领取，提升学历吧！',
        image: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png',
        url: AppShareUrl + '/active/gccScholarship/index?inviteId=' + inviteId + '&scholarship=121&regOrigin=42&regChannel='+ regChannel+'&pfsnLevel='+this.pfsnLevel,
        channel: '1',
      }
      bridge.callHandler('shareWebPage',params);
    }
  }
}
