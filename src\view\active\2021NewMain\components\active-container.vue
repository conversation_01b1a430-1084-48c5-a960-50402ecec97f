<template>
  <div class="yz-active-container" :class='{ iphoneX: isIphoneX, invite: inviteId }'>
    <inviteTop @getInviteId='getInviteId' />
    <!-- 分享组件 -->
    <share :title="shareTitle" :desc="shareDesc" :link="shareLink" :isActivity="true" :scholarship="scholarship"
      :regOrigin='regOrigin' ref="share" :levelId="levelId" />
    <!-- 顶部的tabs -->
    <slot name='tab-bar' v-if='isNeedTabBar'>
      <div v-if='headerTabs.length > 0' class="tabs-bar head" :class='{ fixed: headFixed }'>
        <div class="item" :class='{ active: headActive == index }' v-for="(item, index) in headerTabs" :key='index'
          @click='headTopClick(index)'>
          {{ item }}
        </div>
      </div>
    </slot>
    <slot name='banner' v-if='isNeedBanner'>
      <div class="container-banner-box" :class='{ fixed: headFixed }' :style='{ height: bannerHeight }'>
        <div class="banner-top">
          <!-- <num-roll :count="rollCount" /> -->
          <swiper-enroll right=".1" top="0" />
        </div>
        <img v-if='bannerResource' class="banner-img" :src="bannerResource" @click='singleBannerClick' alt="">
      </div>
    </slot>
    <!-- 默认内容插入 -->
    <slot></slot>
    <!-- 留言区和常见问题 -->
    <div v-if='isNeedMsgBar' class="tabs-bar" :class='{ fixed: msgFixed }' ref='msgBar'>
      <!-- <div class="item" :class='{active: msgOrQuestion == "msg"}' @click='msgOrQuestion = "msg";'>留言区</div> -->
      <div class="item" :class='{ active: msgOrQuestion == "question" }' @click='msgOrQuestion = "question";'>常见问题</div>
    </div>
    <div class="msg-question" :class='{ mt5: msgFixed }'>
      <user-message v-show='msgOrQuestion == "msg"' :pink-btn='mibanPink' :answerBgColor='answerBgColor'
        :scholarship="scholarship" />
      <!-- 常见问题容器 -->
      <slot name='question' v-if='msgOrQuestion == "question" && showQuestion'>
        <questionList :type='questionType' />
      </slot>
    </div>

    <div class="copy-right">
      承办单位: 广州远智教育科技有限公司<br />邮编: 516600 粤ICP备12034252号-1
    </div>
    <!-- 米瓣 -->
    <miban-btn :pink='mibanPink' :scholarship='scholarship' />
    <second-footer :inviteId='inviteId' :noEnroll='noEnroll' :statisticKey='statisticKey' @footerMethod='footerMethod'
      @enroll='enroll' />
  </div>
</template>

<script>
import { isIphoneX } from '@/common';
import appShare from '@/mixins/appShare';
import share from "@/components/share";
import inviteTop from "@/view/active/enrollAggregate/components/invite-top";
import SwiperEnroll from "@/view/active/enrollAggregate/components/swiper-enroll";
import questionList from "@/components/activePage/questionList";
import UserMessage from "../components/user-message";
import MibanBtn from "../components/miban-btn";
import SecondFooter from "../components/second-footer";
import NumRoll from "../components/num-roll";

export default {
  mixins: [appShare],
  components: {
    share,
    inviteTop,
    UserMessage,
    MibanBtn,
    SecondFooter,
    NumRoll,
    SwiperEnroll,
    questionList,
  },
  props: {
    scholarship: {
      type: [String, Number],
      default: '164', // 164
    },
    shareTitle: {
      type: String,
      default: '在线报读，名校录取，学信网可查，助你提升专本、研究生学历！',
    },
    shareDesc: {
      type: String,
      default: '远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！',
    },
    shareImage: { // oss链接
      type: String,
      default: '',
    },
    link: { // 不要带域名！！ 格式：/active/****
      type: String,
      default: '/active/newMain',
    },
    regOrigin: {
      type: [String, Number],
      default: '16',
    },
    mibanPink: { // 紫色米瓣按钮
      type: Boolean,
      default: false,
    },
    showQuestion: { // 紫色米瓣按钮
      type: Boolean,
      default: true,
    },

    // dreamBuild openUniversity selfTought graduate 邀约有礼页面 share.json
    // inviteFrom: {
    //   type: String,
    //   default: 'dreamBuild',
    // },
    // 聊天框回答的背景色
    answerBgColor: {
      type: String,
      default: 'rgba(145, 145, 145, 0.05);',
    },
    isNeedTabBar: { // 是否需要tabbar
      type: Boolean,
      default: true,
    },
    isNeedBanner: { // 是否需要banner
      type: Boolean,
      default: true,
    },
    isNeedAppShare: { // 是否需要app分享
      type: Boolean,
      default: true,
    },
    isNeedScroll: { // 是否需要滑动方法
      type: Boolean,
      default: false,
    },
    isNeedMsgBar: { // 是否需要留言信息和常见问题的tab
      type: Boolean,
      default: true,
    },
    headerTabs: { // 顶部tabs
      type: Array,
      default: () => [],
    },
    noEnroll: { // 是否需要滑动方法
      type: Boolean,
      default: false,
    },
    initHeadActive: {
      type: [String, Number],
      default: 0,
    },
    bannerHeight: {
      type: String,
      default: '3.2rem',
    },
    rollCount: {
      type: Number,
      default: 0,
    },
    bannerResource: { // 图片资源
      type: [Object, String, Blob],
      default: null,
    },
    isSlotTabBar: { // 是否自定义tabbar
      type: Boolean,
      default: false,
    },
    questionType: {
      type: String,
      default: 'dreamBuild', // openUniversity
    },
    statisticKey: {
      type: String,
      default: '',
    },
    // 层次ID
    levelId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      isIphoneX: isIphoneX(),
      inviteId: '',
      msgOrQuestion: 'question', // 留言还是问题
      msgFixed: false,
      msgOffsetTop: 0,
      headActive: 0,
      headFixed: false,
    }
  },
  computed: {
    shareLink() {
      return window.location.origin + this.link;
    },
    appShareParams() {
      return {
        title: this.shareTitle,
        content: this.shareDesc,
        url: this.link,
        image: this.shareImage,
        scholarship: this.scholarship,
        regOrigin: this.regOrigin,
      };
    },
  },
  watch: {
    appShareParams() {
      if (this.isNeedAppShare) {
        this.setShareParams(this.appShareParams);
      }
    },
    headActive() {
      this.initHeadActiveIndex();
    },
  },
  mounted() {
    this.headActive = this.initHeadActive;
    this.initScroll();
    this.getMsgBarScrollTop();
    if (this.isNeedAppShare) {
      this.initAppShare(() => {
        this.setShareParams(this.appShareParams);
      });
    }
  },
  beforeDestroy() {
    if (this.isNeedScroll) {
      window.removeEventListener('scroll', this.scroll);
    }
  },
  methods: {
    // 邀约id
    getInviteId(id) {
      this.inviteId = id;
      this.$emit('getInviteId', id);
    },
    footerMethod(methods) {
      this.$emit('footerMethod', methods);
    },
    // 报读按钮
    enroll() {
      this.$emit('enroll');
    },
    singleBannerClick() {
      this.$emit('singleClick');
    },
    initScroll() {
      if (this.isNeedScroll) {
        window.addEventListener('scroll', this.scroll);
      }
    },
    scroll() {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      this.$emit('scroll', scrollTop);
      if (this.isNeedMsgBar) {
        this.msgFixed = scrollTop >= (this.msgOffsetTop - 25); // - 一半容器高度
      }
      if (this.headerTabs.length > 0 || this.isSlotTabBar) { // 头部tabs吸顶
        this.headFixed = scrollTop > 120; // 邀约头的高度
      }
    },
    // 获取留言的距离顶部高度
    getMsgBarScrollTop() {
      if (!this.isNeedMsgBar) {
        return;
      }
      // 要固化banner的高度，不然图片加载慢了，取出来的高度不对
      this.msgOffsetTop = this.$refs.msgBar.offsetTop;
    },
    headTopClick(index) {
      this.headActive = index;
      this.$emit('tabChange', index);
      this.$nextTick(() => {
        this.getMsgBarScrollTop();
      });
    },
    initHeadActiveIndex() {
      if (this.headerTabs.length > 0) {
        this.headActive = this.initHeadActive;
      }
    },
  }
};
</script>

<style lang="less">
.yz-active-container {
  padding-bottom: 0.6rem;

  // &.invite{
  //   padding-top: 0.5rem;
  // }
  .banner_swiper {
    z-index: 2;
  }

  &.iphoneX {
    padding-bottom: 0.94rem;
  }

  .copy-right {
    text-align: center;
    padding-bottom: 0.24rem;
    font-size: 0.12rem;
    margin-top: 0.24rem;
  }

  .tabs-bar {
    display: flex;
    background: #FCD9CC;
    width: 100%;
    position: relative;
    z-index: 5;

    &.fixed {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }

    &.invite {
      position: static;
    }

    &.head {
      background: #BC0001;
      z-index: 4;

      // position: fixed;
      // top:0;
      // left: 0;
      // right: 0;
      .item {
        color: #fff;
        font-weight: 500;

        &.active {
          background: linear-gradient(180deg, #FFBF3C 0%, #F88215 100%);
        }
      }
    }

    .item {
      height: 0.5rem;
      flex: 1;
      line-height: 0.5rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #D23C3D;
      text-align: center;
      position: relative;
      transition: background 0.2s, color 0.2s;

      &.active {
        color: #fff;
        background: #D23C3D;
        // &::after{
        //   content: '';
        //   position: absolute;
        //   width: 0.4rem;
        //   height: 0.02rem;
        //   border-radius: 10px;
        //   background: #fff;
        //   bottom: 0.05rem;
        //   left: 50%;
        //   transform: translateX(-50%);
        // }
      }
    }
  }

  .msg-question {
    &.mt5 {
      margin-top: 0.5rem;
    }
  }

  .container-banner-box {
    position: relative;
    height: 3.2rem;

    &.fixed {
      margin-top: 0.5rem;
    }

    .banner-top {
      position: absolute;
      width: 100%;
      top: 0.2rem;
      padding-left: .1rem;
    }

    .banner-img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
