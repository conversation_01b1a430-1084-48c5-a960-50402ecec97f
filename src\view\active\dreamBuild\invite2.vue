<template>
  <div>
    <div class="head">
      <div class="name">姓名</div>
      <div class="mobile">手机号</div>
      <div class="regTime">注册时间</div>
    </div>
    <div class="content">
      <van-list
        v-if="list.length > 0"
        v-model="loading"
        :finished="finished"
        :offset="50"
        @load="getInviteStatisticsUser"
      >
        <div class="item" v-for="(item, index) in list" :key="index">
          <div class="item-name">{{ item.realName }}</div>
          <div class="item-mobile">{{ desensitizePhone(item.mobile) }}</div>
          <div class="item-regTime">
            {{ item.regTime | formatTimeByMoment("YYYY-MM-DD HH:mm:ss") }}
          </div>
        </div>
      </van-list>
      <div class="none" v-else>
        <div>
          <img
            src="https://yzpres.oss-cn-guangzhou.aliyuncs.com/configureIntroductionPage/chickenBrother.png"
            alt=""
          />
        </div>
        <div>暂无记录</div>
      </div>
    </div>
    <div class="btn" @click="invite()">邀约有礼</div>
  </div>
</template>

<script>
import bridge from "@/plugins/bridge";
export default {
  data() {
    return {
      fansCount: 0,
      zmReward: 0,
      loading: false,
      finished: false,
      pageNum: 1,
      waitNum: 0,
      list: [],
      isChecking: false,
      showTips: false,
      isAppOpen: false,
    };
  },
  created() {
    this.getInviteStatisticsUser();
  },
  methods: {
    desensitizePhone(phone) {
      if (!phone) return "";
      const prefix = phone.substring(0, 3);
      const suffix = phone.substring(phone.length - 4);
      const stars = "*".repeat(phone.length - 7);
      return `${prefix}${stars}${suffix}`;
    },
    getIsAppOpen() {
      bridge.callHandler("isAppOpen").then((res) => {
        if (res.appOpen) {
          console.log("app?");
          this.isAppOpen = true;
        }
      });
    },

    // 获取邀约统计列表
    async getInviteStatisticsUser() {
      this.loading = true;
      const res = await this.$http.post("/us/getInviteStatisticsUser/1.0/", {
        pageNum: this.pageNum,
        pageSize: 7,
      });
      this.loading = false;
      if (res.code != "00") return;
      const body = res.body || [];
      this.list = this.pageNum == 1 ? body : this.list.concat(body);
      if (!res.body || res.body.length == 0) {
        this.finished = true;
      } else {
        this.pageNum += 1;
      }
    },
    invite() {
      if (this.isAppOpen) {
        this.$yzStatistic(
          "invite.gift.entrance.event",
          "",
          "app-我的邀约【邀请有礼】点击"
        );
      } else {
        this.$yzStatistic(
          "invite.gift.entrance.event",
          "",
          "公众号-我的邀约【邀请有礼】点击"
        );
      }
      location.href = '/new-h5/newPages/active/withCourtesy'
    },
  },
};
</script>

<style lang="less" scoped>
.head {
  display: flex;
  background: #F3F2F2;
  padding: 0 0.2rem;
  height: .5rem;
  line-height: .5rem;
  font-weight: 600;
  font-size: .12rem;
  color: #606266;

  .name {
    margin-left: 0.45rem;
  }

  .mobile {
    margin-left: 0.7rem;
  }

  .regTime {
    margin-left: .6rem;

  }
}

.content {
  margin-bottom: .45rem;
}
  .item {
    display: flex;
    padding: 0 0.2rem;
    height: .5rem;
    line-height: .5rem;
    font-weight: 500;
    font-size: .12rem;
    color: #333333;
    background: #FFFFFF;

    .item-name {
      width: 1.2rem;
      text-align: center;
    }

    .item-mobile {
      width: 0.7rem;
      text-align: center;
      // margin: 0 .18rem 0 .17rem;
    }

    .item-regTime {
      width: 1.5rem;
      text-align: center;

    }


  }

  .none {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 5.96rem;
    font-weight: 400;
    font-size: .14rem;
    color: #606266;

    img {
      width: 1.63rem;
      height: 1.6rem;
      margin-bottom: .16rem;
    }
  }

.btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: .45rem;
  background: linear-gradient(90deg, #F09190 0%, #F07877 100%);
  font-weight: 600;
  font-size: .16rem;
  color: #FFFFFF;
  line-height: .22rem;
  line-height: .45rem;
  text-align: center;
}
</style>