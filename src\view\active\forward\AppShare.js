import bridge from '@/plugins/bridge';
import { AppShareUrl } from './config'
import { isAndroid, isIOS } from "@/common";
export default {
  mounted(){
    let self = this;
    //全局挂在
    bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        window['$appShare'] = () =>{
          self._SHARE();
        };
        self.$setShareMenus();
      }
    });

  },
  methods:{
    //设置h5在app内右上角的菜单
    $setShareMenus(){
      let params = [
        {
          btnString: "",
          btnImage:"http://yzims.oss-cn-shenzhen.aliyuncs.com/fakerappshare.png",
          callback: "$appShare"
        }
      ]
      bridge.callHandler('setMenus',params);
    },
    _getCookie(name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return false;
      }
    },
    //调用app分享
    _SHARE(){
      let inviteId = this._getCookie('authToken');
      let regChannel = '';

      if(isAndroid()){
        regChannel = '5';
      }
      if(isIOS()){
        regChannel = '6';
      }

      let params = {
        title: '限时千元活动，报读可赢取上进专项奖学金！',
        content: '活动旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！',
        image: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png',
        url: AppShareUrl + '/active/forward/index?inviteId=' + inviteId + '&scholarship=125&regOrigin=16&regChannel='+ regChannel+'&pfsnLevel='+this.pfsnLevel,
        channel: '1'
      }
      bridge.callHandler('shareWebPage',params);
    }
  }
}
