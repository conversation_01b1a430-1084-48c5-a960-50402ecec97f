<template>
  <div>
    <div class="oldWrap" :class="{active:isIphoneX}">
      <inviteTop :inviteId="inviteId"></inviteTop>
      <div class="banner">
        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
      </div>

      <div class="content" :class="{'bg-g':tabName!='recruit'}">
        <div class="tab-3">
          <p :class="{'active':tabName=='recruit'}" @click="tabName='recruit'">
            <span>招生主页</span>
          </p>
          <p :class="{'active':tabName=='introduce'}" @click="tabName='introduce'">
            <span>学校介绍</span>
          </p>
          <p :class="{'active':tabName=='common'}" @click="tabName='common'">
            <span>常见问题</span>
          </p>
        </div>
      </div>
      <transition name="fade2">
        <div v-if="tabName=='recruit'">
          <div class="schoolAdvantage">
            <button @click="enroll"></button>
          </div>
          <div class="charge">
            <div class="chargetop">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
              <p>院校专业及收费</p>
            </div>

          </div>
          <div class="highUniversityBox">
            <div class="highUniversity">
              <div class="title">
                <span class="text">- 高起专(学制:2.5年) -</span>
              </div>
              <table>
                <tr>
                  <td width="20%">理工类</td>
                  <td>建筑工程技术、工程造价、机电一体化技术、计算机应用技术</td>
                  <td width="20%" rowspan="4">3600 <br>元/学年</td>
                </tr>
                <tr>
                  <td>文史类</td>
                  <td>大数据与会计、电子商务、工商企业管理</td>
                </tr>
                <!-- <tr>
                  <td>外语类</td>
                  <td>商务英语</td>
                </tr>
                <tr>
                  <td>艺术类</td>
                  <td>艺术设计</td>
                </tr> -->
              </table>
            </div>
            <div style="margin-top:0.1rem"></div>
            <div class="highUniversity">
              <div class="title">
                <span class="text">- 高起专(学制:3年) -</span>
              </div>
              <table>
                <tr>
                  <td>外语类</td>
                  <td>商务英语</td>
                  <td width="20%" rowspan="4">3000 <br>元/学年</td>
                </tr>
                <tr>
                  <td>艺术类</td>
                  <td>艺术设计</td>
                </tr>
              </table>
            </div>
            <p style="padding-left:.2rem;margin-top:.05rem;color: rgba(23, 6, 6, 0.6)"><span style="color:#F06E6C">•</span> 书杂费:&ensp;400元/学年,按学制收取</p>
            <p style="padding-left:.2rem;color: rgba(23, 6, 6, 0.6)"><span style="color:#F06E6C">•</span> 学制:&ensp;2.5年(部分专业3年学制)</p>

            <!-- <p  style="color: rgba(23, 6, 6, 0.6);padding-left:.2rem;float:left;width:20%">
              <span style="color:#F06E6C">•</span> 考区:</p>
              <p style="width:75%;float:left;color:rgba(23,6,6,.6);    margin-left: -.05rem;">惠州、广州、深圳、东莞、阳江、梅州、肇庆、湛江、汕尾、潮州、韶关、清远、汕头、河源、佛山、茂名、江门</p> -->
          </div>
          <div class="story">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span>上进故事</span>
            </div>
            <div class="content">
              <a @click="toStory(item.scholarshipId,inviteId,item.resourcesUrl,item.createTime)" class="item"
                 v-for="(item,index) in storyList" :key="index">
                <img :src="item.articlePicUrl+storyImgLimit|imgBaseURL" />
                <p class="text">{{item.articleTitle}}</p>
                <p class="date">{{item.createTime.substring(0,11)}}</p>
              </a>
            </div>
          </div>
          <!-- <div class="userMessage">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">留言区</span>
            </div>
            <div class="textContent">
              <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                  <div class="fl">
                    <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2" alt />
                  </div>
                  <div class="fr">
                    <p class="userName">{{(item.realName|| item.nickname)|hideName}}</p>
                    <div class="bottom" v-if="item.msgReply">
                      <p class="uesrQuestion">{{item.msgContent}}</p>
                      <div class="content">
                        <div class="line"></div>
                        <p class="answer">
                          <span>回复:&nbsp;</span>
                          {{item.msgReply}}
                        </p>
                      </div>
                    </div>
                    <div class="bottom2" v-if="!item.msgReply" style="margin-top:.16rem">
                      <p class="uesrQuestion">{{item.msgContent}}</p>
                    </div>
                  </div>
                  <div class="line"></div>
                </div>
              </div>
            </div>
            <div class="userMessageContent">
              <div class="fl">
                <img :src="userImg?userImg+headLimit:userImg|defaultAvatar2" alt />
              </div>
              <div class="fr">
                <p class="userName">{{userName}}</p>
                <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                <span>{{message.length}}/50</span>
                <button @click="enrollMsg()">提交</button>
              </div>
            </div>
          </div> -->
        </div>
      </transition>
      <transition name="fade2">
        <div v-if="tabName=='introduce'">
          <div class="schoolDetails" :class="{showall:true,active:showall}">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
            <p class="schoolDetailsText">
              &nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;
              广州城建职业学院是经广东省人民政府批准、国家教育部备案的全日制普通高等职业院校。学校起源于1960年广州市建筑工程局主办的广州业余建筑工程学院，2007年在原广州大学城建学院基础上独立设置，是一所工科类民办高职院校。
              <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;
              学校位于广州市从化区，校园环境优美，办学条件优越，基础设施完备，服务保障高效。2012年学校以优异成绩通过了广东省高职院校人才培养工作评估，是教育部现代学徒制试点高校、广东省教育厅自主招生试点院校、广东省唯一获批开展四年制应用型本科人才培养试点的民办高职院校。
              <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;学校积极探索“产教融合、校企合作”的协同育人机制，与国际著名企合作，加入了国家大数据百校计划；组建了以广州天马集团和广州市建筑集团为主导、行业协会为依托、名优企业为基础的广东城建职业教育集团，与政府、行业、企业联合打造了广东城建职业教育集团建筑产业园。
            </p>
          </div>
          <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="!showall">
            <a :class="{active:showall}" @click="lookMore">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span>查看更多</span>
            </a>
          </div>
          <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="showall">
            <a :class="{active:showall}" @click="lookMoreCancle">
              <span class="down"></span>
              <span>收起</span>
            </a>
          </div>
          <div class="studentStory">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">学员风采</span>
            </div>
            <div class="swiper">
              <swiper :options="swiperOption">
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/1.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/9.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/2.png">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/3.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/4.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/5.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/6.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/7.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/8.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/10.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/11.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/12.jpg">
                </swiper-slide>
                <swiper-slide>
                  <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/13.jpg">
                </swiper-slide>
              </swiper>
            </div>
          </div>
          <!-- <div class="userMessage">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">留言区</span>
            </div>
            <div class="textContent">
              <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                  <div class="fl">
                    <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2" alt />
                  </div>
                  <div class="fr">
                    <p class="userName">{{item.nickname}}</p>
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                    <div class="content" v-if="item.msgReply">
                      <div class="line"></div>
                      <p class="answer">
                        <span>回复:&nbsp;</span>
                        {{item.msgReply}}
                      </p>
                    </div>
                  </div>
                  <div class="line"></div>
                </div>
              </div>
            </div>
            <div class="userMessageContent">
              <div class="fl">
                <img :src="userImg?userImg+headLimit:userImg|defaultAvatar2" alt />
              </div>
              <div class="fr">
                <p class="userName">{{userName}}</p>
                <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                <span>{{message.length}}/50</span>
                <button @click="enrollMsg">提交</button>
              </div>
            </div>
          </div> -->
        </div>
      </transition>
      <transition name="fade2">
        <div v-if="tabName=='common'" style="padding-bottom: .6rem">
          <div class="texta">
            <van-collapse v-model="activeName" accordion>
              <van-collapse-item name="1">
                <div slot="title">
                  <span class="indexText">Q1</span>自考会不会比较好
                </div>自考与成考都属于成人教育系列，均是国家承认的学历，证书学信网可查。
              </van-collapse-item>
              <van-collapse-item name="2">
                <div slot="title">
                  <span class="indexText">Q2</span>有课上吗？
                </div>有的。购买了上进礼包后，会包含辅导课在里面的。
              </van-collapse-item>
              <van-collapse-item name="3">
                <div slot="title">
                  <span class="indexText">Q3</span>怎么查询是否报考成功呀？
                </div>您可以在公众号-学员服务-远智学堂中查看状态，状态为“考前确认”即表示报考成功。
              </van-collapse-item>
              <van-collapse-item name="4">
                <div slot="title">
                  <span class="indexText">Q4</span>考不上怎么办？
                </div>考试不难的，只要去考，认真答完试卷基本都能通过。
              </van-collapse-item>
              <van-collapse-item name="5">
                <div slot="title">
                  <span class="indexText">Q5</span>应该很难考的吧，没考上可以不读吧？
                </div>考试不难的，在远智辅导的学员，参加成考的通过率高达93.8%。若真的没考上，可以选择不读。
              </van-collapse-item>
              <van-collapse-item name="6">
                <div slot="title">
                  <span class="indexText">Q6</span>本科毕业的也能报吗，搞个双学位
                </div>可以的。
              </van-collapse-item>
              <van-collapse-item name="7">
                <div slot="title">
                  <span class="indexText">Q7</span>一共考几科？
                </div>成人高考一共考3科，专科考“语文、数学、英语”，本科考“政治、英语、专业科”。
              </van-collapse-item>
              <van-collapse-item name="8">
                <div slot="title">
                  <span class="indexText">Q8</span>可以自选居住地的考点吗？
                </div>选择了哪个城市就在哪个城市参考。
              </van-collapse-item>
              <van-collapse-item name="9">
                <div slot="title">
                  <span class="indexText">Q9</span>有什么专业可选？
                </div>可以在链接中点击自己所在的城市查看。
              </van-collapse-item>
              <van-collapse-item name="10">
                <div slot="title">
                  <span class="indexText">Q10</span>户口不在广东了可以考吗？
                </div>在广东工作就可以，不受户口限制。
              </van-collapse-item>
              <van-collapse-item name="11">
                <div slot="title">
                  <span class="indexText">Q11</span>已经报名，然后咧？
                </div>报名后只需要利用碎片时间复习即可，另外会有助学老师跟进你的学习情况和对你入学前的指导。
              </van-collapse-item>
              <van-collapse-item name="12">
                <div slot="title">
                  <span class="indexText">Q12</span>报名了，要缴费吗？
                </div>筑梦计划是免费报名的。可以自愿选择是否购买299元的上进礼包。
              </van-collapse-item>
              <van-collapse-item name="13">
                <div slot="title">
                  <span class="indexText">Q13</span>免费报名的没辅导课辅导书，自己复习？
                </div>是的。
              </van-collapse-item>
              <van-collapse-item name="14">
                <div slot="title">
                  <span class="indexText">Q14</span>要交考试费啊？
                </div>考试费是由教育局收取的，37元/科，3科合计111元，在9月初现场确认时缴纳。
              </van-collapse-item>
              <van-collapse-item name="15">
                <div slot="title">
                  <span class="indexText">Q15</span>中专可以升大专吗？
                </div>中专已取得毕业证的同学可以报读成人大专。
              </van-collapse-item>
              <van-collapse-item name="16">
                <div slot="title">
                  <span class="indexText">Q16</span>我在读大专，明年拿证，可以报本科吗？
                </div>如果是在明年3月1日前能取得大专毕业证的话，那么可以以应届生的身份参加今年的成人高考。
              </van-collapse-item>
              <van-collapse-item name="17">
                <div slot="title">
                  <span class="indexText">Q17</span>报名要交什么资料的吗？
                </div>报名需要填写相关的个人报读信息以及提供身份证正反面、毕业证复印件。
              </van-collapse-item>
              <van-collapse-item name="18">
                <div slot="title">
                  <span class="indexText">Q18</span>在职工作是不是真的可以报读？
                </div>可以。
              </van-collapse-item>
              <van-collapse-item name="19">
                <div slot="title">
                  <span class="indexText">Q19</span>去哪里上课的？必须要去学校吗？
                </div>课程我们都是安排在周末或者晚上，通过手机直播上课或者是通过我们的远程上课平台学习即可。无需到学校去。
              </van-collapse-item>
              <van-collapse-item name="20">
                <div slot="title">
                  <span class="indexText">Q20</span>考试是闭卷的还是开卷考？
                </div>闭卷考。
              </van-collapse-item>
              <van-collapse-item name="21">
                <div slot="title">
                  <span class="indexText">Q21</span>初中毕业能报名吗？
                </div>抱歉，初中学历不符合报读资格。需要中专或者高中以上学历才可以。
              </van-collapse-item>
              <van-collapse-item name="22">
                <div slot="title">
                  <span class="indexText">Q22</span>你好，教了资料费后怎么领资料？
                </div>会有老师在2个工作日内联系你，跟你确认报读信息以及收件地址。
              </van-collapse-item>
              <van-collapse-item name="23">
                <div slot="title">
                  <span class="indexText">Q23</span>如考试未达295，那学费是多少？
                </div>如未达295分，学费按全价收取，相关院校专业的费用请在链接中查看。
              </van-collapse-item>
              <van-collapse-item name="24">
                <div slot="title">
                  <span class="indexText">Q24</span>报名成功了，没说什么时候考试的？
                </div>报名成功后，在今年9月初现场确认办理准考证，10月中下旬参加成人高考。
              </van-collapse-item>
              <van-collapse-item name="25">
                <div slot="title">
                  <span class="indexText">Q25</span>什么是筑梦计划？
                </div>筑梦计划是远智教育联合各合作高校为广大上进青年提供的一个提升学历的公益项目。
              </van-collapse-item>
            </van-collapse>
          </div>
        </div>
      </transition>
      <share :title="title" :desc="desc" :link="shareLink" :scholarship="scholarship" :imgUrl="shareImg"
             :regOrigin='regOrigin' ref="share" />
    </div>
    <!-- <o-footer tabName="newintroduce" :Expired="enrollEnd" @isIphoneX="isIphoneX=true" :inviteId="inviteId" from="introduce" :actName="actName" :unvs="unvs" :scholarship="scholarship"></o-footer> -->
    <second-footer @enroll='enroll' />
  </div>
</template>

<script>
import appShare from '@/mixins/appShare';
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { toLogin, isEmployee, isIOS, getIsInTimeByType } from "../../../common";
import share from "@/components/share";
import { Collapse, CollapseItem } from "vant";
import DigitRoll from "@huoyu/vue-digitroll";
import { imgBaseURL, activityTime } from "../../../config";
import countdown from "@/components/active/countdown5";
import oFooter from "@/components/activePage/footer"
import inviteTop from "../enrollAggregate/components/invite-top"
import SecondFooter from "@/view/active/2021NewMain/components/second-footer";
import statistic from '@/view/active/2021NewMain/statistic.json';

let scholarship = "164"; //优惠类型
export default {
  mixins: [appShare],
  components: {
    Collapse,
    CollapseItem,
    swiper,
    swiperSlide,
    share,
    countdown,
    oFooter,
    DigitRoll,
    inviteTop,
    SecondFooter,
  },
  data () {
    return {
      tabName: "recruit",
      activeName: "",
      animatePraise: false,
      animate: false,
      message: "",
      shareLink: "",
      isIphoneX: false,
      isLogin: null,
      scholarship: scholarship,
      inviteId: "",
      content: [],
      isPay: true,
      started: true,
      learnInfo: "",
      isEmployee: isEmployee(),
      userName: "",
      showall: false,
      userImg: "",
      showInvite: false,
      items: [],
      invite: {},
      enrollMsgList: [],
      storyList: [],
      hiddleLotteryTime: 1548680400000, //2019/1/28/21:00 隐藏抽奖入口
      shareObj: { title: "", desc: "", img: "" },
      swiperOption: {
        initialSlide: 1,
        autoplay: 2000,
        centeredSlides: true,
        loop: true,
        slidesPerView: "auto",
        loopedSlides: 1,
        autoplayDisableOnInteraction: false
      },
      isShowEnd: 0,
      storyImgLimit: "?x-oss-process=image/resize,m_fixed,h_122,w_155",
      loadingFlag: true,
      headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
      actName: "",
      activityContent: {},
      // isChange: true, //是否切换城建冲刺
      couponNum: "", //优惠劵领取人数
      isReceive: false, //是否领取
      sprintStatus: 0,
      CJsprintInfo: {}, //见证信息
      stdName: "",
      limitCount: 0,
      limitCountShow: 0,
      learnCount: 0,
      isScrollEnrollList: true,
      enrollEnd: false, //停止报名
      showCard: false,
      cardHeadUrl: "",
      cardWechatQrcode: "",
      propagateUrl: "",
      flag: false,
      isShow: false,
      sprintList: [],
      pageNum: 0,
      pageSize: 10,
      isLoading: false,
      allLoaded: false,
      EnrolmentCount: 0,
      activityInfo: {},
      title: '读学历来广州城建职业学院，在线报名，学信网可查！',
      desc: '平台旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！',
      activityStartTime: 0,
      changeTime: 0,
      startTime: 0,
      endTime: 0,
      relation: '',
      isMarch: false,
      unvs: {
        unvsName: "广州城建职业学院",
        unvsId: "153180146776432500"
      },
      isInvite5MonthActive: getIsInTimeByType('decisive'), // 邀约2020-5月活动
      pfsnLevel: '',
      shareImg: 'https://yzims.oss-cn-shenzhen.aliyuncs.com/actOtherConfig/2_chengjian.png',
      regOrigin: 42,
    };
  },

  created () {
    this.tabName = this.$route.query.tab || 'recruit'
    if (this.$route.query.tab) {
      this.toSprint()
    }
    this.isLogin = !!this.storage.getItem("authToken");
    this.userName = this.storage.getItem("realName") || this.storage.getItem("zmcName") || this.storage.getItem("mobile");
    this.stdName = this.storage.getItem("realName") || this.storage.getItem("zmcName");
    this.userImg = this.storage.getItem("headImg") || "";
    this.relation = this.storage.getItem('relation') || 0;
    (this.inviteId = this.$route.query.inviteId || ""),
      (this.action = this.$route.query.action || "");
    this.pfsnLevel = this.$route.query.pfsnLevel;
    this.shareLink = `${window.location.origin}/active/enrollmentHomepage`;
    this.CJsprintInfo = this.storage.getItem("CJHNsprintInfo") || {};
    // this.getContent();
    setInterval(this.scroll, 3000);
    setInterval(this.scrollText, 3000);
    this.getNewRegList();
    this.getActivityInfo();
    // this.getCouponGiveAllNum();
    // this.isGetCoupon();
    if (this.pfsnLevel) {
      this.$yzStatistic('marketing.base.browse', statistic.schoolPage[this.pfsnLevel].main.id, statistic.schoolPage[this.pfsnLevel].main.name);
    }
    this.initAppShare(() => {
      this.setShareParams({
        title: this.title,
        content: this.desc,
        url: '/active/enrollmentHomepage',
        image: this.shareImg,
        regOrigin: this.regOrigin,
      });
    });
  },

  computed: {
    regChannel () {
      return this.$route.query.regChannel || '';
    },
    jumpUrl: function () {
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName: this.actName,
          recruitType: '1',
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
          unvs: JSON.stringify({
            unvsName: "广州城建职业学院",
            unvsId: "153180146776432500"
          })
        }
      };
      return url;
    }
  },
  mounted: function () {
    window.addEventListener("scroll", this.handleScroll, true);
    //获取邀约人信息
    if (!!this.inviteId) {
      this.getInviteInfo();
    }
  },
  methods: {
    enroll () {
      if (!this.isOpenAppLogin()) {  // 该方法来自appShare.js
        return;
      }
      this.$router.push(this.jumpUrl);
    },
    enrollEndTip () {
      this.$modal({ message: '当前活动已结束~', icon: 'warning' });
    },
    //报读
    cjToEnroll () {
      // if(this.activityInfo.limitCount - this.activityInfo.learnCount <=0){
      //   this.$modal({message: '来迟一步啦，当前名额已满，请联系助学老师。', icon: 'warning'});
      //   return;
      // }
      this.$router.push({
        name: 'adultExamEnrollCheck', query: {
          action: 'login', activityName: 'scholarship', recruitType: '1', inviteId: this.inviteId, actName: this.actName, scholarship: this.scholarship, unvs: JSON.stringify({
            unvsName: '广州城建职业学院',
            unvsId: '153180146776432500'
          })
        }
      })

    },
    phoneNumberMobile () {
      window.location.href = "tel:" + this.cardMobile;
    },
    phoneNumber () {
      if (this.articleId) {
        window.location.href = "tel:" + this.cardMobile;
        return;
      }
      window.location.href = "tel:4008336013";
    },
    getSptintList () {
      this.$http
        .post("/mkt/getAdvanceWitnessList/1.0/", {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          scholarship: this.scholarship
        })
        .then(res => {
          if (res.code == "00") {
            const datas = res.body || [];
            this.sprintList.push(...res.body);
            this.$nextTick(() => {
              this.allLoaded = datas.length === 0;
              this.isLoading = this.allLoaded;
            });
            //this.sprintListFlag=true
          }
        });
    },
    getInviteInfo () {
      let inviteId = (
        window.sessionStorage.getItem("inviteId") ||
        this.$route.query.inviteId ||
        decodeURIComponent(
          this.$route.query.inviteId ||
          this.getQueryString(this.redirect, "inviteId") ||
          ""
        )
      ).replace(/ /g, "+");
      if (inviteId) {
        this.$http
          .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
          .then(res => {
            let { code, body } = res;
            if (code !== "00") return;
            this.invite = body || {};
            this.showInvite = true;
            if (!this.articleId) {
              if (this.invite.relation == "2" || this.invite.relation == "6") {//取自己的名片
                this.getArtile(this.invite.userId);
              } else {//其他情况取跟进人
                this.getCard(this.invite.userId);
              }
            }
          });
      }
    },
    getCard (userId) {
      this.$http
        .post("/mkt/getFollowCardByUserId/1.0/", { userId: userId })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          if (res.body === null) {
            this.articleId = "";
          } else {
            this.showCard = true;
            Object.assign(this.$data, res.body);
            if (this.cardHeadUrl) {
              this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
            }
            if (this.cardWechatQrcode) {
              this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
            }
            //this.propagateUrl = this.propagateUrl + "&articleId=" + id;
          }
        });
    },
    //获取优惠劵领取人数
    getCouponGiveAllNum () {
      this.$http
        .post("/mkt/getCouponGiveAllNumByType/1.0/", {
          couponType: "advance.witness.act.202001.cj"
        })
        .then(res => {
          if (res.code == '00') {
            this.EnrolmentCount = parseInt(res.body || 0)
          }
        });
    },
    //是否有领取优惠劵
    isGetCoupon () {
      if (!this.storage.getItem("authToken")) {
        return;
      }
      this.$http.post('/mkt/isGiveCouponByType/1.0/', { couponType: 'advance.witness.act.202001.cj' }).then(res => {
        if (res.code == '00') {
          if (res.body) {
            this.isReceive = true;
            this.sprintStatus = 1;
          }
        }
      })
    },
    //领取优惠劵
    getCoupon () {
      if (this.activityStartTime)
        this.$http
          .post("/mkt/giveCouponByType/1.0/", {
            couponType: "advance.witness.act.202001.cj",
            token: this.storage.getItem("authToken"),
            notPrompt: 1
          })
          .then(res => {
            if (res.code == "00") {
              this.isReceive = true;
              this.$router.push({
                name: "CJSprintGetCouponSuccess",
                query: {
                  inviteId: this.inviteId,
                  scholarship: scholarship,
                  activityName: "scholarship",
                  actName: this.actName,
                  type: "cj",
                  unvs: JSON.stringify({
                    unvsName: "广州城建职业学院",
                    unvsId: "153180146776432500"
                  })
                }
              });
            } else if (res.code == "E60055") {
              this.isReceive = true;
              this.sprintStatus = 1
              this.$modal({
                message: "您已领取该优惠券，请勿重复领取！",
                icon: "warning"
              });
            }
          });
    },
    loadMore () {
      this.pageNum++;
      this.getSptintList();
    },
    //去认证
    toSprint () {
      this.tabName = "sprint";
      clearInterval(this.timer);
      if (!this.storage.getItem("authToken")) {
        this.loadMore();
        // toLogin.call(this);
        return;
      }
      this.loadMore();
      document.querySelector('body').scrollTop = 0;
      document.querySelector('html').scrollTop = 0;
      if (Object.keys(this.CJsprintInfo).length > 0) {
        this.sprintStatus = 2;
        return;
      }
      // 获取认证信息
      this.$http
        .post("/mkt/getAdvanceWitnessByCoverToken/1.0/", {
          coverToken: this.storage.getItem("authToken"),
          scholarship: "55"
        })
        .then(res => {
          if (res.code == "00") {
            if (res.body) {
              this.sprintStatus = 2;
              this.CJsprintInfo = res.body;
              this.storage.setItem("CJHNsprintInfo", this.CJsprintInfo);
            }
          }
        });
    },
    toMethod () {
      this.$router.push({ name: 'sprintProcess' })
    },
    toActivity () {
      this.$router.push({ name: 'southChinaAgriculturalUniversity', query: { inviteId: this.$route.query.inviteId } })
    },
    //分享见证
    openShareSprint () {
      this.title = `求助！我是${this.storage.getItem('realName') || this.storage.getItem('zmcName')}，邀请您为我上进承诺见证助威！`;
      this.desc = '我正在申请参加远智教育2020年成人学历报读上进奖学金活动，需要您的见证才可以激活3000元奖学金，快来帮帮我！';
      this.shareLink = `${window.location.origin}/active/enrollmentHomepage/CJmysprint?sprint_token=${encodeURIComponent(this.storage.getItem('authToken'))}&stdName=${encodeURIComponent(this.storage.getItem('realName') || this.storage.getItem('zmcName'))}&type=cj&regOrigin=14`
      this.$nextTick(() => {
        this.$refs.share.openNO(true);
      });
    },
    //监听滑动的距离
    handleScroll () {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (this.loadingFlag) {
        if (scrollTop > 330) {
          this.getEnrollMsgList();
          this.getStoryList();
          this.loadingFlag = false;
        }
      }
    },
    // 点击查看更多
    lookMore: function () {
      this.showall = true;
    },
    lookMoreCancle: function () {
      this.showall = false;
    },

    // 获取学员基础信息
    getPayInfo: function (learnId) {
      let data = {
        learnId: learnId,
        itemCode: "Y0"
      };
      this.$http
        .post("/mkt/selectTutionPaidCountByLearnId/1.0/", data)
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          this.isPay = body.ifPay == "1" ? true : false;
        });
    },
    //购买教材
    toPayment: function () {
      this.$router.push({
        name: "stuPayment",
        query: {
          learnId: this.learnInfo.learnId,
          activityName: "dreamBuildScholarship",
          unvsid: this.learnInfo.unvsId
        }
      });
    },
    //邀请好友报名
    openShare: function () {
      this.$refs.share.open(
        null,
        "login",
        `${this.$route.fullPath}${this.$route.fullPath.includes("?") ? "&" : "?"
        }action=share`
      );
    },
    tips () {
      this.$modal({ message: "活动暂未开始！", icon: "warning" });
    },
    lotterytips () {
      let now = new Date().getTime();
      let stop = 1548680400000; // 2019/1/28 21:00 抽奖入口关闭
      if (now > stop) {
        this.$modal({ message: "抽奖活动已经下线！", icon: "warning" });
      } else {
        this.$router.push({ path: "/active/iphoneXLotteryThr" });
      }
    },
      toStory(id, inviteId, resourcesUrl, createTime) {
          this.$router.push({
            name: "scholarshipStoryInfo",
            query: {
              id: id,
              inviteId: inviteId,
              resourcesUrl: resourcesUrl,
              createTime: createTime
            }
          });
      },
    // 获取留言列表
    getEnrollMsgList: function () {
      this.$http
        .post("/mkt/getEnrollMsgList/1.0/", { scholarship: scholarship })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.enrollMsgList = body;
          }
        });
    },
    // 评论
    enrollMsg: function () {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      if (this.isEmployee) {
        this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
        return;
      }
      if (!this.message) {
        this.$modal({ message: "请输入评论内容", icon: "warning" });
        return;
      }

      this.$http
        .post("/mkt/enrollMsg/1.0/", {
          scholarship: scholarship,
          msgContent: this.message
        })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.$modal({
              message: "提交成功",
              beforeClose: (action, instance, done) => {
                done();
                this.message = "";
                this.getEnrollMsgList();
              }
            });
          }
        });
    },
    getSystemDateTime () {
      const now = new Date().getTime();
      this.nowTime = now;
      this.started = now > this.startTime;
      this.Expired = now > this.endTime;
      this.$refs.countdown && this.$refs.countdown.getSystemDateTime(now);
    },
    //筑梦信息
    getActivityInfo (scholarshipDream = scholarship) {
      this.$http
        .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarshipDream })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          if (scholarshipDream == '67') {
            if (Date.now() < body.EndTime && Date.now() > body.StartTime) {
              this.isMarch = true
            }
            return;
          } else {
            this.getActivityInfo('67')
          }
          // console.log(body,'0000');
          this.startTime = body.StartTime;
          this.isShowEnd = res.body.isShowEnd;
          this.endTime = body.EndTime;
          this.actName = body.actName;
          this.EnrolmentCount = parseInt(body.learnCount);
          if (Date.now() > body.EndTime) {
            this.enrollEnd = true;
          }
          this.$nextTick(() => {
            this.getSystemDateTime();
          })
        });
    },
    // 获取故事列表
    getStoryList () {
      this.$http
        .post("/mkt/scholarshipStoryList/1.0/", { informationType: 1 })
        .then(res => {
          const { code, body } = res;
          if (code === "00") {
            this.storyList = body;
          }
        });
    },

    // 轮播用户评论
    scroll () {
      if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
        return;
      }
      this.animatePraise = true;
      setTimeout(() => {
        this.enrollMsgList.push(this.enrollMsgList[0]);
        this.enrollMsgList.shift();
        this.animatePraise = false;
      }, 500);
    },

    // 轮播文字
    scrollText () {
      this.animate = true;
      setTimeout(() => {
        this.items.push(this.items[0]);
        this.items.shift();
        this.animate = false;
      }, 500);
    },
    // 获取最新注册用户列表
    getNewRegList: function () {
      this.$http.post("/us/getNewRegList/1.0/").then(res => {
        let { code, body } = res;
        if (code === "00") {
          this.items = body;
          if (this.items.length == 1) {
            this.items.push(body[0]);
          }
        }
      });
    }
  },
  beforeDestroy () {
    window.removeEventListener("scroll", this.handleScroll, true);
  }
};
</script>


<style lang="less" scoped>
@themecolor:rgba (189, 98, 28, 1);
.texta {
  .van-collapse {
    .van-collapse-item {
      .van-cell {
        line-height: 0.54rem;
        height: auto;
        .van-cell__right-icon {
          margin-top: 3%;
        }
        .indexText {
          width: 0.15rem;
          height: 0.17rem;
          background: rgba(240, 110, 108, 0.1513);
          border-radius: 8px 8px 8px 0px;
          color: #f06e6c;
          font-size: 0.12rem;
          margin-left: 0.17rem;
          margin-right: 0.09rem;
        }
      }
      .van-collapse-item__wrapper {
        .van-collapse-item__content {
          padding: 0.13rem 0.56rem 0.13rem 0.44rem;
          color: rgba(23, 6, 6, 0.8);
        }
      }
    }
  }
}
.oldWrap {
  position: relative;
  background: rgb(246, 246, 246);
  &.active {
    padding-bottom: 34px;
  }
  .invite {
    background-image: url('../../../assets/image/active/enrollmentHomepage/invitBg2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;
    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }
    .rightView {
      float: left;
      margin-top: 2.3%;
      p {
        font-size: 0.16rem;
        span {
          color: #e15443;
          font-size: 0.13rem;
        }
        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }
  .banner {
    img {
      height: 1.11rem;
      width: 3.75rem;
    }
  }
  .content {
    &.bg-g {
      background-image: none;
    }
    .tab {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: rgba(246, 246, 246, 1);
      p {
        float: left;
        height: 0.38rem;
        width: 25%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;
        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }
        &.active {
          padding: 0rem;
          line-height: 0.38rem;
          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;
            &:before {
              content: '';
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }
        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }

    .tab-3 {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: rgba(246, 246, 246, 1);
      p {
        float: left;
        height: 0.38rem;
        width: 33.3%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;
        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }
        &.active {
          padding: 0rem;
          line-height: 0.38rem;
          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;
            &:before {
              content: '';
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }
        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }
  }

  .lottery {
    position: absolute;
    left: 2.76rem;
    float: left;
    width: 0.94rem;
    height: 0.86rem;
    top: 0.73rem;
    border: 0;
  }

  .schoolAdvantage {
    width: 3.75rem;
    height: 3.55rem;
    background: url('../../../../static/schoolHome/commonBg.png');
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
    button {
      border: none;
      width: 1.8rem;
      height: 0.4rem;
      background: url('../../../../static/schoolHome/commonBtn.png') no-repeat center;
      background-size: 100% 100%;
      position: absolute;
      bottom: 0.5rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .content {
      width: 2.95rem;
      height: 3.55rem;
      border-radius: 10px;
      margin: 0 auto;
      margin-top: 0.2rem;
      overflow: hidden;
      text-align: left;
      .contentTile {
        width: 1.45rem;
        height: 0.32rem;
        position: absolute;
        background: linear-gradient(318deg, rgba(240, 110, 108, 1) 0%, rgba(240, 145, 144, 1) 100%);
        box-shadow: 0px 2px 4px 0px rgba(240, 110, 108, 0.2), 0px 1px 1px 0px rgba(255, 255, 255, 0.5);
        border-radius: 0px 0px 10px 10px;
        font-size: 0.17rem;
        font-weight: 600;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        margin-left: 1rem;
        margin-top: -0.05rem;
      }
      .title {
        width: 1.43rem;
        height: 0.2rem;
        background: url('../../../assets/image/active/gdJinRong/<EMAIL>') no-repeat center;
        margin: 0 auto;
        margin-top: 0.7rem;
        background-size: 100% 100%;
        text-align: center;
        color: rgba(23, 6, 6, 0.8);
        span {
          text-decoration: line-through;
        }
      }
      .detail {
        margin-top: 1.2rem;
        .items {
          width: 2.7rem;
          margin: 0.01rem auto 0rem;
          height: auto;
          overflow: hidden;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 0.02rem;
          padding: 0.05rem 0.08rem 0rem 0.05rem;
          margin-top: 0.19rem;
          &:last-of-type {
            margin-bottom: 0;
          }
          i {
            font-style: normal;
            float: left;
            width: 0.26rem;
            margin-left: 0.1rem;
            img {
              float: left;
              width: 0.14rem;
              height: 0.13rem;
              margin-top: 0.07rem;
              margin-right: 0.12rem;
            }
          }
          p {
            width: 2.21rem;
            float: left;
            color: rgba(54, 54, 54, 0.8);
            font-size: 0.14rem;
            text-align: left;
            margin-top: 0.03rem;
            margin-left: 0;
          }
        }
        p {
          margin-left: 0.35rem;
          color: rgba(23, 6, 6, 0.4);
          margin-top: 0.08rem;
        }
      }
    }
  }
  .charge {
    background: rgb(255, 255, 255);
    .chargetop {
      background: #fff;
      width: 3.75rem;
      height: 0.6rem;
      line-height: 0.6rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-left: 0.1rem;
        margin-top: -0.05rem;
      }
      p {
        display: inline-block;
        font-size: 0.17rem;
        height: 0.24rem;
        line-height: 0.24rem;
        color: #170606;
      }
    }

    .region {
      margin-top: 0.05rem;
      .regionTop {
        position: relative;
        height: 0.24rem;
        line-height: 0.24rem;
        .line {
          display: inline-block;
          width: 0.04rem;
          height: 0.16rem;
          background-color: #f06e6c;
          margin-left: 0.4rem;
        }
        .regionTitle {
          position: absolute;
          display: inline-block;
          font-size: 14px;
          color: rgba(23, 6, 6, 1);
          font-weight: 400;
          left: 0.54rem;
          top: -0.02rem;
        }
      }
      .regionText {
        p {
          margin-left: 0.54rem;
          display: inline-block;
          width: 2.94rem;
          font-size: 14px;
        }
      }
    }
  }

  .highUniversityBox {
    background: #fff;
    padding-bottom: 0.1rem;
    overflow: hidden;
    .title {
      background: #fff;
      width: 100%;
      height: 0.48rem;
      .fl {
        width: 50%;
        height: 0.48rem;
        float: left;
        position: relative;
        .flTop {
          height: 0.21rem;
          line-height: 0.21rem;
          .line {
            position: absolute;
            display: inline-block;
            width: 0.04rem;
            height: 0.04rem;
            background-color: #f06e6c;
            top: 0.05rem;
            left: 23%;
          }
          .schoolType {
            position: absolute;
            display: inline-block;
            font-size: 14px;
            color: rgba(23, 6, 6, 0.4);
            font-weight: 400;
            left: 0.54rem;
            top: -0.02rem;
          }
        }
        .year {
          padding-left: 0.54rem;
          font-size: 0.2rem;
          color: rgba(23, 6, 6, 0.6);
        }
        span {
          color: #f06e6c;
          color: rgba(23, 6, 6, 0.6);
        }
      }
      .fr {
        width: 50%;
        height: 0.48rem;
        float: left;
        position: relative;
        .frTop {
          height: 0.21rem;
          line-height: 0.21rem;
          .line {
            position: absolute;
            display: inline-block;
            width: 0.04rem;
            height: 0.04rem;
            background-color: #f06e6c;
            top: 0.05rem;
            left: 14%;
          }
          .bookPrice {
            position: absolute;
            display: inline-block;
            font-size: 14px;
            color: rgba(23, 6, 6, 0.4);
            font-weight: 400;
            left: 0.35rem;
            top: -0.02rem;
            span {
              font-size: 12px;
              color: rgba(23, 6, 6, 0.4);
            }
          }
        }
        .bookPriceYear {
          padding-left: 0.35rem;
          font-size: 0.2rem;
          color: rgba(23, 6, 6, 0.6);
        }
        span {
          color: #f06e6c;
          color: rgba(23, 6, 6, 0.6);
        }
      }
    }
    .highUniversity {
      margin: auto 0.1rem;
      background-color: #fff;
      border-radius: 0.1rem;
      .title {
        // width: 3.35rem;
        height: 0.5rem;
        line-height: 0.5rem;
        text-align: center;
        border-radius: 0.1rem 0.1rem 0rem 0rem;
        background: rgba(240, 110, 108, 0.0798);
        border: 1px solid rgba(240, 110, 108, 0.4);
        border-bottom: none;
        .lineLeft {
          width: 0.1rem;
          height: 0.02rem;
          border-radius: 0.01rem;
          background: #f06e6c;
        }
        .text {
          font-size: 0.17rem;
          color: #f06e6c;
          font-weight: 600;
        }
      }
      table {
        width: 3.55rem;
        border-collapse: collapse;
        td {
          text-align: center;
          vertical-align: middle;
          border: 1px solid rgba(240, 110, 108, 0.4);
          padding: 0.1rem 0;
        }
      }
      .content {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          height: 0.8rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.42rem;
            text-align: center;
            margin-top: 55.5%;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          height: 0.8rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          .contentText {
            display: inline-block;
            width: 1.86rem;
            margin: 0.1rem 0.17rem 0.1rem 0.13rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 32.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
      .contentTwo {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          float: left;
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.42rem;
            text-align: center;
            margin-top: 110%;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          border-left: 1px solid rgba(240, 110, 108, 0.4);
          .contentText {
            display: inline-block;
            width: 1.86rem;
            margin: 0.1rem 0.17rem 0.1rem 0.13rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 40.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
      .contentThree {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          height: 0.58rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.58rem;
            text-align: center;
            line-height: 0.58rem;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          height: 0.58rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          line-height: 0.58rem;
          .contentText {
            display: inline-block;
            width: 1.86rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
            padding-left: 0.1rem;
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 20.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
    }
  }

  .clear {
    clear: both;
  }
  .story {
    background: rgb(255, 255, 255);
    margin-top: 0.1rem;
    height: 2.74rem;
    margin-bottom: 1rem;
    .top {
      width: 3.75rem;
      height: 0.5rem;
      line-height: 0.5rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-top: -0.05rem;
        margin-left: 0.1rem;
      }
      span {
        font-size: 0.17rem;
      }
    }

    .content {
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      position: relative;
      height: 2.26rem;
      .item {
        display: inline-block;
        width: 1.55rem;
        margin-left: 0.1rem;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.04),
          0px 2px 6px 0px rgba(0, 0, 0, 0.04);
        border-radius: 5px;
        height: 2.16rem;
        position: relative;
        img {
          width: 1.55rem;
          height: 1.22rem;
          border-radius: 5px 5px 0px 0px;
          object-fit: cover;
        }
        p {
          margin-left: 0.05rem;
        }
        .text {
          width: 1.39rem;
          font-size: 0.13rem;
          margin-top: 0.08rem;
          color: rgba(23, 6, 6, 0.8);
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
        .date {
          position: absolute;
          font-size: 0.12rem;
          color: rgba(23, 6, 6, 0.4);
          bottom: 0.02rem;
        }
      }
    }
  }
  .userMessage {
    background: rgb(255, 255, 255);
    margin-top: 0.1rem;
    margin-bottom: 0.6rem;
    .top {
      width: 3.75rem;
      height: 0.7rem;
      line-height: 0.7rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }
      .topTitle {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }
    .textContent {
      overflow: hidden;
      height: 4.1rem;
      .userMessageContentList {
        width: 3.75rem;
        &.anim {
          transition: all 1s;
          margin-top: -1.7rem;
        }
        .content {
          overflow: hidden;
          height: 1.33rem;
          position: relative;
          .line {
            position: absolute;
            bottom: 0.01rem;
            height: 1px;
            width: 2.76rem;
            left: 0.64rem;
            background-color: rgba(23, 6, 6, 0.08);
          }
          .fl {
            width: 0.64rem;
            img {
              width: 0.38rem;
              height: 0.38rem;
              float: right;
              border-radius: 50%;
              margin-top: 0.11rem;
            }
          }
          .fr {
            width: 3.11rem;
            height: 1.14rem;
            .userName {
              margin-left: 0.1rem;
              margin-top: 0.12rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }
            .uesrQuestion {
              display: inline-block;
              width: 2.76rem;
              margin-top: 0.03rem;
              margin-left: 0.1rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }
            .content {
              position: relative;
              margin-bottom: 0.09rem;
              height: auto;

              .line {
                position: absolute;
                display: inline-block;
                width: 0.02rem;
                height: 0.14rem;
                background-color: #f06e6c;
                top: 0.17rem;
                left: 0.01rem;
              }
              .answer {
                display: inline-block;
                background: rgba(248, 248, 248, 1);
                border-radius: 3px 3px 3px 0px;
                width: 2.86rem;
                font-size: 0.14rem;
                color: rgba(23, 6, 6, 0.8);
                padding: 0.13rem 0.07rem 0.13rem 0.1rem;
              }
            }
          }
        }
      }
    }

    .userMessageContent {
      width: 3.75rem;
      overflow: hidden;
      .fl {
        width: 0.64rem;
        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }
      .fr {
        width: 3.11rem;
        position: relative;
        .userName {
          margin-left: 0.1rem;
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
          display: inline-block;
          width: 2.48rem;
          height: 0.24rem;
        }
        textarea {
          width: 2.87rem;
          height: 0.86rem;
          border-radius: 0px 5px 5px 5px;
          // opacity:0.4;
          padding: 0.13rem 0.1rem 0.13rem 0.1rem;
          margin-top: 0.03rem;
        }
        span {
          position: absolute;
          font-size: 0.09rem;
          color: rgba(23, 6, 6, 0.4);
          top: 1.07rem;
          right: 0.305rem;
        }
        button {
          width: 1rem;
          height: 0.35rem;
          background: rgba(240, 110, 108, 1);
          border: 0;
          margin-top: 0.1rem;
          margin-left: 0.89rem;
          margin-bottom: 0.2rem;
          border-radius: 0.2rem;
          color: #ffffff;
          font-size: 0.16rem;
          font-weight: 500;
        }
      }
    }
  }
  .InvitationBg {
    position: relative;
    img {
      width: 3.75rem;
      height: 1.53rem;
      // margin-top: .05rem;
    }
    button {
      position: absolute;
      width: 1.2rem;
      height: 0.35rem;
      background: rgba(255, 255, 255, 1);
      border: 0;
      border-radius: 0.2rem;
      color: #f06e6c;
      font-size: 0.15rem;
      font-weight: 500;
      top: 0.87rem;
      left: 0.32rem;
    }
  }
  .QRCode {
    position: relative;
    width: 3.75rem;
    height: 2.08rem;
    background: rgb(255, 255, 255);
    .bg {
      width: 3.75rem;
      height: 1.66rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .QRCodeImg {
      position: absolute;
      width: 0.75rem;
      height: 0.74rem;
      left: 2.02rem;
      top: 0.6rem;
    }
    .topText {
      position: absolute;
      font-size: 0.12rem;
      color: rgba(23, 6, 6, 0.8);
      left: 1.99rem;
      top: 0.35rem;
      font-weight: 600;
    }
    .bottomText {
      position: absolute;
      font-size: 0.12rem;
      color: rgba(23, 6, 6, 0.8);
      left: 1.99rem;
      top: 1.42rem;
      font-weight: 600;
    }
  }
  .fixBtn {
    position: fixed;
    bottom: 0.01rem;
    right: 0.01rem;
    width: 1.25rem;
    height: 1.27rem;
    line-height: 0.4rem;
    text-align: center;
    color: #fff;
    z-index: 999;
    background-image: url('../../../assets/image/active/enrollmentHomepage/<EMAIL>');
    background-size: 100%;
    border-radius: 10px;
    a {
      display: block;
      height: 100%;
      color: #fff;
      font-size: 21px;
      text-decoration: none;
    }
  }
  .bottomBox {
    background: rgb(255, 255, 255);
    width: 3.75rem;
    height: 0.5rem;
    line-height: 0.5rem;
    text-align: center;
    a {
      color: #f06e6c;
      font-size: 0.14rem;
    }
    span {
      color: #f06e6c;
      display: inline-block;
      margin-top: 0.01rem;
    }
    .callUs {
      margin-right: 0.1rem;
    }
    .official {
      margin-left: 0.1rem;
    }
  }
  .showall {
    height: 4.2rem;
  }
  .showall.active {
    height: auto;
  }
  .schoolDetails {
    width: 3.75rem;
    background: rgb(255, 255, 255);
    overflow: hidden;
    img {
      width: 3.55rem;
      height: 1.79rem;
      border-radius: 0.01rem;
      margin: 0.1rem 0.1rem 0rem 0.1rem;
    }
    p {
      display: inline-block;
      width: 3.15rem;
      margin: 0.2rem 0.3rem 0.1rem 0.3rem;
      padding: 0rem 0.05rem 0rem 0.05rem;
    }
  }
  .lookMore {
    background: rgb(255, 255, 255);
    padding-top: 0.06rem;
    text-align: center;
    height: 0.54rem;
    line-height: 0.54rem;
    position: relative;
    span {
      color: rgba(23, 6, 6, 0.4);
    }
    img {
      position: absolute;
      top: -0.03rem;
      left: 1.7rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .studentStory {
    background: rgb(255, 255, 255);
    height: 2.41rem;
    width: 3.75rem;
    margin-top: 0.1rem;
    .top {
      height: 0.7rem;
      width: 3.75rem;
      line-height: 0.7rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }
      span {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }
  }
}

.swiper {
  width: 100%;
  height: 160px;
  overflow: hidden;
  margin-top: 0.22rem;
}
.swiper-slide {
  width: 80%;
  height: 160px;
}
.swiper-slide-active img {
  margin-top: 0;
  width: 100%;
  height: 100%;
}
.swiperImg {
  display: block;
  margin: 0 auto;
  margin-top: 3.5%;
  width: 85%;
  height: 85%;
  border-radius: 5px;
}

.van-cell {
  height: 0.54rem;
  line-height: 0.54rem;
}
.lotteryRed {
  position: fixed;
  bottom: 1rem;
  right: 0.1rem;
  width: 0.87rem;
  transition: all 1s;
  z-index: 99;
}
.fixBottom {
  position: fixed;
  bottom: 0;
  height: 0.6rem;
  z-index: 9999;
  .leftBox {
    display: inline-block;
    width: 1.96rem;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;
    border-top: 1px solid #f06e6c;
    img {
      width: 0.39rem;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }
    span {
      display: inline-block;
    }
    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }
    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.13rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }
  .rightBox {
    display: inline-block;
    width: 1.79rem;
    height: 0.6rem;
    background: #f06e6c;
    float: left;
    position: relative;
    .line {
      position: absolute;
      display: inline-block;
      width: 1px;
      height: 0.25rem;
      background: rgba(255, 255, 255, 0.4);
      top: 0.16rem;
      left: 0.88rem;
    }
    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;
      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }
      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;
      img {
        opacity: 0.8;
        width: 0.24rem;
        margin-top: 0.1rem;
      }
      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
  }
}
</style>


