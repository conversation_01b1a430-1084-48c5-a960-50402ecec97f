import { loadPcdJson } from "../common";

export default [
  {
    path: "/about/privacy",
    name: "newAgreement",
    component: () => import("../view/account/agreement"),
    meta: {
      title: "远智教育会员协议",
    },
  },
  {
    path: "/QRCodeLogin",
    name: "QRCodeLogin",
    component: () => import("../view/account/QRCodeLogin"),
    meta: {
      title: "扫码登录",
    },
  },
  {
    path: "/basic/idcardBind",
    name: "idcardBind",
    component: () => import("../view/student/basic/idcardBind"),
    children: [
      {
        path: "agreement",
        name: "agreement",
        component: () => import("../view/account/agreement"),
        meta: {
          title: "远智教育会员协议",
        },
      },
    ],
    meta: {
      title: "绑定身份证",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/profile",
    name: "profile",
    component: () => import("../view/student/basic/profile"),
    meta: {
      title: "新生完善成考报名信息",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/diploma",
    name: "diploma",
    component: () => import("../view/student/basic/diploma"),
    meta: {
      title: "新生完善成考报名信息",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/idcardUpload",
    name: "idcardUpload",
    component: () => import("../view/student/basic/idcardUpload"),
    meta: {
      title: "新生完善成考报名信息",
      requiresAuth: true,
    },
  },
  {
    path: "/roleAuth",
    name: "roleAuth",
    component: () => import("../view/account/roleAuth"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student",
    name: "stuInfo",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智学堂-我的信息",
      requiresAuth: true,
      keepAlive: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/selfExaminationDelay",
    name: "selfExaminationDelay",
    component: () => import("../view/student/basic/selfExaminationDelay"),
    meta: {
      title: "自考延长服务期提醒",
      requiresAuth: true,
    },
  },
  {
    path: "/basic/completeMaterial",
    name: "completeMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/completeMaterial"),
    meta: {
      title: "完善资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/eduCompleteMaterial",
    name: "eduCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/eduCompleteMaterial"),
    meta: {
      title: "完善成教资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/newCompleteMaterial",
    name: "newCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/newCompleteMaterial"),
    meta: {
      title: "完善资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/selfCompleteMaterial",
    name: "selfCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/selfCompleteMaterial.vue"),
    meta: {
      title: "完善资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/graduateCompleteMaterial",
    name: "graduateCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () =>
      import("../view/student/basic/graduateCompleteMaterial.vue"),
    meta: {
      title: "完善资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/openCompleteMaterial",
    name: "openCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/openMaterial"),
    meta: { title: "完善资料", requiresAuth: true, loadDict: true },
  },
  {
    path: "/basic/middleCompleteMaterial",
    name: "middleCompleteMaterial",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/middleCompleteMaterial.vue"),
    meta: {
      title: "完善资料",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/basic/jobPage",
    name: "jobPage",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/basic/jobPage.vue"),
    meta: {
      title: "职位类别",
      requiresAuth: true,
      loadDict: true,
    },
  },
  /*{
  path: '/student/home/<USER>/:learnId',
  component: () => import('../view/student/home/<USER>'),
  meta: {title: '远智学堂-我要缴费', requiresAuth: true}
},*/
  {
    path: "/pay/tuition",
    name: "stuPayment",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智学堂-我要缴费",
      requiresAuth: false,
    },
    children: [
      {
        path: "alipay",
        name: "alipayment",
        component: () => import("../view/student/home/<USER>"),
        meta: {
          title: "支付宝支付",
          requiresAuth: false,
        },
      },
    ],
  },
  {
    path: "/pay/stagesPay",
    name: "stagesPay",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "分期",
      requiresAuth: false,
    },
  },
  {
    path: "/pay/postagePayment",
    name: "postagePayment",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "我要缴费",
      requiresAuth: false,
      // keepAlive: true
    },
    children: [
      {
        path: "alipay",
        name: "alipaymentPost",
        component: () => import("../view/student/home/<USER>"),
        meta: {
          title: "支付宝支付",
          requiresAuth: false,
          isHideBtn: true,
        },
      },
    ],
  },
  // {
  //   path:'/pay/wxfriendPay',
  //   name:'wxfriendpay',
  //   component: () => import('../view/student/home/<USER>'),
  //   meta:{title:'邀请好友代付',requiresAuth:false},
  // },
  {
    path: "/pay/tuition/success",
    name: "stuPaymentSuccess",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智学堂-报读成功",
      requiresAuth: true,
    },
  },
  {
    path: "/pay/paySuccess",
    name: "paySuccess",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "缴费成功-远智教育",
      requiresAuth: true,
    },
  },
  {
    path: "/pay/tePaySuccess",
    name: "tePaySuccess",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "缴费成功-远智教育",
      requiresAuth: true,
    },
  },
  {
    path: "/student/stuPaylist/:learnId",
    name: "stuPaylist",
    component: () => import("../view/student/home/<USER>"),
    children: [
      {
        path: "receipt/express",
        name: "receiptExpressAddr",
        component: () => import("../view/student/receipt/express"),
        beforeEnter: loadPcdJson,
        meta: {
          title: "申请纸质收据",
          requiresAuth: true,
        },
      },
      {
        path: "receipt/campus",
        name: "campusReceiptAddr",
        component: () => import("../view/student/receipt/campus"),
        beforeEnter: loadPcdJson,
        meta: {
          title: "申请纸质收据",
          requiresAuth: true,
        },
      },
      {
        path: "receipt/result",
        name: "applyReceiptResult",
        component: () => import("../view/student/receipt/result"),
        meta: {
          title: "申请纸质收据",
          requiresAuth: true,
        },
      },
    ],
    meta: {
      title: "远智学堂",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/pay/postage",
    name: "payPostage",
    component: () => import("../view/student/receipt/payment"),
    meta: {
      title: "申请纸质收据",
      requiresAuth: true,
    },
  },
  {
    path: "/student/subOrderList",
    name: "subOrderList",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: " ",
      requiresAuth: true,
    },
  },
  {
    path: "/student/myaccount",
    name: "myAccount",
    component: () => import("../view/student/account/myAccount"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/myaccount/apply",
    name: "myAccountApply",
    component: () => import("../view/student/account/myAccountApply"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/myaccount/record",
    name: "myAccountRecord",
    component: () => import("../view/student/account/myAccountRecord"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask",
    name: "myTask",
    component: () => import("../view/student/myTask/mytask"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/address",
    name: "selectAddressTask",
    component: () => import("../view/student/myTask/children/selectAddress"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/examRChoose/roomChoose",
    name: "examRChoose",
    component: () => import("../view/student/myTask/examRChoose/roomChoose"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/examRChoose/confirmInfo",
    name: "confirmExamRInfo",
    component: () => import("../view/student/myTask/examRChoose/confirmInfo"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/exam/room",
    name: "examRoomInfo",
    component: () => import("../view/student/myTask/examRChoose/examRoomInfo"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/exam/room/print",
    name: "examRoomInfoPrint",
    component: () => import("../view/student/myTask/examRChoose/print"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/openacademicDegree",
    name: "openacademicDegree",
    component: () => import("../view/student/myTask/openacademicDegree/index"),
    meta: {
      title: "学士学位英语报名通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/openacademicDegree/text",
    name: "openacademicDegree/text",
    component: () => import("../view/student/myTask/openacademicDegree/text"),
    meta: {
      title: "学士学位英语报名通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/exam/room/examMap",
    name: "examMap",
    component: () => import("../view/student/myTask/examRChoose/examMap"),
    meta: {
      title: "远智学堂-我的任务",
    },
  },
  {
    path: "/student/mytask/exam/room/makeup",
    name: "makeup",
    component: () => import("../view/student/myTask/examRChoose/makeup"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/unComplete",
    name: "unComplete",
    component: () => import("../view/student/myTask/unComplete"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/taskInfo",
    name: "taskInfo",
    component: () => import("../view/student/myTask/taskInfo"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/confirm/address",
    name: "confirmAddress",
    component: () => import("../view/student/myTask/confirmAddress/confirm"),
    children: [
      {
        path: "choose",
        name: "toChooseAddress",
        component: () =>
          import("../view/student/myTask/confirmAddress/chooseAddress"),
        beforeEnter: loadPcdJson,
        meta: {
          title: "远智学堂-我的任务",
          requiresAuth: true,
        },
      },
    ],
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/myCurriculum",
    name: "myCurriculum",
    component: () => import("../view/student/myCurriculum/myCurriculum"),
    meta: {
      title: "远智学堂-我的课表",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/classCheck",
    name: "classCheck",
    component: () => import("../view/student/myCurriculum/classCheck"),
    meta: {
      title: "我的课表",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/newCurriculum",
    name: "newCurriculum",
    component: () => import("../view/student/myCurriculum/newCurriculum"),
    meta: {
      title: "我的课表",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/more",
    name: "more",
    component: () => import("../view/student/more/more"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/more/curriculResources",
    name: "curriculResources",
    component: () => import("../view/student/more/curriculResources"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/myscores",
    name: "myscores",
    component: () => import("../view/student/more/myscores"),
    meta: {
      title: "成考成绩",
      requiresAuth: true,
    },
  },
  {
    path: "/myfulltimeScores",
    name: "myfulltimeScores",
    component: () => import("../view/student/more/myfulltimeScores"),
    meta: {
      title: "全日制成绩",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer",
    name: "offer",
    component: () => import("../view/student/offer/offer"),
    children: [
      {
        path: "notice",
        name: "offerNotice",
        component: () => import("../view/student/offer/notice"),
        meta: {
          title: "2024级新生缴费注册通知",
          requiresAuth: true,
        },
      },
    ],
    meta: {
      title: "2024级新生缴费注册通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/payGuide",
    name: "schoolPayGuide",
    component: () => import("../view/student/offer/schoolPayGuide"),
    meta: {
      title: "学费缴纳指引",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/201803",
    name: "offer201803",
    component: () => import("../view/student/offer/offer2"),
    meta: {
      title: "201803级新生通知书查看通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/201809",
    name: "offer201809",
    component: () => import("../view/student/offer/offer2"),
    meta: {
      title: "201809级新生通知书查看通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/201903",
    name: "offer201903",
    component: () => import("../view/student/offer/offer2"),
    meta: {
      title: "201903级新生通知书查看通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/201909",
    name: "offer201909",
    component: () => import("../view/student/offer/offer2"),
    meta: {
      title: "201909级新生通知书查看通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/offer/202103",
    name: "offer202103",
    component: () => import("../view/student/offer/offer2"),
    meta: {
      title: "202103级新生通知书查看通知",
      requiresAuth: true,
    },
  },
  {
    path: "/student/studentOffer",
    name: "studentOffer",
    component: () => import("../view/student/offer/offer2"),
    meta: { requiresAuth: true },
  },
  {
    path: "/student/application",
    name: "myApplication",
    component: () => import("../view/student/more/myApplication"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/material",
    name: "materialApplyList",
    component: () => import("../view/student/material/applyList"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/material/apply",
    name: "materialApply",
    component: () => import("../view/student/material/apply"),
    children: [
      {
        path: "filename",
        name: "fileName",
        component: () => import("../view/student/material/children/fileName"),
      },
    ],
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/major/change",
    name: "majorChange",
    component: () => import("../view/student/major/change"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
      loadDict: true,
    },
  },

  {
    path: "/student/diploma",
    name: "getDiploma",
    component: () => import("../view/student/myTask/diploma/getDiploma"),
    meta: {
      title: "毕业证领取",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/graduate/submit/data",
    name: "submitGraduateData",
    component: () => import("../view/student/myTask/graduate/submitData"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/apply/registration/form",
    name: "applyRegistrationRorm",
    component: () => import("../view/student/myTask/graduate/registrationForm"),
    children: [
      {
        path: "address",
        name: "registrationRormAddress",
        component: () =>
          import("../view/student/myTask/graduate/selectAddress"),
        beforeEnter: loadPcdJson,
        meta: {
          title: "远智学堂-我的任务",
          requiresAuth: true,
        },
      },
    ],
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/exam/checkin",
    component: () => import("../view/student/myTask/examCheckin/result"),
    meta: {
      title: "考场签到",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/thesis",
    name: "thesisNotice",
    component: () => import("../view/student/myTask/thesis/notice"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/examCheck",
    name: "examCheck",
    component: () => import("../view/examCheck/examCheck"),
    meta: {
      title: "考场签到",
      requiresAuth: true,
    },
  },
  {
    path: "/examCheckFalse",
    name: "examCheckFalse",
    component: () => import("../view/examCheck/examCheckFalse"),
    meta: {
      title: "签到失败",
    },
  },
  {
    path: "/exam/checkin/search",
    component: () => import("../view/student/myTask/examCheckin/search"),
    meta: {
      title: "考场代签到",
      requiresAuth: true,
    },
  },
  {
    path: "/exam/checkin/search/result",
    name: "examCheckinResult",
    component: () => import("../view/student/myTask/examCheckin/result"),
    meta: {
      title: "考场代签到",
    },
  },
  {
    path: "/student/isInfoCheck/isInfoCheck",
    name: "isInfoCheck",
    component: () => import("../view/student/myTask/isInfoCheck/isInfoCheck"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/isInfoCheck/flowChart",
    name: "isInfoflowChart",
    component: () => import("../view/student/myTask/isInfoCheck/flowChart"),
    meta: {
      title: "远智学堂-我的任务",
    },
  },
  {
    path: "/student/live/course",
    name: "liveCourse",
    component: () => import("../view/student/live/course"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/live/newcourse",
    name: "newliveCourse",
    component: () => import("../view/student/live/newcourse"),
    meta: {
      title: "远智学堂",
      requiresAuth: true,
    },
  },
  {
    path: "/student/transcript",
    name: "myTranscript",
    component: () => import("../view/student/more/myTranscript"),
    meta: {
      title: "远智学堂-我的成绩",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/GKTranscript",
    name: "GKTranscript",
    component: () => import("../view/student/more/GKTranscript"),
    meta: { title: "远智学堂-我的成绩", requiresAuth: true, loadDict: true },
  },
  {
    path: "/student/graduationResult",
    name: "graduationResult",
    component: () => import("../view/student/more/graduationResult"),
    meta: {
      title: "论文答辩结果",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/myscore",
    name: "myscore",
    component: () => import("../view/student/more/myscore"),
    meta: {
      title: "远智学堂-我的成绩",
      requiresAuth: true,
    },
  },
  {
    path: "/student/myEnglish",
    name: "myEnglish",
    component: () => import("../view/student/more/myEnglish"),
    meta: {
      title: "远智学堂-我的成绩",
      requiresAuth: true,
    },
  },
  {
    path: "/student/receipt",
    name: "receiptUp",
    component: () => import("../view/student/more/receipt"),
    meta: {
      title: "远智学堂-上传报名回执",
      requiresAuth: true,
    },
  },
  {
    path: "/student/applicationProve",
    name: "applicationProve",
    /*children: [
    {
      path: 'reUpload',
      name: 'applicationProvereUpload',
      component: () => import('../view/student/more/applicationProve/reUpload'),
      meta: {title: '远智学堂-我的申请'}
    }
  ],*/
    component: () =>
      import("../view/student/more/applicationProve/applicationProve"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的申请",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/applicationProve/applicationProveInfo",
    name: "applicationProveInfo",
    component: () =>
      import("../view/student/more/applicationProve/applicationProveInfo"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "查看详情",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/applicationProve/applicationInvoiceInfo",
    name: "applicationInvoiceInfo",
    component: () =>
      import("../view/student/more/applicationProve/invoiceInfo"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "查看详情",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/applicationProve/invoiceApplyGuide",
    name: "invoiceApplyGuide",
    component: () =>
      import("../view/student/more/applicationProve/invoiceApplyGuide"),
    meta: {
      title: "发票申请指南",
    },
  },
  {
    path: "/student/applicationProve/proveAddress",
    name: "proveAddress",
    component: () => import("../view/student/more/applicationProve/address"),
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的申请",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/BachelorEnglish",
    name: "BachelorEnglish",
    component: () =>
      import("../view/student/myTask/BachelorEnglish/BachelorEnglish.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/BachelorSign",
    name: "BachelorSign",
    component: () =>
      import("../view/student/myTask/BachelorEnglish/BachelorSign.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/BachelorResult",
    name: "BachelorResult",
    component: () =>
      import("../view/student/myTask/BachelorEnglish/BachelorResult.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/courseInform",
    name: "courseInform",
    component: () => import("../view/student/myTask/courseInform/courseInform"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/courseInform/checkAdmissionGuide",
    name: "checkAdmissionGuide",
    component: () =>
      import("../view/student/myTask/courseInform/checkAdmissionGuide.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/courseInform/guokaiGuide",
    name: "guokaiGuide",
    component: () =>
      import("../view/student/myTask/courseInform/guokaiGuide.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/newbornData",
    name: "newbornData",
    component: () =>
      import("../view/student/myTask/newbornData/newbornData.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/qingshu/guide",
    name: "qingshuGuide",
    component: () => import("../view/student/myTask/qingshu/guide"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/TestSite",
    name: "TestSite",
    component: () => import("../view/student/myTask/TestSite/TestSite"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/bkExamSign",
    name: "bkExamSign",
    component: () => import("../view/student/myTask/bkExamSign/bkExamSign"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/diplomaReceive",
    name: "diplomaReceive",
    beforeEnter: loadPcdJson,
    component: () =>
      import("../view/student/myTask/diplomaReceive/diplomaReceive"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/diplomaReceive/saveArchive",
    name: "saveArchive",
    beforeEnter: loadPcdJson,
    meta: {
      title: "远智学堂-我的任务",
    },
    component: () => import("../view/student/myTask/diplomaReceive/saveArchive"),
  },
  {
    path: "/student/mytask/diplomaReceive/onsite",
    name: "onsite",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/diplomaReceive/onsite"),
  },
  {
    path: "/student/mytask/diplomaReceive/shunfeng",
    name: "shunfeng",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/diplomaReceive/shunfeng"),
  },
  {
    path: "/student/mytask/examConfirm",
    name: "examConfirm",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/examConfirm/examConfirm"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/spotInvite",
    name: "spotInvite",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/spotInvite/spotInvite"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/spotInviteDes",
    name: "spotInviteDes",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/spotInvite/spotInviteDes"),
    meta: {
      title: "远智学堂-我的任务",
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/spotInviteMap",
    name: "spotInviteMap",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/spotInvite/spotInviteMap"),
    meta: {
      title: "远智学堂-我的任务",
      loadDict: true,
    },
  },
  {
    path: "/student/mytask/signQr",
    name: "examSignQr",
    beforeEnter: loadPcdJson,
    component: () => import("../view/student/myTask/signQr/signScan"),
    meta: {
      title: "远智学堂-我的任务",
    },
  },
  {
    path: "/student/mytask/informationCheck",
    name: "informationCheck",
    component: () => import("../view/student/myTask/informationCheck"),
    meta: {
      title: "",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/receivingGoodsConfirm",
    name: "receivingGoodsConfirm",
    component: () => import("../view/student/myTask/receivingGoodsConfirm"),
    meta: {
      title: "教材收货确认通知",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam",
    name: "entranceExam",
    component: () => import("../view/student/myTask/entranceExam/home"),
    children: [
      {
        path: "idCardCheck",
        name: "idCardCheck",
        // component: resolve => require.ensure([], () => require('../view/student/myTask/entranceExam/idCardCheck'), 'entranceExam'),
        component: () =>
          import("../view/student/myTask/entranceExam/idCardCheck"),
        meta: {
          title: "入学水平测试",
          requiresAuth: true,
          loadDict: true,
        },
      },
    ],
    meta: {
      title: "真题大作战",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam/entrance",
    name: "entrance",
    component: () => import("../view/student/myTask/entranceExam/entrance"),
    meta: {
      title: "入学水平测试",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam/begintest",
    name: "begintest",
    component: () => import("../view/student/myTask/entranceExam/project"),
    meta: {
      title: "入学水平测试",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam/test",
    name: "test",
    component: () => import("../view/student/myTask/entranceExam/test"),
    meta: {
      title: "入学水平测试",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam/showAnswer",
    name: "showAnswer",
    component: () => import("../view/student/myTask/entranceExam/showAnswer"),
    meta: {
      title: "入学水平测试",
      requiresAuth: true,
    },
  },
  {
    path: "/entranceExam/score",
    name: "entranceExamScore",
    component: () => import("../view/student/myTask/entranceExam/score"),
    meta: {
      title: "入学水平测试",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/noticeConfirm",
    name: "noticeConfirm",
    component: () => import("../view/student/myTask/notice/noticeConfirm.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/questionlist",
    name: "questionlist",
    component: () => import("../view/student/myTask/question/questionlist.vue"),
    meta: {
      title: "远智学堂-我的任务",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/questiondetail",
    name: "questiondetail",
    component: () =>
      import("../view/student/myTask/question/questiondetail.vue"),
    meta: {
      title: "问卷调查",
      requiresAuth: true,
    },
  },
  {
    path: "/student/mytask/certificatesCollect",
    name: "certificatesCollect",
    component: () => import("../view/student/myTask/certificatesCollect"),
    meta: {
      title: "照片资料收集",
      requiresAuth: false,
    },
  },
  {
    path: "/exam/uploadExamSign",
    name: "uploadExamSign",
    component: () =>
      import("../view/student/myTask/examCheckin/uploadExamSign"),
    meta: {
      title: "成教考场签到表上传",
      requiresAuth: false,
    },
  },
  {
    path: "/exam/GKuploadExamSign",
    name: "GKuploadExamSign",
    component: () =>
      import("../view/student/myTask/examCheckin/GKuploadExamSign"),
    meta: {
      title: "国开考场签到表上传",
      requiresAuth: false,
    },
  },
  {
    path: "/student/mytask/oneQuestion",
    name: "oneQuestion",
    component: () => import("../view/student/myTask/oneQuestion.vue"),
    meta: {
      title: "选择毕业论文题目",
      requiresAuth: true,
    },
  },
  {
    path: "/pay/coustomPay",
    name: "coustomPay",
    component: () => import("../view/student/myTask/coustomPay.vue"),
    meta: {
      title: "费用缴纳",
      requiresAuth: false,
    },
    children: [
      {
        path: "alipay",
        name: "alipaymentCoustomPay",
        component: () => import("../view/student/home/<USER>"),
        meta: {
          title: "支付宝支付",
          requiresAuth: false,
        },
      },
    ],
  },
  {
    path: "/student/mytask/payInfoPage",
    name: "payInfoPage",
    component: () => import("../view/student/myTask/payInfoPage.vue"),
    meta: {
      title: "费用详情",
      requiresAuth: true,
    },
  },
  {
    path: "/toExam",
    name: "toExam",
    component: () => import("../view/exam/toExam.vue"),
    meta: {
      title: "远智教育",
      requiresAuth: true,
    },
  },
  // {
  //   path: '/student/agreement',
  //   name: 'student.agreement',
  //   component: () => import('../view/student/agreement'),
  //   meta: {
  //     title: '远智教育用户协议',
  //     loadDict:true,
  //   },
  // },
  {
    path: "/student/selfAgreement",
    name: "student.selfAgreement",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智教育用户协议",
    },
  },
  {
    path: "/student/graduateAgreement",
    name: "student.graduateAgreement",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智教育用户协议",
    },
  },
  // 协议通用地址，只需要传一个pdf文件过去，就可以展示
  {
    path: "/student/generalAgreements",
    name: "student.generalAgreements",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智教育用户协议",
    },
  },
  {
    path: "/student/guidePageLogin",
    name: "student.guidePage",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "登录/注册",
    },
  },
  {
    path: "/pay/examPay",
    name: "examPay",
    component: () => import("../view/student/myTask/guokaiExam/examPay.vue"),
    meta: { title: "约考费用", requiresAuth: false },
    children: [
      {
        path: "alipay",
        name: "alipaymentExamPayPay",
        component: () => import("../view/student/home/<USER>"),
        meta: { title: "支付宝支付", requiresAuth: false },
      },
    ],
  },
  // {
  //   path: '/student/mytask/payInfoPage',
  //   name: 'payInfoPage',
  //   component: () => import('../view/student/myTask/payInfoPage.vue'),
  //   meta: {title: '费用详情',requiresAuth: true}
  // },
  {
    path: "/student/mytask/guokaiExam",
    name: "guokaiExam",
    component: () => import("../view/student/myTask/guokaiExam/index.vue"),
    meta: { title: "考试预约", requiresAuth: true, loadDict: true },
  },
  {
    path: "/student/more/currentMask",
    name: "currentMask",
    component: () => import("../view/student/more/currentMask.vue"),
    meta: { title: "本学期任务", requiresAuth: true, loadDict: true },
  },
  {
    path: "/student/applicationProcess",
    name: "applicationProcess",
    component: () => import("../view/student/home/<USER>"),
    meta: { title: "成教报读流程" },
  },
  {
    path: "/settings/sendStamps/ScholarshipStamps",
    name: "ScholarshipStamps",
    component: () =>
      import("../view/settings/sendStamps/ScholarshipStamps.vue"),
    meta: { title: "奖学金" },
  },
  {
    path: "/settings/sendStamps/success",
    name: "success",
    component: () => import("../view/settings/sendStamps/success.vue"),
    meta: { title: "奖学金" },
  },
  {
    path: "/settings/sendStamps/SendVoucherTool",
    name: "SendVoucherTool",
    component: () => import("../view/settings/sendStamps/SendVoucherTool.vue"),
    meta: { title: "派券工具" },
  },
  {
    path: "/settings/sendStamps/share",
    name: "SendVoucherToolShare",
    component: () => import("../view/settings/sendStamps/share.vue"),
    meta: { title: "派券工具" },
  },
  {
    path: "/student/progress",
    name: "progress",
    component: () => import("../view/student/progress"),
    meta: { title: "上进学习历程" },
  },
  {
    path: "/student/mytask/nrsImage",
    name: "nrsImage",
    component: () => import("../view/student/myTask/nrsImage"),
    meta: {
      title: "成教资料提交系统",
      requiresAuth: true,
    },
  },
  {
    path: "/student/schooldata",
    name: "schooldata",
    component: () => import("../view/student/schooldata/guokai"),
    meta: {
      title: "国开资料",
      // requiresAuth: true
    },
  },
  {
    path: "/student/schooldata/new",
    name: "schooldata2",
    component: () => import("../view/student/schooldata/index"),
    meta: {
      title: "成教资料",
      requiresAuth: true,
    },
  },
  {
    path: "/student/schooldata/old",
    name: "schooldata3",
    component: () => import("../view/student/schooldata/old"),
    meta: {
      title: "成教资料",
      requiresAuth: true,
    },
  },
  {
    path: "/student/schooldata/feedback",
    name: "feedback",
    component: () => import("../view/student/schooldata/feedback"),
    meta: {
      title: "成教资料",
      requiresAuth: true,
    },
  },
  {
    path: "/student/schooldata/finish",
    name: "schooldata.finish",
    component: () => import("../view/student/schooldata/finish"),
    meta: {
      title: "成教资料",
      requiresAuth: true,
    },
  },
  {
    path: "/student/studySuggestions",
    name: "questionnaire",
    redirect: "/student/studySuggestions/questionnaire",
    component: () => import("../view/student/studySuggestions/index"),
    meta: {
      title: "学习建议",
    },
    children: [
      {
        path: "questionnaire",
        name: "questionnaire",
        component: () =>
          import("../view/student/studySuggestions/questionnaire"),
        meta: {
          title: "学习建议",
          // requiresAuth: true
        },
      },
      {
        path: "normalResult",
        name: "normalResult",
        component: () =>
          import("../view/student/studySuggestions/normalResult"),
        meta: {
          title: "学习建议",
        },
      },
      {
        path: "abnormalResult",
        name: "abnormalResult",
        component: () =>
          import("../view/student/studySuggestions/abnormalResult"),
        meta: {
          title: "学习建议",
        },
      },
      {
        path: "elective",
        name: "elective",
        component: () => import("../view/student/studySuggestions/elective"),
        meta: {
          title: "学习建议",
        },
      },
      {
        path: "result",
        name: "result",
        component: () => import("../view/student/studySuggestions/result"),
        meta: {
          title: "学习建议",
        },
      },
      {
        path: "/student/guidePage",
        name: "guidePage",
        component: () => import("../view/student/guidePage"),
        meta: {
          title: "说明指导页",
        },
      },
    ],
  },
  {
    path: "/student/audition",
    name: "audition",
    component: () => import("../view/student/audition/index"),
    meta: {
      title: "试听课",
    },
  },
  {
    path: "/student/studyGuide",
    name: "studyGuide",
    component: () => import("../view/student/studyGuide"),
    meta: {
      title: "上课指引",
      requiresAuth: true,
    },
  },
  {
    path: "/student/giftPackage",
    name: "giftPackage",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "上进礼包",
    },
  },
  {
    path: "/pay/paySettlement",
    name: "paySettlement",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "远智学堂-缴费",
    },
    children: [
      {
        path: "alipay",
        component: () => import("../view/student/home/<USER>"),
        meta: {
          title: "支付宝支付",
          requiresAuth: false,
        },
      },
    ],
  },
  {
    path: "/pay/instalment",
    name: "instalment",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "分期贷款",
    },
  },
  {
    path: "/student/baiduStaging",
    name: "baiduStaging",
    component: () => import("../view/student/home/<USER>"),
    meta: {
      title: "分期",
    },
  },
  {
    path: "/student/usually",
    name: "usually",
    component: () => import("../view/student/more/usually"),
    meta: {
      title: "成绩单", // 成教，全日制成绩单页面
      loadDict: true,
    },
  },
  {
    path: "/student/more/gkCoursePoint",
    name: "gkCoursePoint",
    component: () => import("../view/student/more/gkCoursePoint"),
    meta: {
      title: "远智学堂",
    },
  },
  // {
  //   path: '/toExam',
  //   name: 'toExam',
  //   component: () => import('../view/exam/toExam.vue'),
  //   meta: {title: '在线考试', requiresAuth: true}
  // },
  {
    path: "/student/admissionTicket",
    name: "admissionTicket",
    component: () => import("../view/student/more/admissionTicket"),
    meta: {
      title: "准考证",
      requiresAuth: true,
      loadDict: true,
    },
  },  
  {
    path: "/student/selfAchievement",
    name: "selfAchievement",
    component: () => import("../view/student/more/selfAchievement"),
    meta: {
      title: "我的成绩查询",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/selfAchievementResult",
    name: "selfAchievementResult",
    component: () => import("../view/student/more/selfAchievementResult"),
    meta: {
      title: "我的成绩",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/courseQr",
    name: "courseQr",
    component: () => import("../view/student/more/courseQr"),
    meta: {
      title: "",
      requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/zkLogin",
    name: "zkLogin",
    component: () => import("../view/student/zkScore/zkLogin"),
    meta: {
      title: "自考成绩查询",
      // requiresAuth: true,
      keepAlive: false,
      loadDict: true,
    },
  },
  {
    path: "/student/zkScore",
    name: "zkScore",
    component: () => import("../view/student/zkScore/zkScore"),
    meta: {
      title: "自考成绩",
      // requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/zkEmpowerAccountPassword",
    name: "zkEmpowerAccountPassword",
    component: () => import("../view/student/zkEmpowerAccountPassword/zkSave"),
    meta: {
      title: "准考证和密码填写",
      // requiresAuth: true,
      loadDict: true,
    },
  },
  {
    path: "/student/zkrecoverAccount",
    name: "zkrecoverAccount",
    component: () => import("../view/student/zkEmpowerAccountPassword/recoverAccount"),
    meta: {
      title: "找回准考证",
      loadDict: true,
    },
  },
  {
    path: "/student/zkrecoverZkPwd",
    name: "zkrecoverZkPwd",
    component: () => import("../view/student/zkEmpowerAccountPassword/recoverZkPwd"),
    meta: {
      title: "找回密码",
      loadDict: true,
    },
  },

  {
    path: "/student/recoverAccount",
    name: "recoverAccount",
    component: () => import("../view/student/zkScore/recoverAccount"),
    meta: {
      title: "找回准考证",
      loadDict: true,
    },
  },
  {
    path: "/student/recoverZkPwd",
    name: "recoverZkPwd",
    component: () => import("../view/student/zkScore/recoverZkPwd"),
    meta: {
      title: "找回密码",
      loadDict: true,
    },
  },
  {
    path: "/student/myTask/materialDetail",
    name: "materialDetail",
    component: () =>
      import("../view/student/myTask/materialDownload/detail.vue"),
    meta: { title: "远智学堂—我的任务", requiresAuth: true },
  },
  {
    path: "/student/myTask/materialImg",
    name: "materialImg",
    component: () =>
      import("../view/student/myTask/materialDownload/imgDownload.vue"),
    meta: { title: "远智学堂—我的任务", requiresAuth: true },
  },
  {
    path: "/student/myTask/materialPdf",
    name: "materialPdf",
    component: () =>
      import("../view/student/myTask/materialDownload/pdfDownload.vue"),
    meta: { title: "远智学堂—我的任务" },
    children: [
      {
        path: "share",
        name: "materialPdfShare",
        component: () =>
          import("../view/student/myTask/materialDownload/share.vue"),
        meta: { title: "远智学堂—我的任务", requiresAuth: false },
      },
    ],
  },
  {
    path: "/student/zkPreEntry",
    name: "zkPreEntryLogin",
    redirect: "/student/zkPreEntry/login",
    component: () => import("../view/student/zkPreEntry/index"),
    children: [
      {
        path: "login",
        name: "zkPreEntryLogin",
        component: () => import("../view/student/zkPreEntry/login"),
        meta: {
          title: "查看自考报考指南",
          keepAlive: false,
          loadDict: true,
          // requiresAuth: true,
        },
      },
      {
        path: "findAccount",
        name: "zkPreEntryFindAccount",
        component: () => import("../view/student/zkPreEntry/findAccount"),
        meta: {
          title: "找回准考证",
          loadDict: true,
          // requiresAuth: true,
        },
      },
      {
        path: "findPwd",
        name: "zkPreEntryFindPwd",
        component: () => import("../view/student/zkPreEntry/findPwd"),
        meta: {
          title: "找回密码",
          loadDict: true,
          // requiresAuth: true,
        },
      },
      {
        path: "guide",
        name: "zkPreEntryGuide",
        component: () => import("../view/student/zkPreEntry/guide"),
        meta: {
          title: "自考报考指南",
          loadDict: true,
          // requiresAuth: true,
        },
      },
      {
        path: "task",
        name: "zkPreEntryTask",
        component: () => import("../view/student/zkPreEntry/task"),
        meta: {
          title: "远智学堂-我的任务",
          loadDict: true,
          // requiresAuth: true,
        },
      },
    ]
  },
];
