  <!--公用组件：
      /**
      * 组件名称 allComment 评论组件
      * @module @/components/comment/allComment
      * @desc 全部评论组件 内嵌弹窗评论组件
      * <AUTHOR>
      * @date 2021/08
      * @param {String} [mappingId] - 文章id 根据该文章ID查询评论
      * @param {Array} [praiseList] - 赞的数组 不传则不显示
      * @param {String} [fabulousNum] - 显示文章的赞数 无则不传
      * @param {Boolean} [moreCommentToApp] - 点击二级评论更多 true去app 不传则弹窗展开更多
      * @example 调用示例
      * <all-comment :praiseList="postInfo.praiseList" :fabulousNum="postInfo.fabulousNum" :mappingId="id" ref="allComment" />
      */
    -->

<template>
  <div class="commentList">
    <div class="commentTitle">
      <div class="commentTitle-word">全部评论&nbsp;{{ commentList.length }}</div>
      <div class="praiseBox" @click="(ios||isWxwork)?downApp():''">
        <img src="../../assets/image/app/circleAd/arrow.png" alt="" class="headImgBox-arrow" />
        <wx-open-launch-app class="launch-more" @error="callAppError" :appid="OpenAppId" :extinfo="extinfo"
                            v-if="!isWxwork">
          <script type="text/wxtag-template">
            <style>
              .anzhuoMore {
                    width: 1000px;
                    height: 1000px;
                }
            </style>
                    <div  class="anzhuoMore"></div>
          </script>
        </wx-open-launch-app>
        <div class="headImgBox">
          <template v-for="(itema, indexa) in praiseList">
            <img v-if="indexa < 4" :src="itema.headImg" alt />
          </template>
        </div>
        <div v-if="praiseList&&praiseList.length !== 0" class="headImgBox-num">
          {{ fabulousNum }}人点赞
        </div>
      </div>
    </div>
    <ul class="ulLists" v-if="commentList.length > 0">
      <li v-for="(item,i) in commentList">
        <div style="overflow: hidden" @click="
                    replyMsg(item.userId, item.realName || item.stdName || item.nickName || '同学', item.commentId,
                                   $event) ">
          <img class="infoHeadImg" :src="item.headUrl" alt />
          <div class="infoBox">
            <p class="InfoName">
              {{ item.userName ||item.realName || item.stdName || item.nickName || "同学" }}
              <img v-if="item.hot !== 0" class="hotInfoIcon"
                   src="../../assets/image/app/adExtend/<EMAIL>" alt />
              <span class='fr-zan-icon' @click.stop="like(item,$event)">
                <img v-if="item.ifFabulous" class="zan-icon" src="../../assets/image/app/circleAd/new_zan.png" alt />
                <img v-if="!item.ifFabulous" class="zan-icon" src="../../assets/image/app/circleAd/new-zan-not.png"
                     alt />
                <span v-if="item.fabulousNum">{{item.fabulousNum}}</span>
              </span>
            </p>
            <p class="infoDate">{{ item.createTime | timeBefore }}</p>
            <p class="infoComment" v-html="item.content" style="display: block"></p>
            <template v-if="item.picUrl" v-for="(img,ix) in item.picUrl">
              <img class="imgs" :src="img | imgBaseURL" alt @click.stop="showImgClick(ix,item.picUrl)" />
            </template>
            <br v-if="item.picUrl" />
          </div>
        </div>
        <template v-if=" item.replyList.length> 0">
          <div class="replyBox" v-for="(it, index) in item.replyList" v-if="index == '0'">
            <span class="tag"></span>
            <p class="replyName">
              {{ it.userName  ||item.realName || item.stdName || item.nickName || "同学"}}
              <span>{{ it.createTime | timeBefore }}</span>
            </p>
            <p class="replyComment" v-html="it.content"></p>
            <br v-if="it.picUrl" />
            <template v-if="it.picUrl" v-for="(img,ix) in it.picUrl">
              <img @click.stop="showImgClick(ix,it.picUrl)" class="replyimgs" :src="img | imgBaseURL" />
            </template>
            <p @click="moreCommentToApp?isWxwork?toDown():'':showAll(item.commentId,i,item.mappingId)"
               v-if="item.replyList.length > 1" class="lookTitle">
              查看全部{{ item.replyList.length }}条
              <wx-open-launch-app class="launch-more" @error="callAppError" :appid="OpenAppId" :extinfo="extinfo"
                                  v-if="!isWxwork&&moreCommentToApp">
                <script type="text/wxtag-template">
                  <style>
              .anzhuoMore {
                    width: 1000px;
                    height: 1000px;
                }
            </style>
                    <div  class="anzhuoMore"></div>
          </script>
              </wx-open-launch-app>
            </p>
          </div>
        </template>
      </li>
    </ul>
    <div class="noCommentBg" v-else>
      <img src="../../assets/image/app/circleAd/comment.png" alt />
      <p>暂无评论～</p>
    </div>
    <div v-if="lookMoreComment&&!showAllComment" class="lookMore" @click="getCommentInfo('lookMore')">
      <img src="../../assets/image/app/adExtend/lookMore.png" alt="" />
      <p>展开更多</p>
    </div>
    <all-reply ref='allReply' :mappingId='comMappingId' :info='commentInfo' @update='updateReplay'
               @updateComment='updateCommentList'></all-reply>
    <div class="bg" @click="cancel" v-if="reply"></div>

    <div v-if="isEdit && reply" class="edit">
      <edit-box v-on:submit="toComment" ref="questionEditBox" key="questionEditBox" :change="true" btnText="发送"
                :inputPlaceholder="holder" :isSocketConnect="true" :scroll="false" locatStorageTime="12" />
    </div>
    <van-image-preview v-model="showImgInfo" :images="imageArr" :start-position="imgIndex">
    </van-image-preview>

  </div>
</template>

<script>
import openApp from "@/mixins/openApp";
import { isIOS, formatEmotions, isWxwork, downloadApp } from '@/common'
import { imgBaseURL } from "@/config";
import AllReply from './comment';
import editBox from "@/components/live/appEditBox";


export default {
  mixins: [openApp],
  components: { AllReply, editBox },
  data () {
    return {
      ios: isIOS(),
      isEdit: false,
      targetUserId: "",
      parentId: "",
      targetNickName: "",
      reply: false,
      holder: "",
      showImgInfo: false,
      imageArr: [],
      imgIndex: 0,
      commentInfo: {},
      comMappingId: '',
      pageNum: 0,
      pageSize: 100,
      lookMoreComment: false,
      commentList: [],
      isWxwork: isWxwork()

    };
  },
  props: {

    praiseList: {
      type: Array,
      default: () => ([])
    },
    mappingId: {
      type: String,
    },
    fabulousNum: {
      type: String | Number,
      default: '0'
    },
    showAllComment: {
      type: Boolean,
      default: false
    },
    moreCommentToApp: {
      type: Boolean,
      default: false
    },
    extinfo: {
      type: String,
      default: '',
    },
  },
  computed: {},
  watch: {},
  methods: {
    toDown () {
      if (!!this.storage.getItem("authToken")) {
        this.downApp();

      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }
    },
    downApp () {
      downloadApp()

    },
    //评论列表
    getCommentInfo (type) {
      if (!this.mappingId) return
      this.$http
        .post("/mkt/getCommentInfo/1.0/", {
          mappingId: this.mappingId,
          mappingType: "4",
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        })
        .then((res) => {
          let { code, body } = res;
          if (code !== "00") return;
          this.commentList = body.list;
          this.lookMoreComment = false;
          if (this.commentList.length > 3 && !type) {
            this.lookMoreComment = true;
            this.commentList = this.commentList.slice(0, 3);
          }
          for (var i = 0; i < this.commentList.length; i++) {
            if (this.commentList[i].picUrl) {
              this.commentList[i].picUrl = this.commentList[i].picUrl.split(
                ","
              );
            }
            this.commentList[i].fabulousNum =
              this.commentList[i].fabulousNum > 99
                ? "99+"
                : this.commentList[i].fabulousNum;
            this.commentList[i].content = formatEmotions(
              this.commentList[i].content
            );
            // 名字
            if (this.commentList[i].mobile) {
              this.commentList[i].userName =
                this.commentList[i].realName ||
                this.commentList[i].stdName ||
                this.commentList[i].nickName ||
                `同学${this.commentList[i].mobile.slice(-4)}`;
              ("同学");
            } else {
              this.commentList[i].userName =
                this.commentList[i].realName ||
                this.commentList[i].stdName ||
                this.commentList[i].nickName ||
                "同学";
            }
            for (var j = 0; j < this.commentList[i].replyList.length; j++) {
              this.commentList[i].replyList[
                j
              ].content = formatEmotions(
                this.commentList[i].replyList[j].content
              );
              if (this.commentList[i].replyList[j].picUrl) {
                this.commentList[i].replyList[j].picUrl = this.commentList[
                  i
                ].replyList[j].picUrl.split(",");
              }
              this.commentList[i].replyList[j].fabulousNum =
                this.commentList[i].replyList[j].fabulousNum > 99
                  ? "99+"
                  : this.commentList[i].replyList[j].fabulousNum;
              if (this.commentList[i].mobile) {
                this.commentList[i].replyList[j].userName =
                  this.commentList[i].replyList[j].realName ||
                  this.commentList[i].replyList[j].stdName ||
                  this.commentList[i].replyList[j].nickName ||
                  `同学${this.commentList[i].replyList[j].mobile.slice(-4)}`;
                ("同学");
              } else {
                this.commentList[i].replyList[j].userName =
                  this.commentList[i].replyList[j].realName ||
                  this.commentList[i].replyList[j].stdName ||
                  this.commentList[i].replyList[j].nickName ||
                  "同学";
              }
            }
          }
        });
    },

    replyMsg (userId, nickName, commentId, event, isChild) {
      if (!this.storage.getItem("authToken")) {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
        return;
      }
      if (this.isSelfComment(userId)) return
      this.isEdit = true;
      this.targetUserId = userId;
      this.parentId = commentId;
      this.targetNickName = nickName;
      this.reply = true;
      this.holder = `回复@${nickName}`;
      let domEle =
        document.getElementsByTagName("html")[0] ||
        document.getElementsByTagName("body")[0];
      this.oldScrollTop = domEle.scrollTop || window.pageYOffset;

      this.parentEle =
        event.currentTarget.parentElement.parentElement.parentElement;
      domEle.scrollTop = this.parentEle.offsetTop;
    },
    showImgClick (index, arr) {
      this.imgIndex = index;
      console.log(imgBaseURL);
      arr = arr.map(item => (imgBaseURL + item))
      this.imageArr = arr
      this.showImgInfo = true;
    },
    // 查看评论 全部
    showAll (id, index, mappingId) {
      this.commentInfo = this.commentList[index];
      this.comMappingId = mappingId
      this.$refs.allReply.show();
      // this.$router.push({name:'scholarshipStoryComment',query:{commentId:id,commentInfo:JSON.stringify(this.comment[index])}})
    },
    updateReplay ({ commentId, replyList }) {
      this.commentList.forEach((item) => {
        if (item.commentId == commentId) {
          item.replyList = replyList;
        }
      });
      this.commentList = [].concat(this.commentList);
    },
    updateCommentList (id) {
      const index = this.commentList.findIndex(item => item.commentId == id);
      this.commentList.splice(index, 1);
      this.commentList = [].concat(this.commentList);
    },
    //评论
    toComment (content, picUrlList) {
      if (!picUrlList) {
        if (!content) {
          this.$modal({ message: "请输入评论内容!", icon: "warning" });
          return;
        }
      }

      if (content.length > 100) {
        this.$modal({ message: "请输入100字以内的评论内容!", icon: "warning" });
        return;
      }
      let data = {
        ifLimit: 0,
        content: content,
        picUrl: picUrlList,
        headUrl: this.headUrl,
        mappingId: this.mappingId,
        commentType: "4",
        nickName: this.nickName,
        realName: this.nickName,
        authToken: this.storage.getItem("authToken"),
      };
      if (this.parentId) {
        data["parentId"] = this.parentId;
        data["targetNickName"] = this.targetNickName;
        data["targetUserId"] = this.targetUserId;
      }
      this.$http.post("/mkt/addNewComment/1.0/", data).then((res) => {
        if (res.code == "00") {
          this.targetNickName = "";
          this.targetUserId = "";
          this.commentMsg = "";
          this.holder = "我要评论...";
          this.reply = false;
          this.isEdit = false;
          this.getCommentInfo("reload");
          this.$nextTick(() => {
            let domEle =
              document.getElementsByTagName("html")[0] ||
              document.getElementsByTagName("body")[0];
            domEle.scrollTop = this.oldScrollTop;
            this.parentId = "";
          });
        }
      });
    },
    // 判断是不是自己的评论
    isSelfComment (userId) {
      return userId === this.storage.getItem('userId')

    },
    cancel () {
      this.reply = false;
      this.holder = "我要评论...";
      this.$nextTick(() => {
        let domEle =
          document.getElementsByTagName("html")[0] ||
          document.getElementsByTagName("body")[0];
        domEle.scrollTop = this.oldScrollTop;
      });
    },
    toEdit () {
      if (!!this.storage.getItem("authToken")) {
        this.isEdit = true;
        this.reply = true;
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }
    },
    async like (item, event) {
      if (!!this.storage.getItem("authToken")) {
        // event.pre
        const res = await this.$http.post('/us/usPraise/1.0/', {
          fabulousNum: item.ifFabulous == 1 ? -1 : 1, // （用户第一次触发传1 第二触发取消点赞传 -1）
          praiseType: 5,
          praiseId: item.commentId,
          userName: this.nickName,
          circleUserId: '',
        });
        if (res.code == '00') {
          item.ifFabulous = item.ifFabulous == 1 ? 0 : 1;
          if (item.ifFabulous) {
            if (!item.fabulousNum) {
              item.fabulousNum = 1;
            } else {
              item.fabulousNum += 1;
            }
          } else {
            item.fabulousNum -= 1;
          }
        }
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }

    },
  },
  created () {
    this.nickName = this.storage.getItem('realName') || this.storage.getItem('zmcName');
    this.headUrl = this.storage.getItem('headImg')
  },
  mounted () {
    let type = this.showAllComment ? 'lookMore' : ''
    this.getCommentInfo(type)


  },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
.commentList {
  background: #fff;
  position: relative;
  .commentTitle {
    display: flex;
    position: relative;
    justify-content: space-between;
    line-height: 0.54rem;
    font-size: 0.17rem;
    font-weight: bold;
    padding-left: 0.15rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  }
  .ulLists {
    overflow: hidden;
    li {
      overflow: hidden;
      padding-bottom: 0.1rem;
      margin-top: 0.15rem;
      .infoHeadImg {
        width: 0.38rem;
        height: 0.38rem;
        border-radius: 50%;
        float: left;
        margin: 0 0 0 0.15rem;
      }

      .infoBox {
        float: right;
        width: 3.11rem;
        .InfoName {
          font-size: 0.15rem;
          font-weight: 600;
          .hotInfoIcon {
            width: 0.47rem;
            margin-top: 0.02rem;
          }
          .fr-zan-icon {
            float: right;
            margin-right: 0.1rem;
            .zan-icon {
              width: 0.24rem;
              height: 0.24rem;
            }
            span {
              font-size: 0.12rem;
              font-weight: 400;
              color: #000000;
              line-height: 0.3rem;
              margin-left: -0.04rem;
            }
          }
        }
        .infoDate {
          display: block;
          margin-right: 0.15rem;
          font-size: 0.1rem;
          color: rgba(23, 6, 6, 0.4);
        }
        .infoComment {
          margin-right: 0.15rem;
          color: rgba(23, 6, 6, 0.6);
          margin-top: 0.07rem;
        }
        .imgs {
          width: 0.65rem;
          height: 0.65rem;
          border-radius: 0.03rem;
          margin-top: 0.03rem;
          margin-left: 0.1rem;
        }
        .imgs:first-of-type {
          margin-left: 0;
        }
        .tag {
          color: #3f9df2;
          margin-top: 0.08rem;
        }
      }
      .replyBox {
        width: 3.01rem;
        float: right;
        margin: 0.08rem 0.1rem 0 0;
        position: relative;
        background: rgba(23, 6, 6, 0.02);
        padding: 0 0 0.1rem 0.1rem;
        // p {
        //   display: inline-block;
        // }
        .tag {
          width: 0.02rem;
          height: 0.15rem;
          background: #f06e6c;
          position: absolute;
          top: 0.1rem;
          left: 0;
        }
        .replyName {
          margin-top: 0.06rem;
          color: #746a6a;
          font-size: 0.12rem;
          span {
            font-size: 0.1rem;
            color: #a29b9b;
            margin-left: 0.05rem;
          }
        }
        .replyComment {
          font-size: 0.12rem;
        }
        .replyimgs {
          width: 0.65rem;
          height: 0.65rem;
          border-radius: 0.03rem;
          margin-left: 0.1rem;
          object-fit: cover;
        }
        .replyimgs:first-of-type {
          margin-left: 0rem;
        }

        .lookTitle {
          color: #004e97;
          margin-top: 0.1rem;
          font-size: 0.12rem;
          font-weight: 400;
          line-height: 0.2rem;
          position: relative;
        }
      }
    }
  }
  .noCommentBg {
    text-align: center;
    img {
      width: 1.65rem;
    }
    p {
      display: block;
      text-align: center;
      color: rgba(23, 6, 6, 0.4);
      line-height: 0.5rem;
      height: 0.5rem;
    }
  }
  .lookMore {
    position: absolute;
    bottom: 0;
    width: 3.75rem;
    height: 1.2rem;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 1) 100%);
    text-align: center;
    img {
      width: 0.32rem;
      margin-top: 0.55rem;
    }
    p {
      text-align: center;
      color: #f06e6c;
      font-weight: bold;
    }
  }
}
.praiseBox {
  position: absolute;
  right: 0;
  height: 0.4rem;
  align-items: center;
  padding-left: 0.2rem;
  background: #fff;
  .headImgBox {
    float: right;
    min-width: 0.23rem;
    margin-left: 0.18rem;
    margin-top: 0.14rem;
    margin-right: -0.04rem;
    img {
      width: 0.23rem;
      height: 0.23rem;
      border-radius: 50%;
      float: left;
      margin-left: -0.1rem;
    }
    &-num {
      float: right;
      font-size: 0.12rem;
      font-weight: 400;
      color: #453838;
    }
    &-arrow {
      width: 0.32rem;
      height: 0.32rem;
      float: right;
      margin-top: 0.1rem;
    }
  }

  p {
    font-size: 0.12rem;
    color: #746a6a;
    margin-left: 0.04rem;
  }
  .interactiveBox {
    margin-left: auto;
    .dianzan {
      margin-right: 0.25rem;
      img {
        width: 0.18rem;
        height: 0.16rem;
      }
      span {
        font-size: 0.12rem;
        vertical-align: top;
        color: #746a6a;
        display: inline-block;
      }
    }
    .pinglun {
      margin-right: 0.2rem;

      img {
        width: 0.16rem;
        height: 0.16rem;
      }
      span {
        font-size: 0.12rem;
        vertical-align: top;
        color: #746a6a;
        display: inline-block;
      }
    }
  }
}
.bg {
  position: fixed;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  top: 0;
  opacity: 0.3;
  z-index: 99;
  background-color: rgb(0, 0, 0);
}
.edit {
  position: fixed;
  bottom: 0;
  height: 0.5rem;
  z-index: 999;
  width: 100%;
  max-width: 640px;
}
.launch-more {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  overflow: hidden;
}
</style>
