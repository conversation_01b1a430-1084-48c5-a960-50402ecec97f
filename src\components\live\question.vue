<template>
    <div class="question_wrap" v-if="isShow">
        <div class="bg"></div>
        <div class="question">
          <div>
            <div class="title">
              <span>答题卡</span>
              <div class="close"><img src="../../assets/image/student/ic_close.png" alt="" @click="isShow=false"></div>
            </div>
            <div class="question_body" v-if="showType=='showanswer'">
              <p><span v-if="data.type=='R'">单选</span><span v-else>多选</span>{{data.content.name}}</p>
            </div>
            <div class="question_body" v-else>
              <p><span v-if="data.type=='R'">单选</span><span v-else>多选</span>{{data.name}}</p>
            </div>
            <div class="question_detail" v-if="showType=='showanswer'">
              <ul>
                <li v-for="item,o in options"  :class="addClass(o)">
                  <img src="../../assets/image/student/ic_true.png" alt="" v-if="data.content.answer.indexOf(o)!=-1">
                  <img src="../../assets/image/student/ic_wrong.png" alt="" v-else-if="data.content.answer.indexOf(o)==-1&&storage.getItem('question').indexOf(o)!=-1">
                  <span v-else>{{o}}. </span>
                  {{item}}</li>
                <!--<li   :class="addClass(i,it)">-->
                  <!--<img src="../../assets/image/student/ic_true.png" alt="" v-if="data.content.answer=='B'">-->
                  <!--<img src="../../assets/image/student/ic_wrong.png" alt="" v-else-if="data.content.answer!='B'&&storage.getItem('question')=='B'">-->
                  <!--<span v-else>B. </span>{{data.content.option2}}</li>-->
              </ul>
            </div>
            <div class="question_detail" v-else>
              <ul v-if="data.type=='R'">
                <li v-for="item,o in options"   @click="select(o)" :class="{active:singleselect==o}" ><span>{{o}}</span>{{item}}</li>
                <!--<li @click="select('B')" :class="{active:singleselect=='B'}"><span>B. </span>{{data.option2}}</li>-->
              </ul>
              <ul v-else>
                <li v-for="item,o in options"   @click="select(o)" :class="{active:manyselect.indexOf(o)!=-1}" ><span>{{o}}</span>{{item}}</li>
              </ul>
            </div>
            <button v-on:click="isShow=false" v-if="showType=='showanswer'">知道了</button>
            <button @click="submit" v-else>提交</button>
          </div>
        </div>
    </div>
</template>

<script>
    export default {
      name: "question",
      props:["data"],
      data(){
          return{
            isShow:false,
            options:{},
            singleselect:'',
            manyselect:[],
            showType:''
          }
      },
      created(){
      },
      mounted(){

      },
      methods:{
        open(type){
            this.isShow=true
            this.showType=type
        },
        submit(){
            let answer;
            if(this.data.type=='R'){
              answer=this.singleselect;
            }else{
              answer=this.manyselect.join("");
            }
            this.storage.setItem('question',answer);
            this.$emit('sendAnswer',answer,this.data.questionId);
            this.isShow=false
          this.$modal2({message:'答案已提交'})
        },
        select(data){
          //R表示单选，C表示多选
          if(this.data.type=='R'){
            this.singleselect=data
          }else{
            if(this.manyselect&&this.manyselect.indexOf(data)==-1){
              let an=[...this.manyselect,data]
              this.manyselect=an;
            }else if (this.manyselect&&this.manyselect.indexOf(data)!=-1) {
              let an=[]
              for(let i=0;i<this.manyselect.length;i++){
                if(data!=this.manyselect[i]){
                  an.push(this.manyselect[i])
                }
              }
              this.manyselect=an;
            }else{
              this.manyselect.push(data)
            }
          }
        },
        addClass(item){
          let num;
          if(this.data.content.answer.indexOf(item)!=-1||this.storage.getItem('question').indexOf(item)!=-1){
            num=this.data.content.answer.indexOf(item)!=-1?1:2;
            return 'class'+num;
          }else{
            return '';
          }
        }
      },
      watch:{
        data:function(newValue){
          this.options={};
          this.manyselect=[]
          this.singleselect=''
          if(this.showType=='showanswer'){
            for(let o in newValue.content){
              if(o.indexOf('option')!=-1){
                if(newValue.content[o]){
                  let obj={};
                  obj[o]=newValue[o];
                  let detail;
                  switch (o) {
                    case 'option1':detail='A';break;
                    case 'option2':detail='B';break;
                    case 'option3':detail='C';break;
                    case 'option4':detail='D';break;
                    case 'option5':detail='E';break;
                    default:break;
                  }
                  this.$set(this.options,detail,newValue.content[o])
                }
              }
            }
          }else{
            for(let o in newValue){
              if(o.indexOf('option')!=-1){
                if(newValue[o]){
                  let obj={};
                  obj[o]=newValue[o];
                  // this.options=[...this.options,this.data[o]];
                  let detail;
                  switch (o) {
                    case 'option1':detail='A';break;
                    case 'option2':detail='B';break;
                    case 'option3':detail='C';break;
                    case 'option4':detail='D';break;
                    case 'option5':detail='E';break;
                    default:break;
                  }
                  this.$set(this.options,detail,newValue[o])
                }
              }
            }
          }
        }
      }
    }
</script>

<style scoped lang="less">
.question_wrap{
  position: fixed;
  width: 100%;
  max-width: 640px;
  height: 100%;
  min-height: 100vh;
  overflow: auto;
  z-index:9999;
  top:0rem;
  left:0;
  bottom:0;
  right: 0;
  margin: auto;
  .bg{
    width: 100%;
    max-width: 640px;
    min-height: 100vh;
    height: 100%;
    position: fixed;
    top:0;
    left:0;
    bottom:0;
    right: 0;
    margin: auto;
    z-index:210;
    background-color: rgba(0,0,0,.3);
  }
  .question{
    position:absolute;
    bottom:0;
    z-index:400;
    width: 100%;
    max-height: 55vh;
    overflow-y: scroll;
    overflow-x: hidden;
    background-color: white;
    .title{
      height: auto;
      overflow: hidden;
      margin-left: .2rem;
      margin-right: .15rem;
      padding-bottom: .05rem;
      border-bottom: solid 1px rgba(23,6,6,.1);
      span{
        display: block;
        float: left;
        background-size: .24rem .24rem;
        font-size: .15rem;
        margin-top: .17rem;
      }
      .close{
        float: right;
        width: .32rem;
        height: .32rem;
        margin-top: .11rem;
        img{
          width: 100%;
        }
      }
    }
  }
  .question_body{
    font-size: .15rem;
    line-height: .2rem;
    font-weight: 600;
    width: 3.38rem;
    background-color: white;
    border-radius: .03rem;
    margin: 0.23rem .25rem .1rem .2rem;
    p{
      word-break: break-word;
      width: 100%;
    }
    span{
      border: solid 1px #3F9DF2;
      font-size: .12rem;
      color: #3F9DF2;
      border-radius: .04rem;
      float: left;
      padding: 0.01rem 0.05rem;
      height: .18rem;
      line-height: .16rem;
      margin-top: .01rem;
      margin-right: .05rem;
    }
  }
  .question_detail{
    width: 100%;
    margin: 0 auto;
    ul{
      li{
        margin: 0 .2rem;
        font-size: .15rem;
        line-height: .2rem;
        min-height: .5rem;
        padding: 0.23rem 0 0 0.2rem;
        &.class1{
          color: #40C65D;
        }
        &.class2{
          color: #E85B57;
        }
        img{
          width: .25rem;
          height:.25rem;
        }
        span{
          display: inline-block;
          width: .25rem;
          height:.25rem;
          text-align: center;
          line-height: .25rem;
        }
        &.active{
          color: #59B7FF;
        }
      }
    }
  }
  button{
    display: block;
    margin: .2rem auto 0;
    line-height: .4rem;
    border: none;
    font-size: .14rem;
    color: #fff;
    background: #E85B57;
    padding: 0 0.1rem;
    width: 100%;
    cursor: pointer;
  }
}
</style>
