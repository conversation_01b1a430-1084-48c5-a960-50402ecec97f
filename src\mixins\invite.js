export default {
  methods: {
    $inviteJump(path, query={}) {
      let inviteInfo = {
        inviteId: this.$route.query.inviteId || undefined,
        regOrigin: this.$route.query.regOrigin || undefined,
        regChannel:  this.$route.query.regChannel || undefined
      };

      inviteInfo = JSON.parse(JSON.stringify(inviteInfo)); //将为undefined的参数去除

      let route = {
        path: path,
        query: {
          ...inviteInfo,
          ...query
        }
      };

      this.$router.push(route);
    }
  }
}
