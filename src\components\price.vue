<template>
  <div class="price">
    <i class="icons i-zhimi vam" :class="{active:big,nozhimi:nozhimi}"></i>
    <b>{{nozhimi?'￥':''}}</b>
    <span class="discount vam" :class="{big:big}">{{salesPrice}}</span>
    <template v-if="originalPrice">
      <!-- <span class="gap">|</span> -->
      <span class="original vam">
        (<span class="s">¥</span>{{salesPrice | transformPrice}})</span>
    </template>
  </div>
</template>

<script>
  export default {
    props: ['salesPrice', 'originalPrice','big','nozhimi'],
    filters: {
      transformPrice(val) {
        return (Number(val) / 100).toFixed(2);
      },
    },
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .price{ line-height:1; display: flex; align-items: center;}
  .discount{ color:@color2; font-size:.18rem; font-weight:bold;
    &.big{
      color: rgba(228, 40, 23, 1);
      font-size: .21rem;
    }
  }
  b{color: #e42817;font-weight: normal;margin-top: .06rem;float: left}
  .original{
    // text-decoration:line-through;
    color:#A29B9B;
    font-size:.14rem;
    .s{
      font-size: 0.12rem;
    }
  }
  .gap{ padding:0 .02rem; color:#999; }
  .i-zhimi{ width:.15rem; height:.19rem; margin-right:.02rem; background-image:url(../assets/image/server/d1.png);
    &.active{width:.15rem;height:.2rem;background-image:url(../assets/image/server/d2.png)}
    &.nozhimi{ display: none;}}

  .small{
    margin-top:.1rem;
    .i-zhimi{ width:.08rem; height:.11rem; }
    .discount{ font-size:.13rem; font-weight:normal; }
  }
</style>
