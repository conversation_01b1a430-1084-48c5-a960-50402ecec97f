<template>
  <div v-show="show" class="popup" @click="close">
    <div class="popup-mask"></div>
    <div class="popup-content">
      <span
        class="popup-item"
        v-for="(item, index) in list"
        :key="index"
        :class="{ selected: index == currentIndex }"
        @click="selected(index)"
      >
        {{ item }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => ["视频", "音频"],
    },
    // 父组件的上下文this
    parentCom: {
      type: Object,
    },
  },
  data() {
    return {
      show: false,
      currentIndex: 0,
    };
  },
  methods: {
    selected(index) {
      if (index == this.currentIndex) return
      this.currentIndex = index;
      this.parentCom.play();
      const text = this.list[index];
      this.parentCom.setStudy(index, text);
    },
    close() {
      this.parentCom.play();
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
/***
  * 1.弹框层组件不要使用rem或者vw
  * 2.如果弹框层组件使用rem和vw, 在全屏模式下, 文字和样式会特别大, 因为rem和vw是根据视宽来自动变化的, 当横屏的时候, 视宽很大, 自然样式和字体也变大了
  * 3.可以使用px或者em, 根据播放器的父元素使用em, 全屏就不会变得很大了
  */
.popup {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 97;
  .popup-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.8;
  }
  .popup-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .popup-item {
      flex: 1;
      color: #fff;
      text-align: center;
      font-size: 1.4em;
      &.selected {
        color: #14a2f4;
      }
    }
  }
}
</style>
