<template>
 <div class="s_wrap" v-if="isShow">
   <div class="bg" @click="isShow=false"></div>
   <div class="specifications" >
     <div class="info">
       <img :src="imgUrl | imgBaseURL" alt="">
       <div class="detail">
         <price :salesPrice="detail.salesPrice" :originalPrice="detail.originalPrice"></price>
         <p>剩余：{{allNum}}个</p>
       </div>
     </div>
     <div class="material" v-if="gsGoodsSalesSpecs.length">
       <p>参数规格 <span v-if="!selectDetail.spec.goodsSalesSpecId">请选择参数规格</span></p>
       <ul class="clearfix">
         <li v-for="(item, index) in gsGoodsSalesSpecs" :key='index'  @click="selectSpecs(item)" :class="{active:selectDetail.spec.goodsSalesSpecId==item.goodsSalesSpecId,nothing:item.specStock==0}">
           {{item.specName}}
         </li>
       </ul>
     </div>
     <div class="num">
       <div class="fl">
         <p>兑换数量</p>
       </div>
       <div class="fr">
         <span class='del' :class='[delDisabled]' @click="del">-</span>
         <input type="text" v-model="goodsNum" @blur="validateNum">
         <span :class='[plusDisabled]' @click="increment">+</span>
       </div>
     </div>
     <!-- <foot-bar title="马上兑换" v-on:submit="submit"></foot-bar> -->
     <add-sales-notify v-if="detail.salesStatus==='2'"
                      :salesId="salesId"
                      :salesName="detail.salesName"
                      :accAmount="accAmount"
                      :ifAddNotify="detail.ifAddNotify"
                      :planCount="detail.planCount"
                      :startTime="detail.startTime"
                      :salesType="salesType"></add-sales-notify>
     <div class="btn-box" v-else>
        <button disabled v-if="detail.ifExchange==='Y' || detail.onceCount == detail.remainingCount">你已达最大限购数量</button>
        <button class="btn1 mt" disabled v-else-if="detail.salesStatus==='1'">兑换已结束，去看看其他商品吧～</button>
        <button class="btn1 mt" disabled v-else-if="detail.goodsCount== 0">商品已兑完，去看看其他商品吧～</button>
        <button v-else :disabled='allNum == 0 || goodsNum == 0' @click='submit'>马上兑换</button>
     </div>
   </div>
 </div>
</template>

<script>
  import price from '@/components/price'
  import footBar from '@/components/footBar'
  import addSalesNotify from '@/components/addSalesNotify'

    export default {
      name: "selectSpecifications",
      components:{price,footBar, addSalesNotify},
      props:["imgUrl","gsGoodsSalesSpecs","allStock","detail", 'salesId'],
      data(){
          return{
            arr:["白色/165M","白色/170M","彩色/165M","黑色/170M"],
            isShow:false,
            goodsNum:1,
            oldGoodsNum:1,
            selectDetail:{spec:{}},
            salesType: 1,
          }
      },
      computed:{
        allNum(){
          if(this.selectDetail.spec.goodsSalesSpecId){
            return this.selectDetail.spec.specStock;
          }else{
            return this.allStock
          }
        },
        delDisabled() {
          return this.goodsNum == 1 ? 'disabled' : '';
        },
        plusDisabled() {
          return this.goodsNum == this.allNum ? 'disabled' : '';
        },
      },
      mounted() {
        this.salesType = this.$route.query.salesType || '1';
      },
      methods:{
        open(){
          this.isShow=true
        },
        close(){
          this.isShow=false
        },
        increment(){
            if(this.goodsNum<this.allNum){
              this.goodsNum++;
            }else{
              this.$modal2({message:'超过库存数量了'})
            }
        },
        del(){
            if(this.goodsNum>1){
              this.goodsNum--;
            }
        },
        selectSpecs(item){
          if(item.specStock>0){
            this.selectDetail.spec=item
          }
          if(this.goodsNum>this.allNum){
            this.goodsNum=this.allNum
          }
          this.$emit('selectD',this.selectDetail)
        },
        submit(){
          //  this.isShow=false
            this.selectDetail.goodsNum=this.goodsNum;
            this.$emit('Buy',this.selectDetail)
        },
        validateNum(){
            var reg=/^[0-9]+$/
          this.goodsNum=this.goodsNum.startsWith("0")?this.goodsNum.substr(1):this.goodsNum
          var  num=isNaN(parseInt(this.goodsNum))?this.goodsNum:parseInt(this.goodsNum)

          if(!reg.test(num)){
              this.goodsNum=this.oldGoodsNum
            }else if(!(num>0&&num<=this.allNum)){
              this.$modal2({message:'宝贝数量超出范围了哦'})
              this.goodsNum=this.oldGoodsNum
            }
            this.oldGoodsNum=this.goodsNum
        }
      },
      watch:{

      }
    }
</script>

<style scoped lang="less">
  @import "../../assets/less/variable";
  @import "../../assets/less/detailPage";
.s_wrap{
  width: 100%;
  height: 100%;
  min-height: 100vh;
  position: fixed;
  left:0;
  top:0;
  z-index: 9999;
  .bg{
    width: 100%;
    height:100%;
    background-color: rgba(0, 0, 0, .7);
    left:0;
    top:0;
  }
  .disabled{
    color:rgba(23,6,6,0.3);
  }
  .specifications{
    width: 100%;
    max-width: 640px;
    height: 4.02rem;
    background-color: #fff;
    padding: .15rem .1rem 0 .1rem;
    border-radius: .1rem .1rem 0 0;
    position: absolute;
    z-index: 3;
    left:50%;
    transform: translate(-50%);
    bottom:0;
    .info{
      width: 100%;
      height: auto;
      overflow: hidden;
      img{
        width: .87rem;
        height: .87rem;
        border-radius: .1rem;
        float: left;
        margin-right: .1rem;
        object-fit: cover;
        border-radius: 0.1rem;
      }
      .detail{
        float: left;
        padding-top: .07rem;
        p{
          margin-top: .09rem;
          color: rgba(23, 6, 6, .6);
          font-size: .12rem;
        }
      }
    }
    .material{
      margin-top: .1rem;
      font-size: .12rem;
      width: 100%;
      p{
        span{
          color: rgba(228, 40, 23, .8);
        }
      }
      ul{
        li{
          height: .4rem;
          padding: .1rem;
          font-size: .14rem;
          border-radius: .05rem;
          background-color: rgba(243, 242, 242, 1);
          margin-right: .15rem;
          margin-top: .15rem;
          float: left;
          word-break: break-all;
          &.active{
            padding: .09rem;
            border: solid 1px rgba(240, 110, 108, 1);
            background-color: rgba(240, 110, 108, .1);
            color: rgba(240, 110, 108, 1);
          }
          &.nothing{
            background-color: rgba(23, 6, 6, .02);
            color: rgba(23, 6, 6, .3);
          }
        }
      }
    }
    .num{
      width: 100%;
      height: .32rem;
      font-size: .12rem;
      line-height: .32rem;
      margin-top: .3rem;
      .fr{
        margin-right: .05rem;
        span{
          float: left;
          width: .32rem;
          height: .32rem;
          text-align: center;
          line-height: .32rem;
          font-size: .2rem;
        }
        input{
          float: left;
          width: .47rem;
          height: .32rem;
          text-align: center;
          background-color: rgba(243, 242, 242, 1);
          border: none;
          border-radius: .05rem;
          color: rgba(23, 6, 6, 1);
          font-size: .14rem;
        }
      }
    }
    .btn-box{
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.05rem 0.1rem;
      &::before{
        .borderTop();
      }
      button{
        background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
        height: 0.4rem;
        border-radius: 100px;
        width: 100%;
        color:#fff;
        font-size:0.15rem;
        font-weight:600;
        &:disabled{
          background: #B9B4B4;
          opacity: 1;
        }
      }
    }
  }
}
</style>
