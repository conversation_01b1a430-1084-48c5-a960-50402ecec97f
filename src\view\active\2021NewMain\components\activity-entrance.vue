<template>
  <div class="yz-activity-entrance cl" v-if='list.length > 0'>
    <div class="item" v-for="(item, index) in list" :key="index" @click='itemClick(item)'>
      <img class="bg" :src="item.imageUrl | imgOssURL" alt="">
      <p class="title">{{item.name | substr}}</p>
      <p class="sub-title">{{item.desc | substr}}</p>
    </div>
  </div>
</template>

<script>
import { getIsAppOpen, toLogin, isLogin, getQueryString } from '@/common';
import { toAppLogin } from '@/common/jump';

export default {
  filters: {
    substr(val) {
      if (!val) {
        return '';
      }
      return val.length > 9 ? val.substr(0, 9) : val;
    },
  },
  props: {
    inviteId: String,
    regOrigin: String,
  },
  data() {
    return {
      list: [],
      isAppOpen: false,
      isLogin: isLogin(),
    };
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.getMarketingBanner();
  },
  watch: {
    isAppOpen(val) {
      if (val) {
        this.getMarketingBanner();
      }
    },
  },
  methods: {
    itemClick(item) {
      if (!this.login()) {
        return;
      }
      if (!item.url) {
        return;
      }
      let searchUrl = '';
      const activeShip = getQueryString(item.url, 'activeShip');
      if(item.url.includes('/active/elevenCoupon')) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${this.regOrigin}&scholarship=119`;
      } else if (activeShip) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${this.regOrigin}&scholarship=${activeShip}`;
      } else {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${this.regOrigin}`;
      }

      const url = item.url.includes('?') ? `${item.url}&${searchUrl}` : `${item.url}?${searchUrl}`;
      window.location.href = url;
    },
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    async getMarketingBanner() {
      const data = { type: 10, bannerBelong: 1 }
      if (this.isAppOpen) {
        data.bannerBelong = 2;
        data.type = this.pfsnLevel == 1 ? 'ck_8' : 'ck_7';
      }
      const { code, body } = await this.$http.post('/sys/getMarketingBanner/1.0/', data);
      if (code == '00') {
        this.list = body || [];
        if (this.list.length > 0) {
          this.list.forEach((item) => {
            if (item.imageUrl) {
              item.imageUrl += `?timestamp=${Date.now()}`;
            }
          });
        }
      }
    },

  },
};
</script>

<style lang="less" scoped>
  .yz-activity-entrance{
    padding: 0 0.1rem;
    margin-top: 0.05rem;
    .item{
      overflow: hidden;
      color: #000;
      font-size: 0.12rem;
      position: relative;
      border-radius: 0.05rem;
      float: left;
      width: 1.75rem;
      height: 0.56rem;
      padding: 0.08rem  0.1rem;
      margin-top: 0.05rem;
      &:not(:nth-child(2n)) {
        margin-right: 0.05rem;
      }
    }
    .bg{
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .title{
      position: relative;
      z-index: 2;
      font-weight: 600;
      font-size: 0.15rem;
      margin-bottom: 0.02rem;
    }
    .sub-title{
      position: relative;
      z-index: 2;
      opacity: 0.5;
    }
  }
</style>
