<template>
  <div>
    <div class="tab-nav bc-w clearfix">
      <a
        class="item"
        href="javascript:;"
        :class="{active:active==='unused'}"
        @click="switchTab('unused')"
      >
        当前可用(<span v-text="couponList.length"></span>)
      </a>
      <a
        class="item"
        href="javascript:;"
        :class="{active:active==='used'}"
        @click="switchTab('used')"
      >
        学历优惠券(<span v-text="MyCouponsSize.unused.length"></span>)
      </a>
      <a
        class="item"
        href="javascript:;"
        :class="{active:active==='school'}"
        @click="switchTab('school')"
      >
        上进学社优惠券({{ schoolCouponLength }})
      </a>
      <a
        class="item"
        href="javascript:;"
        :class="{active:active==='expired'}"
        @click="switchTab('expired')"
      >
        已失效(<span v-text="MyCouponsSize.expired.length+MyCouponsSize.used.length"></span>)
      </a>
    </div>
    <mt-tab-container :swipeable="false" v-model="active">
      <mt-tab-container-item id="unused">
        <div class="coupon" v-if="hasLearnId">
          <div class="study">
            <div class="currentStudy" @click="switchLearnInfo">
              <div class="fl">
                <span class="studyInfo">
                    <template v-if="learnInfos.length>1">当前学籍:</template>
                    {{ unvsName }}/{{ pfsnName }}[{{ pfsnLevel == '5' ? '大专' : '本科' }}]
                </span>
                <span class="scholarship">报读类型：{{ activityName }}</span>
              </div>
              <img src="../../assets/image/ic_unfold_down.png" alt v-if="learnInfos.length>1"/>
            </div>
            <div class="studyList" style="display: none" ref="learnInfo">
              <p class v-for="item in learnInfos" :class="{active:item.unvsName==unvsName}"
                 @click="setLearnId(item)">
                                <span
                                  class="studyInfo">{{ item.unvsName }}/{{
                                    item.pfsnName
                                  }}[{{ item.pfsnLevel == '5' ? '大专' : '本科' }}]</span>
                <span class="scholarship">报读类型：{{ item.activityName }}</span>
              </p>
            </div>
          </div>
          <template v-if="couponList.length">
            <div class="item-box" v-for="(item,index) in couponList">
              <div class="item">
                <div class='item-pri'>
                  <span>¥</span>
                  <span class='item-pri-num'>{{ parseInt(Number(item.amount)) }}</span>
                </div>
                <div class="item-name">
                  <div class="item-name-title">
                    {{ item.couponName }}
                  </div>
                  <div class="item-name-time">
                    有效时间：{{ item.availableStartTime }}-{{ item.availableExpireTime }}
                  </div>
                </div>
              </div>
              <div class="desc" @click="clickItemExpired(index,'enble')">

                <p class="remark" ref="expiredRemarkenble" style="display: none" v-if="item.remark">
                  <template v-for="it,i in item.remark.split('\n')">
                    <span>{{ it }}</span>
                    <br/>
                  </template>
                </p>
                <div class="des">
                  {{ item.actName }}
                  <div class="des-fr">
                    使用规则
                    <img class="arrowImg" ref="expiredArrowenble"
                         src="../../assets/image/active/registerReading/icon-up.png" alt
                         style="transform:rotate(0deg)">
                  </div>
                </div>
              </div>
            </div>
          </template>

          <div class="no-data" v-else>
            <p v-if="learnInfos.length>1">
              当前学籍暂无可用奖学金
              <br/>可切换学籍或在全部奖学金查看
            </p>
            <p v-else>
              当前学籍暂无可用奖学金
              <br/>可在全部奖学金查看
            </p>
          </div>
        </div>
        <div class="no-data" v-else>暂无奖学金</div>
      </mt-tab-container-item>
      <mt-tab-container-item id="used">
        <div class="coupon">
          <template v-if="MyCouponsSize.unused.length">
            <div class="item-box" v-for="(item, index) in MyCouponsSize.unused">
              <div class="item">
                <div class='item-pri'>
                  <span>¥</span>
                  <span class='item-pri-num'>{{parseInt(Number(item.amount))}}</span>
                </div>
                <div class="item-name">
                  <div class="item-name-title">
                    {{item.couponName}}
                  </div>
                  <div class="item-name-time">
                    有效时间：{{item.availableStartTime}}-{{item.availableExpireTime}}
                  </div>
                </div>
              </div>
              <div class="desc" @click="clickItemExpired(index)">

                <p class="remark" ref="expiredRemark" style="display: none" v-if="item.remark">
                  <template v-for="it,i in item.remark.split('\n')">
                    <span>{{it}}</span>
                    <br />
                  </template>
                </p>
                <div class="des">
                  {{item.actName}}
                  <div class="des-fr">
                    使用规则
                    <img class="arrowImg" ref="expiredArrow"
                         src="../../assets/image/active/registerReading/icon-up.png" alt
                         style="transform:rotate(0deg)">
                  </div>

                </div>
              </div>
            </div>
          </template>
          <div class="no-data" v-else>暂无奖学金~</div>
        </div>
      </mt-tab-container-item>
      <mt-tab-container-item id="school">
        <div class="voucher-tabs">
          <div class="voucher-tabs-nav">
            <div
              class="voucher-tab"
              :class="{ active: vTActiveName === 0}"
              @click="handleTabClick(0)"
            >未使用</div>
            <div
              class="voucher-tab"
              :class="{ active: vTActiveName === 1}"
              @click="handleTabClick(1)"
            >已使用</div>
            <div
              class="voucher-tab"
              :class="{ active: vTActiveName === 2}"
              @click="handleTabClick(2)"
            >已失效</div>
          </div>
          <div class="voucher-tabs-content">
            <div v-show="voucherList.length > 0" class="coupon">
              <voucher-list ref="voucherList" @load="getSchoolCoupons" :list="voucherList" />
            </div>
            <div v-show="voucherList.length <=0" class="no-data" >暂无优惠卷~</div>
          </div>
        </div>

      </mt-tab-container-item>
      <mt-tab-container-item id="expired">
        <div class="coupon expired">
          <template v-if="MyCouponsSize.expired.length||MyCouponsSize.used.length">
            <div
              class="coupon-item cl used"
              v-for="(item,index) in MyCouponsSize.used"
              ref="index"
              @click="clickItemUsed(index)"
            >
              <div class="fl">
                <span>&yen;</span>
                <span v-text="~~item.amount"></span>
              </div>
              <div class="fl">
                <p v-text="item.couponName">学费抵扣劵</p>
                <p>
                  使用时间:
                  <span>{{item.usedTime|formatDate('yyyy.MM.dd hh:mm')}}</span>
                  <!--<span v-text="item.availableExpireTime.substring(0,16)"></span>-->
                </p>
                <img src="../../assets/image/ic_coupon_used.png" alt class="status1" />
              </div>
              <div class="desc">
                <p
                  v-if="item.actName"
                  style="padding-top: .1rem;padding-left:.1rem;font-weight: bold;font-size: .12rem"
                >
                  “{{item.actName }}”
                  <span>报读· 专用</span>
                </p>
                <p class="des">
                  使用规则
                  <img
                    class="arrowImg"
                    ref="usedArrow"
                    src="../../assets/image/ic_unfold.png"
                    alt
                    style="transform:rotate(0deg)"
                  />
                  <!-- <img ref="arrow"  @click="cancleDes()" src="../../assets/image/up.png" alt=""> -->
                </p>
                <!--<p><span v-for="txt  in  item.itemCodes">{{txt}}&nbsp;</span></p>-->
                <p class="remark" ref="remarkUsed" style="display: none">
                  <!--1.适用-->
                  <!--<span v-for="txt  in  item.itemCodes">{{txt}}&nbsp;</span>-->
                  <!--<br>-->
                  <template v-for="it,i in item.remark.split('\n')">
                    <span>{{it}}</span>
                    <br />
                  </template>
                </p>
              </div>
            </div>
            <div
              class="coupon-item cl"
              v-for="(item,index) in MyCouponsSize.expired"
              ref="index"
              @click="clickItemExpired(index)"
            >
              <div class="fl">
                <span>&yen;</span>
                <span v-text="~~item.amount"></span>
              </div>
              <div class="fl">
                <p v-text="item.couponName">学费抵扣劵</p>
                <!-- <p>
                  <span v-for="txt  in  item.itemCodes">{{txt}}</span>
                </p>-->
                                <!--<p v-if="item.couponId==signcouponId">单张折扣最高抵扣100元</p>-->
                                <p v-if="item.couponId==signcouponId">{{item.remark}}</p>
                                <p v-if="item.invalidTime">
                                    作废时间:
                                    <span>{{item.invalidTime|formatDate('yyyy.MM.dd hh:mm')}}</span>
                                    <!--<span v-text="item.availableExpireTime.substring(0,16)"></span>-->
                                </p>
                                <p v-else>
                                    有效时间:
                                    <span>{{item.availableStartTime|formatDate('yyyy.MM.dd')}}</span>-
                                    <span>{{item.availableExpireTime|formatDate('yyyy.MM.dd hh:mm')}}</span>
                                </p>
                                <img src="../../assets/image/ic_coupon_timeout.png" alt class="status1"
                                     v-if="item.timeout" />
                                <img src="../../assets/image/ic_coupon_useless.png" alt class="status1" v-else />
                            </div>
                            <div class="desc">
                                <p v-if="item.actName"
                                   style="padding-top: .1rem;padding-left:.1rem;font-size: .12rem;font-weight: bold">
                                    “{{item.actName }}”
                                    <span>报读· 专用</span>
                                </p>
                                <p class="des">
                                    使用规则
                                    <img class="arrowImg" ref="expiredArrownot" src="../../assets/image/ic_unfold.png"
                                         alt style="transform:rotate(0deg)" />
                                </p>
                                <p class="remark" ref="expiredRemarknot" style="display: none" v-if="item.remark">
                                    <!--1.适用-->
                                    <!--<span v-for="txt  in  item.itemCodes">{{txt}}&nbsp;</span>-->
                                    <!--<br>-->
                                    <template v-for="it,i in item.remark.split('\n')">
                                        <span>{{it}}</span>
                                        <br />
                                    </template>
                                </p>
                            </div>
                        </div>
                    </template>
                    <div class="no-data" v-else>暂无失效奖学金~</div>
                </div>
            </mt-tab-container-item>
    </mt-tab-container>
    </div>
</template>

<script>
import loadBar from "@/components/loadBar";
import newtime from "../../components/active/countdown7";
import voucherList from '@/view/aspirantUniversity/components/voucherList'
export default {
  props: [
    "accountSerial",
    "isLoading",
    "allLoaded",
    "accountSerial2",
    "isLoading2",
    "allLoaded2",
    "MyCouponsSize",
    "couponList",
    "unvsName",
    "learnInfos",
    "pfsnName",
    "pfsnLevel",
    "hasLearnId",
    "scholarship",
    "activityName"
  ],
  components: {
    loadBar,
    newtime,
    voucherList
  },
  data() {
    return {
      active: "unused",
      bottomStatus: "",
      index: 0,
      index2: 0,
      dateNow: "",
      signcouponId: "154555501212201510",
      timeDetail: {},
      vTActiveName: 0,
      voucherList: [],  // 上进大学优惠卷list
      pagination: {
        num: 0,
        size: 7,
      },
      schoolCouponLength: 0
    };
  },
  mounted() {
    this.getNowDate();
    this.getSchollAvailableLength()
  },
  methods: {
    getSchollAvailableLength() {
      this.$http.post('/pu/getPuUserCouponAvailableCount/2.0/')
        .then(res => {
          const { code, body } = res;
          if (code ==='00') {
            this.schoolCouponLength = body || 0;
          }
        })
    },
    // 获取上进大学优惠卷
    getSchoolCoupons(state) {
      this.pagination.num += 1;
      let params = {
        state: this.vTActiveName,
        pageNum: this.pagination.num,
        pageSize: this.pagination.size
      }
      this.$http.post('/pu/getPuUserCouponInfoList/2.0/', params)
        .then(res => {
          const { code, body } = res;
          if (code === '00') {
            this.voucherList = this.voucherList.concat(body);
            // 加载状态结束
            this.$refs['voucherList'].loading = false;
            //到底了
            if(body.length === 0 || body.length < this.pagination.size) {
              this.$refs['voucherList'].finished = true;
            }
          }
        })
    },
    // 上进大学优惠卷切换
    handleTabClick(name) {
      // 重置
      this.vTActiveName = name;
      this.pagination.num = 0;
      this.voucherList = [];
      this.$refs['voucherList'].loading = false;
      this.$refs['voucherList'].finished = false;
      this.getSchoolCoupons(this.vTActiveName)
    },
    switchTab(name) {
      this.active = name;
      if (name === 'school') {
        this.vTActiveName = 0;
        this.pagination.num = 0;
        this.voucherList = [];
        this.$refs['voucherList'].loading = false;
        this.$refs['voucherList'].finished = false;
        this.getSchoolCoupons(this.vTActiveName)
      }
    },
    clickItem(index) {
      if (this.$refs.remark[index].style.display ==="none") {
        this.$refs.remark[index].style.display = "block";
        this.$refs.arrowTwo[index].style.transform = "rotate(180deg)";
      } else if (this.$refs.remark[index].style.display === "block") {
        this.$refs.remark[index].style.display = "none";
        this.$refs.arrowTwo[index].style.transform = "rotate(0deg)";
      }
    },
    clickItemAll(index) {
      console.log(this.$refs.remarkAll[index]);
      if (this.$refs.remarkAll[index].style.display === "none") {
        this.$refs.remarkAll[index].style.display = "block";
        this.$refs.allArrow[index].style.transform = "rotate(180deg)";
      } else if (this.$refs.remarkAll[index].style.display === "block") {
        this.$refs.remarkAll[index].style.display = "none";
        this.$refs.allArrow[index].style.transform = "rotate(0deg)";
      }
    },
    clickItemUsed(index) {
      if (this.$refs.remarkUsed[index].style.display === "none") {
        this.$refs.remarkUsed[index].style.display = "block";
        this.$refs.usedArrow[index].style.transform = "rotate(180deg)";
      } else if (this.$refs.remarkUsed[index].style.display === "block") {
        this.$refs.remarkUsed[index].style.display = "none";
        this.$refs.usedArrow[index].style.transform = "rotate(0deg)";
      }
    },
    switchLearnInfo() {
      if (this.learnInfos.length === 1) {
        return;
      }
      if (this.$refs.learnInfo.style.display === "none") {
        this.$refs.learnInfo.style.display = "block";
      } else {
        this.$refs.learnInfo.style.display = "none";
      }
    },
    getNowDate() {
      this.dateNow = new Date().getTime();
    },
    loadMore() {
      this.index++;
      this.$emit("LoadMore", this.index);
    },
    setLearnId(item) {
      this.$emit(
        "setLearnId",
        item.learnId,
        item.unvsName,
        item.recruitType,
        item.pfsnName,
        item.pfsnLevel
      );
      this.$refs.learnInfo.style.display = "none";
    },
    loadMore2() {
      this.index2++;
      this.$emit("LoadMore2", this.index2);
    },
    clickItemExpired(index) {
      if (this.$refs.expiredRemark[index].style.display === "none") {
        this.$refs.expiredRemark[index].style.display = "block";
        this.$refs.expiredArrow[index].style.transform = "rotate(180deg)";
      } else if (this.$refs.expiredRemark[index].style.display === "block") {
        this.$refs.expiredRemark[index].style.display = "none";
        this.$refs.expiredArrow[index].style.transform = "rotate(0deg)";
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import '../../assets/less/variable';

.other {
  padding: 0.15rem 0;
  clear: both;
  text-align: center;
  color: #868686;
  font-size: 0.12rem;
}

.tab-nav {
  line-height: 0.44rem;
  position: relative;
  overflow: auto;

  &:after {
    .borderBottom;
  }

  .item {
    display: block;
    position: relative;
    //padding: 0 .2rem;
    width: 35vw;
    flex-shrink: 0;
    flex: none;
    text-align: center;
    color: #666;

    &.active {
      color: @color;

      span {
        color: @color;
      }

      &:after {
        position: absolute;
        right: 0rem;
        bottom: 0;
        left: 0rem;
        height: 2px;
        background-color: @color;
        content: "";
      }
    }

    span {
      font-size: 0.12rem;
      color: #999;
    }
  }

  .mint-tab-container-item {
    li {
      display: block;
    }

    a.item {
      display: block;
      height: 0.8rem;
      padding: 0.15rem;
      background-color: #fff;
      position: relative;

      &:after {
        .borderBottom;
      }

      .fl {
        padding-top: 0.07rem;
        width: 3rem;
        overflow: hidden;
        text-overflow: ellipsis;

        p {
          margin: 0;

          &:nth-of-type(1) {
            color: #444;
            font-size: 0.14rem;
          }

          &:nth-of-type(2) {
            color: #666;
            font-size: 0.12rem;
          }

          &:last-child {
            color: #999;
            font-size: 0.12rem;
            font-weight: 100;
          }
        }
      }

      .fr {
        line-height: 0.5rem;
        color: @color;
        font-size: 0.16rem;
      }
    }
  }
}
</style>
<style lang="less" scoped>
@import '../../assets/less/variable';
.coupon {
  padding: 0rem 0rem 0.12rem 0rem;

  .study {
    margin-bottom: 0.1rem;
    background-color: #fff;
    padding: 0 0.1rem;

    .currentStudy {
      width: 100%;
      height: auto;
      white-space: nowrap;
      overflow: hidden;
      color: rgba(23, 6, 6, 0.6);
      font-size: 0.14rem;
      padding: 0.14rem 0 0.14rem 0.18rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        float: right;
        margin-top: -0.05rem;
      }

      .studyInfo {
        display: block;
        font-size: 0.14rem;
        line-height: 0.2rem;
        color: rgba(23, 6, 6, 0.6);
        width: 2.9rem;
        height: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .scholarship {
        display: block;
        color: rgba(23, 6, 6, 0.4);
        font-size: 0.12rem;
      }
    }

    .studyList {
      position: relative;

      &:before {
        content: '';
        position: absolute;
        background-color: rgba(23, 6, 6, 0.08);
        width: 3.55rem;
        height: 1px;
        left: 0;
        top: 0;
      }

      p {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 0.15rem;
        color: rgba(23, 6, 6, 0.8);
        padding: 0.14rem 0 0.14rem 0.4rem;

        .studyInfo {
          font-size: 0.14rem;
          line-height: 0.2rem;
          color: rgba(23, 6, 6, 0.6);
          width: 100%;
          height: auto;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .scholarship {
          display: block;
          color: rgba(23, 6, 6, 0.4);
          font-size: 0.12rem;
        }

        &.active {
          background-image: url('../../assets/image/ic_green_select.png');
          background-size: 0.32rem 0.32rem;
          background-repeat: no-repeat;
          background-position: 0.08rem;
        }
      }
    }
  }

  .coupon-item {
    position: relative;
    /*background-color: #fff;*/
    border-radius: 0.04rem;
    // height: 1.05rem;
    overflow: hidden;
    width: 3.55rem;
    margin: 0 auto 0.08rem;

    &:first-of-type {
      margin-top: 0.1rem;
    }

    img {
      position: absolute;
      top: 0.02rem;
      right: 0.02rem;
      width: 0.55rem;
      height: 0.55rem;
    }

    .fl {
      position: relative;

      &:first-child {
        width: 0.85rem;
        height: 0.87rem;
        text-align: center;
        padding: 0;
        border-radius: 0.1rem 0 0 0;
        border-right: 1px dashed #f0f0f0;
        background-image: linear-gradient(to left top, #f09190, #f06e6c);

        span {
          color: white;
          font-size: 0.16rem;
          line-height: 0.87rem;
          font-weight: 500;
          word-break: break-all;

          &:last-child {
            font-size: 0.26rem;
          }
        }
      }

      &:nth-of-type(2) {
        float: left;
        width: 2.7rem;
        height: 0.87rem;
        background-color: #fff;
        padding-top: 0.1rem;
        border-left: 1px dotted #f5f6f8;
        border-bottom: 1px dashed #ccc;
        position: relative;

        p {
          color: #999;
          font-size: 0.12rem;
          word-break: break-all;
          /*width: 2.1rem;*/
          overflow: hidden;
          // white-space:nowrap;
          // text-overflow: ellipsis;
          padding-left: 0.12rem;
          padding-right: 0.1rem;
          width: 2.7rem;
          height: 0.4rem;
          line-height: 0.2rem;

          &:first-child {
            color: rgba(23, 6, 6, 0.8);
            font-weight: bold;
            font-size: 0.14rem;
            padding-left: 0.1rem;
          }

          &:nth-of-type(2) {
            line-height: 0.19rem;
            height: auto;
            padding-left: 0.1rem;
            padding-right: 0rem;
            color: rgba(23, 6, 6, 0.8);
            margin-top: 0.08rem;
          }
        }

        .status1 {
          width: 0.85rem;
          height: 0.81rem;
          position: absolute;
          top: 0.1rem;
          right: 0;
        }
      }

      .remark {
        background: rgba(255, 229, 228, 1);
        padding: 0 0.1rem 0.1rem 0.1rem;
      }
    }

    .desc {
      float: left;
      width: 100%;
      height: auto;
      overflow: hidden;
      background-color: #fff;

      .des {
        position: relative;
        height: 0.32rem;
        line-height: 0.32rem;
        padding-left: 0.1rem;
        color: rgba(23, 6, 6, 0.8);
        font-weight: bold;
        font-size: 0.12rem;

        img {
          float: right;
          width: 0.32rem;
          height: 0.32rem;
          margin-right: 0.09rem;
        }
      }
    }

    .remark {
      background: #fff;
      color: rgba(23, 6, 6, 0.6);
      font-size: 0.12rem;
      padding: 0 0.1rem 0.1rem 0.1rem;
    }
  }

  &.used,
  &.expired {
    .coupon-item {
      background-image: url('../../assets/image/coupon_used.png');
      background-repeat: no-repeat;
      background-size: 0.55rem 0.55rem;
      background-position: 2.92rem 0.03rem;
      border-color: #9b9b9b;

      .fl:nth-of-type(2) {
        span {
          color: rgba(23, 6, 6, 0.3);
        }

        p {
          color: rgba(23, 6, 6, 0.3);
        }
      }

      .desc {
        p {
          color: rgba(23, 6, 6, 0.3);
        }
      }

      .fl:first-child {
        background-image: none;
        background-color: #b9b4b4;

        span {
          color: #fff;
        }
      }
    }
  }

  &.expired {
    .coupon-item {
      background-image: url('../../assets/image/coupon_expired.png');
    }
  }

  .item-box {
    font-family: PingFangSC-Regular, PingFang SC;

    .item {
      margin: 0 auto;
      width: 3.75rem;
      // width: 2.9rem;
      height: 1.5rem;
      // margin-left: 0.45rem;
      background: url('../../assets/image/active/registerReading/coupon.png') no-repeat;
      background-size: contain;
      position: relative;

      &-pri {
        font-size: 0.14rem;
        color: #ffe5b7;
        position: absolute;
        top: 0.4rem;
        left: 0.33rem;
        font-weight: 400;
        text-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);

        &-num {
          font-size: 0.22rem;
          font-weight: 500;
        }
      }

      &-name {
        position: absolute;
        width: 2.3rem;
        left: 1.23rem;
        top: 0.28rem;

        &-title {
          height: 0.44rem;
          font-size: 0.16rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #c13935;
          line-height: 0.22rem;
          text-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);
          margin-bottom: 0.15rem;
        }

        &-time {
          font-size: 0.1rem;
          font-weight: 400;
          color: #c13935;
          line-height: 0.14rem;
          text-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);
        }
      }
    }

    .desc {
      width: 3.6rem;
      height: auto;
      overflow: hidden;
      margin: 0 auto;
      margin-top: -0.28rem;
      text-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);

      .remark {
        background-color: #fff;
        width: 3.4rem;
        margin: 0 auto;
        font-size: 0.11rem;
        font-weight: 400;
        color: #666666;
        line-height: 0.2rem;
        padding: 0.1rem;
        box-shadow: 0 0.02rem 0.1rem 0px rgba(0, 0, 0, 0.1);
      }

      .des {
        position: relative;
        height: 0.64rem;
        width: 3.6rem;
        margin-top: -0.08rem;
        background-image: url('../../assets/image/active/registerReading/bottom-bg.png');
        background-size: contain;
        background-repeat: no-repeat;
        line-height: 0.6rem;
        padding-left: 0.2rem;
        font-size: 0.1rem;
        font-weight: 400;
        color: #666666;

        &-fr {
          float: right;
          width: 0.9rem;
          font-size: 0.1rem;
          font-weight: 400;
          color: #333333;

          img {
            width: 0.6rem;
            height: 0.6rem;
            position: absolute;
            right: 0;
          }
        }
      }
    }
  }
}

.no-data {
  width: 2rem;
  padding-top: 1.7rem;
  margin: 0.2rem auto;
  background-image: url('../../assets/image/nothing.png');
  background-repeat: no-repeat;
  background-position: 0.1rem 0;
  background-size: 1.7rem;
  text-align: center;
  color: rgba(23, 6, 6, 0.6);
}
</style>
<style lang="scss" scoped>
.voucher-tabs {
  .voucher-tabs-nav {
    width: 100%;
    overflow: hidden;
    padding: .1rem .1rem 0 .1rem;

    .voucher-tab {
      padding: .06rem .12rem;
      border-radius: .15rem;
      border: 1px solid #CCCCCC;
      float: left;
      margin-right: .1rem;
      font-size: .13rem;
    }

    .active {
      color: #EA5A59;
      background: rgba(234, 90, 89, 0.1);
      border-color: rgba(234, 90, 89, 0.3);
    }

  }

  .voucher-tabs-content {
    display: block;
  }
}
</style>
