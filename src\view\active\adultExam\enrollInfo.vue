<template>
  <div class="children-page">
    <div class="inner content">
      <div class="table">
        <div class="item">
          <span>姓名:</span>
          <span>{{stdName}}</span>
        </div>
        <div class="item">
          <span>证件号:</span>
          <span>{{idCard | hideIdCard1}}</span>
        </div>
        <div class="item">
          <span>报读类型:</span>
          <span>{{learnInfo.scholarship|findDict('scholarship')}}</span>
          <scholarship-detail :scholarship="learnInfo.scholarship" :grade="learnInfo.grade" style="display:inline-block">
            <span class="info">点击了解&gt;&gt;</span>
          </scholarship-detail>
        </div>
        <!--<div class="item">
          <span>招生类型:</span>
          <span>{{learnInfo.recruitType | findDict('recruitType')}}</span>
        </div>-->
        <div class="item">
          <span>年级:</span>
          <span>{{learnInfo.grade}}</span>
        </div>
        <div class="item">
          <span>层次:</span>
          <span>{{learnInfo.pfsnLevel | findDict('pfsnLevel')}}</span>
        </div>
        <div class="item">
          <span>院校:</span>
          <span>{{learnInfo.unvsName}}</span>
        </div>
        <div class="item">
          <span>专业:</span>
          <span>{{learnInfo.pfsnName}}</span>
        </div>
        <div class="item">
          <span>考试县区:</span>
          <span>{{learnInfo.taName}}</span>
        </div>
      </div>
    </div>
    <foot-bar title="立即缴费" v-on:submit="toPayment" v-if="learnInfo.hasUnpaid==='1'&& ['201903','2020'].includes(grade)"></foot-bar>
  </div>
</template>

<script>
  import topBar from '@/components/topBar';
  import footBar from '@/components/footBar';
  import scholarshipDetail from '@/components/scholarshipDetail';

  export default {
    data() {
      return {
        stdName: '',
        idCard: '',
        learnId: '',
        learnInfo: {},
        grade:'',
        activityName: '',
        scholarship: '',
      }
    },
    created() {
      this.stdName = this.$route.query.stdName;
      this.idCard = this.$route.query.idCard;
      this.learnId = this.$route.query.learnId;
      this.activityName = this.$route.query.activityName;
      this.getLearnInfo();
    },
    methods: {
      getLearnInfo: function () {
        this.$http.post('/mkt/getLearnInfoByLearnId/1.0/', {learnId: this.learnId}).then(res => {
          if (res.code === '00') {
            this.learnInfo = res.body || {};
            this.scholarship = this.learnInfo.scholarship;
            this.grade = this.learnInfo.grade;
          }
        });
      },
      toPayment: function () {
        this.$router.push({
          name: 'stuPayment',
          query: {learnId: this.learnInfo.learnId, activityName: this.activityName, unvsId: this.learnInfo.unvsId,unvsName: this.learnInfo.unvsName}
        });
      }
    },
    components: {topBar, footBar, scholarshipDetail}
  }
</script>

<style lang="less" scoped>
  @import "../../../assets/less/variable";

  .children-page {
    background-color: @backgroundColor;
  }

  .link-r {
    color: #444;
    font-size: .15rem;
    position: absolute;
    right: .15rem;
    top: .115rem;
  }

  .content {
    height: 4.6rem;
    padding: 1.1rem 0 0;
    background-image: url("../../../assets/image/infor_bg.png");
    background-size: 100%;
    .title {
      font-size: .18rem;
      padding-left: .8rem;
    }
    .table {
      padding-top: .13rem;
      .item {
        span {
          color: #666;
          text-align: left;
          line-height: 1.8;
          &:first-child {
            display: inline-block;
            width: 1.2rem;
            text-align: right;
            padding-right: .05rem;
          }
          &.info{ width:auto;margin-left:.1rem;color:#69a5da;font-size:.1rem;}
        }
      }
    }
  }

  .other{
    padding: .05rem .2rem .2rem;
    p {
      font-size: .12rem;}
    }
  .next{
    padding-left: .6rem;
    margin-top: .3rem;
    color: #666;
  }
</style>
