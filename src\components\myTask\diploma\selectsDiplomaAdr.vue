<template>
  <picker :slots="slots" :isSetDefaultVal="isSetDefaultVal" v-model="val" ref="picker"/>
</template>

<script>
  import picker from '@/components/picker';
  import {provinceName,cityName,districtName} from '../../../common/index';

  export default {
    props: {
      dictName: String,
      options: Array,
      ext1: String,
      isSetDefaultVal: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      defaultIndex: {
        type: Number,
        default: 0
      },
      filterValue: Array
    },
    data() {
      return {
        slots: [],
        val: [{}],
        vals: []
      }
    },
    created() {
      this.init();
    },
    methods: {
      init: function () {
        this.slots = [{flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex}];
      },
      getOptions: function () {
        let options = [];
        if (this.options) {
          this.vals = this.options || [];
          for (let item of this.vals) {
            let province = provinceName(item.provinceCode);
            let city = cityName(item.cityCode,item.provinceCode);
            let district = districtName(item.districtCode,item.provinceCode,item.cityCode);

            let addressTxt=province+city+district;
            Object.assign(item,{addressTxt:addressTxt});
            options.push(item.placeName);
          }
        }
        return options;
      },
      open: function () {
        if(this.disabled) return;
        this.$refs.picker.open();
      },
      setValues: function (value) {
        if (value) {
          const name = (this.vals.find(val => val.dictValue === value) || {}).dictName;
          if (name) {
            this.$refs.picker.setValues([name]);
          }
        }
      }
    },
    watch: {
      val: function () {
        this.$emit('input', this.vals[this.val[0].index] || {});
      }
    },
    components: {picker}
  }
</script>
