<template>
  <div class="swiper-murquee">
    <div class="murquee-content cl" v-if='!isHide' :style='{width: containerWidth + "px", transform: transform}'>
      <div class="item" v-for="(item,index) in list" :key='index'>
        <!-- 需要有个固定宽度 -->
        <div class="box">
          <img :src="item.headImg | defaultAvatar" class="img" alt="">
          <span>恭喜</span>
          <span> {{item.userName|hideNickname}} </span>
          <span>获得 </span>
          <span class="name">{{item.prizeVO.prizeName}}</span>
          <span>*1</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  nacme: "swiper-murquee",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    time: {
      type: Number,
      default: 40,
    },
  },
  data () {
    return {
      timer: null,
      move: 0,
      isHide: false,
      transform: '',
    };
  },
  computed: {
    containerWidth () {
      if (this.list.length > 0) {
        return this.list.length * 324; // 324是单个item的宽度 加上一个空白容器的宽度
      }
      return 9999;
    },
  },
  watch: {
    list (val) {
      if (val && val.length > 0) {
        this.start();
      }
    },
  },
  mounted () {
    if (this.list && this.list.length > 0) {
      this.start();
    }
  },
  methods: {
    start () {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        this.move -= 2;
        this.transform = `translateX(${this.move}px)`;
        if (Math.abs(this.move) >= this.containerWidth) {
          clearInterval(this.timer);
          this.isHide = true;
          this.move = 0;
          this.transform = `translateX(${this.move}px)`;
          setTimeout(() => {
            this.isHide = false;
            this.start();
          }, 100);
        }
      }, this.time);
    },
  },
  beforeDestroy () {
    // clearInterval(this.timer)
  }
}
</script>

<style lang="less">
.swiper-murquee {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  .murquee-content {
    width: 9999px;
    .item {
      width: 324px;
      float: left;
    }
    .box {
      display: inline-block;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      border-radius: 0.2rem;
      height: 0.24rem;
      line-height: 0.24rem;
      font-size: 0.13rem;
      padding-right: 0.1rem;
      .img {
        width: 0.24rem;
        height: 0.24rem;
        border-radius: 50%;
        float: left;
        margin-right: 0.08rem;
      }
    }
    .name {
      max-width: 1.04rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
