<template>
  <div>
    <div  class="mainBoxNew" :class="{active:isIphoneX}">
      <inviteTop :inviteId="inviteId" @showInvite="showInvite=true"></inviteTop>
      <div class="newTab tab1" :class="{active:showInvite}" >
        <p @click="tabName='newintroduce'">
          <span>成人高考</span>
        </p>
        <p @click="tabName='openUniversity'" >
          <span>开放教育</span>
        </p>
        <p @click="tabName='selfTought'" >
          <span>自学考试</span>
        </p>
        <!-- <p @click="tabName='vocational'" >
          <span>职业教育</span>
        </p> -->
        <p @click="tabName='graduate'">
          <span>在职研究生</span>
        </p>
        <div class="line" :class="clName"></div>
      </div>
      <template v-if="tabName=='newintroduce'">
        <div class="advance"  :class="{active:showInvite}">
          <div class="content" :class="{'bg-g':tabName!='introduce'}">
            <transition name="fade2">
              <div v-if="tabName=='newintroduce'">
                <div class="newBanner">
                  <!-- <img src="../../../assets/image/active/enrollAggregate/banner.jpg" alt>
                  <router-link v-if="!Expired"
                    :to="{name:'adultExamEnrollCheck',query:{action:'login',activityName:'scholarship',inviteId:inviteId,scholarship:scholarship,actName:this.actName,recruitType:'1'}}"
                  >
                    <div class="enrollBtn"></div>
                  </router-link> -->
                  <banner-list :Expired='Expired' :inviteId='inviteId' @toEnroll='toEnroll' />
                  <div class="enrollBtn" v-if='Expired' @click="tips"></div>
                  <img src="../../../assets/image/active/enrollAggregate/dreamBuild.png" alt="" style="position: absolute;width: 1.78rem;top:.1rem;left:.1rem;z-index: 2">

                  <p class="enrollText">已报名人数:</p>
                  <div class="enrollCount">
                    <DigitRoll :rollDigits="EnrolmentCount"/>
                  </div>
                  <swiperEnroll right=".1"  top=".32"/>
                </div>
                <div class="tab tab1" style="z-index: 1000">
                  <div @click.prevent="smallTab01='home'" class="item" :class="{active:smallTab01=='home'}">
                    <span>2022级招生</span>
                  </div>
                  <div @click.prevent="smallTab01='introduce'" class="item" :class="{active:smallTab01=='introduce'}">
                    <span>筑梦介绍</span>
                  </div>
                  <div class="line" :class="{active:smallTab01=='introduce'}"></div>
                </div>
                <div class="enroll" v-if="smallTab01=='home'">
                  <div class="scroll_wrap"></div>
                  <!--<div class="hotActivity" v-if="isShowBanner==1&&banner">-->
                  <!--<div class="headBox">-->
                  <!--<img src="../../../assets/image/active/enrollAggregate/title-hotActivity.png" alt="">-->
                  <!--</div>-->
                  <!--<div class="banner" >-->
                  <!--<router-link :to="{name:'MarchAct',query:{inviteId:inviteId}}">-->
                  <!--<img :src="banner|imgBaseURL" alt="">-->
                  <!--</router-link>-->
                  <!--</div>-->
                  <!--</div>-->
                  <div class="hotActivity" v-if="oneYear" >
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-hotActivity.png" alt="">
                    </div>
                    <div class="oneyear">
                      <router-link :to="{name:'newOneYearSchool',query:{inviteId:inviteId}}">
                        <img src="../../../assets/image/active/oneYear/btn_hous.png" alt="" class="btn">
                      </router-link>
                    </div>
                  </div>
                  <div class="bag_wrap">
                    <div class="bigPackage" :class="{active:isShowEnd==1}">
                      <ul>
                        <li>
                          <img src="../../../assets/image/active/enrollAggregate/gift.png" alt="">
                          <p class="imgTxt">考前辅导课(礼包价199)</p>
                        </li>
                        <li>
                          <img src="../../../assets/image/active/enrollAggregate/gift.png" alt="">
                          <p class="imgTxt">三本教材(礼包价100)</p>
                        </li>
                        <!-- <li>
                          <img src="../../../assets/image/active/enrollAggregate/gift.png" alt="">
                          <p class="imgTxt">成考过295分, 奖3年学费等值智米</p>
                        </li> -->
                        <!-- <li style="margin-top: .1rem">
                          <p class="imgTxt" style="font-size:.12rem;color: rgba(54, 54, 54, .6)">*上述考分不含政策性加分</p>
                        </li> -->
                      </ul>
                      <button @click="toPayment">¥299马上购买</button>
                      <template v-if="isShowEnd==1">
                        <p class="activityText">剩余时间</p>
                        <div class="content">
                          <div  class="cutdown">
                            <newcountdown :startTime="startTime" :endTime="endTime-10*60*1000" txt_color=" #FFFFFF" ref="countdown"></newcountdown>
                          </div>
                        </div>
                      </template>
                      <p class="tips">参加辅导课的学员98%都被心仪的高校录取了哦！</p>
                    </div>
                  </div>
                  <div class="fourAdvantageWrap">
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-advantage.png" alt="" class="fourright">
                    </div>
                    <div class="items">
                      <div class="rightBox">
                        <img src="../../../assets/image/active/enrollAggregate/01-free.png" alt="">
                         <div class="title">免费报名</div>
                        <p class="bottomText">
                          在线报名·提交资料，参加成人高考，录取入学</p>
                      </div>
                    </div>
                    <div class="items">
                      <div class="rightBox">
                        <img src="../../../assets/image/active/enrollAggregate/02-study.png" alt="">
                         <div class="title">学费补贴</div>
                        <p class="bottomText">
                          报名广商、城建、广生态、岭职、华夏院校，成考过295分，奖励3年学费奖学金
                        </p>
                      </div>
                    </div>
                    <div class="items">
                      <div class="rightBox">
                        <img src="../../../assets/image/active/enrollAggregate/03-progress.png" alt="">
                         <div class="title">手续简便</div>
                        <p class="bottomText">
                          凭身份证、毕业证可报名参考，无需单位证明、街道证明、手续简便
                        </p>
                      </div>
                    </div>
                    <div class="items">
                      <div class="rightBox">
                        <img src="../../../assets/image/active/enrollAggregate/04-school.png" alt="">
                         <div class="title">名校录取</div>
                        <p class="bottomText">
                          暨南大学、华南农业大学、汕头大学、广东金融学院等近20余所高校
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="cooperationSchool no-pop">
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-schools.png" alt="" class="fourright">
                    </div>
                    <div class="content2">
                      <div class="school" :class="{active:showAll}">
                        <div class="item noc" >
                          <router-link  :to="{name: 'jnUniversity', query: { inviteId: this.inviteId }}">
                            <i class="jndxNew"></i>
                            <span>暨南大学</span>
                          </router-link>
                        </div>
                        <div class="item noc">
                          <router-link   :to="{name: 'southChinaAgriculturalUniversity', query: { inviteId: this.inviteId }}">
                            <i class="hnnyNew"></i>
                            <span>华南农业大学</span>
                          </router-link>
                        </div>
                        <!-- <div class="item noc ">
                          <router-link    :to="{name: 'stUnviersity', query: { inviteId: this.inviteId }}">
                            <i class="stdx"></i>
                            <span>汕头大学</span>
                          </router-link>
                        </div> -->
                        <div class="item noc ">
                          <router-link  :to="{name:'gdJinRong',query:{inviteId:inviteId}}">
                            <i class="gdjrNew"></i>
                            <span>广东金融学院</span>
                          </router-link>
                        </div>
                        <div class="item noc " @click="gzdxNew()">
                          <i class="gzdxNew"></i>
                          <span>广州大学</span>
                        </div>
                        <div class="item noc " @click="dglgNew()">
                          <i class="dglgNew"></i>
                          <span>东莞理工学院</span>
                        </div>
                        <div
                          class="item noc "
                        >
                          <router-link
                            :to="{name: 'gccScholarship'}"
                          >
                          <i class="gzsxy"></i>
                          <span>广州商学院</span>
                           </router-link>
                        </div>

                        <div class="item noc " @click="toSchoolHome('lnsf')">
                          <i class="lnsfNew"></i>
                          <span>岭南师范学院</span>
                        </div>
                        <!-- <div       class="item noc ">
                          <router-link
                            :to="{name: 'enrollmentHomepage', query: { inviteId: this.inviteId }}"
                          >
                          <i class="gzcjNew"></i>
                            <span>广州城建职业学院</span>
                          </router-link>
                        </div> -->
                        <!-- <div        class="item noc">
                          <router-link

                            :to="{name: 'gdProfessionUniversity', query: { inviteId: this.inviteId }}"
                          >
                            <i class="gdzyjsNew"></i>
                            <span>广东职业技术学院</span>
                          </router-link>
                        </div> -->
                        <div class="item noc ">
                          <router-link   :to="{name: 'gzOpenUniversityForward', query:{ inviteId: this.inviteId } }">
                            <i class="gzgbNew"></i>
                            <span>广州开放大学</span>
                          </router-link>
                        </div>
                        <div class="item noc " @click="toSchoolHome('zknygc')">
                          <i class="zknyNew"></i>
                          <span>仲恺农业工程学院</span>
                        </div>
                        <div
                          class="item noc " @click="toSchoolHome('gdkmzy')"
                        >
                          <i class="gdkmNew"></i>
                          <span>广东科贸职业学院</span>
                        </div>
                        <!-- <div
                          class="item noc " @click="toSchoolHome('gdstgczy')"
                        >
                          <i class="gdstNew"></i>
                          <span>广东生态工程职业学院</span>
                        </div> -->
                        <div class="item noc " @click="toSchoolHome('dgzyjs')">
                          <i class="dgzyNew"></i>
                          <span>东莞职业技术学院</span>
                        </div>
                        <div class="item noc " >
                         <router-link
                            :to="{name: 'forward'}"
                          >
                          <i class="zqxyNew"></i>
                          <span>肇庆学院</span>
                          </router-link>
                        </div>
                        <div class="item noc " @click="toSchoolHome('jy')">
                          <i class="jyxyNew"></i>
                          <span>嘉应学院</span>
                        </div>
                        <!-- <div  class="item noc">
                          <router-link  :to="{name:'gdLinNan',query:{inviteId:inviteId}}">
                            <i class="gdlnNew"></i>
                            <span>广东岭南职业技术学院</span>
                          </router-link>
                        </div> -->
                        <div
                          class="item noc "
                        >
                            <router-link
                            :to="{name: 'forward'}"
                          >
                          <i class="qyzyNew"></i>
                          <span>清远职业技术学院</span>
                          </router-link>
                        </div>
                        <!-- <div class="item noc last">
                            @click="toEnroll({unvsName:'南海成人学院',unvsId:'154745015706371402'})"
                          <i class="nhcrNew"></i>
                          <span>南海成人学院</span>
                        </div> -->
                        <!-- <div
                          class="item noc last" @click="toEnroll({unvsName:'汕尾职业技术学院',unvsId:'31'})"
                        >
                          <i class="swzyNew"></i>
                          <span>汕尾职业技术学院</span>
                        </div> -->
                        <div
                          class="item noc "
                         @click="toSchoolHome('mmzyjs')"
                        >
                          <i class="mmzyjsNew"></i>
                          <span>茂名职业技术学院</span>
                        </div>
                        <div class="item noc">
                          <router-link :to="{ name: 'shanweiInstituteTechnology', query:{inviteId:inviteId} }">
                            <i class="shanweiInstituteTechnology"></i>
                            <span>汕尾职业技术学院</span>
                          </router-link>
                        </div>
                        <div class="item noc">
                          <router-link  :to="{name:'ssProfressionUniversityForward',query:{inviteId:inviteId}}">
                            <i class="sszyjs"></i>
                            <span>广东松山职业技术学院</span>
                          </router-link>
                        </div>
                        <div
                          class="item noc "
                         @click="toSchoolHome('gdjtzy')"
                        >
                          <i class="gdjtzy-icon"></i>
                          <span>广东交通职业技术学院</span>
                        </div>
                        <div class="item noc">
                          <router-link  :to="{name:'jdProfressionUniversityForward',query:{inviteId:inviteId}}">
                            <i class="gdjdzy-icon"></i>
                            <span>广东机电职业技术学院</span>
                          </router-link>
                        </div>
                        <!-- <div
                          class="item noc last"  @click="toSchoolHome('gzhxzy')"
                        >
                          <i class="gzhxzyNew"></i>
                          <span>广州华夏职业学院</span>
                        </div> -->
                      </div>
                      <div class="btn" @click="showAllSchool">
                        <template v-if="!showAll">
                          查看全部20所院校 <img src="../../../assets/image/active/enrollAggregate/arrow_down.png" alt="" >

                        </template>
                        <template v-else>
                          收起院校
                          <img src="../../../assets/image/active/enrollAggregate/arrow_up.png" alt="" >
                        </template>


                      </div>
                    </div>
                  </div>
                  <div class="cityBox no-pop">
                    <div class="content2">
                      <div class="headBox">
                        <img src="../../../assets/image/active/enrollAggregate/title-city.png" alt="" class="fourright">
                      </div>
                      <div class="school" >
                        <a @click="toBuildInfo('gz',{inviteId:inviteId,scholarship:scholarship})" class="bg">
                          <div class="item noc">
                            <i>广州</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('sz',{inviteId:inviteId,scholarship:scholarship})"  class="bg">
                          <div class="item noc last">
                            <i>深圳</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('hz',{inviteId:inviteId,scholarship:scholarship})"  class="bg">
                          <div class="item noc">
                            <i>惠州</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('dg',{inviteId:inviteId,scholarship:scholarship})"  class="bg">
                          <div class="item noc last">
                            <i>东莞</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('hy',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc last">
                            <i>河源</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('yj',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>阳江</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('sw',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>汕尾</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('cz',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>潮州</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('sg',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>韶关</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('zj',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc last">
                            <i>湛江</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('zq',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>肇庆</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('mm',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>茂名</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('qy',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>清远</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('mz',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>梅州</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('jm',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc last">
                            <i>江门</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('st',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>汕头</i>
                          </div>
                        </a>
                        <a @click="toBuildInfo('fs',{inviteId:inviteId,scholarship:scholarship})">
                          <div class="item noc">
                            <i>佛山</i>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="swiper">
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-certification.png" alt="" class="fourright">
                    </div>
                    <swiper :options="swiperOption" ref="mySwiper">
                      <swiper-slide v-for="(item,index) in swiperImgList" :key="index" @click="showIndex(index)">
                        <img class="swiperImg" v-lazy="item.image">
                      </swiper-slide>
                    </swiper>
                  </div>
                </div>
                <div class="introduce" v-if="smallTab01=='introduce'">
                  <div class="activityDetails" >
                    <img src="../../../assets/image/active/enrollAggregate/bg-dreamBuild.png" alt>
                    <p class="schoolDetailsText" >
                       筑梦计划, 是为响应国家号召, 提高全民素质, 推动广东省产业升级, 推出的新生代上进青年培养计划。
                    </p>
                    <p class="schoolDetailsText" >
                      是由远智教育联合<span>广州商学院、广州城建职业学院、广东生态工程职业学院、广东岭南职业技术学院、广州华夏职业学院</span>，5所院校共同实施的公益助学项目, 项目实行<span>成考过295分，奖励3年学费奖学金</span>的奖励机制。
                    </p>
                    <p class="schoolDetailsText" >
                      筑梦计划旨在帮助那些曾经与大学失之交臂, 又努力奋斗的上进青年实现大学梦, 回报社会，实施以来已成功帮助100000人成功报读大专或本科学历。
                    </p>
                  </div>
                  <div class="scholarshipStory">
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-scholarStory.png" alt="" class="fourright">
                    </div>
                    <router-link :to="{name:'scholarshipStoryInfo',query:{id:item.scholarshipId,inviteId:inviteId,resourcesUrl:item.resourcesUrl,createTime:item.createTime}}" v-for="item,index in scholarshipStoryList" :key="item.scholarshipId" class="item cl">

                      <template v-if="(index+1)%4==0&&index!=0">
                        <p class="title img" >{{item.articleTitle}}</p>
                        <div class="img_wrap">
                          <img  v-for="item in item.newAppPicUrl.split(',')" :src="item | imgBaseURL" alt="">
                        </div>
                        <div class="date">
                          <div class="heart">
                            <span class="heartbeat" :class="{active:item.fabulous}" @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)">{{item.fabulousNum}}</span>
                            <span class="read">{{item.readNum}}</span></div>
                          <span style="float: right">{{item.createTime.substring(0,11)}}</span>
                        </div>
                      </template>
                      <template v-else>
                        <div class="fl">
                          <p class="title" style="text-align: left;font-weight: normal">{{item.articleTitle}}</p>
                          <!--<p class="description">{{item.articleLinkDes}}</p>-->
                          <div class="date">
                            <div class="heart">
                              <span class="heartbeat" :class="{active:item.fabulous}" @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)">{{item.fabulousNum}}</span>
                              <span class="read">{{item.readNum}}</span>
                            </div>
                            <span style="float: right">{{item.createTime.substring(0,11)}}</span></div>
                        </div>
                        <div class="fr">
                          <img class="pic" :src="item.articlePicUrl|imgBaseURL" alt="">
                        </div>
                      </template>
                    </router-link>
                    <div class=" cl buttomWrap" style="margin-top: .15rem">
                      <button class="lookMore" @click="lookMoreStotyList">查看更多 ></button>
                    </div>

                    <div style="height:.1rem;background:#fff"></div>
                  </div>
                  <div class="studentStory">
                    <div class="headBox">
                      <img src="../../../assets/image/active/enrollAggregate/title-student.png" alt="" class="fourright">
                    </div>
                    <div class="swiper">
                      <swiper :options="swiperOption">
                        <swiper-slide >
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/1.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/9.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/2.png"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/3.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/4.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/5.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/6.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/7.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/8.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/10.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/11.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/12.jpg"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/fullTimeSystem/student/13.jpg"
                          >
                        </swiper-slide>
                      </swiper>
                    </div>
                  </div>
                </div>

                <div class="tab tab1" style="z-index: 1001">
                  <div @click="smallTab02='msg'" class="item" :class="{active:smallTab02=='msg'}">
                    <span>留言区</span>
                  </div>
                  <div @click="smallTab02='question'" class="item" :class="{active:smallTab02=='question'}">
                    <span>常见问题</span>
                  </div>
                  <div class="line" :class="{active:smallTab02=='question'}"></div>
                </div>
                  <userMessage :scholarship="scholarship" v-show="smallTab02=='msg'"></userMessage>
                <div  style="padding-bottom: .1rem;background-color: white" v-show="smallTab02=='question'">
                  <questionList />
                </div>
              </div>
            </transition>
          </div>
          <template v-if="smallTab01=='home'">
            <div class="bg_copyright">
              <p>

                承办单位: 广州远智教育科技有限公司<br/>
                邮编: 516600 粤ICP备12034252号-1
              </p>
              <div class="link" v-if="isEmployee">
                <router-link class="miban"
                             :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'newDreamBuild'}}"
                >  <img src="../../../assets/image/active/enrollAggregate//btn_miban.png" alt=""> </router-link>
              </div>
            </div>
          </template>
          <share
            :title="title"
            :desc="desc"
            :link="shareLink"
            :isActivity="true"
            :scholarship="scholarship"
            imgUrl="http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png"
            ref="share"
          />
          <div class="img_wrap_single" v-if="isShow">
            <div class="bg" @click="isShow=false"></div>
            <img :src="showImgUrl" alt>
          </div>

        </div>
      </template>
        <div class="openUniversity_wrap" v-if="tabName=='openUniversity'" :class="{active:showInvite}">
          <div >
            <div  class="newBanner" :class="{active:showInvite}">
              <img src="../../../assets/image/active/enrollAggregate/open_banner.jpg" alt>
              <router-link
                :to="{name:'adultExamEnrollCheck',query:{action:'login',activityName:'scholarship',inviteId:inviteId,scholarship:'1',actName:'普通全额',recruitType:'2'}}"
              >
                <div class="enrollBtn"></div>
              </router-link>
              <p class="enrollText">已报名人数:</p>
              <div class="enrollCount">
                <DigitRoll :rollDigits="openEnrollCount"/>
              </div>
              <swiperEnroll right=".1"  top=".12"/>
            </div>
            <div class="tab tab2 tab1" style="z-index: 1000">
              <div @click.prevent="smallTab11='home'" class="item" :class="{active:smallTab11=='home'}">
                <span>招生主页</span>
              </div>
              <div @click.prevent="smallTab11='introduce'" class="item" :class="{active:smallTab11=='introduce'}">
                <span>院校介绍</span>
              </div>
              <div class="line" :class="{active:smallTab11=='introduce'}"></div>
            </div>
            <div class="enroll" v-if="smallTab11=='home'">
              <div class="schoolAdvantage">
                <div class="content">
                  <div class="headTitle">
                    <img src="../../../assets/image/active/enrollAggregate/open_title_advantage.png" alt="">
                  </div>
                  <div class="topView">
                    <div class="left">
                      <p class="topTitle"><img src="../../../assets/image/active/enrollAggregate/01_open.png" alt=""></p>

                      <p class="contentText" style="textalgin:center">
                        教育部直属, 权威有保证!
                      </p>
                    </div>
                    <div class="right">
                      <p class="topTitle"><img src="../../../assets/image/active/enrollAggregate/02_open.png" alt=""></p>
                      <p class="contentText">
                        <!-- 直接入学, 不用参加考试 -->
                        直接入学，不用参加全国成考
                      </p>
                    </div>
                  </div>
                  <div class="middleView">
                    <p class="topTitle"><img src="../../../assets/image/active/enrollAggregate/03_open.png" alt=""></p>
                    <p class="contentText">
                      国家开放大学颁发, 证书可在国家教育部学信网网查, 学历国家承认, 符合相关条件的本科毕业生还可获授学士学位
                    </p>
                  </div>
                  <div class="bottomView">
                    <p class="topTitle"><img src="../../../assets/image/active/enrollAggregate/04_open.png" alt=""></p>
                    <p class="contentText">
                      网络自主学习、远程学习支持服务与面授辅导相结合的新型学习模式
                    </p>
                  </div>
                </div>
              </div>
              <div class="charge">
                <div class="chargetop">
                  <img src="../../../assets/image/active/enrollAggregate/open_title_fee.png" alt="">
                </div>

              </div>
              <div class="highUniversityBox">
                <div class="highUniversity">
                  <div class="title">
                    <span class="text">- 高起专 -</span>
                  </div>
                  <div class="content">
                    <div class="titleText">
                      <p>文史、 经管类</p>
                    </div>
                    <div class="flLeft">
                      <p class="contentText">
                      学前教育、工商企业管理、会计、电子商务、市场营销(市场开发与营销方向)、小学教育、人力资源管理、行政管理、体育运营与管理
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">1710</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <div class="contentTwo">
                    <div class="titleText">
                      <p>理工类</p>
                    </div>
                    <div class="flLeft">
                      <p class="contentText">
                        计算机信息管理、机电一体化技术、建设工程管理
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">1976</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <div class="contentTwo">
                    <div class="titleText" style="height: .5rem;">
                      <p style="margin-top: 25%;">理工类</p>
                    </div>
                    <div class="flLeft" style="height: .5rem;lineHeight:.3rem;">
                      <p class="contentText">
                        建筑工程技术
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">1976</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <div class="contentTwo">
                    <div class="titleText" style="height: .5rem;">
                      <p style="margin-top: 25%;">医学类</p>
                    </div>
                    <div class="flLeft" style="height: .5rem;lineHeight:.3rem;">
                      <p class="contentText">
                        药学
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">2301</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <!--<div class="bottomText">-->
                  <!--考区：阳江、梅州、东莞、河源、肇庆、深圳、清远、韶关、惠州、广州、佛山-->
                  <!--</div>-->
                </div>
                <div class="highUniversityTwo">
                  <div class="title">
                    <span class="text">- 专升本 -</span>
                  </div>
                  <div class="content">
                    <div class="titleText">
                      <p>文史、 经管类</p>
                    </div>
                    <div class="flLeft">
                      <p class="contentText">
                          汉语国际教育、会计学、行政管理、学前教育、小学教育、工商管理、汉语言文学（师范方向）、汉语言文学
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">1863</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <div class="contentTwo">
                    <div class="titleText">
                      <p>理工类</p>
                    </div>
                    <div class="flLeft">
                      <p class="contentText">
                        计算机科学与技术
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">2000</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <div class="contentTwo">
                    <div class="titleText">
                      <p>理工类</p>
                    </div>
                    <div class="flLeft">
                      <p class="contentText">
                      土木工程
                      </p>
                    </div>
                    <div class="flRight">
                      <p class="top">2147</p>
                      <p class="bottom">元/学期</p>
                    </div>
                  </div>
                  <!--<div class="bottomText">-->
                  <!--考区：阳江、梅州、东莞、河源、肇庆、深圳、清远、韶关、惠州、广州、佛山-->
                  <!--</div>-->
                </div>
                <div class="bottomText">
                  <p>
                    <span class="iconBox"></span>
                    <span>教材费：</span><span>250元/学期（按4学期收取）</span>
                  </p>
                  <p>
                    <span class="iconBox"></span>
                    <span>地区：</span>
                    <span>阳江、梅州、东莞、河源、肇庆、深圳、清远、韶关、惠州、广州、佛山、茂名、湛江</span>
                  </p>
                </div>
              </div>

            </div>
            <div v-if="smallTab11=='introduce'">
              <div class="schoolDetails" :class="{showall:true,active:showall}">
                <img src="../../../assets/image/active/openUniversity/<EMAIL>" alt="">
                <p class=schoolDetailsText>
                  &emsp;&emsp;国家开放大学是中华人民共和国教育部直属的，以现代信息技术为支撑，学历教育与非学历教育并举，实施远程开放教育的新型高等学校。学校是在中央广播电视大学和地方广播电视大学的基础上组建，以现代信息技术为支撑，办学网络立体覆盖全国城乡，学历与非学历教育并重，面向全体社会成员，没有围墙的新型大学。国家开放大学有权授予学士学位，由学校向北京市学位委员会申请并获批后，报国务院学位委员会备案。目前，注册在学生359万人，其中本科学生105万人，专科学生254万人，包括近20万农民学生，10万士官学生，6000多残疾学生。国家开放大学的组建成立，标志着广播电视大学系统在新的历史起点上踏上了新的征途。
                  <br><br>
                  <strong>历史使命</strong><br><br>
                  &emsp;&emsp;国家开放大学强调“开放、责任、质量、多样化、国际化”的办学理念，大力发展非学历继续教育，稳步发展学历继续教育，推进现代科技与教育的深度融合，搭建终身学习“立交桥”，适应国家经济社会发展和人的全面发展需要，促进终身教育体系建设，促进全民学习、终身学习的学习型社会形成。经过10年努力，把国家开放大学建设成为我国高等教育体系中一所新型大学；世界开放大学体系中富有中国特色的开放大学；我国学习型社会的重要支柱。
                  <br><br>
                  <strong>学习模式</strong><br><br>
                  &emsp;&emsp;国家开放大学致力于实现有支持的开放式学习，探索以学习者为中心，基于网络自主学习、远程学习支持服务与面授辅导相结合的新型学习模式。以需求为导向，以能力培养为核心，改革教学内容和课程体系，与行业企业合作，科学、灵活、有针对性地开设国家开放大学特色专业。改进教学方法，为学习者提供集多媒体资源、教学交互、学习评价和学习支持服务于一体的海量、优质网络课程。通过遍布全国的学习中心提供面授辅导，也可以通过高清、快速的双向视频系统促进师生实时交流，为学习者提供随时随地的远程学习支持服务。推进以终结性考试为主向形成性考核为主的多元评价模式转变。
                  <br><br>
                  <strong>公共服务</strong><br><br>
                  &emsp;&emsp;国家开放大学也是一所新型的公益性大学，扎根基层，服务社会。利用国家开放大学网络平台和数字化学习资源库，开设网上大讲堂，向全体社会成员提供形式多样、内容丰富的网络讲座、公开课，提升公民科学文化素质，满足社会成员多样化、个性化学习需求。为普通高校、中高职院校、社会培训机构、行业企业、城乡社区提供包括远程学习支持、相关教育培训、信息咨询等各类公共服务。与相关国际组织、大学和机构开展针对性、多层次、宽领域的教育交流与合作。加强基于网络的孔子学院建设，大力推进对外汉语教学，促进中华文化走向世界。
                </p>
              </div>
              <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="!showall">
                <a :class="{active:showall}" @click="lookMore">
                  <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                  <span>查看更多</span>
                </a>
              </div>
              <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="showall">
                <a :class="{active:showall}" @click="showall=false">
                  <span class="down"></span>
                  <span>收起</span>
                </a>
              </div>
              <div class="story">
                <div class="top">
                  <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">

                  <span>学员上进故事</span>
                </div>
                <router-link :to="{name:'scholarshipStoryInfo',query:{id:item.scholarshipId,inviteId:inviteId,resourcesUrl:item.resourcesUrl,createTime:item.createTime}}" v-for="item,index in scholarshipStoryList" :key="item.scholarshipId" class="item cl">

                  <template v-if="(index+1)%4==0&&index!=0">
                    <p class="title img" >{{item.articleTitle}}</p>
                    <div class="img_wrap">
                      <img  v-for="item in item.newAppPicUrl.split(',')" :src="item | imgBaseURL" alt="">
                    </div>
                    <div class="date"><div class="heart"><span class="heartbeat" :class="{active:item.fabulous}" @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)">{{item.fabulousNum}}</span><span class="read">{{item.readNum}}</span></div><span style="float: right">{{item.createTime.substring(0,11)}}</span></div>
                  </template>
                  <template v-else>
                    <div class="fl">
                      <p class="title" style="text-align: left;font-weight: normal">{{item.articleTitle}}</p>
                      <!--<p class="description">{{item.articleLinkDes}}</p>-->
                      <div class="date"><div class="heart"><span class="heartbeat" :class="{active:item.fabulous}" @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)">{{item.fabulousNum}}</span><span class="read">{{item.readNum}}</span></div><span style="float: right">{{item.createTime.substring(0,11)}}</span></div>
                    </div>
                    <div class="fr">
                      <img class="pic" :src="item.articlePicUrl|imgBaseURL" alt="">
                    </div>
                  </template>
                </router-link>
                <div class=" cl buttomWrap" style="margin-top: .15rem">
                  <button class="lookMore" @click="lookMoreStotyList">查看更多 ></button>
                </div>

                <div style="height:.1rem;background:#fff"></div>
              </div>
              <div class="studentStory">
                <div class="top">
                  <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                  <span class="topTitle">学员风采</span>
                </div>
                <div class="swiper">
                  <swiper :options="swiperOption">
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/1.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/9.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/2.png"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/3.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/4.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/5.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/6.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/7.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/8.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/10.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/11.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/12.jpg"
                      >
                    </swiper-slide>
                    <swiper-slide>
                      <img
                        class="swiperImg"
                        src="../../../assets/image/active/fullTimeSystem/student/13.jpg"
                      >
                    </swiper-slide>
                  </swiper>
                </div>
              </div>
            </div>
            <div class="tab tab2 tab1" style="z-index: 1001">
              <div @click="smallTab12='msg'" class="item" :class="{active:smallTab12=='msg'}">
                <span>留言区</span>
              </div>
              <div @click="smallTab12='question'" class="item" :class="{active:smallTab12=='question'}">
                <span>常见问题</span>
              </div>
              <div class="line" :class="{active:smallTab12=='question'}"></div>
            </div>
            <!--<div class="userMessage" v-if="smallTab12=='msg'">-->
              <!--<div class="textContent">-->
                <!--<div class="userMessageContentList" :class="{anim:animatePraise==true}">-->
                  <!--<div class="content" v-for="(item,index) in openenrollMsgList" :key="index">-->
                    <!--<div class="fl">-->
                      <!--<img :src= "item.headImg?item.headImg+headLimit:item.headImg |defaultAvatar2" alt="">-->
                    <!--</div>-->
                    <!--<div class="fr">-->
                      <!--<p class="userName">{{item.nickname|hideNickname}}</p>-->
                      <!--<p class="uesrQuestion">{{item.msgContent}}</p>-->
                      <!--<div class="content" v-if= item.msgReply>-->
                        <!--&lt;!&ndash;<div class="line"></div>&ndash;&gt;-->
                        <!--<p class="answer"><span>回复:&nbsp;</span>{{item.msgReply}}</p>-->
                      <!--</div>-->
                    <!--</div>-->
                    <!--<div class="line"></div>-->
                  <!--</div>-->
                <!--</div>-->
              <!--</div>-->
              <!--<div class="userMessageContent">-->
                <!--<div class="fl">-->
                  <!--<img :src= "userImg |defaultAvatar2" alt="" >-->
                  <!--&lt;!&ndash;<img src='../../../assets/image/active/enrollmentHomepage/defaultUserIcon.jpg'  v-if= !userImg>&ndash;&gt;-->
                <!--</div>-->
                <!--<div class="fr">-->
                  <!--<p class="userName">{{userName}}</p>-->
                  <!--<textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>-->
                  <!--<span>{{message.length}}/50</span>-->
                  <!--<button @click="enrollMsg('1')">提交留言</button>-->
                <!--</div>-->
              <!--</div>-->
            <!--</div>-->
            <userMessage scholarship="1" v-show="smallTab12=='msg'"></userMessage>
            <div  style="padding-bottom: .1rem;background-color: white" v-show="smallTab12=='question'">
              <questionList type="openUniversity" :needTop="true" />
            </div>
            <share
              title="来国家开放大学读学历，权威有保证！"
              desc="报读缴费即入学，不用参加全国成考，证书学信网可查！"
              :link="openLink"
              :isActivity="true"
              scholarship="1"
              imgUrl="http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/gjkf.png"
              ref="share"
            />
          </div>
        </div>
        <div class="selfTought_wrap" v-if="tabName=='selfTought'" :class="{active:showInvite}">
          <div class="yz-self-tought__header">
            <img src="../../../assets/image/active/selfTought/1-1.png" v-if='isNewSelf' class="bg" alt="">
            <img src="../../../assets/image/active/selfTought/1.jpg" v-else class="bg" alt="">
            <div class="top">
              <div class="has-people">
                <span>已报名人数：</span>
                <DigitRoll :rollDigits="selfEnrollCount"/>
              </div>
              <swiperEnroll right=".1"  top=".12"/>
            </div>
          </div>
          <div class="yz-self-tought__content">
            <div class="tab2 tab1" style="z-index: 1001">
              <div @click.prevent="smallTab21 = 0" class="item" :class="{active:smallTab21 === 0}">
                <span>2021招生</span>
              </div>
              <div @click.prevent="smallTab21 = 1" class="item" :class="{active:smallTab21 === 1}">
                <span>常见问题</span>
              </div>
              <div class="line" :class="{active:smallTab21 === 1}"></div>
            </div>
            <recruit-students :inviteId='inviteId' v-if='smallTab21 === 0'></recruit-students>
            <question v-if='smallTab21 === 1'></question>
             <div class="link" v-if="isEmployee">
                <router-link class="miban"
                             :to="{name:'inviteLink',query: {scholarshipName:'selfTought'}}"
                >  <img src="../../../assets/image/active/dreamBuild2021/btn_miban.png" alt=""> </router-link>
              </div>
          </div>
          <share
            title="自考精英计划，靠谱的教学质量，学历提升无忧虑"
            desc="自考精英计划是远智教育精心打造的学历提升产品，旨在帮助更多上进青年提升学历，拥抱更好的未来。"
            :link="selfLink"
            :isActivity="true"
            scholarship="1"
            ref="share"
          />
        </div>
        <div class="vocational_wrap" v-if="tabName=='vocational'" :class="{active:showInvite}">
          <div class="nothing">
            <div>即将上线, 敬请期待～</div>
            <div class="button-box">
              <router-link to="/active/collectOpinion">
                <button >
                  <span>提升个人技能</span>
                  <i></i>
                </button>
              </router-link>
            </div>
          </div>
        </div>
      <div class="graduate_wrap" v-if="tabName=='graduate'" :class="{active:showInvite}">
        <div class="graduate__header">
          <div v-if="recruitType !== '5'" @click="toGrade()">
            <div class="newBanner" :class="{active:showInvite}">
                <img  src="../../../assets/image/active/enrollAggregate/graduate/banner.png" alt />
            </div>
          </div>
          <router-link v-else to="/student">
            <div class="newBanner" :class="{active:showInvite}">
                <img  src="../../../assets/image/active/enrollAggregate/graduate/banner.png" alt />
            </div>
          </router-link>
          <div class="top">
            <div class="has-people">
              <span>已报名人数：</span>
              <DigitRoll :rollDigits="graduateEnrollCount" />
            </div>
            <swiperEnroll right=".1" top=".12" />
          </div>
        </div>

        <div class="graduate__content">
          <div class="tab2 tab1" style="z-index: 1001">
            <div @click.prevent="graduateTab = 0" class="item" :class="{active:graduateTab === 0}">
              <span>院校专业</span>
            </div>
            <div @click.prevent="graduateTab = 1" class="item" :class="{active:graduateTab === 1}">
              <span>报考流程</span>
            </div>
            <div @click.prevent="graduateTab = 2" class="item" :class="{active:graduateTab === 2}">
              <span>八大优势</span>
            </div>
            <div @click.prevent="graduateTab = 3" class="item" :class="{active:graduateTab === 3}">
              <span>政策咨询</span>
            </div>
            <div
              class="line"
              :class="[{'active':graduateTab === 1},{'active2':graduateTab === 2},{'active3':graduateTab === 3}]"
            ></div>
          </div>
          <div class="tab-content" v-if="graduateTab === 0">
            <ul>
                <li class="MBALi">
                  <div>
                    <!-- <p class="en-title">MBA</p> -->
                    <p class="major">工商管理硕士</p>
                    <button class="lookSchool" @click="getScoolList('工商管理硕士（MBA）')">查看院校 <img src="../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
                  </div>
                </li>
                <li class="MEMLi">
                  <div>
                    <!-- <p class="en-title">MEM</p> -->
                    <p class="major">工程管理硕士</p>
                    <button class="lookSchool" @click="getScoolList('工程管理硕士（MEM）')">查看院校 <img src="../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
                  </div>
                </li>
                <li class="MPALi">
                  <div>
                    <!-- <p class="en-title">MPA</p> -->
                    <p class="major">公共管理硕士</p>
                    <button class="lookSchool" @click="getScoolList('公共管理硕士（MPA）')">查看院校 <img src="../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
                  </div>
                </li>
            </ul>
          </div>

          <div class="tab-content-2" v-if="graduateTab === 1">
            <img src="../../../assets/image/active/enrollAggregate/graduate/tqpc.png">
            <img class="img2" src="../../../assets/image/active/enrollAggregate/graduate/zcpc.png">
          </div>

          <div class="tab-content-3" v-if="graduateTab === 2">
            <ul>
              <li v-for="item in tabContent3ImgList">
                <img :src="item.imgUrl">
                <p class="text-1" >{{item.text1}}</p>
                <p class="text-2" v-html="item.text2"></p>
              </li>
            </ul>
          </div>

          <graduateQuestion v-if='graduateTab === 3'></graduateQuestion>
          <div class="messageHead">
            <img src="../../../assets/image/active/enrollAggregate/graduate/message.png">
            <span>留言区</span>
          </div>
          <userMessage class="graduateUserMessage" scholarship="137" v-show="smallTab12=='msg'"></userMessage>
          <div class="link" v-if="isEmployee">
            <router-link
              class="miban"
              :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'selfTought'}}"
            >
              <img src="../../../assets/image/active/dreamBuild2021/btn_miban.png" alt />
            </router-link>
          </div>
        </div>
        <share
          title="上班族在职也不怕，利用空闲时间一起来拿名校双证硕士！"
          desc="本科毕业三年，专科毕业五年，现在报考，多重优惠，点击了解"
          :link="graduateLink"
          :isActivity="true"
          scholarship="137"
          ref="share"
        />
      </div>
      </div>
      <van-popup round v-model="showGraduate" position="bottom"  class="graduatePop">
        <div class="head_box">
          <div class="line"></div>
          <p class="pfName">{{selectpfname}}</p>
          <img src="../../../assets/image/active/enrollAggregate/graduate/close.png" @click="showGraduate=false">
        </div>
        <ul class="enrollSchoolUl">
          <template v-for="(item, index) in graduateSchoolList">
            <li :key='index'>
                <img class="back" :src="'http://yzims.oss-cn-shenzhen.aliyuncs.com/'+ 'university/backgroundForH5/'+ item.unvsCode+'.png'" alt  @click="showGraduate=false"/>
                <img class="icon" :src="'http://yzims.oss-cn-shenzhen.aliyuncs.com/'+ 'university/logo/'+ item.unvsCode+'.png'" alt  @click="showGraduate=false"/>
                <p class="school_name">{{ item.unvsName }}</p>
                <button @click="ToGraduateForm(item.unvsName,item.unvsId,item.unvsCode)">报读<img src="../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
            </li>
          </template>
        </ul>
      </van-popup>
      <footer-bar @isIphoneX="isIphoneX=true" :Expired="Expired" :inviteId="inviteId" :scholarship="scholarship" :actName="actName" :openactName="openactName" :tabName="tabName" :regChannel='regChannel' :regOrigin='regOrigin' />
      <!-- 一次性代码 618 过期删 -->
      <fix-btn />
    </div>
</template>
<script>
  import { toLogin, isEmployee, isQQ, getIsInTimeByType, isNewSelf,isStudent} from "../../../common";
  import { swiper, swiperSlide } from "vue-awesome-swiper";
  import { Collapse, CollapseItem } from "vant";
  import share from "@/components/share";
  import countdown from "@/components/active/countdown2";
  import newcountdown from "@/components/active/countdown5";
  import graduateQuestion from "@/components/activePage/graduateQuestion";
  import DigitRoll from "@huoyu/vue-digitroll";
  import { filterEmoji } from "../../../common";

  import commonPop from "@/components/commonPopBlank"
  import loadBar from "@/components/loadBar"
  import footerBar from "@/components/activePage/footer"
  import questionList from "@/components/activePage/questionList"
  import { Tabs } from 'vant';
  import RecruitStudents from './../selfTought/components/recruitStudents';
  import Question from './../selfTought/components/question';
  import YFooter from './../selfTought/components/footer';
  import swiperEnroll from "./components/swiper-enroll"
  import inviteTop from "../enrollAggregate/components/invite-top"
  import userMessage from "./components/userMessage"
  import bannerList from "./components/banner-list";
  // 618活动 一次代码 过期就删掉
  import fixBtn from '../618Activity/components/fix-btn'

  let scholarship = "164"; // 优惠类型
  export default {
    components: {
      share,
      countdown,
      Collapse,
      CollapseItem,
      newcountdown,
      swiper,
      swiperSlide,
      commonPop,
      loadBar,
      footerBar,
      questionList,
      Tabs,
      RecruitStudents,
      Question,
      YFooter,
      swiperEnroll,
      inviteTop,
      DigitRoll,
      userMessage,
      bannerList,
      graduateQuestion,
      fixBtn, // 618
    },
    data() {
      return {
        selectpfname:'',
        graduatePfsnName:'',
        showGraduate:false,
        title:'在线报读，名校录取，学信网可查，助你拿大专本科学历！',
        desc:'筑梦计划是远智教育携手各大高校联合推出，旨在帮助更多产业工人和上进青年实现大学梦的奖励计划。',
        EnrolmentCount: 0,
        animate: false,
        animatePraise: false,
        animateAwardlist: false,
        tabName: "newintroduce",
        tabNameArr:{
          0:['newintroduce','openUniversity','selfTought','vocational','graduate'],
          "01":['home','introduce'],
          "02":['msg','question'],
          "11":['home','introduce'],
          "12":['msg','question'],
          "21":[0,1],
        },
        isIphoneX:false,
        inviteId: "",
        scholarship: scholarship,
        shareLink: "",
        openLink:window.location.origin+'/active/newDreamBuildUpdate?tab=openUniversity',
        selfLink:window.location.origin+'/active/newDreamBuildUpdate?tab=selfTought',
        graduateLink:window.location.origin + "/active/newDreamBuildUpdate?tab=graduate",
        message: "",
        showMsgBox: false,
        showArea: false,
        items: [],
        isLogin: null,
        isEmployee: isEmployee(),
        Expired: false,
        started: true,
        arrowIndex: 1,
        smallTab01:'home',
        smallTab11:'home',
        smallTab02:'msg',
        smallTab12:'msg',
        enrollMsgList: [], //留言列表
        invite: {},
        showInvite: false,
        nowTime: "",
        startTime: '', // 倒计时开始时间：2018-11-11 20:55:00
        endTime: '', //   修改结束时间：2019年1月28日23时59分59秒
        userImg: "",
        userName: "",
        swiperOption: {
          initialSlide: 1,
          autoplay: 2000,
          centeredSlides: true,
          loop: true,
          slidesPerView: "auto",
          loopedSlides: 1,
          autoplayDisableOnInteraction: false,
          on:{
            click: ()=>{
              let swiper = this.$refs.mySwiper.swiper;
              console.log(swiper);
              let i = swiper.activeIndex;
              vm.showIndex(i)
            }
          }
        },
        showall: false,
        showallBox: false,
        isShow: false,
        storyImgLimit: "?x-oss-process=image/resize,m_fixed,h_116,w_143",
        loadingFlag: true,
        headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
        swiperImgList: [
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/cj.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/gj.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/gk.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/hn.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/jy.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/sw.jpg")
          },
          {
            image: require("../../../assets/image/active/newDreamBuild/dreamFive/zk.jpg")
          }
        ],
        actName:'',
        openactName:'',
        isShowEnd:0,
        scholarshipStoryList:[],
        isShowFlag:false,
        isShowBanner:0,
        graduateEnrollCount:0,
        banner:'',
        oneYear:false,
        nickName:'',
        showAll:false,
        showImgUrl:'',
        smallTab21: 0,
        graduateTab: 0,
        selfEnrollCount: 0,
        openEnrollCount:0,
        newRegList: [], // 已注册人数
        touch:{startX:0},
        deltaX:0,
        deltaY:0,
        isInvite5MonthActive: getIsInTimeByType('decisive'), // 邀约2020-5月活动
        bannerList: [],
        actTime:0,
        tabContent3ImgList:[
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/ssxw.png"),text1:'硕士学位',text2:`双证毕业，国家学位网可查，<br>含金量最高`},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/szgl.png"),text1:'实战管理',text2:`提升综合管理能力、强化企业<br>运作效率`},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/jyrm.png"),text1:'精英人脉',text2:'汇聚各行各业中高层管理者，<br>优质人脉圈'},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/zyzh.png"),text1:'资源整合',text2:'利用校友资源、名校资源实现<br>1+1＞2效果'},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/ktsy.png"),text1:'开拓视野',text2:'系统学习MBA课程，实战案<br>例剖析，提升格局'},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/szjx.png"),text1:'升职加薪',text2:'起点高、人脉广、资源多、<br>机会自然也多'},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/xymr.png"),text1:'校友名人',text2:'同各知名企业名人高管、政<br>府事业单位领导成为校友'},
          {imgUrl: require("../../../assets/image/active/enrollAggregate/graduate/tab-3/slbj.png"),text1:'实力背景',text2:'公司上市用于对外学历共识，<br>强化个人背景光环'},
        ],
        graduateSchoolList:[],
        recruitType:'',
        isNewSelf,
        regChannel:"",
        regOrigin:"",
      }
    },
    created() {
      this.recruitType = this.storage.getItem('recruitType')
      this.shareLink = `${window.location.origin}/active/newDreamBuildUpdate`;
      if(this.$route.query.tab&&['newintroduce','openUniversity','selfTought','graduate'].includes(this.$route.query.tab)){
        this.tabName=this.$route.query.tab
      }
      this.getActivityInfo();
      // this.gethlTime();
      this.actTime = new Date().getTime();
      this.isLogin = !!this.storage.getItem("authToken");
      this.nickName=this.storage.getItem('zmcName')
      this.userName=this.storage.getItem('realName')||this.storage.getItem('zmcName')||this.storage.getItem('mobile');
      this.userImg = this.storage.getItem("headImg") || "";
      this.inviteId = this.$route.query.inviteId || "";
      this.regOrigin = this.$route.query.regOrigin || "";
      this.regChannel = this.$route.query.regChannel || "";
      this.action = this.$route.query.action || "";
      window.sessionStorage.setItem(
        "channelId",
        this.$route.query.channelId || ""
      ); // 用于统计来源

      if (this.action === "share" && !this.isLogin) {
        toLogin.call(this, null);
      }
      const { query } = this.$route;
      this.$router.push({
        name: 'newMain',
        query: { ...query },
      });
    },
    mounted() {
      setTimeout(() => {
        MtaH5.clickStat("wxdreambuild");
      }, 1000);
      window.addEventListener("scroll", this.handleScroll, true);
      if(!!this.isLogin){
          this.justIdcard()
      }

    },
    watch: {
      tabName: function(newValue) {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
        if(newValue=='openUniversity'){
          if(this.openEnrollCount==0){
            this.getActivityInfo('1')
          }
        }else if(newValue=='selfTought'){
          if(this.selfEnrollCount==0){
            this.getActivityInfo('66')
          }
        }else if(newValue=='newintroduce'){
          this.$nextTick(()=>{
            const now = new Date().getTime();
            this.nowTime = now;
            this.started = now > this.startTime;
            this.Expired = now > this.endTime;
            this.$refs.countdown && this.$refs.countdown.getSystemDateTime(now);
          })
        }else if(newValue =='graduate') {
          this.getActivityInfo("106");
        }
      },
      smallTab01:function(newValue){
        if(newValue=='introduce'&this.scholarshipStoryList.length==0){
            this.getScholarshipStoryList()
        }
      },
      smallTab11:function(newValue){
        if(newValue=='introduce'&this.scholarshipStoryList.length==0){
          this.getScholarshipStoryList()
        }
      },
    },
    computed: {
      jumpUrl: function() {
        let url = {
          name: "adultExamEnrollCheck",
          query: {
            activityName: "scholarship",
            inviteId: this.inviteId,
            action: "login",
            scholarship: this.scholarship,
            actName:this.actName,
            recruitType:'1'
          }
        };
        return url;
      },
      clName:function(){
        return this.tabName
      }
    },
    methods: {
      justIdcard(){
        let phone =  this.storage.getItem("mobile") ||  this.storage.getItem("phone");
        this.$http.post("/us/getUserIdCard/1.0/", { mobile: phone }).then(res => {
          let { code, body } = res;
          if (body == null) {
            this.storage.setItem("bindStudent", "0");
          } else if (body.bindStudent == "1") {
            this.storage.setItem("bindStudent", "1");
          } else {
            this.storage.setItem("bindStudent", "2");
          }
        })
      },
      toGrade(){
        // console.log(this.storage.getItem('bindStudent') !='0'&&isStudent(),this.storage.getItem('bindStudent')!= '2')
        // return
         if(this.storage.getItem('bindStudent') !== '2'&&(this.storage.getItem('bindStudent') !='0'&&isStudent())){
          const url=window.location.pathname
             this.$router.push({ name: 'roleAuth', query: { redirect:url} })
             return
        }
        this.$router.push({path:"/active/graduate/form"})
      },
      toSchoolHome(res){
         this.$router.push({
          path: "/active/schoolHome/" + res ,query:{inviteId: this.inviteId,regOrigin:this.regOrigin,regChannel:this.regChannel}
        });
      },
    /**
     * 在职研究生相关
      */
    getScoolList(pfsnName) {
      this.graduatePfsnName = pfsnName;
      this.$http.post('/bds/getResearchUnvs/1.0/',{pfsnName:pfsnName,pageSize:99,pageNum:0}).then(res=>{
        let {code,body} = res;
        if(code !== '00') return;
        this.graduateSchoolList = body;
        this.$nextTick(()=>{
          if(pfsnName == '工商管理硕士（MBA）') {
            this.selectpfname = '工商管理硕士(MBA)'
          }else if(pfsnName == '工程管理硕士（MEM）') {
            this.selectpfname = '工程管理硕士(MEM)'
          }else if(pfsnName == '公共管理硕士（MPA）') {
            this.selectpfname = '公共管理硕士(MPA)'
          }
          this.showGraduate = true;
        })
      })
    },
    ToGraduateForm(unvsName,unvsId,unvsCode) {
      if(this.recruitType !== '5') {
        let current = {unvsName,unvsId,unvsCode}
        this.$router.push({name:'graduateForm',query:{unvs:JSON.stringify(current),pfsnName:this.graduatePfsnName}});
      }else {
        this.$router.push({name:'stuInfo'})
      }

    },
      golive(){
         this.$router.push({ path: "/app/liveStream"})
      },
      gzdxNew(){
        this.$router.push({ path: "/active/guangZhouUniversity",query:{inviteId:this.inviteId,regOrigin:this.regOrigin}})
      },
      gzsxy(){
        this.$router.push({ path: "/active/Guangzhoucommercial",query:{inviteId:this.inviteId,regOrigin:this.regOrigin}});
      },
      dglgNew(){
        this.$router.push({ path: "/active/DongguanUniversity",query:{inviteId:this.inviteId,regOrigin:this.regOrigin} });
      },
      showIndex(i){
        this.showImgUrl=this.swiperImgList[i];
        this.isShow=true
      },
      showAllSchool(){
        this.showAll=!this.showAll
      },
      getStyle(oEle, attr) {
        if (window.getComputedStyle) {
          return window.getComputedStyle(oEle, null)[attr];
        }
        return oEle.currentStyle[attr];
      },
      lookMoreStotyList() {
        this.$router.push({ name: "scholarshipStory",query:{inviteId:this.inviteId,scholarship:this.scholarship,regOrigin:this.regOrigin}});
      },
      //筑梦信息
      getActivityInfo(scholarshipDream=scholarship) {
        console.log(scholarshipDream);
        this.$http
          .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarshipDream })
          .then(res => {
            let { code, body } = res;
            if (code !== "00") return;
            switch (scholarshipDream) {
              case scholarship:
                this.isShowBanner = body.isShowBanner;
                this.banner = body.banner;
                this.startTime = body.StartTime;
                this.isShowEnd=res.body.isShowEnd;
                this.endTime = body.EndTime;
                this.actName = body.actName;
                this.EnrolmentCount = parseInt(body.learnCount);
                this.$nextTick(()=>{
                  this.getSystemDateTime();
                })
                    break;
              case '66':  this.selfEnrollCount = parseInt(body.learnCount); break;
              case '1':  this.openEnrollCount = parseInt(body.learnCount);this.openactName = body.actName; break;
              case "106":this.graduateEnrollCount = parseInt(body.learnCount);break;
            }
          });
      },
      gethlTime() {
        this.$http
          .post("/mkt/getActivityInfo/1.0/", { scholarship:"72" })
          .then(res => {
            const { code, body } = res;
            let hlstartTime= body.StartTime;
            let  hlendTime = body.EndTime;
            const now = new Date().getTime();
            // if(now>hlstartTime&&now<hlendTime){
            //   this.oneYear=true
            // }
          });
      },
      //滑动切换
      touchStart(e){
        const touch = e.touches[0]
        this.touch.startX = touch.pageX
        this.touch.startY = touch.pageY

      },
      touchMove(e) {
        const touch = e.touches[0]
        //横向和纵向偏离位置
        this.deltaX = touch.pageX - this.touch.startX
        this.deltaY = touch.pageY - this.touch.startY

      },touchEnd() {
        const deltaX=this.deltaX
        const deltaY=this.deltaY
        console.log(deltaX,deltaY);
        console.log(Math.abs(deltaY),Math.abs(deltaX),Math.abs(deltaY) > Math.abs(deltaX));
        if (Math.abs(deltaY) > Math.abs(deltaX)||Math.abs(deltaY)>20) {
          this.deltaY=0;
          this.deltaX=0;
          return
        }
        var index=0
        for (let i=0;i<document.getElementsByClassName('tab1').length;i++){
         if(this.getStyle(document.getElementsByClassName('tab1')[i],"position")=='fixed')
           index=i
        }
        const tabIndex=index==0?0:this.tabNameArr[0].indexOf(this.tabName).toString()+index;
        var tabNameIndex;
        if(index==0){
           tabNameIndex=this.tabNameArr[tabIndex].indexOf(this.tabName)
        }else{
          tabNameIndex=this.tabNameArr[tabIndex].indexOf(this["smallTab"+tabIndex])
        }
        if(deltaX<-30){
          if(index==0){
            this.tabName=this.tabNameArr[tabIndex][tabNameIndex<3?tabNameIndex+1:3]
          }else{
            this["smallTab"+tabIndex]=this.tabNameArr[tabIndex][tabNameIndex<1?tabNameIndex+1:1]
          }
        }else if(deltaX>30){

          if(index==0){
            this.tabName=this.tabNameArr[tabIndex][tabNameIndex>0?tabNameIndex-1:0]
          }else{
            this["smallTab"+tabIndex]=this.tabNameArr[tabIndex][tabNameIndex>0?tabNameIndex-1:0]
          }
        }
        this.deltaY=0;
        this.deltaX=0;
      },
      //监听滑动的距离
      handleScroll() {
        let scrollTop =
          window.pageYOffset ||
          document.documentElement.scrollTop ||
          document.body.scrollTop;
        if (this.loadingFlag) {
          if (scrollTop > 500) {
            if (this.action === "share" && !this.isLogin) {
              toLogin.call(this, null);
            } else {
              this.loadingFlag = false;
            }
          }
        }
        let oTab=document.getElementsByClassName('tab1')
        for (let i=0;i<oTab.length;i++){
          if(this.getStyle(oTab[i],"position")=='relative'){
            this.tabName=='newintroduce'?oTab[i].top=oTab[i].offsetTop+document.getElementsByClassName('newTab')[0].offsetHeight+oTab[i].parentNode.offsetTop: oTab[i].top=oTab[i].offsetTop
          }
          if(scrollTop>=oTab[i].top||(scrollTop>=oTab[i].top&&oTab[i].className.indexOf("newTab")!=-1)) {
            oTab[i].nextElementSibling.style.marginTop=oTab[i].offsetHeight+'px'
            oTab[i].style.position = "fixed";
              oTab[i].style.top = "0";
              oTab[i].style.left = "0";
              oTab[i].style.right = "0";
              oTab[i].style.margin = "auto";
              if(oTab[i].children[0].className.indexOf('active')!=-1&&(oTab[i].children[0].children[0].innerText=='2021级招生')){
                  document.getElementsByClassName('scroll_wrap')[0].style.position="fixed";
                  document.getElementsByClassName('scroll_wrap')[0].style.top=".5rem";
              }
            } else if(scrollTop<oTab[i].top) {
              oTab[i].style.position="relative";
              oTab[i].style.top="0";
              oTab[i].nextElementSibling.style.marginTop=0
            if(oTab[i].children[0].className.indexOf('active')!=-1&&(oTab[i].children[0].children[0].innerText=='2021级招生')){
              document.getElementsByClassName('scroll_wrap')[0].style.position="absolute";
              document.getElementsByClassName('scroll_wrap')[0].style.top="0";
            }
            }
        }
      },
      phoneNumberMobile() {
        window.location.href = "tel:" + this.cardMobile;
      },
      jump() {
        window.location.href = this.propagateUrl ? this.propagateUrl : "";
      },
      countAdd() {
        this.EnrolmentCount += 1;
      },
      toStory(id, inviteId) {
        this.$router.push({
          name: "scholarshipStoryInfo",
          query: { id: id, inviteId: inviteId }
        });
      },
      toBuildInfo(id, query) {
        query["actName"]=this.actName
        this.$router.push({
          path: "/active/dreamBuild/newDreamBuildInfo/" + id,
          query: query
        });
      },
      phoneNumber() {
        if (this.cardMobile) {
          window.location.href = "tel:" + this.cardMobile;
          return;
        }
        window.location.href = "tel:4008336013";
      },
      // 点击查看更多
      lookMore() {
        this.showall = true;
        this.showallBox = true;
      },
      // 获取服务器时间，判断活动是否已结束
      getSystemDateTime() {
        const now = new Date().getTime();
        this.nowTime = now;
        this.started = now > this.startTime;
        this.Expired = now > this.endTime;
        this.$refs.countdown && this.$refs.countdown.getSystemDateTime(now);
      },
      turn: function() {
        if (this.started) {
          this.showArea = true;
        } else {
          this.tips();
        }
      },
      openShare: function(url) {
        this.$router.push({
          name: "dreamBuildInvite",
          query: { from:url}
        });
      },
      toCode(){
        this.$router.push({name:'myEwm',query:{shareLink:`${window.location.origin}/active/newDreamBuildUpdate?tab=openUniversity`}})
      },
      openShare1(){
        this.$refs.share.open();
      },
      closeArea: function() {
        this.showArea = false;
      },
      // 轮播文字
      scroll() {
        this.animate = true; // 因为在消息向上滚动的时候需要添加css3过渡动画，所以这里需要设置true
        setTimeout(() => {
          //  这里直接使用了es6的箭头函数，省去了处理this指向偏移问题，代码也比之前简化了很多
          this.items.push(this.items[0]); // 将数组的第一个元素添加到数组的
          this.items.shift(); //删除数组的第一个元素
          this.animate = false; // margin-top 为0 的时候取消过渡动画，实现无缝滚动
        }, 500);
      },
      toDetail(){
        this.$router.push({name:'gdProfessionUniversity',inviteId:this.$route.query.inviteId})
      },
      openShowMsgBox() {
        this.showMsgBox = true;
      },
      closeShowMsgBox() {
        this.showMsgBox = false;
      },
      // 获取奖学金故事列表
      getScholarshipStoryList() {
        this.$http.post('/mkt/scholarshipStoryList/1.0/',{informationType:1,authToken:this.storage.getItem('authToken')}).then(res => {
          const {code, body} = res;
          if (code === '00') {
            this.scholarshipStoryList=body.sort(function(a,b){
              return new Date(b.createTime).getTime()-new Date(a.createTime).getTime()
            }).slice(0,4);
          }
        });
      },
      updateFabulousNum(id,num,i){
        let data={
          scholarshipId:id,
          fabulousNum:num?-1:1,
          authToken:this.storage.getItem('authToken')
        }
        this.$http.post('/mkt/updateFabulousNum/1.0/',data).then(res=>{
          if(res.code=='00'){
            this.scholarshipStoryList[i].fabulous=num?false:true;
            num?this.scholarshipStoryList[i].fabulousNum-=1:this.scholarshipStoryList[i].fabulousNum=1+parseInt(this.scholarshipStoryList[i].fabulousNum)
          }
        })
      },
      showStory(){
        this.tabName='newStory';
      },
      // 评论
      enrollMsg: function(scholarship) {
        if (!this.isLogin) {
          toLogin.call(this, null);
          return;
        }
        if (this.isEmployee) {
          this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
          return;
        }
        if (!this.message) {
          this.$modal({ message: "请输入评论内容", icon: "warning" });
          return;
        }
        this.$http
          .post("/mkt/enrollMsg/1.0/", {
            scholarship: scholarship,
            msgContent: filterEmoji(this.message)
          })
          .then(res => {
            let { code, body } = res;
            if (code === "00") {
              this.$modal({
                message: "提交成功",
                beforeClose: (action, instance, done) => {
                  done();
                  this.message = "";
                  this.getEnrollMsgList(scholarship);
                }
              });
            }
          });
      },
      // 去报读页
      toPayment: function() {
         if(!this.Expired){
           this.toEnroll();
         }else{
           this.tips()
         }
      },
      toEnroll(unvs){
        this.$router.push({
          name: "adultExamEnrollCheck",
          query: {
            activityName: "scholarship",
            inviteId: this.inviteId,
            action: "login",
            scholarship: this.scholarship,
            actName:this.actName,
            unvs:JSON.stringify(unvs),
            recruitType:'1'
          }
        });
      },
      tips() {
        this.$modal({ message: "活动暂已结束！", icon: "warning" });
      },
      gotoEnroll: function () {
        if (!this.storage.getItem('authToken')) {
          this.$router.push({name: 'adultExamEnrollCheck',query:{unvs:JSON.stringify({
                unvsName:'国家开放大学',
                unvsId:'46',
              }),actName:this.actName,recruitType:'2'}});
        } else {
          this.$refs.share.isBindMobile('/active/adultExam/check', () => {
            this.$router.push({name: 'adultExamEnrollCheck',query:{unvs:JSON.stringify({
                  unvsName:'国家开放大学',
                  unvsId:'46',
                }),actName:this.actName,recruitType:'2'}});
          });
        }
      },
      lotterytips(){
        // this.$modal({message:  '抽奖活动已经下线！', icon: 'warning'});
        let now = new Date().getTime();
        let stop = 1561716000000;// 2019/6/28 18:00 抽奖入口关闭
        if(now > stop) {
          this.$modal({message: '抽奖活动已经下线！', icon: 'warning'});
        }else {
          this.$router.push({path:'/active/iphoneXLotteryThr',query:{scholarship:scholarship}})
        }
      },

    },
    beforeDestroy() {
      window.removeEventListener("scroll", this.handleScroll, true);
    },
  };
</script>
<style lang="less" scoped>
  @import "../../../assets/less/variable";
  .mainBoxNew {
    font-family: STSongti-SC-Light !important;
    .gifBox {
      width: 1.44rem;
      height: 1.11rem;
      position: fixed;
      top: 50%;
      right: 0;
      z-index: 9;
      .bg {
        position: absolute;
        width: 100%;
        height: 100%;
      }
      .img1 {
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
        top: .05rem;
        width: 1.38rem;
        height: .85rem;
        -webkit-animation: scaleDraw 2.5s ease-in-out infinite;
      }
      .img2 {
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
        bottom: -.05rem;
        width: 1.56rem;
        height: .85rem;
        -webkit-animation: scaleDraw 2.5s ease-in-out infinite;
        animation-delay: 2s;
        -webkit-animation-delay: 2s;
      }
    }
    .Expired {
      text-align: center;
      img {
        margin-top: 1.38rem;
        width: 1.6rem;
      }
      p {
        color: #999;
        margin-top: 0.5rem;
      }
      a {
        background: @bgColor;
        display: inline-block;
        width: 2.14rem;
        height: 0.44rem;
        line-height: 0.44rem;
        border-radius: 1rem;
        color: #fff;
        font-size: 0.16rem;
        margin-top: 0.2rem;
      }
    }
  }

  .mainBoxNew /deep/.texta {
    animation: slide 1s;
    margin-bottom: 0;
    .van-collapse {
      .van-collapse-item {
        .van-cell {
          line-height: 0.54rem;
          height: auto;
          .van-icon-arrow:before{
            color: RGBA(175, 72, 16, 1);
          }
          .van-cell__right-icon {
            margin-top: 3%;
          }
          .van-cell__value--alone{
            color: RGBA(175, 72, 16, 1);
          }
          .indexText {
            width: 0.15rem;
            height: 0.17rem;
            background: RGBA(255, 244, 241, 1);
            border-radius: 8px 8px 8px 0px;
            color: RGBA(159, 55, 0, 1);
            font-weight: 600;
            font-size: 0.12rem;
            margin: .1rem 0.17rem 0rem .09rem;
            padding-left: 0.03rem;
            padding-right: 0.03rem;
          }
        }
        .van-collapse-item__wrapper {
          .van-collapse-item__content {
            padding: 0.13rem 0.56rem 0.13rem 0.44rem;
            color: RGBA(175, 72, 16, 1);
            background-color: RGBA(255, 244, 241, 1)
          }
        }
      }
    }
    [class*=van-hairline]::after{
      border: 0 solid RGBA(252, 227, 222, 1);
    }
  }
  .mainBoxNew /deep/ .d-roll-list {
    margin-top: -0.05rem;
    font-family: PingFang SC;
    font-weight: 400;
    color: #C12500;
    font-size: 0.15rem;
  }
  .mainBoxNew /deep/ .d-roll-item {
    height: 0.25rem !important;
    flex-grow: 0.03;
  }
  .mainBoxNew /deep/ .van-collapse-item__content {
    background-color:rgba(158, 77, 0, .08);
  }
  .mainBoxNew .advance /deep/ .swiper-slide {
     background-size: 100% 100%;
  }
  .mainBoxNew .advance /deep/ .swiper .swiper-container{
     img{
       border: 2px solid #cccccc;
     }
   }
  .mainBoxNew .advance /deep/ .swiper-slide-active {
    background-size: 100% 100%;
  }
  .mainBoxNew /deep/ .d-roll-wrapper {
    margin: 0 auto;
  }
  .mainBoxNew /deep/ .van-cell {
    line-height: 0.4rem !important;
  }
  .mainBoxNew {
    &.active{
      padding-bottom: 34px;
    }
    .headBox{
     background:white;
     height: 0.7rem;
    }
    .scholarshipStory {
      margin-bottom: .6rem;
      background:white;
      .invite {
        background-image: url('../../../assets/image/active/enrollmentHomepage/invitBg2.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: .68rem;
        position: relative;
        margin-bottom: -.1rem;
        z-index: 99;
        img {
          width: .48rem;
          height: .48rem;
          float: left;
          border-radius: 50%;
          margin-top: 1.8%;
          margin-left: .12rem;
          margin-right: .07rem;
        }
        .rightView {
          float: left;
          margin-top: 2.3%;
          p {
            font-size: .16rem;
            span {
              color: #e15443;
              font-size: .13rem;
            }
            &:first-of-type {
              font-size: .13rem;
            }
          }
        }
      }
      .item {
        display: block;
        background-color: #fff;
        position: relative;
        padding: .2rem .15rem .1rem .15rem;
        &:before {
          .borderBottom;
        }
        .fl {
          width: 69%;
          p{
            line-height: 1.5;
          }
        }
        .fr {
          width: 30%;
          text-align: right;
        }
        .title {
          font-size: .15rem;
          color: #363636;
          min-height: .54rem;
          overflow: hidden;
          &.img{
            min-height: .27rem;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .date {
          margin-top: .1rem;
          .heart{
            width: 1.5rem;
            float: left;
            span{
              display: block;
              float: left;
              margin-right: .17rem;
              padding-left: .21rem;
              line-height: .18rem;
              overflow: hidden;
              &.heartbeat{
                background: url("../../../assets/image/scholarshipStory/ic_school_like.png") no-repeat;
                background-size: .18rem;
                &.active{
                  background: url("../../../assets/image/scholarshipStory/ic_school_like_select.png") no-repeat ;
                  background-size: .18rem;
                }
              }
              &.read{
                background: url("../../../assets/image/scholarshipStory/ic_school_like copy.png") no-repeat 0 .01rem;
                background-size: .16rem;
              }
            }
          }
          font-size: .12rem;
          color: rgba(54,54,54,.6);
        }
        .img_wrap{
          width: 100%;
          height: auto;
          overflow: hidden;
          margin-bottom: .11rem;
          img{
            width: 1.12rem;
            height: 1.12rem;
            float: left;
            margin-left: .04rem;
            border-radius: .05rem;
            &:first-of-type{
              margin-left: 0;
            }
          }
        }
        .description{
          font-size: .12rem;
          color: #444;
        }
        .pic {
          width: .72rem;
          height: .72rem;
          object-fit: cover;
          border-radius: .05rem;
        }
      }
    }
    .lottery {
      position: fixed;
      // 隐藏
      // display: none;
      //top: {{this.showInvite?"3.08rem":"1.92rem"}};
      /*left: 2.66rem;*/
      bottom:.7rem;
      right:0rem;
      width: .87rem;
      transition: all 1s;
      z-index: 99;
      /*height: 1.32rem;*/
      /*visibility: hidden;*/
    }
    .lottery2 {
      position: fixed;
      // 隐藏
      // display: none;
      //top: {{this.showInvite?"3.08rem":"1.92rem"}};
      /*left: 2.66rem;*/
      bottom:.7rem;
      right:-.37rem;
      width: .37rem;
      transition: all 1s;
      z-index: 99;
      /*height: 1.32rem;*/
      /*visibility: hidden;*/
    }
    .fixBtn {
      position: fixed;
      bottom: 7%;
      right: 0.15rem;
      width: 0.64rem;
      height: 0.72rem;
      line-height: 0.35rem;
      text-align: center;
      color: #fff;
      z-index: 999;
      /*background-color: #f1a664;*/
      background-image: url("../../../assets/image/active/dreamBuild/bmrk.png");
      background-size: 100%;
      border-radius: 0.05rem;
      &.lf {
        right: auto;
        left: 0.15rem;
      }
      a {
        display: block;
        height: 100%;
        color: #fff;
        font-size: 0.16rem;
      }
    }

    .four {
      text-align: center;
      padding: 0 0.1rem;
      img {
        width: 100%;
      }
    }

    .isPay {
      position: fixed;
      top: 0.4rem;
      left: 0.1rem;
      img {
        width: 0.6rem;
      }
    }
    .newTab {
      width: 3.75rem;
      height: 0.5rem;
      z-index: 999;
      background-color: rgba(8, 87, 168, 1);
      position: fixed;
      top:0;
      left:0;
      right:0;
      margin: auto;
        &.active{
          position: relative;
        }
      p {
        position: relative;
        float: left;
        height: 0.5rem;
        width: 25%;
        text-align: center;
        span {
          line-height: .46rem;
          display: inline-block;
          width: 100%;
          font-size: 0.16rem;
          margin-top: 0.02rem;
          font-weight: bold;
          color: #fff;
          font-family: STSongti-SC-Light;
        }
      }
      .line {
        position: absolute;
        height: 2px;
        width: 0.4rem;
        background: #fff;
        bottom: 0.07rem;
        transition: all .5s;
        &.newintroduce{
          left: .27rem;
        }
        &.openUniversity{
          left: 1.2rem;
        }
        &.selfTought{
          left: 2.14rem;
        }
        &.graduate{
          left: 3.08rem;
        }
      }
    }
    .advance {
      position: relative;
      margin-top: .5rem;
      padding-bottom: .6rem;
      &.active{
        margin-top: 0rem;
      }
      > .content {
        background-image: url("../../../assets/image/active/dreamBuild/content-bg.png");
        background-size: 100%;
        /*background-repeat: no-repeat;*/
        &.bg-g {
          background-image: none;
          .bottom {
            .lxwm {
              color: #fff;
            }
            .pic {
              span {
                color: #fff;
              }
            }
            .ewm {
              border-color: #fff;
              p {
                color: #fff;
              }
            }
          }
        }



        .tab-3 {
          height: 0.38rem;
          margin-top: -0.02rem;
          p {
            float: left;
            height: 0.38rem;
            width: 25%;
            background-color: #198381;
            text-align: center;
            padding: 0.1rem 0;
            span {
              display: inline-block;
              width: 100%;
              border-right: 0.02rem solid #126462;
              /*line-height: .5rem;*/
              font-size: 0.14rem;
              color: #fff;
            }
            &.active {
              background-color: #fff;
              padding: 0rem;
              line-height: 0.38rem;
              span {
                position: relative;
                /*border-bottom: .04rem solid #198381;*/
                width: auto;
                border-right: none;
                color: #177d7b;
                border-radius: 0.02rem;
                &:before {
                  content: "";
                  height: 0.04rem;
                  background-color: #198381;
                  width: 0.3rem;
                  bottom: -0.05rem;
                  transform: translateX(-50%);
                  left: 50%;
                  position: absolute;
                  border-radius: 2px;
                }
              }
            }
            &:last-of-type {
              span {
                border-right: none;
              }
            }
          }
        }
        .newBanner {
          position: relative;
          img {
            width: 100%;
          }
          .enrollText {
            position: absolute;
            top: .11rem;
            right: .65rem;
            font-size: .14rem;
            font-family: PingFang SC;
            font-weight: 400;
            color: #C12500;
            opacity: 0.9;
            line-height: .15rem;
            z-index: 2;
          }
          .enrollCount {
            // width: 0.55rem;
            height: 0.22rem;
            position: absolute;
            top: .11rem;
            text-align: center;
            color: rgba(54, 54, 54, 1);
            font-size: 0.13rem;
            right:.1rem;
            padding-left: 1.2%;
            font-weight: bold;
            z-index: 2;
          }
          .enrollBtn{
            position:absolute;
            width:1.65rem;
            height:.41rem;
            left:1.06rem;
            top:2.76rem;
            color: white;
            font-size: .22rem;
            text-align: center;
            line-height: .58rem;
          }
          .enrolled{
            width: 100%;
            position: absolute;
            color:rgba(252, 66, 27, 1);
            font-size: .14rem;
            font-style: normal;
            top:2.65rem;
            text-align: center;
          }
        }
        .scroll_wrap{
          background-image: url("../../../assets/image/active/enrollAggregate/bg-school.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          height: 3.25rem;
          width: 100%;
          z-index: 1;
          position: absolute;
          top:0rem;
        }
        .userMessage {
          background-color: #fff;
          //   background: url('../../../assets/image/active/newDreamBuild/dreamFive/userMessageBg.png') no-repeat center;
          border-radius: 0.1rem;
          width: 3.75rem;
          margin: 0 auto;
          height: auto;
          overflow: hidden;
          animation: slide .5s ;
          .headBox {
            width: .6rem;
            height: 0.3rem;
            text-align: center;
            img {
              width: 2.46rem;
              margin-top: 0.19rem;
              margin-bottom: 0.15rem;
            }
          }
          .textContent {
            //background: url("../../../assets/image/active/newDreamBuild/dreamFive/userMessageBg.png")
            //no-repeat center;
            background-size: 3.75rem;
            overflow: hidden;
            height: 4.1rem;
            .userMessageContentList {
              width: 3.75rem;
              &.anim {
                transition: all 1s;
                margin-top: -1.7rem;
              }
              .content {
                overflow: hidden;
                height: 1.33rem;
                position: relative;
                .fl {
                  width: 0.64rem;
                  img {
                    width: 0.38rem;
                    height: 0.38rem;
                    float: right;
                    border-radius: 50%;
                    margin-top: 0.11rem;
                  }
                }
                .fr {
                  width: 3.11rem;
                  height: 1.14rem;
                  .userName {
                    margin-left: 0.1rem;
                    margin-top: 0.12rem;
                    font-size: 0.14rem;
                    color: rgba(23, 6, 6, 0.8);
                  }
                  .uesrQuestion {
                    display: inline-block;
                    width: 2.76rem;
                    margin-top: 0.03rem;
                    margin-left: 0.1rem;
                    font-size: 0.14rem;
                    color: rgba(54, 54, 54, .8);
                  }
                  .line {
                    position: absolute;
                    display: inline-block;
                    width: 0.02rem;
                    height: 0.14rem;
                    background-color:rgba(239, 186, 6, 1);
                    top: 0.17rem;
                    left: 0.01rem;
                  }
                  .content {
                    position: relative;
                    margin-bottom: 0.09rem;
                    height: auto;
                    .line {
                      position: absolute;
                      display: inline-block;
                      width: 0.02rem;
                      height: 0.14rem;
                      background-color:rgba(239, 186, 6, 1);
                      top: 0.17rem;
                      left: 0.01rem;
                    }
                    .answer {
                      display: inline-block;
                      border-radius: 0.05rem;
                      width: 2.66rem;
                      font-size: 0.14rem;
                      color: rgba(54, 54, 54, 1);
                      padding: 0.13rem 0.07rem 0.13rem 0.1rem;
                    }
                  }
                }
              }
            }
          }

          .userMessageContent {
            width: 3.75rem;
            overflow: hidden;
            .fl {
              width: 0.64rem;
              img {
                width: 0.38rem;
                height: 0.38rem;
                float: right;
                border-radius: 50%;
                margin-top: 0.11rem;
              }
            }
            .fr {
              width: 3.11rem;
              position: relative;
              .userName {
                margin-left: 0.1rem;
                margin-top: 0.12rem;
                font-size: 0.14rem;
                color: rgba(23, 6, 6, 0.8);
                display: inline-block;
                width: 2.48rem;
                height: 0.24rem;
              }
              textarea {
                width: 2.77rem;
                height: 0.86rem;
                border: 1px solid rgba(240, 86, 10, 1);
                padding: 0.13rem 0.1rem 0.13rem 0.1rem;
                margin-top: 0.03rem;
                border-radius: .05rem;
              }
              span {
                position: absolute;
                font-size: 0.09rem;
                color: rgba(23, 6, 6, 0.4);
                top: 1.07rem;
                right: 0.38rem;
              }
              button {
              width: 1.34rem;
              height: 0.4rem;
              border-radius: .18rem;
              background-image: linear-gradient(to bottom,rgba(255, 132, 0, 1),rgba(251, 50, 3, 1));
              border-bottom: solid 4px rgba(185, 35, 0, 1);
              margin: 0.2rem 0 0.4rem .54rem;
              color:#fff;
              font-size: 0.17rem;
              font-weight: 600;
              }
            }
          }
        }
        .enroll,.introduce{
          animation: slide 1s;
          position: relative;
          background-color: #E6E6E6;
        }
        .hotActivity{
          width: 100%;
          background-color: transparent;
          height: auto;
          overflow: hidden;
          .headBox{
            width:3.55rem;
            img{
              width: 100%;
            }
          }
          .oneyear{
            width: 3.55rem;
            margin: 0 auto;
            height: 1.05rem;
            background: url("../../../assets/image/active/oneYear/banner.png");
            background-size:100%  100%;
            .btn{
              width: 1.2rem;
              height: .4rem;
              margin-top: 0.7rem;
              margin-left:.2rem ;
              -webkit-animation: download 1s linear alternate infinite;
              animation: download 1s linear alternate infinite;
              @-webkit-keyframes download{
                0%{-webkit-transform:scale(0.9);}
                100%{-webkit-transform:scale(1);}
              }
              @keyframes download{
                0%{transform:scale(0.9);}
                100%{transform:scale(1);}
              }
            }
          }
          .banner{
            width: 100%;
            height: auto;
            overflow: hidden;
            margin-top: .1rem;
            margin-bottom: .1rem;
            img{
              width: 3.55rem;
              float: left;
              margin-left: .1rem;
              border-radius: .05rem;

            }
          }
        }
       .bag_wrap{
            background: white;
         .bigPackage {
           position: relative;
           width: 100%;
           height: 3.25rem !important;
           text-align: center;
           padding-top: 0.75rem;
           z-index: 2;
           &.active{
             height:2.88rem;
             background-size: 100% 100%;
             background-repeat: no-repeat;
           }
           .activityText {
             position: absolute;
             left: 0.64rem;
             top: 2.87rem;
             font-size: 0.12rem;
            font-family: PingFang SC;
            font-weight: 400;
            color: #FFFFFF;
           }
           .content {
             position: absolute;
             top: 2.87rem;
             left: 1.16rem;
           }
           button {
             width: 1.72rem;
             height: .4rem;
             line-height: .4rem;
             text-align: center;
             color: #fff;
             font-size: .17rem;
             border-radius: .36rem;
             font-weight: bold;
             display: block;
             margin: 0.1rem auto ;
             margin-top: .24rem;
            background: linear-gradient(0deg,  #A50609,#DB3135);
            // box-shadow: 0px 0.04rem 0.04rem 0px rgba(166, 19, 8, 0.4);
            border-bottom: 0.04rem solid #511001;
           }
           .title{
             text-align: center;
             font-size: 0.3rem;
             font-family: ysbh;
             color: #F31539;
             padding-top: 0.25rem;
             line-height: 1;
           }
           .tips{
             font-size:0.12rem;
             margin-top: .12rem;
           }
           ul {
             width: 2.7rem;
            //  padding: 0.7rem 0 0.2rem 0rem;
             margin: auto;
             li {
               width: 100%;
               font-size: 0.15rem;
               text-align: left;
               font-weight: 500;
               color: rgba(54, 54, 54, 1);
               line-height: 0.18rem;
               margin-top: 0.2rem;
               padding-left: 0.1rem;
                   display: flex;
                align-items: baseline;
               img {
                 color: rgba(252, 66, 27, 1);
                 width: .15rem;
                 height: .14rem;
                 margin-top: .02rem;
                 border-radius: .03rem;
                 font-size: .13rem;
               }
               &:last-of-type{
                 p{
                   margin-left: 0.04rem;
                 }
               }
             }
             p {
               color: #363636;
               display: inline-block;
               font-size: 0.15rem;
               margin-top: -0.06rem;
              margin-left: .03rem;
              font-family: PingFang SC;
              font-weight: 400;
             }
           }
         }
       }

        .fourAdvantageWrap {
          /*padding-bottom: 0.4rem;*/
           background: white;
          height: auto;
          overflow: hidden;
          .headBox {
            width: 3.75rem;
            text-align: center;
            background-position: .5rem 0;
            position: relative;
           .fourright{
             width: 1.61rem;
             height: 0.35rem;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
           }
          }
          .items {
            width: 1.68rem;
            float: left;
            height: auto;
            overflow: hidden;
            margin-bottom: .1rem;
            padding-bottom: .07rem;
            background-repeat: no-repeat;
            background-size: 100%;
            background-color: #fff;
            border-radius: .05rem;
            margin-left:.1rem ;
            margin-bottom: 0.1rem;
            .rightBox {
              width:1.53rem;
              margin: 0 auto;
              img{
                width: 0.45rem;
                height: 0.46rem;
              }
              .title{
              font-size: 0.17rem;
              font-weight: bold;
              color: #F03F00;
              display: inline-block;
              height: 0.46rem;
              line-height: 0.43rem;
              font-family: PingFang SC;
              text-align: center;
              }
              .topText {
                font-weight: bold;
                color: rgba(230, 75, 35, 1);
                font-size: 0.17rem;
                text-align: center;
              }
              .bottomText {
                font-size: 0.13rem;
                color: rgba(54, 54, 54, 1);
                line-height: 0.19rem;
                word-break: break-all;
                margin-left: 0.09rem;
              }
            }
          }
        }

        .dreamBuildIntroduce {
          text-align: center;
          background-color: #fff;
          padding-bottom: 0.2rem;
          .dreamBuildImg {
            width: 3.35rem;
            margin-top: 0.1rem;
          }
          .dreamBuildText {
            width: 3.35rem;
            margin: 0 auto;
            margin-top: 0.15rem;
            text-align: left;
          }
        }

        .studentStory {
          background: rgb(255, 255, 255);
          //   height: 2.41rem;
          width: 3.75rem;
          height: auto;
          overflow: hidden;
          .headBox{
             width:100%;
            height: 0.8rem;
            position: relative;
            .fourright{
                width: 1.61rem;
                height: 0.35rem;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
            }
          }
          .swiper{
            background-color: white;
            background-image: none;

            .swiper-container{
              padding-bottom: 0;
            }
          }
        }
        .cooperationSchool {
          background-color: transparent;
         margin-top: 0.1rem !important;
         background: white;
         padding-bottom: 0.19rem !important;
          .headBox {
             width: 3.75rem;
             height: 1rem;
            text-align: center;
            background-position: .5rem 0;
            position: relative;
           .fourright{
             width: 1.61rem;
             height: 0.35rem;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
           }
          }
          position: relative;
          text-align: center;
          width: 100%;
          margin: 0 auto ;
          overflow: hidden;
          height: auto;
          padding-bottom: .1rem;
          .content2 {
            height: auto;
            p {
              position: absolute;
              left: 41%;
              font-size: 0.14rem;
              font-weight: bold;
              color: #fff;
              line-height: 0.33rem;
              top: 0;
            }
            .school {
              &.active{
                height: 5.4rem;
                .item{
                  &.last{
                    opacity: 1;
                  }
                }
              }
              transition: all 1s;
              height: 3.58rem;
              overflow: hidden;
              display: flex;
              display: -webkit-flex;
              flex-wrap: wrap;
              -webkit-flex-wrap: wrap;
              padding: 0.02rem 0.17rem 0.1rem;
              justify-content: flex-start;
              -webkit-justify-content: flex-start;
              .item_big {
                width: 1.7rem;
                margin-bottom: 0.15rem;
                img {
                  width: 1.7rem;
                }
              }
              .item {
                display: block;
                border-radius: .025rem;
                box-shadow: .01rem  .02em 0.04rem  rgba(0, 0, 0, 0.3) ;
                opacity: 1;
                transition: opacity 1s;
                background-color: white;
                a{
                  display: block;
                  width: 100%;
                  height: 100%;
                }
                &:nth-of-type(4n+1){
                  margin-left: 0;
                }
                &.last{
                  opacity: 0;
                }
                margin-left: .08rem;
                width: 0.79rem;
                height: 0.83rem;
                margin-bottom: 0.07rem;
                background-size: 100%;
                position: relative;
                i {
                  display: block;
                  margin: 0.05rem auto 0;
                  width: 0.42rem;
                  height: 0.42rem;
                  object-fit: cover;
                }
                span {
                  display: block;
                  font-size: 0.11rem;
                  margin: 0 auto;
                  text-align: center;
                  color: rgba(54, 54, 54, 1);
                  font-weight: bold;
                  width: 0.73rem;
                }
                &:nth-of-type(3n) {
                  margin-right: 0;
                }
              }
            }
            .btn{
              width: 2.18rem;
              height: .3rem;
              border-bottom: solid 3px rgba(185, 35, 0, 1);
              border-radius: .15rem;
              color: white;
              font-weight: bold;
              line-height: .3rem;
              text-align: center;
              margin: .2rem auto 0;
              background: linear-gradient(0deg,  #A50609,#DB3135);
              // box-shadow: 0px 0.02rem 0.02rem 0px rgba(166, 19, 8, 0.4);
             border-bottom: 0.04rem solid #511001;
              img{
                width: .12rem;
                height: .12rem;
                margin-top: .09rem;
              }
            }
          }
        }
        .cityBox {
          position: relative;
          text-align: center;
          width: 3.75rem;
          margin: 0 auto;
          background: white;
          margin-top: .1rem;
          background: transparent;
          overflow: hidden;
          .content2 {
             background: white;
            .headBox {
              width: 3.75rem;
              height: 1rem;
              text-align: center;
              background-position: .5rem 0;
              position: relative;
              .fourright{
                width: 3.01rem;
                height: 0.35rem;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%,-50%);
              }
            }
            p {
              position: absolute;
              left: 41%;
              font-size: 0.14rem;
              font-weight: bold;
              color: #fff;
              line-height: 0.33rem;
              top: 0;
            }

            .school {
              //   height: 2.7rem;
              overflow: hidden;
              padding: 0 0.08rem 0.1rem;
              a {
                float: left;
                display: block;
                width: 0.6rem;
                height: 0.4rem;
                color: #fff;
                margin-bottom: .19rem;
                border-bottom: solid .04rem #511001;
              background: linear-gradient(0deg,#A50609,  #DB3135);
                margin-right: .115rem;
                border-radius: .025rem;
                &.bg{
                  width: .8rem;
                  height: .59rem;
                  border-radius: .025rem;
                  margin-right: .09rem;
                  margin-bottom: .1rem;
                  border: none;
                  .item{
                    width: 100%;
                    height: 100%;
                    i{
                      line-height: .59rem;
                      background-position: .56rem .24rem;
                      padding-left: .17rem;
                    }
                  }
                  &:first-of-type{
                    background-image: url("../../../assets/image/active/enrollAggregate/btn-guangzhou.png");
                    background-size: 100% 100%;
                  }
                  &:nth-of-type(2){
                    background-image: url("../../../assets/image/active/enrollAggregate/btn-shenzhen.png");
                    background-size: 100% 100%;
                  }
                  &:nth-of-type(3){
                    background-image: url("../../../assets/image/active/enrollAggregate/btn-huizhou.png");
                    background-size: 100% 100%;
                  }
                  &:nth-of-type(4){
                    background-image: url("../../../assets/image/active/enrollAggregate/btn-dongguan.png");
                    background-size: 100% 100%;
                  }
                }
                .item {
                  width: 100%;
                  height:100%;
                  border-radius: 2.5px;
                  margin-bottom: 0;
                  position: relative;
                  i {
                    display: block;
                    height: 0.4rem;
                    background-color: rgba(230, 75, 35, .1);
                    line-height: 0.36rem;
                    width: 100%;
                    color: #fff;;
                    font-size: 0.17rem;
                    font-style: normal;
                    text-align: left;
                    background-image: url("../../../assets/image/active/enrollAggregate/arrow.png");
                    background-repeat: no-repeat;
                    background-size: .06rem .1rem;
                    background-position: .5rem .15rem;
                    padding-left: .09rem;
                  }
                  &:nth-of-type(3n) {
                    margin-right: 0;
                  }
                  &.dsnm:after {
                    content: "";
                    position: absolute;
                    width: 0.405rem;
                    height: 0.28rem;
                    background-image: url("../../../assets/image/active/adultExam/dsnm_new.png");
                    background-repeat: no-repeat;
                    background-size: 100%;
                    right: -0.1rem;
                    top: -0.1rem;
                  }
                  &.dsnj:after {
                    content: "";
                    position: absolute;
                    width: 0.405rem;
                    height: 0.28rem;
                    background-repeat: no-repeat;
                    background-size: 100%;
                    right: -0.1rem;
                    top: -0.1rem;
                  }
                }
              }
            }
          }
        }
        .recruitStudentsCity {
          position: relative;
          text-align: center;
          width: 3.55rem;
          margin: 0 auto;
          border: 1px solid rgba(243, 180, 34, 1);
          border-radius: 0.1rem;
          margin-top: 0.2rem;
          .titleImg {
            width: 2.51rem;
          }
          p {
            position: absolute;
            left: 37%;
            font-size: 0.14rem;
            font-weight: bold;
            color: #fff;
            line-height: 0.33rem;
            top: 0;
          }
          .mapBox {
            position: relative;
            margin-top: 0.1rem;
            width: 3.51rem;
            height: 3.71rem;
            background: url("../../../assets/image/active/newDreamBuild/map/map.png")
            no-repeat center center;
            background-size: 3.51rem;
            .clickText {
              position: absolute;
              left: 1.8rem;
              top: 2.75rem;
              font-weight: bold;
              color: rgba(54, 54, 54, 1);
              font-size: 0.12rem;
            }
            .clickCityText {
              position: absolute;
              left: 1.8rem;
              top: 2.95rem;
              font-weight: bold;
              color: rgba(54, 54, 54, 1);
              font-size: 0.12rem;
            }
            .cityNameImg {
              position: absolute;
              left: 2.1rem;
              top: 2.75rem;
              font-weight: bold;
              color: rgba(54, 54, 54, 1);
              font-size: 0.12rem;
              width: 0.6rem;
            }
            .city {
              width: 0.39rem;
              position: absolute;
            }
            .meizhou {
              left: 2.59rem;
              top: 0.05rem;
            }
            .shaoguan {
              top: 0.3rem;
              left: 1.32rem;
            }
            .heyuan {
              top: 0.55rem;
              left: 2.15rem;
            }
            .chaozhou {
              top: 0.4rem;
              left: 3.11rem;
            }
            .qingyuan {
              left: 0.7rem;
              top: 0.6rem;
            }
            .guangzhou {
              top: 0.96rem;
              left: 1.37rem;
            }
            .huizhou {
              top: 1.08rem;
              left: 2.05rem;
            }
            .shantou {
              top: 0.8rem;
              left: 2.98rem;
            }
            .zhaoqing {
              top: 1.21rem;
              left: 0.49rem;
            }
            .dongguan {
              top: 1.41rem;
              left: 1.67rem;
            }
            .shanwei {
              top: 1.23rem;
              left: 2.6rem;
            }
            .foshan {
              top: 1.61rem;
              left: 1.09rem;
            }
            .shenzhen {
              top: 1.69rem;
              left: 2.17rem;
            }
            .jiangmen {
              top: 2.01rem;
              left: 1.44rem;
            }
            .yangjiang {
              top: 2.23rem;
              left: 0.82rem;
            }
            .maoming {
              top: 2.39rem;
              left: 0.2rem;
            }
            .zhanjiang {
              left: 0.05rem;
              top: 3.04rem;
            }
          }
        }
        .newStory {
          background: rgb(255, 255, 255);
          position: relative;
          margin-top: 0.1rem;
          .headBox {
            width: 3.55rem;
            text-align: center;
            margin-bottom: 0.3rem;
          }
          .item {
            display: block;
            background-color: #fff;
            position: relative;
            padding: 0.1rem 0.16rem;
            border-bottom: 1px solid rgba(220, 220, 220, 0.1);
            &:before {
              .borderBottom;
            }
            .fl {
              width: 69%;
              p {
                line-height: 1.5;
              }
            }
            .fr {
              width: 30%;
              text-align: right;
            }
            .title {
              font-size: 0.15rem;
              color: #444;
              min-height: 0.54rem;
              overflow: hidden;
              padding-top: 0.05rem;
            }
            .date {
              font-size: 0.12rem;
              color: #999;
            }
            .description {
              font-size: 0.12rem;
              color: #444;
            }
            .pic {
              width: 0.72rem;
              height: 0.72rem;
              object-fit: cover;
            }
            .lookMore {
              border: none;
              width: 0.92rem;
              height: 0.34rem;
              background-color: rgba(230, 75, 35, 1);
              font-weight: bold;
              color: #fff;
              font-size: 0.14rem;
            }
          }
          .buttomWrap {
            text-align: center;
            padding: 0.2rem 0.16rem;
          }
        }
        .invite {
          text-align: center;
          padding: 0.4rem 0 0.6rem;
          span {
            display: inline-block;
            width: 1.4rem;
            height: 1.1rem;
            color: #333;
            line-height: 1.6rem;
            font-size: 0.16rem;
            background-image: url("../../../assets/image/active/dreamBuild/man.png");
            background-repeat: no-repeat;
            background-size: 100%;
            margin-right: 0.5rem;
            &:last-of-type {
              background-image: url("../../../assets/image/active/dreamBuild/woman.png");
              margin-right: 0;
            }
          }
          .link {
            margin-top: 0.08rem;
            a {
              color: #239e9c;
            }
          }
        }
        .bottom {
          padding: 0 0.1rem;
          padding-bottom: 0.3rem;
          .lxwm {
            width: 100%;
            text-align: center;
            font-size: 0.18rem;
            color: #239e9c;
            background-image: url("../../../assets/image/active/dreamBuild/line4.png");
            background-repeat: no-repeat;
            background-position-y: 0.05rem;
            background-size: 100%;
          }
          .pic {
            i {
              display: inline-block;
              vertical-align: bottom;
              margin-right: 0.05rem;
              width: 0.32rem;
              height: 0.32rem;
              background-image: url("../../../assets/image/active/dreamBuild/phone.png");
              background-size: 100%;
              &.www {
                background-image: url("../../../assets/image/active/dreamBuild/www.png");
              }
            }
            span {
              margin-right: 0.35rem;
              color: #239e9c;
              font-size: 0.18rem;
              &:last-of-type {
                margin-right: 0;
              }
            }
          }
          .ewm {
            text-align: center;
            padding: 0.16rem 0;
            margin: 0.3rem 0;
            border: 1px dashed #239e9c;
            img {
              width: 1.35rem;
              border: 0.06rem solid #177d7b;
              border-radius: 0.04rem;
            }
            p {
              font-size: 18px;
              margin-top: 0.1rem;
              color: #239e9c;
            }
          }
        }
      }
    }

    .codeImg {
      img {
        width: 3.75rem;
        height: 1.66rem;
      }
    }
    .link {
      height: 0.5rem;
      width: 100%;
      // margin-bottom: 0.6rem;
      line-height: 0.5rem;
      text-align: center;
      font-size: 0.16rem;
      // background: rgba(255, 255, 255, 1);
      span {
        color: #f3b422;
      }
    }
    .bottomBox {
      width: 3.75rem;
      height: 0.9rem;
      line-height: 0.9rem;
      text-align: center;
      margin-bottom: 0.6rem;
      a {
        color: #f3b422;
        font-size: 0.17rem;
      }
      span {
        color: #f3b422;
        display: inline-block;
        margin-top: 0.01rem;
      }
      .callUs {
        margin-right: 0.1rem;
      }
      .official {
        margin-left: 0.1rem;
      }
    }

    .companyInformation {
      p {
        text-align: center;
        color: rgba(54, 54, 54, 0.8);
        font-size: 0.12rem;
        font-weight: bold;
      }
      img{
        display: block;
        margin: 0 auto;
        width: 2.53rem;
      }
    }
    .bg_copyright{
      padding-bottom: .15rem;
      background-color: #fff;
      p{
        text-align: center;
        margin-bottom: .2rem;
      }
      .link{
        display: flex;
        justify-content: center;
        margin-bottom: .15rem;
        margin-top: 0.2rem;
        .miban{
          /*margin-right: .3rem;*/
        }
        .yz,.miban{
          width: 1.63rem;
          height: .44rem;
          img{
            width: 100%;
          }
        }
        .yz2{
          width: 1.93rem;
          height: .44rem;
          img{
            width: 100%;
            height:100%;
          }
        }
      }
    }
    .InvitationBg {
      background-color: white;
      padding: .1rem;
      text-align: center;
      img {
        width: 3.55rem;
      }
    }
    .fixBottom {
      position: fixed;
      bottom: 0;
      width: 3.75rem;
      height: 0.6rem;
      z-index: 999;
      .leftBox {
        display: inline-block;
        width: 52%;
        height: 0.6rem;
        float: left;
        background: #fff;
        position: relative;
        border-top: 1px solid rgba(230, 75, 35, 1) ;
        img {
          width: 0.39rem;
          margin-left: 0.1rem;
          margin-top: 0.12rem;
        }
        span {
          display: inline-block;
        }
        .textOne {
          margin-top: 0.12rem;
          font-weight: bold;
          color: rgba(54, 54, 54, 1);
          font-size: 0.14rem;
          width: 1.4rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .textTwo {
          position: absolute;
          left: 0.52rem;
          top: 0.3rem;
          font-size: 0.14rem;
          color: rgba(54, 54, 54, 0.6);
          &.inviteGift{
            width: 1.4rem;
            display: block;
            img{
              width: .8rem;
              margin-top: 0;
            }
          }
        }
      }
      .rightBox {
        display: inline-block;
        width: 48%;
        height: 0.6rem;
        /*background: rgba(39, 89, 164, 1);*/
        background-color: rgba(230, 75, 35, 1);
        /*background-image: linear-gradient(to bottom,rgba(255, 177, 56, 1),rgba(251, 106, 13, 1));*/
        float: left;
        position: relative;
        .line {
          position: absolute;
          display: inline-block;
          width: 1px;
          height: 0.25rem;
          background: rgba(255, 255, 255, 0.4);
          top: 0.16rem;
          left: 0.88rem;
        }
        .phoneIcon {
          text-align: center;
          display: inline-block;
          float: left;
          width: 50%;
          height: 100%;
          img {
            width: 0.24rem;
            margin-top: 0.1rem;
          }
          p {
            color: #fff;
            font-size: 0.14rem;
          }
        }
        .signUpIcon {
          text-align: center;
          display: inline-block;
          float: left;
          width: 50%;
          height: 100%;
          img {
            opacity: 0.8;
            width: 0.24rem;
            margin-top: 0.1rem;
          }
          p {
            color: #fff;
            font-size: 0.14rem;
          }
        }
      }
    }

    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.4s;
    }

    .fade-enter,
    .fade-leave-to {
      opacity: 0;
    }

    .fade2-enter-active,
    .fade-leave-active {
      transition: opacity 0.3s;
    }

    .fade2-enter,
    .fade-leave-to {
      opacity: 0;
    }

    .newBanner /deep/.countdown {
      .num {
        /*color: rgba(61, 91, 244, 1);*/
        color: #E64B23;
        width: 0.13rem;
        height: 0.22rem;
        background: rgba(255, 255, 255, 1);
        border-radius: 4px;
      }
    }

    .swiper {
      width: 100%;
      overflow: hidden;
      background-color: transparent;
      height: auto;
      padding-bottom: .1rem;
      background: white;
      .headBox{
        width:100%;
        height: 0.8rem;
        position: relative;
        .fourright{
            width: 1.61rem;
             height: 0.35rem;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
        }
      }
    }
    .hb {
      height: 2.6rem;
      background-size: 100%;
      text-align: center;
      margin-top: .22rem;
      .msnxf {
        padding-top: .5rem;
        img {
          width: 2.6rem;
        }
      }
      .mfbm {
        a {
          position: relative;
          display: inline-block;
          color: #333;
          width: 1rem;
          font-size: .16rem;
          line-height: .38rem;
          background-color: #FFD100;
          border-radius: .04rem;
          &:before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border: 1px solid #FFD100;
            top: .02rem;
            right: .02rem;
            border-radius: .04rem;
          }
        }
      }
      .textContent {
        margin: 0 auto;
        top: .4rem;
        left: .7rem;
        width: 2.75rem;
        height: .3rem;
        line-height: .3rem;
        overflow: hidden;
        .gdBox {
          width: 100%;
          p {
            font-size: .12rem;
            height: .3rem;
            display: flex;
            justify-content: center;
            -webkit-justify-content: center;
            img {
              width: .3rem;
              height: .3rem;
              border-radius: 50%;
              /*margin-right: .1rem;*/
              margin-top: 0;
            }
            span {
              margin-left: .1rem;
              display: inline-block;
              float: left;
              color: #fff;
              &:first-of-type {
                max-width: .7rem;
                overflow: hidden;
                height: .3rem;
                text-overflow: ellipsis;
              }
            }
          }
          &.anim {
            transition: all 1s;
            margin-top: -.38rem;
          }
        }
      }
      .title {
        padding-top: .35rem;
        color: #FFD100;
        font-size: .16rem;
        text-align: center;
        background-image: url("../../../assets/image/active/dreamBuild/line2.png");
        background-repeat: no-repeat;
        background-position-y: .42rem;
        background-size: 100%;
        width: 3rem;
        margin: 0 auto;
      }
      > .content {
        width: 2.7rem;
        margin: 0 auto;
        color: #fff;
        font-size: .12rem;
        padding-top: .08rem;
        p {
          text-indent: .2rem;
          text-align: left;
        }
      }
    }
    .scholarshipStory{
      background-color: white;
      .headBox{
        width:100%;
        height: 0.8rem;
        position: relative;
        .fourright{
            width: 1.85rem;
             height: 0.35rem;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
        }
      }
      margin-bottom: .1rem;
      .lookMore {
        display: block;
        border: none;
        width: 1.35rem;
        height: 0.34rem;
        background-image: linear-gradient(to bottom,rgba(255, 132, 0, 1),rgba(251, 50, 3, 1));
        color: #fff;
        font-size: 0.17rem;
        margin: .1rem auto;
        border-radius: .18rem;
        border-bottom: solid 4px rgba(185, 35, 0, 1);
      }
    }
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.4s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .fade2-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s;
  }

  .fade2-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .newBanner /deep/.countdown {
    .num {
      color: rgba(61, 91, 244, 1);
      width: 0.13rem;
      height: 0.22rem;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
    }
  }

  .swiper {
    width: 100%;
    height: 1.6rem;
    overflow: hidden;
  }
  .swiper-slide {
    width: 80%;
    height: 1.6rem;
  }
  .swiper-slide-active img {
    display: block;
    margin: 2% auto;
    width: 92%;
    height: 96%;
  }
  .swiperImg {
    display: block;
    margin: 0 auto;
    margin-top: 3.5%;
    width: 100%;
    height: 85%;
    border-radius: .05rem;
    background: #FFFFFF;
    // box-shadow: .01rem .03rem .27rem 0 rgba(10, 39, 82, 0.14);
  }

  .activityDetails {
    width: 3.75rem;
    height: auto;
    background: rgb(255, 255, 255);
    overflow: hidden;
    padding-bottom: .25rem;
    position: relative !important;
    top:0 !important;
    margin-bottom: 0.1rem;
    img {
      width: 3.75rem;
      height: 1.79rem;
      margin-bottom: 0.14rem;
    }
    p {
      text-indent: 2em;
      display: inline-block;
      width: 3.32rem;
      line-height: .21rem;
      margin: 0 0.25rem 0rem 0.21rem;
      font-size: .14rem;
      color: rgba(54, 54, 54, 1);
      &:last-of-type{
        margin-top: 0;
      }
      span{
        color: rgba(240, 63, 0, 1);
      }
    }

  }
  .img_wrap_single {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 999;
    .bg {
      position: fixed;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 999;
    }
    img {
      width: 2.5rem;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      z-index: 99999;
    }
  }
  .card_wrap {
    position: fixed;
    top: 0.6rem;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
    overflow: hidden;
    .bg{
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, .7);
      top:0;
      left:0;
    }
  }
  .card {
    width: 3.43rem;
    height: 1.78rem;
    margin: 0.16rem;
    overflow: hidden;
    background-image: url("../../../assets/image/active/enrollAggregate/bg-card.png");
    background-size: 100%;
    border-radius: 0.1rem;
    position: relative;
    .talk {
      position: absolute;
      right: 0.24rem;
      top: 0.1rem;
      height: 0.2rem;
      color: white;
      font-size: 0.1rem;
    }
    .edit {
      position: absolute;
      right: 0;
      top: 0;
      width: 0.6rem;
      height: 0.6rem;
      background: url("../../../assets/image/teacherKit/<EMAIL>") no-repeat;
      background-size: 100%;
    }
    .img {
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 50%;
      float: left;
      margin-right: 0.06rem;
      overflow: hidden;
      background-color: #fff;
      img {
        width: 100%;
        margin: 0;
      }
    }
    .txt {
      color: white;
      line-height: 0.24rem;
      margin: 0.3rem 0 0 0.2rem;
      h3 {
        font-weight: 500;
        font-size: 0.22rem;
        line-height: 0.3rem;
      }
    }
    .tel {
      width: 2.1rem;
      height: 0.42rem;
      float: left;
      color: white;
      margin-left: 0.2rem;
      margin-top: 0.2rem;
      span {
        /*width:1.95rem;*/
        /*white-space: nowrap;*/
        /*overflow: hidden;*/
        /*text-overflow: ellipsis;*/
        &.active {
          width: 1.9rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        display: block;
        line-height: 0.22rem;
        &:first-child {
          float: left;
        }
      }
      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 2.05rem;
        height: 0.25rem;
        font-size: 0.14rem;
        line-height: 0.25rem;
        overflow: hidden;
        i {
          display: inline-block;
          width: 0.12rem;
          height: 0.25rem;
          margin-left: 0.03rem;
          img {
            width: 100%;
            margin-top: 0.06rem;
          }
        }
      }
    }
    .code {
      width: 0.7rem;
      height: 0.94rem;
      float: right;
      margin-top: 0.12rem;
      margin-right: 0.3rem;
      overflow: hidden;
      /*background-color: #fff;*/
      img {
        width: 100%;
        height: 0.7rem !important;
        overflow: hidden;
      }
      span {
        display: block;
        width: 0.8rem;
        height: 0.24rem;
        color: white;
        font-size: 0.1rem;
        padding-left: 0.21rem;
        background: url("../../../assets/image/teacherKit/微信@3x.png") no-repeat
          0 -0.02rem;
        background-size: 28%;
      }
    }
  }
  .invite {
    padding: 0.09rem 0;
    height: .6rem;
    background-color: #fff;
    background-size: 100%;
    img {
      width: 0.41rem;
      height: 0.41rem;
      border-radius: 50%;
      float: left;
      margin-left: 0.12rem;
    }
    .headTxt {
      float: left;
      margin-left: 0.12rem;
      width: 1.9rem;
      overflow: hidden;
      p {
        font-size: 0.16rem;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        line-height: .16rem;
        span {
          color: #e15443;
          font-size: 0.14rem;
        }
        &:first-of-type {
          font-size: 0.12rem;
          margin-bottom: .08rem;
        }
      }
    }
    .card_btn{
      width: 1rem;
      height: .34rem;
      color: white;
      line-height: .34rem;
      float: right;
      margin-right: .1rem;
      border-radius: .05rem;
      margin-top: .06rem;
      background-color: rgba(25, 209, 0, 1);
      padding-left: .38rem;
      background-image: url("../../../assets/image/teacherKit/微信@3x.png");
      background-size: .32rem .32rem;
      background-position: .05rem .02rem;
      background-repeat: no-repeat;
    }
  }
  .invite-top {
    position: relative;
    z-index: 10;
  }
</style>
<style lang="scss" scoped>
  @mixin bgPosition(
    $spriteWidth,
  $spriteHeight,
  $iconWidth,
  $iconHeight,
  $iconX,
  $iconY
  ) {
    background-position: (
      ($iconX / ($spriteWidth - $iconWidth)) * 100%
      ($iconY / ($spriteHeight - $iconHeight)) * 100%
    );
  }
  .st {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 0);
  }
  .sw {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 161);
  }
  .qy {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 322);
  }
  .sg {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 482);
  }
  .zj {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 644);
  }
  .zq {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 804);
  }
  .sz {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 965);
  }
  .yj {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1126);
  }
  .mz {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1290);
  }
  .fs {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1448);
  }
  .gz {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1609);
  }
  .cz {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1770);
  }
  .dg {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 1930);
  }
  .jm {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 2091);
  }
  .mm {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 2253);
  }
  .hy {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 2413);
  }
  .hz {
    background: url("../../../assets/image/active/newDreamBuild/map/map.png") 0 0
    no-repeat;
    background-size: 0.73rem 13.26rem;
    @include bgPosition(146, 2652, 73, 78, 0, 2576);
  }
  .hn {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1547);
  }
  .gzzyy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 206);
  }
  .gdjr {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0 no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1341);
  }
  .dglg {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 928);
  }
  .gzcj {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0 no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1238);
  }
  .gdzyjs {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 413);
  }
  .gzgb {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1134);
  }
  .lnsf {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 825);
  }
  .zkny {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 516);
  }
  .gdkm {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1444);
  }
  .gdst {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 619);
  }
  .dgzy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1031);
  }
  .zqxy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 722);
  }
  .jyxy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 1650);
  }
  .qyzy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0 no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 0);
  }
  .nhcr {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 309);
  }
  .swzy {
    background: url("../../../assets/image/active/newDreamBuild/schoolLogo/school_logo.png")
    0 0rem no-repeat;
    background-size: 0.5rem 8.5rem;
    @include bgPosition(100, 1700, 50, 50, 0, 103);
  }

  .hnnyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/hnnyLogo.png");
    background-size: 0.42rem ;
  }
  .gzdxNew {
     background: url('../../../assets/image/active/newDreamBuild/dreamFive/<EMAIL>') no-repeat center;
    background-size: 0.42rem .42rem;
  }
  .gdjrNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 1214);
  }
  .dglgNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 84);
  }

  .gzcjNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 170);
  }
  .gdzyjsNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 256);
  }
  .gzgbNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 344);
  }
  .lnsfNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 430);
  }
  .zknyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 518);
  }
  .gdkmNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 606);
  }
  .gdstNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 692);
  }
  .dgzyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 780);
  }
  .zqxyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 866);
  }
  .jyxyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 954);
  }
  .qyzyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 1040);
  }
  .nhcrNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, -2);
  }
  .swzyNew {
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/css_sprites_school_logo.png")
    0 0 no-repeat;
    background-size: 0.42rem 6.28rem;
    @include bgPosition(84, 1256, 42, 42, 0, 1126);
  }
  .gzsxy{
    background: url("../../../assets/image/active/newDreamBuild/gzsxy.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .stdx{
    background: url("../../../assets/image/active/newDreamBuild/stdx.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .jndxNew{
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/jndx.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .gdlnNew{
    background: url("../../../assets/image/active/enrollAggregate/ic_logo_gdln.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .sszyjs{
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/sszy.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .mmzyjsNew{
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/schoolBg/mmzyjs.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .gzhxzyNew{
    background: url("../../../assets/image/active/dreamBuild2021/gzhx.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .shanweiInstituteTechnology{
    background: url("../../../assets/image/active/shanweiInstituteTechnology/logo.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .gdjtzy-icon{
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/jtlogo.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .gdjdzy-icon{
    background: url("../../../assets/image/active/newDreamBuild/dreamFive/gdjdzyLogo.png") no-repeat;
    background-size: .42rem  .42rem;
  }
  .lotteryEntrance{
    position: fixed;
    right: .1rem;
    bottom:1rem;
    width: .88rem;
    z-index: 9999;
    img{
      width: 100%;
    }
  }
  .activity_rule{
    position: absolute;
    right: 0;
    text-align: center;
    height: .34rem;
    top:.55rem;
    width: .77rem;
    font-size: .14rem;
    z-index: 999;
    background-image: url("../../../assets/image/active/enrollAggregate/activity_rule.png");
    background-size: 100%;
    a{
      display: block;
      width: 100%;
      line-height: .3rem;
      padding-left: .02rem;
      text-align: center;
      color: rgba(130, 77, 9, 1);
    }
  }
  .activity_note{
    position: absolute;
    right: 0;
    text-align: center;
    height: .34rem;
    top:.46rem;
    width: .77rem;
    font-size: .14rem;
    z-index: 9999;
    background-image: url("../../../assets/image/active/enrollAggregate/activity_rule.png");
    background-size: 100%;
    a{
      display: block;
      width: 100%;
      line-height: .3rem;
      padding-left: .02rem;
      text-align: center;
      color: rgba(130, 77, 9, 1);
    }
  }

  .banner{ height:1.48rem; z-index:1;}
  .banner .img{
    object-fit: cover;
  }

  .scholarshipStory {
    margin-bottom: .6rem;
    .invite {
      background-image: url('../../../assets/image/active/enrollmentHomepage/invitBg2.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      height: .68rem;
      position: relative;
      margin-bottom: -.1rem;
      z-index: 99;
      img {
        width: .48rem;
        height: .48rem;
        float: left;
        border-radius: 50%;
        margin-top: 1.8%;
        margin-left: .12rem;
        margin-right: .07rem;
      }
      .rightView {
        float: left;
        margin-top: 2.3%;
        p {
          font-size: .16rem;
          span {
            color: #e15443;
            font-size: .13rem;
          }
          &:first-of-type {
            font-size: .13rem;
          }
        }
      }
    }
    .item {
      display: block;
      background-color: #fff;
      position: relative;
      padding: .2rem .16rem .1rem .16rem;
      /*&:before {*/
      /*.borderBottom;*/
      /*}*/
      .fl {
        width: 69%;
        p{
          line-height: 1.5;
        }
      }
      .fr {
        width: 30%;
        text-align: right;
      }
      .title {
        font-size: .15rem;
        color: #363636;
        min-height: .54rem;
        overflow: hidden;
      }
      .date {
        margin-top: .1rem;
        .heart{
          width: 1.5rem;
          float: left;
          span{
            display: block;
            float: left;
            margin-right: .17rem;
            padding-left: .18rem;
            line-height: .18rem;
            overflow: hidden;
            &.heartbeat{
              background: url("../../../assets/image/scholarshipStory/ic_school_like.png") no-repeat;
              background-size: .18rem;
              &.active{
                background: url("../../../assets/image/scholarshipStory/ic_school_like_select.png") no-repeat ;
                background-size: .18rem;
              }
            }
            &.read{
              background: url("../../../assets/image/scholarshipStory/ic_school_like copy.png") no-repeat 0 .01rem;
              background-size: .16rem;
            }
          }
        }
        font-size: .12rem;
        color: rgba(54,54,54,.6);
      }
      .description{
        font-size: .12rem;
        color: #444;
      }
      .pic {
        width: .72rem;
        height: .72rem;
        object-fit: cover;
      }
    }
  }
  .commonPop-content{
    text-align: left;
    font-size: 15px;
    font-weight: 500;
    margin-top: .1rem;
  }
  .title1{
    margin: 0 auto;
    width: 1.1rem;
    height: .34rem;
    background-image: url("../../../assets/image/active/enrollAggregate/friendtip.png");
    background-size: 100%;
  }
  .button{
    margin: .1rem auto;
    width: 2.38rem;
    button{
      border-radius: .2rem;
      width: 2.38rem;
      text-align: center;
      height: .4rem;
      line-height: .4rem;
      color: white;
      background-color: rgba(237, 82, 6, 1);
      border: none;
      margin-top: .2rem;
      &:last-of-type{
        background-color: transparent;
        color: rgba(237, 82, 6, 1);
        height: .2rem;
        line-height: .2rem;
      }
    }
  }
  .tab {
    height: 0.5rem;
    overflow: hidden;
    background-color:rgba(251, 218, 218, 1);
    &.tab1{
      position: relative;
    }
    &.tab2{
      .item{

        width: 50%;
        span{
          color: rgba(233, 6, 42, 1);
        }
        &:first-of-type{
          position: relative;
          width: 50%;
          &:after {
            content: "";
            background-color: inherit;
            width: 0rem;
            height:.6rem;
            top: 0rem;
          }
        }
        &.active{
          background-color: rgba(255, 76, 80, 1);
        }
      }
    }
    .item{
      float: left;
      height: 0.5rem;
      width: 51%;
      background-color: rgba(240, 63, 0, 0.2);
      text-align: center;
      span {
        display: inline-block;
        width: 100%;
        line-height: .5rem;
        font-size: 0.16rem;
        color: #F03F00;
        font-weight: bold;
      }
      &:first-of-type{
        position: relative;
        width: 49%;
        // &:after {
        //   content: "";
        //   background-color: inherit;
        //   width: 0.2rem;
        //   height:.6rem;
        //   top: 0rem;
        //   transform: rotate(-15deg);
        //   right: -.1rem;
        //   position: absolute;
        // }
      }
      &.active {
        background: #F03F00;
        span {
          position: relative;
          width: auto;
          border-right: none;
          color: #fff;
        }
      }
    }
    .line {
      position: absolute;
      width: 0.4rem;
      left: .9rem;
      height: 0.02rem;
      background-color: #fff;
      bottom: 0.09rem;
      transform: translateX(-50%);
      border-radius: 2px;
      transition: all .5s;
      &.active{
        left:2.8rem;
      }
    }
  }
  .openUniversity_wrap{
    padding-bottom: .6rem;
    .schoolAdvantage {
      width: 3.75rem;
      height: 3.9rem;
      background-image: url("../../../assets/image/active/enrollAggregate/bg-open_advantage.jpg");
      background-size: 100%;
      overflow: hidden;
      .headTitle{
        width: 1.4rem;
        margin: .35rem auto .2rem;
        img{
          width: 100%;
        }
      }
      .content {
        width: 3.35rem;
        height: 3.9rem;
        border-radius:10px;
        margin: 0 auto;
        overflow: hidden;
        text-align: center;
        .contentTile {
          width: 1.45rem;
          height: .32rem;
          position:absolute;
          background:linear-gradient(318deg,rgba(240,110,108,1) 0%,rgba(240,145,144,1) 100%);
          box-shadow:0px 2px 4px 0px rgba(240,110,108,0.2),0px 1px 1px 0px rgba(255,255,255,0.5);
          border-radius:0px 0px 10px 10px;
          font-size: .17rem;
          font-weight: 600;
          text-align: center;
          color:rgba(255,255,255,1);
          margin-left: 1rem;
          margin-top: -.05rem;
        }
        .topView {
          width: 3.45rem;
          height: .81rem;
          margin-left: 0rem;
          .left ,.right{
            width: 1.62rem;
            height: .81rem;
            background:rgba(255,255,255,1);
            border-radius:.05rem;
            float: left;
            padding: .1rem;
            .topTitle {
              width: 0.75rem;
              img {
                width:100%;
              }
            }
            .contentText {
              width: 100%;
              display: inline-block;
              font-size: .15rem;
              color:rgba(54, 54, 54, 1);
              /*margin-top: .05rem;*/
              text-align: left;
            }
          }
          .left{
            margin-right: .1rem;
          }
        }
        .middleView, .bottomView  {
          width:3.35rem;
          height:auto;
          overflow: hidden;
          background:rgba(255,255,255,1);
          border-radius:.05rem;
          margin-top: .1rem;
          padding: .1rem;
          .topTitle {
            width: 1.3rem;
            img {
              width: 1.3rem;
            }
          }
          .contentText {
            text-align: left;
            font-size: .15rem;
            color:rgba(54, 54, 54, 1);
            /*margin-top: .05rem;*/
            display: inline-block;
            line-height: .2rem;
          }
        }

        button {
          border: none;
          width:1rem;
          height:.35rem;
          background:linear-gradient(270deg,rgba(251,106,20,1) 0%,rgba(255,184,13,1) 100%);
          box-shadow:0px 3px 0px 0px rgba(251,106,20,1);
          border-radius:.05rem;
          font-weight: 500;
          font-size: .16rem;
          color:rgba(255,255,255,1);
          margin-top: .2rem;
        }
      }

    }
    .charge {
      background: rgb(255, 255, 255);
      width: 3.55rem;
      padding-bottom: .1rem;
      margin-left: .1rem;
      .chargetop {
        background: #fff;
        width: 3.55rem;
        height: .5rem;
        line-height: .7rem;
        text-align: center;
        img {
          width: 1.92rem;
          vertical-align:middle;
        }
      }
      .title1 {
        background: #fff;
        width: 100%;
        height: .28rem;
        .fl {
          width: 50%;
          height: .28rem;
          float: left;
          position: relative;
          .flTop {
            height: .21rem;
            line-height: .21rem;
            .line {
              display: inline-block;
              width: .04rem;
              height: .16rem;
              background-color: #F06E6C;
              margin-left: .22rem;
              margin-top: .04rem;
            }
            .bookPrice {
              position:absolute;
              display: inline-block;
              font-size:14px;
              line-height: .28rem;
              color:rgba(23,6,6,1);
              font-weight:400;
              left: .35rem;
              top: -0.02rem;
              width:1.9rem;
              span {
                font-size: 14px;
                color:#F06E6C;
              }
            }
          }
          .bookYear {
            padding-left: .05rem;
            font-size: .2rem;
            color: #F06E6C;
          }
          span {
            color: #F06E6C;
            font-size: .12rem;
          }
        }
      }
    }
    .highUniversityBox {
      height: auto;
      overflow: hidden;
      width: 3.55rem;
      margin-left: .1rem;
      padding-bottom: .08rem;
      padding-top: .08rem;
      background: #fff;
      margin-top: -.1rem;
      .highUniversity {
        margin: auto .1rem;
        background-color: #fff;
        border-radius: .05rem;
        // box-shadow:0px 4px 20px 0px rgba(0,0,0,0.2);
        border:1px solid rgba(248, 187, 40, 1);
        .title {
          // width: 3.35rem;
          height: .5rem;
          line-height: .5rem;
          text-align: center;
          border-radius:.05rem .05rem 0rem 0rem;
          background:rgba(248, 187, 40, .1);
          .lineLeft {
            width: .1rem;
            height: .02rem;
            border-radius: .01rem;
            background: #F06E6C;
          }
          .text {
            font-size: .17rem;
            color: rgba(233, 6, 42, 1);
            font-weight: 600;
          }
        }
        .content {
          border-top: 1px solid rgba(248, 187, 40, 1);
          overflow: hidden;
          position: relative;
          .titleText {
            width: .6rem;
            height: 1.4rem;
            float: left;
            border-right: 1px solid rgba(248, 187, 40, 1);
            display: inline-block;
            p {
              display: inline-block;
              width: .6rem;
              height: .42rem;
              text-align: center;
              margin-top: 85.5%;
              font-size: .15rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flLeft {
            width: 2.11rem;
            height: 1.4rem;
            float: left;
            border-right: 1px solid rgba(248, 187, 40, 1);
            .contentText {
              display: inline-block;
              width: 1.86rem;
              height: .8rem;;
              margin: .1rem .17rem .1rem .13rem;
              font-size: .14rem;
              color:rgba(23,6,6,.8);
            }
          }
          .flRight {
            float: left;
            width: .5rem;
            text-align: center;
            line-height: 100%;
            position:absolute;
            left: 2.77rem;
            top: 32.5%;
            .content {
              width: 0.42rem;
              height: 0.38rem;
              background-color: aquamarine;
            }
            p {
              margin-top: .04rem;
            }
            .top {
              color: rgba(54, 54, 54, 1);
              font-size: .15rem;
            }
            .bottom {
              font-size: .12rem;
              color: rgba(54, 54, 54, 1);
            }
          }
        }
        .contentTwo {
          border-top: 1px solid rgba(248, 187, 40, 1);
          //border-bottom: 1px solid rgba(252, 143, 8, 1);
          overflow: hidden;
          position: relative;
          .titleText {
            width: .6rem;
            height: .75rem;
            float: left;
            border-right: 1px solid rgba(248, 187, 40, 1);
            display: inline-block;
            p {
              display: inline-block;
              width: .6rem;
              height: .42rem;
              text-align: center;
              margin-top: 32.5%;
              font-size: .15rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flLeft {
            width: 2.11rem;
            height: .75rem;
            float: left;
            border-right: 1px solid rgba(248, 187, 40, 1);
            .contentText {
              display: inline-block;
              width: 1.86rem;
              height: .4rem;;
              margin: .1rem .17rem .1rem .13rem;
              font-size: .14rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flRight {
            float: left;
            width: .5rem;
            text-align: center;
            line-height: 100%;
            position:absolute;
            left: 2.77rem;
            top: 17.5%;
            .content {
              width: 0.42rem;
              height: 0.38rem;
              background-color: aquamarine;
            }
            p {
              margin-top: .04rem;
            }
            .top {
              color: rgba(54, 54, 54, 1);
              font-size: .15rem;
            }
            .bottom {
              font-size: .12rem;
              color: rgba(54, 54, 54, 1);
            }
          }
        }
      }
      .highUniversityTwo {
        margin: auto .1rem;
        background-color: #fff;
        border-radius: .05rem;
        border:1px solid  rgba(248, 187, 40, 1);
        margin-top: .11rem;
        .title {
          height: .5rem;
          line-height: .5rem;
          text-align: center;
          border-radius:.05rem .05rem 0rem 0rem;
          background:rgba(248, 187, 40, .1);
          .lineLeft {
            width: .1rem;
            height: .02rem;
            border-radius: .01rem;
            background: #F06E6C;
          }
          .text {
            font-size: .17rem;
            color: rgba(233, 6, 42, 1);
            font-weight: 600;
          }
        }
        .content {
          border-top: 1px solid  rgba(248, 187, 40, 1);
          overflow: hidden;
          position: relative;
          .titleText {
            width: .6rem;
            height: 1rem;
            float: left;
            border-right: 1px solid rgba(248, 187, 40, 1);
            display: inline-block;
            p {
              display: inline-block;
              width: .6rem;
              height: .42rem;
              text-align: center;
              margin-top: 35.5%;
              font-size: .15rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flLeft {
            width: 2.11rem;
            height: 1rem;
            float: left;
            border-right: 1px solid  rgba(248, 187, 40, 1);
            .contentText {
              display: inline-block;
              width: 1.86rem;
              height: .8rem;;
              margin: .1rem .17rem .1rem .13rem;
              font-size: .14rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flRight {
            float: left;
            width: .5rem;
            text-align: center;
            line-height: 100%;
            position:absolute;
            left: 2.77rem;
            top: 26.5%;
            .content {
              width: 0.42rem;
              height: 0.38rem;
              background-color: aquamarine;
            }
            p {
              margin-top: .04rem;
            }
            .top {
              color: rgba(54, 54, 54, 1);
              font-size: .15rem;
            }
            .bottom {
              font-size: .12rem;
              color: rgba(54, 54, 54, 1);
            }
          }
        }
        .contentTwo {
          border-top: 1px solid rgba(248, 187, 40, 1);
        //  border-bottom: 1px solid  rgba(252, 143, 8, 1);
          overflow: hidden;
          position: relative;
          .titleText {
            width: .6rem;
            height: .6rem;
            float: left;
            border-right: 1px solid  rgba(248, 187, 40, 1);
            display: inline-block;
            p {
              display: inline-block;
              width: .6rem;
              height: .42rem;
              text-align: center;
              margin-top: 35.5%;
              font-size: .15rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flLeft {
            width: 2.11rem;
            height: .6rem;
            float: left;
            border-right: 1px solid  rgba(248, 187, 40, 1);
            .contentText {
              display: inline-block;
              width: 1.86rem;
              height: .4rem;;
              margin: .2rem 0rem .2rem .13rem;
              font-size: .14rem;
              color:rgba(54, 54, 54, 1);
            }
          }
          .flRight {
            float: left;
            width: .5rem;
            text-align: center;
            line-height: 100%;
            position:absolute;
            left: 2.77rem;
            top: 17.5%;
            .content {
              width: 0.42rem;
              height: 0.38rem;
              background-color: aquamarine;
            }
            p {
              margin-top: .04rem;
            }
            .top {
              color: rgba(54, 54, 54, 1);
              font-size: .15rem;
            }
            .bottom {
              font-size: .12rem;
              color: rgba(54, 54, 54, 1);
            }
          }
        }
      }
      .bottomText {
        height: .68rem;
        margin-top: .1rem;
        padding: 0 .1rem;
        p{
          .iconBox {
            width: .04rem;
            height: .04rem;
            display: inline-block;
            background:rgba(248, 187, 40, 1);
            margin-left: .1rem;
            margin-top: .08rem;
            position: absolute
          }
          span {
            font-size: .14rem;
            color:rgba(54, 54, 54, 1);
            margin-left: .24rem;
            &:last-of-type{
              margin-left: 0;

            }
          }
        }
      }
    }
    .invitation{
      width: 100%;
      height: 1.5rem;
      position: relative;
      img{
        width: 100%;
      }
      .btns{
        width: 100%;
        height: .6rem;
        position: absolute;
        top:.95rem;
        button{
          /*width: 1.4rem;*/
          width: 1.69rem;
          height: .6rem;
          background-color: transparent;
          background-size: 100% 100%;
          background-image: url("../../../assets/image/active/enrollAggregate/btn_code.png");
          &:first-of-type{
            background-image: url("../../../assets/image/active/enrollAggregate/btn_friend.png");
            margin-left: .1rem;
          }
        }
      }
    }
    .newBanner {
      margin-top: .5rem;
      &.active{
        margin-top: 0rem;
      }
      position: relative;
      img {
        width: 100%;
      }
      .enrollText {
        position: absolute;
        top: .17rem;
        left: .1rem;
        color: rgba(54, 54, 54, 1);
        font-family: STSongti-SC-Black;
        font-size: 0.13rem;
        opacity: 0.9;
        line-height: .15rem;
        font-weight: bold;
        z-index: 2;
      }
      .enrollCount {
        width: 0.55rem;
        height: 0.22rem;
        position: absolute;
        top: .17rem;
        text-align: center;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.13rem;
        left:.75rem;
        padding-left: 1.2%;
        z-index: 2;
      }
      .enrollBtn{
        position:absolute;
        width:1.65rem;
        height:.41rem;
        left:1.06rem;
        top:2.26rem;
        color: white;
        font-size: .22rem;
        text-align: center;
        line-height: .58rem;
      }
      .enrolled{
        width: 100%;
        position: absolute;
        color:rgba(252, 66, 27, 1);
        font-size: .14rem;
        font-style: normal;
        top:2.65rem;
        text-align: center;
      }
    }
    .userMessage {
      background: rgb(255, 255, 255);
      .top {
        width: 3.75rem;
        height: .7rem;
        line-height: .7rem;
        img {
          width: 0.32rem;
          height: 0.32rem;
          margin-left: .1rem;
          vertical-align: middle;
          margin-top: -.05rem;
        }
        .topTitle {
          font-size: .17rem;
          color:rgba(23,6,6,0.8);
        }
      }
      .textContent {
        overflow: hidden;
        height: 4.1rem;
        .userMessageContentList {
          width: 3.75rem;
          &.anim {
            transition: all 1s;
            margin-top: -1.7rem;
          }
          .content {
            overflow: hidden;
            height: 1.33rem;
            position: relative;
            .line {
              position:absolute;
              bottom: .01rem;
              height: 1px;
              width: 2.76rem;
              left: .64rem;
              background-color: rgba(23,6,6,0.08);

            }
            .fl {
              width: .64rem;
              img {
                width: 0.38rem;
                height: 0.38rem;
                float: right;
                border-radius: 50%;
                margin-top: .11rem;
              }
            }
            .fr {
              width: 3.11rem;
              height: 1.14rem;
              .userName {
                margin-left: .1rem;
                margin-top: .12rem;
                font-size: .14rem;
                color:rgba(23,6,6,0.8);
              }
              .uesrQuestion {
                display: inline-block;
                width: 2.76rem;;
                margin-top: .03rem;
                margin-left: .1rem;
                font-size: .14rem;
                color:rgba(23,6,6,0.4);
              }
              .content {
                position: relative;
                margin-bottom: .09rem;
                height: auto;

                .line {
                  position:absolute;
                  display: inline-block;
                  width: 0.02rem;
                  height: 0.14rem;
                  background-color: #F06E6C;
                  top: .17rem;
                  left: .01rem;
                }
                .answer {
                  display: inline-block;
                  border-radius:3px 3px 3px 0px;
                  width: 2.86rem;
                  font-size: .14rem;
                  color:rgba(23,6,6,0.8);
                  padding: .13rem .07rem .13rem .1rem;
                }
              }
            }
          }
        }
      }

      .userMessageContent {
        width: 3.75rem;
        overflow: hidden;
        .fl {
          width: 0.64rem;
          img {
            width: 0.38rem;
            height: 0.38rem;
            float: right;
            border-radius: 50%;
            margin-top: .11rem;
          }
        }
        .fr {
          width: 3.11rem;
          position: relative;
          .userName {
            margin-left: .1rem;
            margin-top: .12rem;
            font-size: .14rem;
            color:rgba(23,6,6,0.8);
            display: inline-block;
            width: 2.48rem;
            height: .24rem;
          }
          textArea {
            width: 2.87rem;
            height: .86rem;
            border-radius:5px;
            border: solid 1px rgba(240, 86, 10, 1);
            opacity:0.9;
            padding: .13rem .1rem .13rem .1rem;
            margin-top: .03rem;
            color:#000;
          }
          span {
            position: absolute;
            font-size: .09rem;
            color:rgba(23,6,6,0.4);
            top: 1.07rem;
            right: .305rem;
          }
          button {
            width: 1.34rem;
            height: 0.4rem;
            border-radius: .18rem;
            background-image: linear-gradient(to bottom,rgba(243, 180, 34, 1),rgba(255, 132, 0, 1));
            border-bottom: solid 4px rgba(185, 35, 0, 1);
            margin: 0.2rem 0 0.4rem .54rem;
            color:#fff;
            font-size: 0.17rem;
            font-weight: 600;
          }
        }
      }
    }
    .showall {
      height: 3.97rem;
    }
    .showall.active {
      height: auto;
    }
    .schoolDetails {
      width: 3.75rem;
      background: rgb(255, 255, 255);
      overflow: hidden;
      img {
        width: 3.55rem;
        height: 1.79rem;
        border-radius: 5px;
        margin: .1rem .1rem 0rem .1rem;
      }
      p {
        display: inline-block;
        width: 3.15rem;
        margin: .2rem .3rem .1rem .3rem;
        padding: 0rem .05rem 0rem .05rem
      }
    }
    .lookMore {
      background: rgb(255, 255, 255);
      padding-top: .06rem;
      text-align: center;
      height: .54rem;
      line-height: .54rem;
      position: relative;
      span {
        color:rgba(23,6,6,0.4);

      }
      img {
        position: absolute;
        top: -.03rem;
        left: 1.7rem;
        width: .32rem;
        height: .32rem;
      }
    }
    .studentStory {
      background: rgb(255, 255, 255);
      height: 2.41rem;
      width: 3.75rem;
      margin-top: .1rem;
      .top {
        height: .7rem;
        width: 3.75rem;
        line-height: .7rem;
        img {
          width: 0.32rem;
          height: 0.32rem;
          margin-left: .1rem;
          vertical-align: middle;
          margin-top: -.05rem;
        }
        span {
          font-size: .17rem;
          color:rgba(23,6,6,0.8);
        }
      }
    }
    .story{
      background: rgb(255, 255, 255);
      margin-top: .1rem;
      height: auto;
      overflow: hidden;
      .top {
        width: 3.75rem;
        height: .5rem;
        line-height: .5rem;
        img {
          width: 0.32rem;
          height: 0.32rem;
          vertical-align: middle;
          margin-top: -.05rem;
          margin-left: .1rem;
        }
        span {
          font-size: .17rem;
        }
      }
      .item {
        display: block;
        background-color: #fff;
        position: relative;
        padding: .2rem .15rem .1rem .15rem;
        /*&:before {*/
          /*.borderBottom;*/
        /*}*/
        .fl {
          width: 69%;
          p{
            line-height: 1.5;
          }
        }
        .fr {
          width: 30%;
          text-align: right;
        }
        .title {
          font-size: .15rem;
          color: #363636;
          min-height: .54rem;
          overflow: hidden;
          &.img{
            min-height: .27rem;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .date {
          margin-top: .1rem;
          .heart{
            width: 1.5rem;
            float: left;
            span{
              display: block;
              float: left;
              margin-right: .17rem;
              padding-left: .21rem;
              line-height: .18rem;
              overflow: hidden;
              &.heartbeat{
                background: url("../../../assets/image/scholarshipStory/ic_school_like.png") no-repeat;
                background-size: .18rem;
                &.active{
                  background: url("../../../assets/image/scholarshipStory/ic_school_like_select.png") no-repeat ;
                  background-size: .18rem;
                }
              }
              &.read{
                background: url("../../../assets/image/scholarshipStory/ic_school_like copy.png") no-repeat 0 .01rem;
                background-size: .16rem;
              }
            }
          }
          font-size: .12rem;
          color: rgba(54,54,54,.6);
        }
        .img_wrap{
          width: 100%;
          height: auto;
          overflow: hidden;
          margin-bottom: .11rem;
          img{
            width: 1.12rem;
            height: 1.12rem;
            float: left;
            margin-left: .04rem;
            border-radius: .05rem;
            &:first-of-type{
              margin-left: 0;
            }
          }
        }
        .description{
          font-size: .12rem;
          color: #444;
        }
        .pic {
          width: .72rem;
          height: .72rem;
          object-fit: cover;
          border-radius: .05rem;
        }
      }
      .lookMore{
        display: block;
        width:1.2rem;
        height: .3rem;
        color: white;
        line-height: .3rem;
        background-color: #f06e6c;
        border-radius: .15rem;
        padding-top: 0;
        margin: .1rem auto;
      }
    }
  }
  .vocational_wrap{
    .nothing{
      width: 100%;
      padding-top: 1.7rem;
      margin: 1.4rem auto;
      background-image: url("../../../assets/image/active/enrollAggregate/<EMAIL>");
      background-size: 1.65rem 1.65rem;
      background-repeat: no-repeat;
      background-position: 1.05rem 0;
      text-align: center;
      color: rgba(23,6,6,.6);
    }
    .button-box{
      margin-top: 0.25rem;
      display: flex;
      justify-content: center;
      button{
        width: 1.3rem;
        color:#fff;
        height: 0.36rem;
        font-size: 0.15rem;
        font-weight:600;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #F06E6C;
        border-radius: 50px;
        i{
          background: url("../../../assets/image/active/enrollAggregate/arrow.png") no-repeat;
          background-size: 100% 100%;
          width: 0.05rem;
          height: 0.12rem;
          margin-left: 0.04rem;
        }
      }
    }
  }

  .selfTought_wrap{
    padding-bottom: .5rem;

    &.active{
      padding-top: 0;
    }
    .yz-self-tought{
      position: relative;
      padding-bottom: 0.6rem;
      img{
        vertical-align: middle;
      }
      ul,li{
        list-style: none;
      }

      .invite {
        padding: 0.16rem 0;
        background-image: url("../../../assets/image/active/dreamBuild/content-bg.png");
        background-size: 100%;
        img {
          width: 0.48rem;
          height: 0.48rem;
          border-radius: 50%;
          float: left;
          margin-left: 0.24rem;
        }
        .headTxt {
          float: left;
          margin-left: 0.12rem;
          p {
            font-size: 0.18rem;
            span {
              color: #e15443;
              font-size: 0.14rem;
            }
            &:first-of-type {
              font-size: 0.14rem;
            }
          }
        }
      }
      .invite-top {
        background-color: #fff;
        height: 0.72rem;
        position: relative;
        z-index: 10;
      }

    }
    .yz-self-tought__header{
      position: relative;
      .bg{
        vertical-align: middle;
        width: 100%;
      }
      .top{
        position: absolute;
        width: 100%;
        padding: 0.12rem 0.1rem 0;
        left: 0;
        top: 0;
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .swiper-container{
          margin: 0;
          height: 0.23rem!important;
          background: rgba(255, 227, 255, 0.4);
          padding-right: 0.05rem;
          border-radius: 0.22rem;
          .swiper-wrapper{
            // height: 0.23rem!important;
          }
        }
        .loop{
          display: flex;
          align-items: center;
        }
        .head-img{
          border-radius: 50%;
          object-fit: cover;
          width: 0.22rem;
          height: 0.22rem;
        }
        .name{
          padding: 0.05rem;
          line-height: 1;
        }
        .has-people{
          // display: flex;
          width: 50%;
          // align-items: center;
          // padding-top: 3rem;
          &>span{
            // height: 25rem;
            /*padding-top: 0.05rem;*/
            float: left;
            line-height: .18rem;
            font-weight: bold;
          }
          .d-roll-list{
            float: left;
            padding-top: 0.03rem;
            font-weight: bold;
            .d-roll-item{
              height: 0.25rem!important;
            }
          }
          .d-roll-wrapper{
            margin:0;
            width: auto;
            font-weight: bold;
          }
        }
      }
      .middle-img{
        position: absolute;
        top: 0.74rem;
        width: 100%;
        left: 0;
        padding: 0 0.4rem;
        img{
          width: 100%;
          display: block;
          &.mg{
            margin: 0.05rem 0 0.13rem;
          }
        }
      }
      .bottom-province{
        position: absolute;
        bottom: 0.1rem;
        background: #FFBA00;
        color: #D81608;
        font-size: 0.12rem;
        display: inline-block;
        padding: 0.04rem 0.17rem;
        line-height: 1;
        left: 50%;
        border-radius: 0.50rem;
        transform: translateX(-50%);
      }
    }
    .yz-self-tought__content{
       .link{
        display: flex;
        justify-content: center;
        margin-bottom: .15rem;
        margin-top: 0.2rem;
        .miban{
          /*margin-right: .3rem;*/
        }
        .yz,.miban{
          width: 1.63rem;
          height: .44rem;
          img{
            width: 100%;
          }
        }
        .yz2{
          width: 1.93rem;
          height: .44rem;
          img{
            width: 100%;
            height:100%;
          }
        }
      }
      .tab2 {
        height: 0.5rem;
        overflow: hidden;
        position: relative;
        .item{
          float: left;
          height: 0.5rem;
          width: 50%;
          background-color: rgba(7, 90, 185, 1);
          background-image:none;
          text-align: center;
          span {
            display: inline-block;
            width: 100%;
            line-height: .5rem;
            font-size: 0.16rem;
            color: #fff;
          }
          &:first-of-type{
            position: relative;
            &:after {
              content: none;
            }
          }
          &.active {
            background: rgba(43, 144, 252, 1);
            span {
              position: relative;
              width: auto;
              border-right: none;
              color: #fff;
            }
          }
        }
        .line {
          position: absolute;
          width: 0.4rem;
          left: .9rem;
          height: 0.02rem;
          background-color: #fff;
          bottom: 0.09rem;
          transform: translateX(-50%);
          border-radius: 2px;
          transition: all .5s;
          &.active{
            left:2.8rem;
          }
        }
      }
      .van-tabs{
        .van-tabs__nav{
          background: #FF4C50;
        }
        .van-tab{
          line-height: 0.5rem;
          color: #fff;
        }
        .van-hairline--top-bottom::after{
          display: none;
        }
        &.van-tabs--line{
          padding-top: 0.5rem;
        }
        .van-tabs__wrap{
          height: 0.5rem;
          z-index: 1;
          .van-ellipsis{
            font-size: 0.16rem;
            display: inline-block;
            position: relative;
          }
        }
        .van-tabs__nav--line{
          padding-bottom: 0.05rem;
        }
        .van-tabs__line{
          // display: none;
          background: #fff;
        }
        .van-tab--active{
          background: #B2272A;
          .van-ellipsis{
            font-weight: 500;
          }
        }
      }


    }
  }
  .graduate_wrap {
  .graduate__header {
    position: relative;
    .newBanner {
      margin-top: 0.5rem;
      img {
        width: 100%;
      }
    }
    .active {
      margin-top: 0;
    }
    .top {
      position: absolute;
      width: 100%;
      padding: 0.12rem 0.1rem 0;
      left: 0;
      top: 0.1rem;
      color:black;
      font-size: 0.13rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      z-index: 9;
      .has-people {
        width: 50%;
        & > span {
          float: left;
          line-height: 0.18rem;
          font-weight: bold;
        }
        .d-roll-list {
          float: left;
          padding-top: 0.03rem;
          font-weight: bold;
          .d-roll-item {
            height: 0.25rem !important;
          }
        }
        .d-roll-wrapper {
          margin: 0;
          width: auto;
          font-weight: bold;
        }
      }
    }
  }
  .graduate__content {
    .link {
      display: flex;
      justify-content: center;
      margin-bottom: 0.15rem;
      margin-top: 0.2rem;
      .miban {
        /*margin-right: .3rem;*/
      }
      .yz,
      .miban {
        width: 1.63rem;
        height: 0.44rem;
        img {
          width: 100%;
        }
      }
      .yz2 {
        width: 1.93rem;
        height: 0.44rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .tab2 {
      height: 0.5rem;
      overflow: hidden;
      position: relative;
      .item {
        float: left;
        height: 0.5rem;
        width: 25%;
        background: #FCE1EA;
        background-image: none;
        text-align: center;
        span {
          display: inline-block;
          width: 100%;
          line-height: 0.5rem;
          font-size: 0.16rem;
          color: #ea3470;
          font-weight: bold;
        }
        &:first-of-type {
          position: relative;
          &:after {
            content: none;
          }
        }
        &.active {
          background: #e93470;
          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #fff;
          }
        }
      }
      .line {
        position: absolute;
        width: 0.4rem;
        left: 0.45rem;
        height: 0.02rem;
        background-color: #fff;
        bottom: 0.09rem;
        transform: translateX(-50%);
        border-radius: 2px;
        transition: all 0.5s;
        &.active {
          left: 1.4rem;
        }
        &.active2 {
          left: 2.35rem;
        }
        &.active3 {
          left: 3.3rem;
        }
      }
    }
    .tab-content {
      ul {
        width: 100%;
        background: #fff;
        text-align: center;
        padding: 0.3rem 0;
        li {
          position:relative;
          width: 3.45rem;
          height: 1.25rem;
          margin: 0 auto;
          box-shadow: 1px 3px 10px 0px rgba(0, 0, 0, 0.35);
          border-radius: 9px;
          div {
            .en-title {
              text-align:left;
              padding-left:.25rem;
              color: rgba(234, 52, 113, 1);
              font-size: 0.24rem;
              padding-top: .24rem;
            }
            .major {
              text-align:left;
              padding-left:.2rem;
              font-size:.17rem;
              color:#363636;
              padding-top: .4rem;
              font-weight: 600;
            }
            .lookSchool {
              width:.75rem;
              height: .25rem;
              font-size: .12rem;
              color:#fff;
              border-radius:12px;
              background: #E93470;
              position: absolute;
              left: .2rem;
              bottom: .26rem;
              img {
                width:5px;
                vertical-align: baseline;
              }
            }
          }
        }
        li:not(:first-child) {
          margin-top: 0.15rem;
        }
        .MBALi{
          background:url('../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
          background-size: 3.54rem 1.35rem;
        }
        .MEMLi {
          background:url('../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
          background-size: 3.54rem 1.35rem;
        }
        .MPALi {
          background:url('../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
          background-size: 3.54rem 1.35rem;
        }
      }
    }
  }
  .tab-content-2 {
    background:#fff;
    img {
      width:100%
    }
    .img2 {
      margin-top:.35rem;
    }
  }
  .tab-content-3 {
    background: #fff;
    ul {
      width: 3.4rem;
      margin: 0 auto;
      overflow: hidden;
      padding-bottom:.3rem;
      li {
        width: 1.7rem;
        float: left;
        margin-top: .15rem;
          img {
            width: .26rem;
            height: .26rem;
            margin-top: .22rem;
          }
          .text-1 {
            font-size: .16rem;
            font-weight: 600;
            color: #363636;
            margin-top: .11rem;
          }
          .text-2 {
            font-size: .12rem;
            color: #363636;

          }
      }
    }
  }
}
.graduatePop {
  z-index: 99999 !important;
  max-height:80%;
  .head_box {
    line-height: .6rem;
    background: #fff;
    border-bottom:1px solid rgba(54, 54, 54, .15);
    position: sticky;
    top: 0;
    .line {
      position: absolute;
      top: .2rem;
      width:.05rem;
      height: .2rem;
      background: #E93470;
    }
    .pfName {
      color: #363636;
      font-size: .18rem;
      font-weight: 600;
      padding-left: .1rem;
    }
    img {
      width: .12rem;
      position: absolute;
      top: .24rem;
      right: .2rem;
    }

  }
  .enrollSchoolUl {
    overflow: hidden;
    background: #fff;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    padding: .1rem .12rem;
    li {
      box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.35);
      width:100%;
      padding: .1rem;
      border-radius: 5px;
      margin-top: .15rem;
      display:flex;
          position: relative;
      align-items: center;
        .icon {
          width: .42rem;
          height: .42rem;
          z-index:2;
        }
        .back {
          position: absolute;
          top:0;
          left:0;
          width:100%;
          height:100%;
          z-index:1;
        }
        .school_name {
          font-size: .16rem;
          font-weight: 600;
          margin: 0 .1rem;
          z-index:2;
        }
        button {
          width: .55rem;
          height: .28rem;
          background: #E93470;
          border-radius:14px;
          color: #fff;
          margin-left: auto;
          flex-shrink:0;
          z-index:2;
          img {
            width: 4px;
            height:auto;
            margin-top: .05rem;
            margin-left:3px;
          }
      }
    }
  }
}

.mianBox /deep/ .graduateUserMessage {
  background: rgba(116, 34, 110, .05);
}
.messageHead {
  line-height:.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(116,34,110,0.05);
  img {
    width:.19rem;
    margin-right: .05rem;
  }
  span {
    font-size:.16rem;
    font-weight:600;
  }
}
.graduate_wrap {
  padding-bottom:.5rem;
}
  .openUniversity_wrap,.selfTought_wrap,.vocational_wrap{
    width: 100%;
    height: 100%;
    margin-top: .5rem;
    &.active{
      margin-top: 0;
    }
    iframe{
      width: 100%;
      height:100vh;
      padding-top: .5rem;
    }
  }
.fixImg {
  position: fixed;
  top:75%;
  right:.09rem;
  z-index:9999;
  img {
    width:.63rem;
  }
}
  @keyframes slide {
    0%{
      opacity: 0  /*开始为原始大小*/
    }
    100%{
      opacity: 1;
    }
  }
  @keyframes scaleDraw {  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/
    0%{
      transform: scale(1);  /*开始为原始大小*/
    }
    25%{
      transform: scale(1.15); /*放大1.1倍*/
    }
    50%{
      transform: scale(1);
    }
    75%{
      transform: scale(1.15);
    }
  }
</style>
