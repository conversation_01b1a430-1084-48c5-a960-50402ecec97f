<template>
  <div class="teammate" v-if="show" @click="close">
    <div class="inner" @click.stop>
      <div class="tit txt-c">恭喜您兑换成功！</div>
      <div class="subtit txt-c">请输入您的队友信息！</div>
      <div class="teammate-item" v-for="(item,index) in teammates" :key="index">
        <div class="label">队友{{index + 1}}</div>
        <div class="cont">
          <div class="lf">
            <input class="input1" v-model.trime="item.name" type="text" placeholder="姓名" autocomplete="off"/>
          </div>
          <div class="rt">
            <input class="input1" v-model.trime="item.phone" type="text" placeholder="电话" autocomplete="off"/>
          </div>
        </div>
      </div>
      <div class="clearfix">
        <a class="fl btn-add" href="javascript:;" @click="addItem"><i>+</i> <span class="vam">继续添加</span></a>
        <button class="fr btn1" @click="submit">确定</button>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['orderNo'],
    data() {
      return {
        show: false,
        sadfdsaf:this.orderNo,
        teammates: [{name: '', phone: ''}]
      }
    },
    methods: {
      open: function () {
        this.show = true;
      },
      close: function () {
        this.show = false;
      },
      addItem: function () {
        this.teammates.push({name: '', phone: '', orderNo: this.orderNo});
      },
      submit: function () {
        let datas = [];
        for (let item of this.teammates) {
          item.orderNo = this.orderNo;
          datas.push(item)
        }
        this.$http.post('/mkt/addActionMember/1.0/', {items: JSON.stringify(datas)}).then(res => {
          if (res.code !== '00') return;
          //this.close();
          //this.$router.push(decodeURIComponent(this.$route.query.redirect));
          this.$router.go(-1);
          this.$modal('提交成功');
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  .teammate{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; background-color:rgba(0, 0, 0, .5);
    .inner{ position:absolute; top:50%; right:.16rem; left:.16rem; max-height:100%; padding:.24rem .12rem .15rem; overflow-y:auto; background-color:#fff; transform:translateY(-50%); border-radius:.04rem; }
    .tit{ font-size:.18rem; }
    .subtit{ padding-bottom:.02rem; color:#999; font-size:.13rem; }
    .btn1{ min-width:.82rem; }
    .btn-add{
      display:block; line-height:.16rem; color:#666;
      i{ display:inline-block; width:.15rem; height:.15rem; line-height:1; vertical-align:middle; text-align:center; font-style:normal; border:1px solid #c7c7cc; border-radius:50%; }
    }
  }
  .teammate-item{
    margin-bottom:.08rem;
    .label{ padding-bottom:.06rem; color:#999; font-size:.13rem; }
    .cont{
      display:flex;
      .lf{ width:.75rem; margin-right:.08rem; }
      .rt{ flex:1; }
    }
    .input1{ width:100%; height:.4rem; padding:0 .1rem; color:#444; font-size:.14rem; border:1px solid #dfdfdf; border-radius:.02rem; }
  }
</style>
