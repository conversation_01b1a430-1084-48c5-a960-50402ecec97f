<template>
  <van-dialog
    v-model="show"
    width='2.64rem'
    class='yz-win-prize'
    :show-confirm-button='false'
    @close='close'
  >
    <div class="d-header">
      <div class="prize-good">
        <img :src="imgSrc" alt="">
      </div>
    </div>
    <div class="name-box">
      <p class="name">{{name}}*1</p>
      <red-btn big width='1.68rem' @click='submit'>立即领取</red-btn>
    </div>
  </van-dialog>
</template>

<script>
import { Dialog, Toast } from 'vant';
import RedBtn from './red-btn';

export default {
  components: { Dialog, RedBtn },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    prizeInfo: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    name() {
      if (this.prizeInfo.fonts) {
        return this.prizeInfo.fonts[0].text;
      }
      return '';
    },
    imgSrc() {
      if (this.prizeInfo.imgs) {
        return this.prizeInfo.imgs[0].src;
      }
      return '';
    },
    prizeId() {
      return this.prizeInfo.prizeId;
    },
  },
  watch: {
    value() {
      this.show = this.value;
    },
  },
  data() {
    return {
      show: false,
    };
  },
  mounted() {
    this.show = this.value;
  },
  methods: {
    close() {
      this.$emit('input', false);
      this.$emit('close');
    },
    submit() {
      switch (Number(this.prizeId)) {
        case 1:
        case 2:
        case 3:
        case 7:
          this.$emit('openAddress', this.prizeInfo.lotteryId);
          break;
        case 4:
        case 8:
          Toast('已成功领取');
          break;
        case 5:
          this.$router.push('/aspirantUniversity/classSelect'); // 月卡 -> 课程
          break;
        case 6:
          this.$router.push('/aspirantUniversity/learningCenter?tabIndex=1'); // 周卡 -> 读书
          break;
        default:
          break;
      }
      this.close();
    },
  },
};
</script>

<style lang="less">
.yz-win-prize{
  border-radius: 0.1rem;
  background: none;
  .d-header{
    background: url(../../../../assets/image/active/618Activity/prize-bg.png) no-repeat;
    background-size: 100%;
    height: 2.83rem;
    position: relative;
  }
  .prize-good{
    background: #fff;
    width: 1.3rem;
    height: 1.3rem;
    border-radius: 50%;
    position: absolute;
    bottom: 0.12rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      width: 0.8rem;
    }
  }
  .name-box{
    text-align: center;
    font-size: 0.13rem;
    padding-top: 0.1rem;
    padding-bottom: 0.2rem;
    background: #fff;
    border-radius: 0 0 0.1rem 0.1rem;
    .name{
      margin-bottom: 0.15rem;
    }
  }
}
</style>
