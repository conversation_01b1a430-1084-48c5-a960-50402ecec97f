<template>
  <div @touchmove.prevent>
    <picker :slots="slots" :isSetDefaultVal="isSetDefaultVal" v-model="val" ref="picker" />
  </div>
</template>

<script>
import picker from '@/components/picker'

export default {
  props: {
    dictName: {
      type: String,
      default: ''
    },
    options: Array,
    ext1: String,
    ext3: String,
    isSetDefaultVal: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    defaultIndex: {
      type: Number,
      default: 0
    },
    filterValue: Array,
    isGuokai: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      slots: [],
      val: [{}],
      vals: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init: function () {
      this.slots = [{ flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex }]
    },
    getOptions: function () {
      let options = []
      if (this.options || this.dictName) {
        this.vals = this.options || dictJson[this.dictName] || []

        if (this.ext1) {
          var ext1 = this.ext1
          if (ext1 == '3') {
            ext1 = '1'
          }
          this.vals = this.vals.filter(val => val.ext1 === ext1)
        }

        if (this.filterValue && this.filterValue.length > 0) {
          this.vals = this.vals.filter(val => this.filterValue.includes(val.dictValue))
        }
        if (this.ext3) {
          if (this.ext1 == '3') {
            this.vals = this.vals.filter(val => val.ext3 === this.ext3 && val.dictValue == '2020')
          } else if (this.ext1 == '2') {
            const changeTime = new Date(2021, 2, 14, 18, 0, 0)
            if (Date.now() >= changeTime.getTime()) {
              this.vals = this.vals.filter(val => val.ext3 === this.ext3 && val.dictValue == '202309')
            } else {
              this.vals = this.vals.filter(val => val.ext3 === this.ext3 && val.dictValue == '202109')
            }
          } else {
            this.vals = this.vals.filter(val => val.ext3 === this.ext3)
          }
        }
        if (this.isGuokai && (this.dictName == 'rprType' || this.dictName == 'nation')) {
          this.vals = this.vals.filter(item => item.dictName != '其他' && item.dictName != '其它')
        }
        for (let item of this.vals) {
          options.push(item.dictName)
        }
      }
      // 1001、1002、1003名族/户口类型 过滤'其他'选项
      if (this.isGuokai && (this.dictName == 'rprType' || this.dictName == 'nation')) {
        options = options.filter(item => item != '其他' && item != '其它')
      }
      return options
    },
    open: function () {
      if (this.disabled) return
      this.$refs.picker.open()
    },

    setValues: function (value) {
      if (value) {
        const name = (this.vals.find(val => val.dictValue === value) || {}).dictName
        if (name) {
          this.$refs.picker.setValues([name])
        }
      }
    }
  },
  watch: {
    dictName(val) {
      this.init()
    },
    isGuokai(val) {
      this.init()
    },
    val: function () {
      this.$emit('input', this.vals[this.val[0].index] || {})
    },
    options: function () {
      this.init()
    }
  },
  components: { picker }
}
</script>
