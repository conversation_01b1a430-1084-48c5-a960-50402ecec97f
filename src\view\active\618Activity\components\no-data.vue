<template>
  <div class="yz-nodata-jg">
    <div class="img-box cl">
      <img src="../../../../assets/image/active/618Activity/empty.png" alt="">
    </div>
    <p class="nodata-text">
      <slot>{{text}}</slot>
    </p>
  </div>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="less" scoped>
.yz-nodata-jg{
  text-align: center;
  .img-box{
    img{
      width: 1.5rem;
      height: 1.5rem;
      vertical-align: middle;
    }
  }
  .nodata-text{
    color: #000;
    font-size: 0.13rem;
    margin-top: 0.1rem;
  }
}
</style>
