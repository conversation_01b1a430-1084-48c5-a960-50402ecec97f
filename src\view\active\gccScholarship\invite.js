export default {
  mounted(){
   this._inviteId = this.$route.query.inviteId || '';
   this._regOrigin = this.$route.query.regOrigin || '';
   this._regChannel =  this.$route.query.regChannel || '';
  },
  data(){
    return {
      _inviteId:'',//邀约人id
      _regOrigin:'',//邀约类型
      _regChannel:''//注册渠道
    }
  },
  methods:{
    $InviteJump(path) {
      let route = {
        path: path,
        query:{
          inviteId:this._inviteId,
          regOrigin:this._regOrigin,
          regChannel:this._regChannel,
          scholarship: '121',
        }
      };
      this.$router.push(route);
    }
  }
}
