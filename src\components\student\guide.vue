<template>
    <div>
        <div class="toast">
            <div class="describe">为了提高您的上课质量与学习体验，<span>公众号上课功能将在3月1日关闭</span>，请尽快前往远智教育APP内进行学习哦~</div>
            <div class="reveal"><img src="../../assets/image/student/toApp.gif" alt=""></div>
            <p>远智教育APP-学堂-学科课</p>
            <wx-open-launch-app id="launch-btn" @error="callAppError" :appid="OpenAppId" extinfo='extinfo'>
              <script type="text/wxtag-template">
                <style>.appBtn{
                    width: 254px;
                    height: 41px;
                    background: #F06E6C;
                    border-radius: 20px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #FFFFFF;
                    line-height: 40px;
                    margin: 0 auto;
                    text-align: center;}
                </style>
                <div class='appBtn'>去APP上课</div>
              </script>
            </wx-open-launch-app>
            <!-- <div class='appBtn' @click="down">去APP上课</div> -->
            <div class="continue" @click="toLive">继续上课</div>
        </div>
    </div>
</template>
<script>
import openApp from '@/mixins/openApp';

export default {
    data(){
        return {
          OpenAppId,
        }
    },
    mixins: [openApp],
    mounted(){
      this.wxCallAppInit(); // 微信标签唤起app
    },
    methods:{
        toLive(){
            this.$emit('gotoLive')
        },
        down(){
            window.location.href='https://sj.qq.com/myapp/detail.htm?apkName=cn.yzou.yzxt';
        }
    }
}
</script>
<style lang="less" scoped>
.toast{
  width: 2.91rem;
  height: 3.83rem;
  background: #FFFFFF;
  text-align: center;
  .describe{
    width: 2.54rem;
    height: .51rem;
    margin: .15rem auto;
    text-align: center;
    font-size: .12rem;
    font-weight: 400;
    color: #746A6A;
    span{
      color: rgba(240, 110, 108, 1);
      font-weight: 600;
    }
  }
  .reveal{
    width: 2.37rem;
    height: 1.67rem;
    margin: 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }
  p{
    font-size: 10px;
    font-weight: 400;
    color: #A29B9B;
    text-align: center;
    margin: .08rem 0 .16rem 0;
  }
  .appBtn{
    width: 2.54rem;
    height: .4rem;
    background: #F06E6C;
    border-radius: .2rem;
    font-size: .14rem;
    font-weight: 400;
    color: #FFFFFF;
    line-height: .4rem;
    margin: 0 auto;
    text-align: center;
  }
  .continue{
    // width: .56rem;
    height: .2rem;
    margin: .15rem auto 0 auto;
    font-size: .14rem;
    font-weight: 400;
    color: #A29B9B;
  }
}
</style>
