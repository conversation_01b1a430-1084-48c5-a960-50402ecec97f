<template>
  <div class="countdown" v-if="isShow">
    <span class="num day">{{day}}</span>
    <span class="num hour">{{hour}}</span>
    <span class="num minute">{{minute}}</span>
    <span class="num second">{{second}}</span>
    <span class="num big millisecond">{{millisecond}}</span>
  </div>
</template>

<script>
  export default {
    name: 'countdown',
    props: ['startTime','endTime'],
    data() {
      return {
        isShow: false,
        timer: null,
        day: '--',
        hour: '--',
        minute: '--',
        second: '--',
        millisecond: '--',
        // startTime: 1529510400000 ,   // 开始时间：2018-06-21 00:00:00
        // endTime: 1530374400000 ,  // 结束时间：2018-06-01 00:00:00
      }
    },
    beforeDestroy() {
      this.clearTimer();
    },
    methods: {
      countdown: function () {
        const now = new Date().getTime();
        const surplus = this.endTime - now;

        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;

        this.day = this.complement(parseInt(s / oneDay));
        this.hour = this.complement(parseInt(s % oneDay / oneHour));
        this.minute = this.complement(parseInt(s % oneDay % oneHour / 60));
        this.second = this.complement(parseInt(s % oneDay % oneHour % 60));
        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      complement: function (num) {
        return num < 10 ? '0' + num : num;
      },
      clearTimer: function () {
        clearInterval(this.timer)
      },
      // 获取服务器时间，判断活动是否已结束
      getSystemDateTime: function (now) {
        // debugger
        // if (this.endTime > now > this.startTime) {
          this.isShow = true;
          this.timer = setInterval(this.countdown, 100);
        // }
      }
    }
  }
</script>

<style lang="less" scoped>
  .mb5{
    margin-bottom: .5rem;
  }
  .countdown{
    height:.15rem;margin:.4rem .18rem .2rem;text-align:left;background:url(../../assets/image/active/scholarship/countdown2.png) no-repeat;background-size:80%;background-position-x: .45rem;
    .num {
      display:inline-block;width:.27rem;height:.27rem;line-height:.02rem;vertical-align:middle;color:#ea7150;font-size:.18rem;
      font-weight: 700;
      &.big{width:.34rem;height:.34rem;line-height:.05rem;font-size:.24rem;}
      &.day{margin-left:.2rem;}
      &.hour{margin-left:.2rem;}
      &.minute{margin-left:.29rem;}
      &.second{margin-left:.28rem;}
      &.millisecond{margin-left:.37rem;}
    }
  }
</style>
