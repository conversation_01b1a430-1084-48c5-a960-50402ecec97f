<template>
  <dialog-container
    v-model="show"
    title='请填写收货信息'
    :showClose='false'
    @close='close'
  >
    <div class="yz-address-dialog">
      <div class="label-input">
        <label>收件人：</label>
        <input v-model.trim="name" class="input" type="text" maxlength="10" placeholder="请输入姓名">
      </div>
      <div class="label-input">
        <label>收货电话：</label>
        <input v-model.trim="mobile" class="input" type="text" placeholder="请输入有效手机号码">
      </div>
      <div class="label-input">
        <label>收货地址：</label>
        <textarea v-model.trim="address" class="input" name="" maxlength="50" minlength="3" placeholder="请输入收货地址"></textarea>
      </div>
      <div class="submit-box tac">
        <red-btn width='1.68rem' big :disabled='btnDisabeld' @click='submit'>提交</red-btn>
      </div>
      <p class="notice">温馨提示：<br />请认真填写收货信息，以便我们及时发放奖品，未填写或错填等后果自行承担。</p>
    </div>
  </dialog-container>
</template>

<script>
import { Toast } from 'vant';
import { isLogin, isphone } from '@/common';
import DialogContainer from './dialog-container';
import RedBtn from './red-btn';

export default {
  components: { DialogContainer, RedBtn },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    lotteryId: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      show: false,
      name: '',
      mobile: '',
      address: '',
    }
  },
  computed: {
    btnDisabeld() {
      if (!this.name || !this.mobile || !this.address) {
        return true;
      }
      return false;
    },
  },
  watch: {
    value(val) {
      this.show = this.value;
      if (val) {
        this.myAddress();
      }
    },
  },
  mounted() {
    this.show = this.value;
    this.myAddress();
  },
  methods: {
    close() {
      this.$emit('input', false);
    },
    async myAddress() {
      if (!isLogin()) {
        return;
      }
      const { code, body } = await this.$http.post('/mkt/findFrontAddressInfo/1.0/');
      if (code == '00' && body) {
        this.name = body.consigneeName;
        this.mobile = body.consigneeMobile;
        this.address = body.consigneeAddress;
      }
    },
    async submit() {
      if (this.name.length > 10) {
        this.$modal({message: '姓名不能超过10位数', icon: 'warning'});
        return;
      }
      let reg = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/im;
      if (reg.test(this.name)) {
        this.$modal({message: '姓名不能含有特殊字符', icon: 'warning'});
        return;
      }
      if (!isphone(this.mobile)) {
        this.$modal({message: '请输入合法手机号', icon: 'warning'});
        return;
      }
      if (this.address.length < 3 || this.address.length > 50) {
        this.$modal({message: '地址长度应该在3-50字之间', icon: 'warning'});
        return;
      }
      const { code, body } = await this.$http.post('/mkt/addShippingAddress/1.0/', {
        consigneeName: this.name,
        consigneeMobile: this.mobile,
        address: this.address,
        lotteryId: this.lotteryId,
      });
      if (code == '00') {
        Toast('已成功领取');
        this.$emit('update');
        setTimeout(() => {
          this.close();
        }, 1000);
      }
    },
  },
};
</script>

<style lang="less">
  .yz-address-dialog{
    .label-input{
      display: flex;
      font-size: 0.14rem;
      margin-bottom: 0.1rem;
      padding-right: 0.2rem;
      label{
        width: 1rem;
        text-align: right;
        padding-right: 0.1rem;
        line-height: 0.38rem;
      }
      .input{
        flex: 1;
        height: 0.38rem;
        background: #F5F6F8;
        border-radius: 0.05rem;
        border: 0;
        padding: 0 0.1rem;
        &::placeholder{
          color: rgba(0, 0, 0, 0.6);
        }
      }
      textarea.input{
        height: 0.76rem;
        padding: 0.1rem;
      }
    }
    .submit-box{
      margin-top: 0.3rem;
      text-align: center;
    }
    .notice{
      padding: 0.2rem 0.2rem 0.15rem;
      color: rgba(69, 56, 56, 0.7);
      font-size: 0.13rem;
    }
  }
</style>
