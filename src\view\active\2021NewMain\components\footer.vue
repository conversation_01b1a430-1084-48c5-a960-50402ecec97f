<template>
  <div class="yz-newMain-footer" :class='{iphonex: isIphoneX}'>
    <!-- 友情提示 -->
    <consult-tips v-model="showTips" :tipName='tipName' :bottom='isIphoneX ? "1.03rem" : "0.69rem"' />
    <teacher-card v-model="showTeacherCard" :inviteId='inviteId' :bottom='isIphoneX ? "0.94rem" : "0.6rem"' @noTeacher='openCustomerService' />
    <!-- tab列表 -->
    <div class="item" @click='home'>
      <img src="../../../../assets/image/active/2021NewMain/ic_home.png" class="icon" alt="">
      <p class='red-text'>首页</p>
    </div>
    <div class="item" @click='toInvite'>
      <img src="../../../../assets/image/active/2021NewMain/ic_invitation.png" class="icon" alt="">
      <p>点击有礼</p>
    </div>
    <div class="item" :class='{"red-point": showPoint, triangle: showTips}' @click='consultBtn'>
      <img src="../../../../assets/image/active/2021NewMain/ic_consult.png" class="icon" alt="">
      <p>在线咨询</p>
    </div>
    <div class="item" @click='myClick'>
      <img v-if='!isLogin' src="../../../../assets/image/active/2021NewMain/ic_my.png" class="icon" alt="">
      <img v-else :src="headImg | defaultAvatar" class="icon head-img" alt="">
      <p>{{isLogin ? '我的' : '未登录'}}</p>
    </div>
  </div>
</template>

<script>
import { isIphoneX, isLogin, toLogin } from '@/common';
// 统一七鱼客服与微信客服配置
import customerService from '@/mixins/customerService';
import TeacherCard from './teacher-card';
import ConsultTips from './consult-tips';

export default {
  mixins: [customerService],
  components: { TeacherCard, ConsultTips },
  props: {
    inviteId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isIphoneX: isIphoneX(),
      isLogin: isLogin(),
      tipName: '小Y客服',
      showTips: false,
      showPoint: false,
      headImg: '',
      showTeacherCard: false, // 老师卡片
    };
  },
  mounted() {
    this.initPoint();
    this.getUserInfo();
  },
  methods: {
    initPoint() {
      setTimeout(() => {
        this.showPoint = true;
      }, 5000);
      setTimeout(() => {
        this.showTips = true;
      }, 10000);
    },
    login() {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return false;
      }
      return true;
    },
    toInvite() {
      setTimeout(() => {
        this.$yzStatistic('marketing.base.click', '3', '首页-邀请有礼');
      }, 200);
      this.$router.push({ name:'dreamBuildInvite', query:{ from: 'newMain2021'} });
    },
    home() {
      window.location.reload();
    },
    consultBtn() {
      setTimeout(() => {
        this.$yzStatistic('marketing.base.click', '4', '首页-在线咨询');
      }, 200);
      if (!this.login()) {
        return;
      }
      this.showTips = false;
      this.showPoint = false;
      if (!this.inviteId) {
        this.openCustomerService();
        return;
      }
      this.showTeacherCard = true;
    },
    myClick() {
      setTimeout(() => {
        this.$yzStatistic('marketing.base.click', '5', '首页-我的');
      }, 200);
      if (!this.login()) {
        return;
      }
      this.$router.push({ name:'settings' });
    },
    // 用户信息
    async getUserInfo() {
      this.headImg = this.storage.getItem('headImg');
      if (!this.isLogin || this.headImg) {
        return;
      }
      const res = await this.$http.post('/us/getUserInfoById/1.0/');
      if (res.code == '00') {
        this.headImg = res.body.headImg;
        this.storage.setItem('headImg', res.body.headImg);
      }
    },
  },
};
</script>

<style lang="less" scoped>

  .yz-newMain-footer{
    position: fixed;
    z-index: 10;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    display: flex;
    &.iphonex{
      padding-bottom: 0.34rem;
      .tips-box{
        bottom: 0.98rem;
      }
    }
    .item{
      text-align: center;
      flex: 1;
      font-size: 0.12rem;
      color: #170606;
      font-weight: 600;
      line-height: 1;
      padding: 0.05rem 0;
      position: relative;
    }
    .red-point{
      position: relative;
      &::after{
        content: '';
        width: 0.08rem;
        height: 0.08rem;
        border-radius: 50%;
        box-shadow: 0px 1px 4px 0px rgba(243, 54, 0, 0.6);
        position: absolute;
        right: 0.3rem;
        top: 0.05rem;
        background: #F33600;
      }
    }
    .triangle::before{
      content: '';
      // background: rgba(0, 0, 0, 0.7);
      border: 0.09rem solid #4c4c4c;
      border-left: 0.1rem solid transparent;
      border-right: 0.1rem solid transparent;
      border-bottom: 0.1rem solid transparent;
      width: 0;
      height: 0;
      position: absolute;
      top: -0.12rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .icon{
      width: 0.32rem;
      height: 0.32rem;
      vertical-align: middle;
      margin-bottom: 0.05rem;
      &.head-img{
        border-radius: 50%;
      }
    }
    .red-text{
      color: #DA3338;
    }
  }
</style>
