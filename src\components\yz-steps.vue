<template>
  <div class="yz-steps">
    <div class="part" :class='[{active: item.active}]' v-for="(item, index) in stepList" :key="index">
      <div class="dot">
        <img class='finish' v-if='item.finish' src="../assets/image/student/ic_sex_sel.png" alt="">
        <img v-else-if='item.active' src="../assets/image/student/ic_logistics_newest.png" alt="">
        <i v-else></i>
      </div>
      <p>{{item.name}}</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    stepNames: {
      type: Array,
      default: () => [],
    },
    current: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      stepList: [],
    };
  },
  computed: {

  },
  watch: {
    current() {
      this.setStep();
    },
  },
  mounted() {
    if (this.stepNames.length > 0) {
      this.stepList = this.stepNames.map(item => ({ active: false, finish: false, name: item }));
      this.setStep();
    }
  },
  methods: {
    setStep() {
      if (this.current <= 0) {
        return;
      }
      const step = this.current > this.stepNames.length ? this.stepNames.length : this.current;
      this.stepList = this.stepList.map((item, index) => {
        return { active: index < step, finish: index < (step - 1), name: item.name };
      });
    },
  },
};
</script>

<style lang="less" scoped>
  .yz-steps{
    width: 100%;
    height: .85rem;
    background-color: white;
    padding: .2rem;
    position: relative;

    .part{
      width: .83rem;
      height:.45rem;
      float: left;
      position: relative;
      &:first-child::before{
        display: none;
      }
      &::before{
        content: '';
        position: absolute;
        left: -50%;
        top: 0.075rem;
        height: 1px;
        background: rgba(23, 6, 6, 0.08);
        width: 100%;
        z-index: 0;
      }
      &.active::before{
        background: rgba(240, 110, 108, 0.35);
      }
      .dot{
        width: .15rem;
        height: .15rem;
        text-align: center;
        margin: 0 auto .12rem;
        position: relative;
        z-index: 1;
        img{
          position: relative;
          width: 100%;
          z-index: 2;
          &.finish{
            z-index: 3
          }
        }
        i{
          display: inline-block;
          width: .07rem;
          height: .07rem;
          border-radius: 50%;
          background-color: rgb(171,165,165);
          margin: .04rem auto;
        }
      }
      p{
        text-align: center;
        color: rgba(23, 6, 6, .8);
        font-size: .13rem;
      }
    }
  }
</style>
