<template>
  <div class="coupons-wrap" v-if="modalVisible">
    <div class="coupons-inner">
      <div class="coupons-hd">
        <div class="cl">
          <div class="fl">奖学金</div>
          <div class="fr">
            <!--<a class="btn-ps" @click="instructionsVisible=true">使用说明</a>-->
            <i class="icons i-del" @click="setModalVisible(false)"></i>
          </div>
        </div>
        <div class="tab-nav">
          <a class="item" :class="{active:active==='available'}" @click="active='available'">可用({{available.length}})</a>
          <a class="item" :class="{active:active==='unavailable'}" @click="active='unavailable'">不可用({{unavailable.length}})</a>
        </div>
      </div>
      <div class="coupons-bd">
        <mt-tab-container :swipeable="false" v-model="active">
          <mt-tab-container-item id="available">
            <div class="coupon-item" v-for="item,index in available" :class="{active:selected.includes(item),disabled:isDisabled(item)}">
            <!-- <div class="coupon-item" v-for="item,index in available" :class="{active:selected.includes(item)}"> -->
             <!-- selected[0].canSame=='1'?item.couponId!=selected[0].couponId:item.scId!=selected[0].scId ||  -->
             <!-- selected[0].canAnother=='1'?item.couponId==selected[0].couponId:item.couponId!=selected[0].couponId -->
              <div @click="select(item,index)">
              <div class="lay-icon" ><i class="icons i-selected"></i></div>
              <div class="amount">
                <span class="price" style="font-size:.22rem" v-if="~~item.amount===19999">学费全免</span>
                <span class="price" style="font-size:.16rem" v-else-if="deductionCoupons.includes(item.couponId)">第三年学费抵用券</span>
                <template v-else>
                  <span class="i">&yen;</span><span class="price"　>{{item.amount>1?~~item.amount:item.amount}}</span>
                </template>
              </div>
              <div class="cont">
                <p>{{item.couponName}}</p>
                <!--<p class="gap fsc1">{{item.itemCodes|itemName(recruitType,grade)}}</p>-->
                <p class="fsc1">
                  {{item.availableStartTime.substring(0,16)|formatDate('yyyy.MM.dd hh:mm')}}-{{item.availableExpireTime.substring(0,16)|formatDate('yyyy.MM.dd hh:mm')}}
                </p>
              </div>
              </div>
              <div class="desc" @click="clickItem(index)">
                <p class="des">
                  使用规则
                  <img
                    class="arrowImg"
                    ref="arrowTwo"
                    src="../assets/image/ic_unfold.png"
                    alt
                    style="transform:rotate(0deg)"
                  >
                </p>
                <p class="remark" ref="remark" style="display: none" v-if="item.remark">
                  <!--1.适用-->
                  <!--<span v-for="txt  in  item.itemCodes">{{txt}}&nbsp;</span>-->
                  <!--<br>-->
                  <template v-for="it,i in item.remark.split('\n')">
                    <span >{{it}}</span>
                    <br/>
                  </template>
                </p>
              </div>
            </div>
            <div class="no-data" v-if="!available.length">暂无可用奖学金~</div>
          </mt-tab-container-item>
          <mt-tab-container-item id="unavailable">
            <div class="coupon-item disabled" v-for="item,index in unavailable" >
             <div @click="$modal({message:'该优惠券不可用于您所勾选的科目！',icon:'warning'})">
               <!--<div class="lay-icon"><i class="icons i-selected"></i></div>-->
               <div class="amount">
                 <span class="price" style="font-size:.22rem" v-if="~~item.amount===19999">学费全免</span>
                 <span class="price" style="font-size:.16rem" v-else-if="deductionCoupons.includes(item.couponId)">第三年学费抵用券</span>
                 <template v-else>
                   <span class="i">&yen;</span><span class="price"　>{{~~item.amount}}</span>
                 </template>
               </div>
               <div class="cont">
                 <p>{{item.couponName}}</p>
                 <!--<p class="gap fsc1">{{item.itemCodes|itemName(recruitType,grade)}}</p>-->
                 <p class="fsc1">
                   {{item.availableStartTime.substring(0,16)|formatDate('yyyy.MM.dd hh:mm')}}-{{item.availableExpireTime.substring(0,16)|formatDate('yyyy.MM.dd hh:mm')}}
                 </p>
               </div>
             </div>
              <div class="desc" @click="clickItemExpired(index)">
                <p class="des">
                  使用规则
                  <img
                    class="arrowImg"
                    ref="expiredArrow"
                    src="../assets/image/ic_unfold.png"
                    alt
                    style="transform:rotate(0deg)"
                  >
                </p>
                <p class="remark" ref="expiredRemark" style="display: none" v-if="item.remark">
                  <!--1.适用-->
                  <!--<span v-for="txt  in  item.itemCodes">{{txt}}&nbsp;</span>-->
                  <!--<br>-->
                  <template v-for="it,i in item.remark.split('\n')">
                    <span >{{it}}</span>
                    <br/>
                  </template>
                </p>
              </div>
            </div>
            <div class="no-data" v-if="!unavailable.length">空空如也~</div>
          </mt-tab-container-item>
        </mt-tab-container>
        <foot-bar title="确定" v-on:submit="submit"></foot-bar>
      </div>
    </div>
    <div class="instructions" v-if="instructionsVisible">
      <div class="instructions-inner">
        <div class="hd">优惠券使用说明</div>
        <div class="bd">
          <p>1.学费抵扣券只能抵用一次</p>
          <p>2.学费抵扣券最多叠加xxx张，学费满xxxx才能抵扣</p>
        </div>
        <div class="ft" @click="instructionsVisible=false">知道了</div>
      </div>
    </div>
  </div>
</template>

<script>
  import footBar from '@/components/footBar';

  export default {
    filters: {
      itemName: function (arr, recruitType, grade) {
        if (!(arr && arr instanceof Array)) return arr;
        const units = recruitType === '2' && grade !== '201709' ? '学期' : '年';
        const itemNames = {
          Y0: '考前辅导费',
          Y1: `第一${units}学费`,
          S1: `第一${units}书费`,
          W1: `第一${units}网络费`,
          Y2: `第二${units}学费`,
          S2: `第二${units}书费`,
          W2: `第二${units}网络费`,
          Y3: `第三${units}学费`,
          S3: `第三${units}书费`,
          W3: `第三${units}网络费`,
          Y4: `第四${units}学费`,
          S4: `第四${units}书费`,
          W4: `第四${units}网络费`
        };
        arr = arr.map(item => {
          return itemNames[item];
        });
        return `可抵扣：${arr.join('、')}`;
      }
    },
    props: {
      couponList: {
        type: Array,
        default: []
      },
      recruitType: String,
      selectedItem: Array,
      itemCodes: Array,
      grade: String
    },
    data() {
      return {
        modalVisible: false,
        instructionsVisible: false,
        selected: [],
        selectedCoupons: [],
        available: [],
        unavailable: [],
        couponsDetail: [],
        active: 'available',
        deductionCoupons:['154468346717167246']
      }
    },
    computed: {
      title: function () {
        if (this.couponList) {
          if (this.deduction > 0) {
            return `优惠金额：<span style="color:#e85b57">${this.deduction.toFixed(2)}</span>`;
          } else {
            if (this.couponList.length === 0) {
              return '无可用'
            } else {
              return `共${this.couponList.length}张奖学金，${this.available.length}张可用`;
            }
          }
        }
        return '';
      },
      // 计算抵扣值
      deduction: function () {
        let count = 0;
        if (this.couponsDetail.length > 0) {
          for (let item of this.couponsDetail) {
            for (let detail of item.couponDetails) {
              count += Number(detail.price)
            }
          }
        }
        return count;
      }
    },
    created() {},
    methods: {
      isDisabled(item){
        let num;
        this.selected.map(selectItem=>{
          if(item.couponId==selectItem.couponId && selectItem.canSame=='0' && !this.selected.includes(item)){
             num=item;
          }
        })
        if(num!=undefined){
          return num
        }
        // canSame：同样券叠加使用: 0不可以(默认)  1可以，canAnother：和其他券叠加使用: 0不可以 1可以(默认)
        if(this.selected.length!=0){
          if(this.selected[0].canSame=='0'&&this.selected[0].canAnother=='1'){
            if(item.canAnother=='0' || (item.couponId==this.selected[0].couponId&&item.scId!=this.selected[0].scId)){
              return true;
            }
          }else if(this.selected[0].canSame=='1'&&this.selected[0].canAnother=='0'){
            if(item.couponId!=this.selected[0].couponId){
              return true;
            }
          }else if(this.selected[0].canSame=='0'&&this.selected[0].canAnother=='0'){
            return item.scId!=this.selected[0].scId;
          }else if(this.selected[0].canSame=='1'&&this.selected[0].canAnother=='1'){
            if((item.couponId==this.selected[0].couponId && item.canSame=='0') || item.canAnother=='0'){
              return true;
            }
          }
        }
      },
      clickItemExpired(index) {
        if (this.$refs.expiredRemark[index].style.display == "none") {
          this.$refs.expiredRemark[index].style.display = "block";
          this.$refs.expiredArrow[index].style.transform = "rotate(180deg)";
        } else if (this.$refs.expiredRemark[index].style.display == "block") {
          this.$refs.expiredRemark[index].style.display = "none";
          this.$refs.expiredArrow[index].style.transform = "rotate(0deg)";
        }
      },
      setModalVisible: function (visible) {
        if (visible) {
          this.selected = [...this.selectedCoupons];
        }
        this.active = 'available';
        this.modalVisible = visible;
      },
      select: function (item,index) {
        // if(this.selected.includes(item)) {
        //   this.selected.splice(this.selected.indexOf(item), 1);
        // }else{
        //   this.selected.push(item);
        // }
        let cannot = null;
        this.available.forEach(el => {
          this.selected.forEach(selectItem=>{
            if(el.couponId==selectItem.couponId && selectItem.canSame=='0' && !this.selected.includes(el)){
              cannot=el;
            }
          })
        });
        if(this.selected.length>0){
          if(this.selected[0].canSame=='1' && this.selected[0].canAnother=='1'){
            if((item.couponId==this.selected[0].couponId && item.canSame=='0') || item.canAnother=='0'){
              this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
              return
            }
            if(this.selected.includes(item)) {
              this.selected.splice(this.selected.indexOf(item), 1);
            }else{
              if(cannot==item){
                this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
              }else{
                this.selected.push(item);
              }
            }
          }else if(this.selected[0].canSame=='0' && this.selected[0].canAnother=='1'){
            if(item.canAnother=='0' || (item.couponId==this.selected[0].couponId&&item.scId!=this.selected[0].scId)){
              this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
              return
            }
            if(item.couponId!=this.selected[0].couponId){
              if(this.selected.includes(item)) {
                this.selected.splice(this.selected.indexOf(item), 1);
              }else{
                if(cannot==item){
                  this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
                }else{
                  this.selected.push(item);
                }
              }
            }else{
              if(this.selected.includes(item)) {
                this.selected.splice(this.selected.indexOf(item), 1);
              }
            }
          }else if(this.selected[0].canSame=='1' && this.selected[0].canAnother=='0'){
            if(item.couponId!=this.selected[0].couponId){
              this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
              return
            }
            if(item.couponId==this.selected[0].couponId){
              if(this.selected.includes(item)) {
                this.selected.splice(this.selected.indexOf(item), 1);
              }else{
                this.selected.push(item);
              }
            }
          }else if(this.selected[0].canSame=='0' && this.selected[0].canAnother=='0'){
            if(this.selected.includes(item)) {
              this.selected.splice(this.selected.indexOf(item), 1);
            }else{
              this.$modal({message: '不可与已选奖学金叠加使用', icon: 'warning'});
            }
          }
        }else{
            this.selected.push(item);
        }
      },
      clickItem(index) {
        if (this.$refs.remark[index].style.display == "none") {
          this.$refs.remark[index].style.display = "block";
          this.$refs.arrowTwo[index].style.transform = "rotate(180deg)";
        } else if (this.$refs.remark[index].style.display == "block") {
          this.$refs.remark[index].style.display = "none";
          this.$refs.arrowTwo[index].style.transform = "rotate(0deg)";
        }
      },
      // 清空已选
      clearSellected: function () {
        this.selectedCoupons = [];
      },
      // 区分可用优惠券和不可用优惠券
      getCoupon() {
        let available = [];
        let unavailable = [];
        for (let item of (this.couponList || [])) {
          let isAvailable = false;
          const now = new Date().getTime();
          const availableExpireTime = new Date(item.availableExpireTime.substring(0, 16).replace(/-/g, '/')).getTime();

          if (availableExpireTime > now) {
            for (let ic of this.itemCodes) {
              if (item.itemCodes.includes(ic.itemCode)) {
                isAvailable = true;
                break;
              }
            }
          }
          isAvailable ? available.push(item) : unavailable.push(item);
        }
        this.available = available;

        this.unavailable = unavailable;
      },
      // 优惠券抵扣详细
      setDeduction: function () {
        this.couponsDetail = [];  // 清空抵扣详细
        let parInfos = this.getParInfos();
        let coupons = this.selectedCoupons.map(item => {
          return {
            couponType:item.couponType,
            couponId: item.couponId,
            scId: item.scId,
            canAnother: item.canAnother,
            canSame: item.canSame,
            amount: Number(item.amount),
            itemCodes: [...item.itemCodes]
          };
        });
        for (let itemFee of parInfos) {
          let availableCoupons = coupons.filter(item => item.itemCodes.includes(itemFee.itemCode));
          // 排序优惠券
          // availableCoupons = availableCoupons.sort((a, b) => {
          //   if (a.itemCodes.length === b.itemCodes.length) {
          //     return Number(b.amount) - Number(a.amount);
          //   } else {
          //     return a.itemCodes.length - b.itemCodes.length;
          //   }
          // });
          for (let itemCoupon of availableCoupons) {
            // 如果优惠券金额大于等于实付金额
            if (Number(itemCoupon.amount) >= Number(itemFee.payable)) {
              this.setCouponsDetail(itemCoupon.couponId, itemCoupon.scId, itemCoupon.canAnother, itemCoupon.canSame, itemFee.itemCode, itemFee.payable,itemCoupon.amount,itemCoupon.itemCodes);
              itemCoupon.amount -= itemFee.payable;
              itemFee.payable = 0;

              if (itemCoupon.amount === 0) {
                coupons.splice(coupons.indexOf(itemCoupon), 1);
              }

              // 过滤剩下优惠的当前科目code值
              coupons = coupons.map(item => {
                if (item.itemCodes.includes(itemFee.itemCode)) {
                  item.itemCodes.splice(item.itemCodes.indexOf(itemFee.itemCode), 1);
                }
                return item;
              });
              break;
            } else {
              this.setCouponsDetail(itemCoupon.couponId, itemCoupon.scId, itemCoupon.canAnother, itemCoupon.canSame, itemFee.itemCode, itemCoupon.amount,itemCoupon.amount,itemCoupon.itemCodes);
              itemFee.payable -= itemCoupon.amount;
              coupons.splice(coupons.indexOf(itemCoupon), 1);
            }
          }
        }
      },
      // 获取缴费科目
      getParInfos: function () {
        let parInfos = [];
        // 深拷贝对象
        for (let items of this.selectedItem) {
          for (let item of items) {
            let {...newItem} = item
            parInfos.push(newItem);
          }
        }
        // 排序，code为Y的排在最前面
        /*parInfos.sort((a, b) => {
          let val1 = a.itemCode.indexOf('Y') === 0 ? Number(a.itemCode.substr(1)) : 10;
          let val2 = b.itemCode.indexOf('Y') === 0 ? Number(b.itemCode.substr(1)) : 10;
          return val1 - val2;
        });*/
        return parInfos.filter((item) => item.itemCode?.indexOf('Y') === 0).concat(parInfos.filter((item) => item.itemCode?.indexOf('Y') !== 0));
      },
      // 记录优惠券使用详细
      setCouponsDetail: function (couponId ,scId, canAnother, canSame, itemCode, price,amount,itemCodes) {
        let couponDetail = this.couponsDetail.find(item => item.scId === scId);
        let details = {itemCode: itemCode, price: Number(price).toFixed(2)};
        if (couponDetail) {
          couponDetail.couponDetails.push(details);
        } else {
          this.couponsDetail.push({couponId: couponId, scId: scId, canAnother:canAnother, canSame:canSame, amount:amount,itemCodes:itemCodes,couponDetails: [details]});
        }
      },
      submit: function () {
        this.selectedCoupons = [...this.selected];
        this.setModalVisible(false);
      }
    },
    watch: {
      couponList: function () {
        this.getCoupon();
      },
      itemCodes: function () {
        this.getCoupon();
      },
      title: function () {
        let info = {
          coupons: this.couponsDetail,
          title: this.title,
          deduction: this.deduction
        }
        this.$emit('input', info);
      },
      selectedCoupons: function () {
        this.setDeduction();
      }
    },
    components: {footBar}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .coupons-wrap{position:fixed;top:0;right:0;bottom:0;left:0;z-index:2001;padding-top:1rem;background-color:rgba(0, 0, 0, .5)}
  .coupons-inner{position:relative;height:100%;background-color:#fff;}
  .coupons-hd{
    > .cl{height:.44rem;line-height:.44rem;}
    .fl{margin-left:.12rem;font-size:.17rem;}
    .btn-ps{color:#808080;font-size:.12rem;}
  }
  .coupons-bd{
    position:absolute;top:.88rem;right:0;bottom:0;left:0;padding-bottom:.2rem;overflow-y:auto;background-color:@backgroundColor;
    &::-webkit-scrollbar{width:6px;}
    &::-webkit-scrollbar-thumb{border-radius:3px;background:#c1c1c1;}
  }
  .coupon-item{
    min-height:.87rem;
    margin:.08rem .1rem 0;
    position: relative;
    height: auto;
    overflow: hidden;
    .lay-icon{
      vertical-align:middle;
      position: absolute;
      top:.28rem;
      right:.1rem;
      z-index: 99;
    }
    .amount{
      width: .85rem;
      height: .87rem;
      text-align: center;
      padding: 0;
      float: left;
      border-radius: .1rem 0 0 0;
      border-right: 1px dashed #F0F0F0;
      background-image: linear-gradient(to left top,#F09190,#F06E6C);
       vertical-align:middle; color:white;
      .i{ font-size:.16rem; font-weight:500;word-break: break-all;}
      .price{ font-size:.26rem; font-weight:500;line-height: .87rem }
    }
    .cont{
      // float:left;
      margin-left: 0.87rem;
      height: .87rem;
      background-color: #fff;
      padding-top: .1rem;
      border-left: 1px dotted #f5f6f8;
      position: relative;
      p {
        color: #999;
        font-size: .12rem;
        word-break: break-all;
        /*width: 2.1rem;*/
        overflow: hidden;
        // white-space:nowrap;
        // text-overflow: ellipsis;
        padding-left: .12rem;
        padding-right: .1rem;
        // width: 1.98rem;
        height: .4rem;
        line-height: .2rem;
        &:first-child {
          color:rgba(23, 6, 6, .8);
          font-weight: bold;
          font-size: .14rem;
          padding-left: .1rem;
        }
        &:nth-of-type(2) {
          height: .19rem;
          padding-left: .1rem;
          color: rgba(23, 6, 6, .8);
          margin-top: .08rem;
        }
      }
      .status{
        width: .85rem;
        height: .81rem;
        position: absolute;
        top:.1rem;
        right: 0;
      }
    }
    .desc{
      float: left;
      width: 100%;
      height: auto;
      overflow: hidden;
      background-color: #fff;
      .des {
        position: relative;
        height: .32rem;
        line-height: .32rem;
        padding-left: .1rem;
        color: rgba(23, 6, 6, .8);
        font-weight: bold;
        font-size: .12rem;
        img {
          float: right;
          width: .32rem;
          height: .32rem;
          margin-right: .09rem;
        }
      }
    }
    .remark {
      background: #fff;
      color: rgba(23, 6, 6, .6);
      font-size: .12rem;
      padding: 0 .1rem .1rem .1rem;
    }

    .i-selected{ width:.32rem; height:.32rem;  background-image:url(../assets/image/ic_select.png); }
    &.active{
      .i-selected{ background-image:url(../assets/image/ic_select_sel.png); }
    }
    &.disabled{
      .amount{background-image: none;
        background-color: #B9B4B4;}
      .cont{
        color:rgba(23, 6, 6, .3);
        p{color:rgba(23, 6, 6, .3);}
      }
      .desc{
        .des{color:rgba(23, 6, 6, .3);}
      }
      .remark{
        color:rgba(23, 6, 6, .3);
      }
    }
    .gap{ padding:.03rem 0 .15rem; }
    .fsc1{ color:#999; font-size:.12rem; }
  }
  .i-del{width:.44rem;height:.44rem;background-image:url(../assets/image/<EMAIL>);}
  .instructions{
    position:absolute;top:0;right:0;bottom:0;left:0;z-index:2002;background-color:rgba(0,0,0,.5);
    .hd{line-height:.44rem;text-align:center;font-size:.17rem;}
    .bd{padding:0 .12rem;line-height:1.8;color:#808080;font-size:.13rem;}
    .ft{
      position:absolute;right:0;bottom:0;left:0;line-height:.44rem;text-align:center;color:@color;font-size:.15rem;
      &:after{.borderTop}
    }
  }
  .instructions-inner{position:absolute;top:50%;left:.16rem;right:.16rem;height:2.5rem;background-color:#fff;border-radius:.04rem;transform:translateY(-50%);}
  .no-data{
    width: 1.7rem;
    padding-top: 1.7rem;
    margin: .2rem auto;
    background-image: url("../assets/image/nothing.png");
    background-repeat: no-repeat;
    background-size: 100%;
    text-align: center;
    color: rgba(23,6,6,.6);
  }
</style>
