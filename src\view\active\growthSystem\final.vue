<template>
  <div class="yz-growth-final" @touchstart='touchstart' @touchmove='touchmove' @touchend='touchend'>
    <div class="final-head" ref='head'>
      <div class="left">
        <img :src="headImg |defaultAvatar" ref='imghe' class="head" alt="">
        <p class="name">{{name}}</p>
      </div>
      <div class="right">
        <div class="before"></div>
        <p class="p1">共完成<span class="yellow">{{selects.length}}</span>件上进事件</p>
        <p class="p2">成为了备受瞩目的</p>
        <p class="p3">“<span class="yellow">{{text}}</span>”</p>
      </div>
    </div>

    <div class="img-list">
      <div class="title-box" ref='title'>
        <img src="../../../assets/image/active/growthSystem/title.png" alt="">
      </div>
      <div class="list small" :style='smallListStyle'>
        <div class="box1" ref='box1'>
          <img :src="cutImg" alt="" class="cut-img2" ref='cutImg2'>
          <img class="img" :class='{mr: (index + 1) % 3 == 0}' :src="item | getImgByIndex" alt="" v-for="(item, index) in selects" :key="index">
        </div>
      </div>
      <!-- <div class="code-box">
        <div class="code-border">
          <div id="code" class="code"></div>
        </div>
        <p class="p1">扫一扫，和我一起开启上进之旅</p>
      </div> -->
    </div>

    <div class="fix-yz" ref='bottom'>
      <img src="../../../assets/image/active/growthSystem/sj.png" alt="">
      <p class="p1">下载远智App，和60w+上<br />进青年一起成为更好的自<br />己！</p>
    </div>
    <img src="../../../assets/image/active/growthSystem/down-btn.png" class="down-fix" @click='download' alt="">
    <div class="long-text" ref='longText'>
      <img src="../../../assets/image/active/growthSystem/finger.png" alt="">
      <span class="text">长按此处文案可保存图片</span>
    </div>
    <!-- 裁剪层 -->
    <div class="cut-box" id="cut">
      <div class="final-head">
        <div class="left">
          <img :src="headSrc |defaultAvatar" class="head" crossOrigin="anonymous" alt="">
          <p class="name">{{name}}</p>
        </div>
        <div class="right">
          <div class="before"></div>
          <p class="p1">共完成<span class="yellow">{{selects.length}}</span>件上进事件</p>
          <p class="p2">成为了备受瞩目的</p>
          <p class="p3">“<span class="yellow">{{text}}</span>”</p>
        </div>
      </div>
      <div class="img-list">
        <div class="title-box">
          <img src="../../../assets/image/active/growthSystem/title.png" alt="">
        </div>
        <div class="list">
          <img :class='{mr: (index + 1) % 3 == 0}' :src="item | getImgByIndex" alt="" v-for="(item, index) in selects" :key="index">
        </div>
        <div class="code-box">
          <div class="code-border">
            <div ref="code" class="code"></div>
          </div>
          <p class="p1">扫一扫，和我一起开启上进之旅</p>
        </div>
      </div>
    </div>
    <!-- 生成的图片 -->
    <img :class='{abs: imgZindex}' :src="cutImg" alt="" class="cut-img" ref='cutImg' >
  </div>
</template>

<script>
import html2canvas from 'html2canvas';
import QrCode from 'qrcodejs2';
import { downloadApp, isAndroid } from '@/common';

export default {
  props: {
    name: {
      type: String,
      default: '',
    },
    selects: {
      type: Array,
      default: () => [],
    },
    show: {
      type: Boolean,
      default: false,
    },
    headImg: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      cutImg: '',
      imgZindex: false,
      smallListStyle: {},
      timeOutEvent: null,
      headSrc: '',
    };
  },
  computed: {
    text() {
      const num = this.selects.length;
      if (num <= 10) {
        return '上进新星';
      } else if (num > 10 && num <= 20) {
        return '上进同学';
      } else if (num > 20 && num <= 30) {
        return '上进青年';
      } else if (num > 30) {
        return '上进达人';
      }
    },
  },
  watch: {
    show(val) {
      if (val) {
        this.$nextTick(() => {
          this.setHeight();
          this.makeCode();
          this.startTrans(this.headImg, b => {
            this.headSrc = b;
            this.$nextTick(() => {
              this.makeImg();
            });
          });
        });
      }
    },
  },
  filters: {
    getImgByIndex(val) {
      return require(`../../../assets/image/active/growthSystem/icon2/${val}.png`);
    },
  },
  mounted() {
  },
  methods: {
    touchstart() {
      this.timeOutEvent = setTimeout(() => {
        this.androidClick();
      }, 500);
    },
    touchmove() {
      clearTimeout(this.timeOutEvent);
    },
    touchend() {
      clearTimeout(this.timeOutEvent);
    },
    startTrans(src, cb) {
        var image = new Image();
        image.crossOrigin = "";  // 支持跨域图片
        image.setAttribute("crossOrigin",'Anonymous');//跨域在前
        image.src = src + '?v=' + Math.random(); // 处理缓存
        image.onload = ()=>{
            var base64 = this.getBase64Image(image);
            cb && cb(base64);
        }
    },
    getBase64Image(img) {
        var canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        var ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, img.width, img.height);
        var dataURL = canvas.toDataURL("image/png");  // 可选其他值 image/jpeg
        return dataURL;
    },
    androidClick() {
      if (isAndroid()) {
        if (window.android && window.android.nativeMethodExecute) {
          var resultJson = window.android.nativeMethodExecute(JSON.stringify({methodName:"isAppOpen",params:{}}));
          const isAppOpen = (JSON.parse(resultJson).appOpen);
          if (isAppOpen) {
            const imgdata = this.cutImg.split(",")[1];
            window.android.nativeMethodExecute(JSON.stringify({methodName:"receiptImage",params:{ imgdata: imgdata }}));
          }
        }
      }
    },
    makeImg() {
      const box = document.getElementById('cut');
      html2canvas(box, {
        useCORS: true,
        // allowTaint: true,
      }).then((canvas) =>{
        this.cutImg = canvas.toDataURL();
        this.$nextTick(() => {
          this.imgZindex = true;
        });
      });
    },
    makeCode() {
      const url = `${window.location.origin}/growthSystem`;
      const qrCode = new QrCode(this.$refs.code, {
        width: 70,
        height: 70,
        text: url,
      });
    },
    download() {
      downloadApp();
    },
    setHeight() {
      const windowHeight = window.innerHeight;
      const headHeight = this.$refs.head.offsetHeight;
      const bottomHeight = this.$refs.bottom.offsetHeight;
      const titleHeight = this.$refs.title.offsetHeight;
      const longTextHeight = this.$refs.longText.offsetHeight;
      const height = windowHeight - headHeight - bottomHeight - titleHeight - longTextHeight - 18;
      this.smallListStyle = {
        height: `${height}px`,
        // 'min-height': `${height + 2}px`,
      };
    },
  }
};
</script>

<style lang="less">
  .yz-growth-final{
    height: 100vh;
    background: url(../../../assets/image/active/growthSystem/bg2.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    padding: 0 0.1rem;
    overflow: hidden;
    width: 100%;
    .yellow{
      color: #FFAE18;
    }
    .cut-box{
      position: fixed;
      background: url(../../../assets/image/active/growthSystem/bg2.png) no-repeat;
      background-size: 100% 100%;
      z-index: -1;
      width: 100%;
      top: -999px;
      left: -999px;
      padding: 0 0.1rem;
      padding-bottom: 0.11rem;
      min-height: 100vh;
      // opacity: 1;
    }
    .cut-img{
      width: 100%;
      height: 100%;
      position: relative;
      opacity: 0;
      &.abs{
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        opacity: 0;
      }
    }

    .final-head{
      background: #FFF2E4;
      border: 0.02rem solid #000;
      border-top: 0;
      border-radius: 0 0 0.24rem 0.24rem;
      height: 1.74rem;
      padding-top: 0.23rem;
      padding-left: 0.14rem;
      text-align: center;
      .left{
        float: left;
        padding-top: 22px;
        width: 40px;
        .head{
          border: 0.02rem solid #000;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
        .name{
          margin-top: 0.05rem;
          font-size: 0.12rem;
          color: #000;
          font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif;;
        }
      }
      .right{
        width: 2.53rem;
        height: 1.37rem;
        margin-left: 70px;
        padding-top: 0.24rem;
        font-size: 0.14rem;
        background: url(../../../assets/image/active/growthSystem/box.png) no-repeat;
        background-size: 100% 100%;
        position: relative;
        .before{
          position: absolute;
          left: -0.18rem;
          top: 0.33rem;
          width: 0;
          height: 0;
          z-index: 2;
          border-top: 0.08rem solid transparent;
          border-bottom: 0.08rem solid transparent;
          border-right: 0.22rem solid #000;
        }
        p{
          margin-left: -0.1rem;
        }
        .p1{
          margin-bottom: 0.03rem;
          margin-top: 0.07rem;
        }
        .p3{
          font-size: 0.29rem;
          margin-top: 0.05rem;
        }
      }
    }
    .img-list{
      background: #FFF2E4;
      border: 0.02rem solid #000;
      border-radius: 0.24rem;
      margin-top: 0.12rem;
      .title-box{
        text-align: center;
        img{
          width: 1.92rem;
          height: 0.68rem;
        }
      }
      .list{
        margin-top: 0.08rem;
        padding: 0 0.25rem;
        position: relative;
        display: flex;
        flex-wrap: wrap;
        &.small{
          height: 3.21rem;
          margin-bottom: 1.7rem;
          overflow: auto;
          z-index: 20;
          display: block;
          flex-wrap: none;
          -webkit-overflow-scrolling: touch;
          .box1{
            display: flex;
            flex-wrap: wrap;
            position: relative;
          }
          .img{
            position: relative;
            z-index: 2;
            pointer-events: none;
          }
        }

        img{
          width: 0.84rem;
          height: 1.03rem;
          // float: left;
          margin-bottom: 0.18rem;
          margin-right: 0.24rem;
          &.mr{
            margin-right: 0;
          }
          // &:nth-child(3n) {
          //   margin-right: 0;
          // }
          &.cut-img2{
            width: 100%;
            height: 100%;
            position: absolute;
            opacity: 0;
            left: 0;
            top: 0;
            z-index: 1;
          }
        }

      }
      .code-box{
        margin-top: 0.18rem;
        padding-bottom: 0.18rem;
        .code-border{
          width: 88px;
          height: 88px;
          border: 4px solid #000000;
          margin: 0 auto;
          background: #fff;
          padding-top: 5px;
          padding-left: 5px;
        }
        .p1{
          font-size: 0.12rem;
          text-align: center;
          margin-top: 0.12rem;
          color: #000000;
        }
      }
    }
    .fix-yz{
      position: fixed;
      left: 0.24rem;
      right: 0.24rem;
      bottom: 0;
      background: #FFF2E4;
      padding-bottom: 0.07rem;
      font-size: 0.1rem;
      line-height: 0.14rem;
      img{
        width: 0.42rem;
        height: 0.42rem;
        float: left;
      }
      p{
        // padding-top: 0.06rem;
        color: #333333;
        padding-left: 0.14rem;
        float: left;
        font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif;;
      }
    }
    .down-fix{
      position: fixed;
      height: 0.38rem;
      width: 1.15rem;
      bottom: 0.08rem;
      right: 0.27rem;
      z-index: 11;
    }
    .long-text{
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 0.14rem;
      color: #FF6A5F;
      text-decoration: underline;
      bottom: 0.49rem;
      left: 0.24rem;
      right: 0.24rem;
      background: #FFF2E4;
      // padding: 0.11rem 0 0.13rem;
      height: 44px;
      letter-spacing: 0.02rem;
      img{
        width: 0.12rem;
        height: 0.17rem;
        margin-right: 0.05rem;
      }
    }
  }
</style>
