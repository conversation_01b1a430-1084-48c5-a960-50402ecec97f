// 封装一些跳转的公共方法
import router from '@/router';
import bridge from '@/plugins/bridge';

// 去app的home页面 默认是我的页面
export const toAppHome = (tab = 4) => {
  window.location.href =`yuanzhiapp://yzwill.cn/Home?params={"tab": ${tab}}`;
}

/**
 * 去app的缴费页面
 * @param {String} learnId 学籍id
 * @param {Object} ofterQuery 其他参数
 */
export const toAppPay = (learnId, ofterQuery = {}) => {
  const params = { learnId, ...ofterQuery };
  window.location.href =`yuanzhiapp://yzwill.cn/Enroll/Payment?params=${JSON.stringify(params)}`;
};

/**
 * 包含app和公众号打开支付页的方法
 * @param {Boolean} isAppOpen 是否是app打开
 * @param {String} learnId 学籍id
 * @param {Object} ofterQuery 其他参数
 */
export const toPayWithApp = (isAppOpen, learnId, ofterQuery = {}) => {
  if (!isAppOpen) {
    router.push({
      name: 'stuPayment',
      query: {
        learnId,
        ...ofterQuery,
      }
    });
    return;
  }
  toAppPay(learnId, ofterQuery);
};

/**
 *  打开app登录
 * @param {Boolean} isNeedConfirm 是否需要确认弹窗
 */
export const toAppLogin = (isNeedConfirm = false) => {
  const name = isNeedConfirm ? 'openLoginPage' : 'openAppLogin';
  bridge.callHandler(name).then(() => {}).catch(() => {});
}

/**
 * 去app的报读页面
 * @param {Object} params 参数
 */
export const toAppEnroll = (params) => {
  const { actName, grade, scholarship, unvs, recruitType } = params;
  const uri = 'yuanzhiapp://yzwill.cn/Enroll/InfoInput';
  const appParams = {
    activeName: actName,
    grade,
    scholarship,
    recruitType,
    schoolId: unvs ? unvs.unvsId : '',
    schoolName: unvs ? unvs.unvsName : '',
  };
  if (unvs) {
    appParams.schoolId = unvs.unvsId;
    appParams.schoolName = unvs.unvsName;
  }
  window.location.href = `${uri}?params=${JSON.stringify(appParams)}`;
}

/**
 * 公共的跳转报读页面方法，含app打开判断
 * @param {Object} params 跳转携带参数
 * @param {Boolean} isAppOpen 是否是app打开
 */
export const toEnrollWithApp = function (params, isAppOpen = false) {
  const { actName, scholarship, unvs, recruitType, activityName, inviteId } = params;
  if (isAppOpen) {
    toAppEnroll(params);
    return;
  }
  const query = {
    activityName: activityName || "scholarship",
    action: "login",
    inviteId,
    scholarship,
    actName,
    recruitType,
  };
  if (unvs) {
    query.unvs = JSON.stringify(unvs);
  }
  router.push({
    name: "adultExamEnrollCheck",
    query,
  });
};
