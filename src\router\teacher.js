export default [
  {
    path: '/search/searchTeacher',
    name: 'teacherSearch',
    component: () => import('../view/search/searchTeacher'),
    meta: {title: '教师查询',loadDict:true}
  },
  {
    path: '/search/qywxQrcode',
    name: 'searchTeacherQrcode',
    component: () => import('../view/search/searchTeacherQrcode'),
    meta: {title: '远智智米中心'}
  },
  {
    path:'/teacherKit/home',
    name:'teacherKit',
    component: () => import("../view/teacherKit/home"),
    meta:{title:'文章推荐工具',requiresAuth:true,}
  },
  {
    path:'/teacherKit/personal',
    name:'teacherSetting',
    component: () => import("../view/teacherKit/personalCenter"),
    meta:{title:'文章推荐工具',requiresAuth: true,}
  },
  {
    path:'/teacherKit/personalDetail',
    name:'teacherDetail',
    component: () => import("../view/teacherKit/personalDetail"),
    meta:{title:'文章推荐工具',}
  },
  {
    path:'/teacherKit/article',
    name:'article',
    component: () => import("../view/teacherKit/article"),
    meta:{}
  },
  {
    path: '/teacherKit/edit',
    name: 'nameEdit1',
    component: () => import('../view/teacherKit/nameEdit'),
    meta: {requiresAuth: true,}
  }, {
    path: '/teacherKit/operation',
    name: 'operation',
    component: () => import('../view/teacherKit/operation'),
    meta: {requiresAuth: true}
  },{
    path: '/settings/sendStamps/gkPay',
    name: 'gkPay',
    component: () => import('../view/settings/sendStamps/gkPay'),
    meta: {title:'国开学费特殊申请',requiresAuth: true}
  },{
    path: '/settings/sendStamps/Educational',
    name: 'Educational',
    component: () => import('../view/settings/sendStamps/Educational'),
    meta: {requiresAuth: true,title:'助学工具'}
  },{
    path: '/settings/sendStamps/UrbanConstructionInvite',
    name: 'UrbanConstructionInvite',
    component: () => import('../view/settings/sendStamps/UrbanConstructionInvite'),
    meta: {title:'奖学金活动'}
  },{
    path: '/settings/sendStamps/UrbanConstructionEnrollment',
    name: 'UrbanConstructionEnrollment',
    component: () => import('../view/settings/sendStamps/UrbanConstructionEnrollment'),
    meta: {title:'奖学金活动'}
  }
];
