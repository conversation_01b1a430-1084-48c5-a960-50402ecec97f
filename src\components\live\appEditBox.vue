<template>
  <form action="" @submit.prevent="submit">
    <div class="edit-box">
      <div class="cl">
        <i id="btnEmotion" class="fl icons btn-emotion" @click="$refs.emotion.toggleBox()"></i>
        <i class="fl icons imgSelect">

          <webuploader @upload="uploadImg" picker="filePicker4" type="contentImg1"></webuploader>
        </i>
        <button class="fr btn" @click="submit" :class="{active:change}">{{btnText}}</button>
      </div>
      <div class="ipt-wrap">
        <textarea ref='edit' :placeholder="inputPlaceholder" v-model.trim="content" @focus="inputFocus"
                  v-if="scroll"></textarea>
        <textarea ref='edit' :placeholder="inputPlaceholder" v-model.trim="content" v-else></textarea>
      </div>

      <div class="imgUpload">
        <div class="file-box" v-if="contentImg1">
          <img class="img" :src="contentImg1|basePath" v-if="contentImg1"
               @click="showImgFn(contentImg1,'contentImg1')" />
          <webuploader v-else @upload="uploadImg" picker="filePicker1" type="contentImg1"></webuploader>
          <div class="btn-upload" v-if="!contentImg1">
            <img src="../../assets/image/app/addImg.png" alt="">
          </div>
        </div>
        <div class="file-box" v-if="contentImg1">
          <img class="img" :src="contentImg2|basePath" v-if="contentImg2"
               @click="showImgFn(contentImg2,'contentImg2')" />
          <webuploader v-else @upload="uploadImg" picker="filePicker2" type="contentImg2"></webuploader>
          <div class="btn-upload" v-if="!contentImg2">
            <img src="../../assets/image/app/addImg.png" alt="">
          </div>
        </div>
        <div class="file-box" v-if="contentImg2">
          <img class="img" :src="contentImg3|basePath" v-if="contentImg3"
               @click="showImgFn(contentImg3,'contentImg3')" />
          <webuploader v-else @upload="uploadImg" picker="filePicker3" type="contentImg3"></webuploader>
          <div class="btn-upload" v-if="!contentImg3">
            <img src="../../assets/image/app/addImg.png" alt="">
          </div>
        </div>
      </div>

      <emotion-list v-model="content" ref="emotion" />
    </div>
    <img-popup ref="imgpop" v-if="showImg" :imgSrc="showImgSrc.src" v-on:changeImg="changeImg"></img-popup>
  </form>
</template>

<script>
import emotionList from '@/components/emotionList';
import webuploader from "@/components/uploaderTwo"
import config from '../../config'
import imgPopup from '@/components/imgPopupChange';

export default {
  props: {
    isSocketConnect: {
      type: Boolean,
      default: false
    },
    inputPlaceholder: {
      type: String,
      default: '说上两句吧...'
    },
    btnText: {
      type: String,
      default: '提交'
    },
    change: {
      type: Boolean,
      default: false
    },
    scroll: {
      type: Boolean,
      default: true
    },
    locatStorageTime: {
      type: String,
      default: ''
    }
  },
  filters: {
    basePath: function (url) {
      if (!url) return url;
      if (url.indexOf('std/') != '-1') {
        return config.imgBaseURL + url
      } else {
        return config.imgBasePath + url;
      }
    }
  },
  data () {
    return {
      content: '',
      contentImg1: '',
      contentImg2: '',
      contentImg3: '',
      showImg: false,
      showImgSrc: {
        src: '',
        type: ''
      },
      isImgUpload: false
    }
  },
  methods: {
    changeStatus () {
      this.isImgUpload = this.isImgUpload ? false : true;
    },
    showImgFn (imgSrc, type) {
      if (!imgSrc) {
        return
      }
      this.showImg = true;
      this.showImgSrc.src = imgSrc;
      this.showImgSrc.type = type;
      this.$nextTick(() => {
        this.$refs.imgpop.fileType = type;
        this.$refs.imgpop.imgSrcs = imgSrc;
        this.$refs.imgpop.open();
      })
    },
    changeImg (fileType, src) {
      this[fileType] = src;
    },
    uploadImg (body, type) {
      switch (type) {
        case 'contentImg1': this.contentImg1 = body;
          break;
        case 'contentImg2': this.contentImg2 = body;
          break;
        case 'contentImg3': this.contentImg3 = body;
          break;
        default: break;
      }
    },

    formatEmotions: function (content) {
      return this.$refs.emotion.formatEmotions(content);
    },
    submit: function () {
      let content = this.content;

      let picUrlList = ''

      if (this.contentImg1 && this.contentImg2 && this.contentImg3) {
        picUrlList = `${this.contentImg1},${this.contentImg2},${this.contentImg3}`
      } else if (this.contentImg1 && this.contentImg2) {
        picUrlList = `${this.contentImg1},${this.contentImg2}`
      } else {
        picUrlList = this.contentImg1;
      }
      if (!picUrlList) {
        if (!content) return;
      }

      if (!this.isSocketConnect) {
        this.$modal({ message: '请稍等，连接中…', icon: 'warning' });
        return;
      }
      this.$emit('submit', content, picUrlList);
      this.content = '';
      this.contentImg1 = '';
      this.contentImg2 = '';
      this.contentImg3 = '';

    },
    inputFocus: function () {
      setTimeout(function () {
        window.scrollTo(0, document.getElementsByTagName('body')[0].offsetHeight);
      }, 600);
    },
    editFocus () {
      this.$refs.edit.focus();
    },
    updateSetLocatContent (type, val) {
      let circlecomment = this.storage.getItem('circlecomment') ? JSON.parse(this.storage.getItem('circlecomment')) : {}
      circlecomment[type] = val
      let daysLater = new Date().getTime() + (12 * 60 * 60 * 1000); //12小时后
      circlecomment.daysLater = daysLater
      this.storage.setItem('circlecomment', JSON.stringify(circlecomment));
    }
  },
  components: { emotionList, webuploader, imgPopup },
  watch: {
    content (newval, oldval) {
      if (this.locatStorageTime === '12') {
        this.updateSetLocatContent('content', newval)

      }
    },
    contentImg1 (newval, oldval) {
      if (this.locatStorageTime === '12') {
        this.updateSetLocatContent('contentImg1', newval)

      }
    },
    contentImg2 (newval, oldval) {
      if (this.locatStorageTime === '12') {
        this.updateSetLocatContent('contentImg2', newval)

      }

    },
    contentImg3 (newval, oldval) {
      if (this.locatStorageTime === '12') {
        this.updateSetLocatContent('contentImg3', newval)

      }

    }
  },
  created () {
    if (this.locatStorageTime === '12') {
      let circlecomment = this.storage.getItem('circlecomment') ? JSON.parse(this.storage.getItem('circlecomment')) : {}
      console.log(circlecomment);
      if (circlecomment && circlecomment.daysLater && circlecomment.daysLater < new Date().getTime()) {
        circlecomment = {}
        this.storage.setItem('circlecomment', JSON.stringify(circlecomment));

      }
      if (circlecomment && circlecomment.daysLater && circlecomment.daysLater > new Date().getTime()) {
        this.content = circlecomment.content ? circlecomment.content : ''
        this.contentImg1 = circlecomment.contentImg1 ? circlecomment.contentImg1 : ''
        this.contentImg2 = circlecomment.contentImg2 ? circlecomment.contentImg2 : ''
        this.contentImg3 = circlecomment.contentImg3 ? circlecomment.contentImg3 : ''
      }
    }

  },
}
</script>

<style lang="less" scoped>
@import '../../assets/less/variable';

.edit-box {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  > .cl {
    max-width: @maxWidth;
    margin: 0 auto;
    padding: 0.07rem 0.15rem;
    background-color: #fff;
  }
  .btn {
    line-height: 0.36rem;
    border: none;
    background: linear-gradient(
      135deg,
      rgba(240, 145, 144, 1) 0%,
      rgba(240, 120, 119, 1) 66%,
      rgba(240, 110, 108, 1) 100%
    );
    color: @color;
    font-size: 0.14rem;
    border-radius: 0.05rem;
    &.active {
      background-color: #e85b57;
      color: white;
      width: 0.5rem;
      height: 0.3rem;
      line-height: 0.3rem;
      text-align: center;
    }
  }
  .ipt-wrap {
    position: relative;
    padding: 0 0.01rem;
    background-color: white;
    text-align: center;
    padding-bottom: 0.1rem;
    // input{width:90%;height:.3rem;padding:0.08rem 0.1rem 0.08rem 0.11rem;line-height:.2rem;font-size:.14rem;color:#333;border:none;background-color:#f7f7f7;border-radius:.04rem;}
    textarea {
      width: 95%;
      height: 0.6rem;
      padding: 0.08rem 0.1rem 0.08rem 0.11rem;
      line-height: 0.2rem;
      font-size: 0.14rem;
      color: #333;
      border: none;
      background-color: #f7f7f7;
      border-radius: 0.04rem;
    }
  }
  .imgUpload {
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    padding-bottom: 0.1rem;
    .file-box {
      float: left;
      width: 0.75rem;
      height: 0.75rem;
      position: relative;
      margin-left: 0.1rem;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .btn-upload {
        height: 100%;
        text-align: center;
        background-color: #f6f6f6;
        border: 1px solid #f2f2f2;
        border-radius: 0.02rem;
        img {
          width: 0.3rem;
          height: 0.3rem;
          margin-top: 33%;
        }
      }
    }
  }
}
.btn-emotion {
  width: 0.32rem;
  height: 0.32rem;
  background-image: url(../../assets/image/student/ic_course_face.png);
}
.imgSelect {
  width: 0.32rem;
  height: 0.32rem;
  background-image: url(../../assets/image/student/<EMAIL>);
  margin-left: 0.05rem;
  overflow: hidden;
  position: relative;
}
.i-quiz {
  position: absolute;
  top: 0.08rem;
  left: 0.1rem;
  width: 0.16rem;
  height: 0.17rem;
  vertical-align: middle;
  background-image: url(../../assets/image/live_ico_quiz.png);
}
</style>
