// import router from '@/router';
import { examUrl } from '@/config';
import crypto from "@/plugins/secret";

/**
 * 跳转到练题库首页
 * @param practiceQuestionBankId
 * @param subId 科目id
 */
export const toExam = (practiceQuestionBankId, subId, bankTitle) => {
  let yzCode = JSON.parse(localStorage.getItem('yzCode')); // 要转义成单引号，否则会有 \转义符
  let url = examUrl + "/autoLogin?freeTag=" + crypto.encrypt(yzCode) + "&type=siteExam&threeTopicBankId=" + practiceQuestionBankId + "&subId=" + subId + '&topicBankType=1&channelType=sanJin&bankTitle='+ bankTitle;
  window.location.href = url;

}
