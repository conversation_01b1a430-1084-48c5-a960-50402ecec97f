.banner {
  position: relative;
  height: 1.45rem;
  background-color: #fff ;
}
.banner .banner-inner {
  display: block;
  height: 100%;
}
.banner .swipe-wrap {
  z-index: 1;
}
.banner .ps {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  line-height: .26rem;
  text-align: center;
  color: #fff;
  font-size: .11rem;
  background-color: rgba(0, 0, 0, 0.3);
}
.banner .img {
  object-fit: contain;
}
.product-detail .h1 {
  line-height: .18rem;
  font-size: .14rem;
}
.product-detail .tit {
  padding: .12rem;
}
.product-detail .tit .inner {
  position: relative;
  padding-left: .5rem;
}
.product-detail .tit .subtitle {
  word-break: break-all;
  color: #888;
  font-size: .12rem;
}
.product-detail .info {
  padding: .12rem;
  font-size: .13rem;
  color: #666;
}
.product-detail .progress-bar {
  margin-bottom: .04rem;
}
.product-detail .address {
  position: relative;
  padding: 0 .12rem;
  line-height: .44rem;
  font-size: .13rem;
  color: #666;
}
.product-detail .address:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 1px;
  background-color: #e4e4e4;
  transform: scale(0.5);
  transform-origin: top left;
  content: '';
}
.product-detail .i-arr-r {
  width: .07rem;
  height: .13rem;
  margin: .13rem 0 0 .1rem;
  background-image: url(../image/arrow-r.png);
}
.record-list .item {
  position: relative;
  margin: 0 .12rem;
  padding: .12rem 0;
}
.record-list .item:after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 1px;
  background-color: #e4e4e4;
  transform: scale(0.5);
  transform-origin: bottom left;
  content: '';
}
.record-list .item:last-child {
  border-bottom: none;
}
.record-list .name {
  padding-bottom: .04rem;
  color: #666;
  font-size: .12rem;
}
.record-list .time {
  font-size: .11rem;
}
.detail-ft .lf {
  line-height: .5rem;
}
.detail-ft .amount-box {
  margin: .11rem .12rem 0 0;
  font-size: 0;
}
.detail-ft .btn-minus,
.detail-ft .btn-plus {
  width: .27rem;
  height: .27rem;
  vertical-align: middle;
  border: none;
  background-color: transparent;
  background-size: 100% 100%;
}
.detail-ft .btn-minus {
  background-image: url(../image/btn-minus.png);
}
.detail-ft .btn-plus {
  background-image: url(../image/btn-plus.png);
}
.detail-ft .input1 {
  width: .48rem;
  height: .27rem;
  vertical-align: middle;
  text-align: center;
  border: none;
  color: #e85b57;
  font-size: .16rem;
}
.link {
  color: #5eb1d3;
  font-size: .12rem;
}
.fs1 {
  font-size: .1rem;
}
.fc1 {
  color: #999;
}
.fc2 {
  color: #e85b57;
}
.fc3 {
  color: #2A2A2A;
}
.fsc1 {
  color: #888;
  font-size: .11rem;
}
.fsc2 {
  color: #666;
  font-size: .12rem;
}
.fsc3 {
  color: #e85b57;
  font-size: .18rem;
  font-weight: bold;
}
