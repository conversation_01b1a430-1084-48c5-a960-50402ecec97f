<template>
  <div v-show="pageShow">
    <div v-if="list.length<=0" class="empty">
      <img src="../../../../assets/image/aspirantUniversity/zore.png" alt="" class="empty-bg">
      <div class="empty-tip">精彩的上课心得总是需要慢慢打磨的</div>
    </div>

    <van-list
      v-else
      class="comment-list"
      v-model="loading"
      :finished="finished"
      finished-text="- 上进是一种动力，要坚持每天学习哦 -"
      @load="onLoad"
    >
      <div class="item" v-for="(item,index) in list" :key="index">

        <div class="comment">
          <div class="head">
            <div class="user-avatar">
              <img :src="item.commentUserHeadImg | defaultAvatar" alt="" width="100%" height="100%">
            </div>
            <div class="user-info">
              <div class="user-name">
                {{ item.commentUserName }}
                <img
                  v-if="item.recommend === '2'"
                  class="featured"
                  src="../../../../assets/image/aspirantUniversity/icon-featured.png"
                >
              </div>
              <div class="release-time">{{ item.commentTime }}</div>
            </div>
          </div>
          <div class="comment-main">
            <div class="comment-content">
              {{item.commentContent}}
              <button @click="openOrShrink(item)" class="more" v-if="item.isFold">
                {{ item.open ? '收起' : '展开'}}
                <van-icon :name=" item.open ? 'arrow-up' : 'arrow-down' " /></button>
            </div>
            <div class="imgs" v-if="item.imgs.length > 0">
              <van-image
                v-for="(url,index) in item.imgs"
                :key="index"
                @click="preview(item.imgs, index)"
                width="1.1rem"
                height="1.1rem"
                fit="cover"
                :src="url + ossImgSize"
              />
            </div>
            <!-- 回复列表 -->
            <div class="sub-comment-list" v-for="subComment in item.replyVOS ">
              <span class="sub-comment-user-name">
                {{ subComment.userLevel == '1' ? '社区管理员: ': subComment.replyUserName }}
              </span>
              <span class="sub-comment-content">
                {{ subComment.replyContent}}
              </span>
            </div>
          </div>
        </div>
        <van-divider :style="{borderColor: '#EDEBEB', paddingTop: '0.1rem',marginTop:'0.1rem' }" />
      </div>
    </van-list>
  </div>
</template>

<script>
import config from "@/config";
import { ImagePreview } from 'vant';
export default {
  props: {
    mappingId: {
      type: [ String, Number],
      default: null
    },
    // course->商品课程 readPlan->读书计划
    type: {
      type: String,
      default: ''
    },
    readPlanId: {
      type: [String, Number],
      default: null
    },
    arrangeId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      sensitiveWords: [], //敏感词库
      list: [],
      loading: false,
      finished: false,
      page: 0,
      pageSize: 5,
      topic: '',
      pageShow: false,
      ossImgSize: '?x-oss-process=image/resize,m_lfit,h_360,w_360',
    };
  },
  methods: {
    refresh() {
      this.page = 0;
      this.list = [];
      this.onLoad();
    },
    preview(imgs, index) {
      ImagePreview(imgs, index);
    },
    // 获取书本信息
    getBookBasicInfo() {
      let params = {
        readPlanId: this.readPlanId,
      }
      this.$http.post('/pu/getReadPlanBasicInfo/1.0/', params)
        .then(res=>{
          const { code, body } = res;
          if( code === '00') {
            if(body) {
              this.topic = `#${body.readPlanName}#`;
            }
          }
        });
    },
    // 获取评论列表
    onLoad() {
      this.page +=1;
      const data = {
        mappingId: this.mappingId,
        arrangeId: this.arrangeId, // 读书计划课程安排id
        type: this.type,
        pageNum: this.page,
        pageSize: this.pageSize
      };

      this.$http.post('/pu/getCommentReplyList/1.0/',data).then(res=>{
        let { code, body } = res;
        if(code === '00'){
          if(Array.isArray(body)){

            body.forEach(item=>{
              item.isFold = false; //是否折叠 默认false
              item.simple = ''; // 存放不完全的内容
              item.fullText = ''; //存放全文
              item.open = false;

              //循环匹配敏感词
              this.sensitiveWords.forEach(word => {
                let reg = new RegExp(word,"g"); //全局匹配

                if(item.commentContent.indexOf(word) !== -1) {
                  item.commentContent = item.commentContent.replace(reg,"*".repeat(word.length));
                }
              });

              //超过多少字符折叠加省略号
              if(item.commentContent.length > 105){
                item.fullText = item.commentContent;
                item.isFold = true; //是否折叠
                item.simple = item.commentContent.substring(0, 105) + '......';//不完全内容
                item.commentContent = item.simple;
              };

              if (typeof item.pictureUrl === 'string' && item.pictureUrl !== '') {
                item.imgs = item.pictureUrl.split(',');
                item.imgs = item.imgs.map(item => {
                  return config.articleURL + item;
                })
              } else {
                item.imgs = []
              }

            });

            this.list = this.list.concat(body);
            this.loading = false;

            if(!this.pageShow) {
              this.pageShow = true;
            }

            if(body.length === 0){
              this.finished = true;
            }

          } else {
            this.pageShow = true;
            this.finished = true;
          }
        }
      });
    },
    //解析敏感词txt,将所有敏感词，解析为数组
    async parsingTxt() {
      let apiUrl =config.articleURL+ 'cache/sensitive-pu.txt?v='+ new Date().getTime();
      let text = await this.$http.get(apiUrl);

      let snsArr = text.split(/[(\r\n)\r\n]+/);
      snsArr.forEach((item, index) => {
        snsArr[index] = item.trim();
        if (!item) {
          snsArr.splice(index, 1); //删除空项
        }
      });

      this.sensitiveWords = snsArr;
    },
    // 评论的展开和收缩
    openOrShrink(item) {
      if(item.open){
        item.commentContent = item.simple;
        item.open = false;
      }else {
        item.commentContent = item.fullText;
        item.open = true;
      }
    },
    // marktopic(text) {
    //   let topicReg = /#([^#]*)#/g;
    //   let content = text.replace(topicReg, function (str, firstIndex, template) {
    //     return `<span style="color: #FB933B">${str}</span>`;
    //   });
    //   return content;
    // },
  },
  created() {
    // this.getBookBasicInfo();
    this.parsingTxt();
    this.onLoad();
  }
};
</script>

<style lang='scss' scoped>
.comment-list {
  background: #fff;
  .item {
    padding: 0.1rem 0.1rem 0 0.1rem;

    /deep/ .van-divider {
      margin:0;
    }

    .head {
      display: flex;

      .user-avatar {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        background: #cccccc;
        flex: 0 0 auto;

        img {
          border-radius: 50%;
        }
      }


      .user-name {
        font-size: 0.14rem;
        color: #170606;

        .featured {
          width: 0.41rem;
          height: 0.15rem;
          vertical-align: text-bottom;
        }
      }

      .user-info {
        margin-left: .05rem;
      }
    }

    .comment-main {
      margin-top: .15rem;
      margin-left: 0.05rem;

      .imgs {
        margin-top: .08rem;
        margin-right: -0.8rem;
        .van-image {
          margin-right: .08rem;
          margin-bottom: .04rem;
        }
      }

      .release-time {
        font-size: 0.12rem;
        color: #B2ACAC;
        transform: rotate(0.9);
        margin-top: 0.02rem;
      }

      .comment-content {
        margin-top: 0.08rem;
        word-break:break-all;

        .more {
          color: #F06E6C;
          font-size: 0.14rem;
          font-weight: 600;
          line-height: inherit;
          background: transparent;
        }
      }


      .sub-comment-list {
        border-radius: 0.1rem;
        margin-top: 0.15rem;
        margin-right: 0.19rem;
        background-color: #F8F7F7;
        padding: 0.15rem 0.15rem 0.15rem 0.1rem;

        .sub-comment-user-name {
          display: inline-block;
          font-size: 0.14rem;
          font-weight: 600;
          color: #170606;
          position: relative;

          &:before {
            content: '';
            width: 0.02rem;
            height: 100%;
            background: #F06E6C;
            position: absolute;
            left: -0.1rem;
          }
        }

        .sub-comment-content {
          font-size: 0.14rem;
          word-break:break-all;
          color: #736868;
        }

      }
    }

  }
}

.empty {
  width: 100%;
  height: 2.89rem;
  padding-top: .62rem;
  text-align: center;

  .empty-bg {
    width: 1.65rem;
    height: 1.65rem;
  }

  .empty-tip {
    color: #B2ACAC;
    font-size: .12rem;
    line-height: .17rem;
  }
}
</style>
