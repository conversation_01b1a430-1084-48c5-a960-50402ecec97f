<template>
<div>
  <div class="invite invite-top" v-if="showInvite">
    <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" alt>
    <div class="headTxt">
      <p>
        您的好友
        <span>{{invite.nickName || invite.realName}}</span>
      </p>
      <p>{{text}}</p>
    </div>
    <div class="card_btn" @click="flag=true" v-if="cardWechatQrcode">老师微信</div>
  </div>
  <div class="card_wrap" v-if="flag">
    <div class="bg" @click="flag=false"></div>
    <div class="card">
      <div class="talk">{{motto}}</div>
      <div class="txt">
        <h3>{{cardNickname }}</h3>
      </div>
      <div class="tel">
        <p>
          <span :class="{active:company.length>13}">{{company||'公司名称未填写'}}</span>
          <i v-if="company">
            <img src="../../../../assets/image/teacherKit/认证管理@3x.png" alt>
          </i>
        </p>
        <span>{{position || '职位未填写'}}</span>
        <span>{{cardMobile || '手机号未填写'}}</span>
      </div>
      <div class="code" v-if="cardWechatQrcode" @click="isShow=true">
        <img :src="cardWechatQrcode?cardWechatQrcode+'?x-oss-process=image/resize,m_fixed,h_140,w_140':cardWechatQrcode | defaultCode" alt>
        <span>长按识别</span>
      </div>
    </div>
  </div>
  <div class="img_wrap" v-if="isShow">
    <div class="bg" @click="isShow=false"></div>
    <img :src="cardWechatQrcode" alt>
  </div>
</div>
</template>

<script>
  import {imgBaseURL} from "@/config";
  import { getQueryString } from '@/common';

  export default {
      name: "invite-top",
      props: {
        inviteId: {
          type: String,
          default: '',
        },
        text: {
          type: String,
          default: '邀请您一起来提升学历',
        },
      },
      data(){
          return{
            flag:false,
            cardHeadUrl: "",
            cardMobile: "",
            cardNickname: "",
            cardWechatQrcode: "",
            company: "",
            motto: "",
            position: "",
            propagateUrl: "",
            slogan: "",
            urlGuide: "",
            headLimit: "?x-oss-process=image/resize,m_fixed,h_76,w_76",
            showInvite:false,
            isShow:false
          }
      },
      created(){
          this.getInviteInfo()
      },
      methods:{
        getInviteInfo() {
          const redirect = this.$route.query.redirect;
          let inviteId = (
            this.inviteId ||
            window.sessionStorage.getItem("inviteId") ||
            this.$route.query.inviteId ||
            decodeURIComponent(this.$route.query.inviteId || getQueryString(redirect, "inviteId") || "")
          ).replace(/ /g, "+");
          if (inviteId) {
            this.$emit('getInviteId', inviteId);
            this.$http
              .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
              .then(res => {
                let { code, body } = res;
                if (code !== "00") return;
                this.invite = body || {};
                this.showInvite = true;
                this.$emit('showInvite')
                if (!this.articleId) {
                  if (this.invite.relation == "2" || this.invite.relation == "6") {//取自己的名片
                    this.getArtile(this.invite.userId);
                  } else {//其他情况取跟进人
                    this.getCard(this.invite.userId);
                  }
                }
              });
          }
        },
        getArtile(id) {
          this.$http
            .post("/mkt/getSpreadCardByRelationId/1.0/ ", { relationId: id })
            .then(res => {
              if (res.code === "00") {
                if (res.body === null) {
                  this.articleId = "";
                } else {
                  this.showCard = true;
                  Object.assign(this.$data, res.body);
                  if (this.cardHeadUrl) {
                    this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
                  }
                  if (this.cardWechatQrcode) {
                    this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
                  }
                  this.propagateUrl = this.propagateUrl + "&articleId=" + id;
                  this.storage.setItem('cardMobile',this.cardMobile)
                }
              }
            });
        },
        getCard(userId){
          this.$http
            .post("/mkt/getFollowCardByUserId/1.0/", { userId: userId })
            .then(res => {
              let { code, body } = res;
              if (code !== "00") return;
              if (res.body ) {
                this.showCard = true;
                Object.assign(this.$data, res.body);
                if (this.cardHeadUrl) {
                  this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
                }
                if (this.cardWechatQrcode) {
                  this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
                }
                this.storage.setItem('cardMobile',this.cardMobile)
                //this.propagateUrl = this.propagateUrl + "&articleId=" + id;
              }
            });
        },
      }
    }
</script>

<style scoped lang="less">
  .invite {
    padding: 0.09rem 0;
    height: .6rem;
    background-color: #fff;
    background-size: 100%;
    img {
      width: 0.41rem;
      height: 0.41rem;
      border-radius: 50%;
      float: left;
      margin-left: 0.12rem;
    }
    .headTxt {
      float: left;
      margin-left: 0.12rem;
      width: 1.72rem;
      overflow: hidden;
      p {
        font-size: 0.16rem;
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        line-height: .16rem;
        span {
          color: #e15443;
          font-size: 0.14rem;
        }
        &:first-of-type {
          font-size: 0.12rem;
          margin-bottom: .08rem;
        }
      }
    }
    .card_btn{
      width: 1rem;
      height: .34rem;
      color: white;
      line-height: .34rem;
      float: right;
      margin-right: .1rem;
      border-radius: .05rem;
      margin-top: .06rem;
      background-color: rgba(25, 209, 0, 1);
      padding-left: .38rem;
      background-image: url("../../../../assets/image/teacherKit/微信@3x.png");
      background-size: .32rem .32rem;
      background-position: .05rem .02rem;
      background-repeat: no-repeat;
    }
  }
  .invite-top {
    position: relative;
    z-index: 10;
  }
  .card_wrap {
    position: fixed;
    top: 0.6rem;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9998;
    overflow: hidden;
    .bg{
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, .7);
      top:0;
      left:0;
    }
  }
  .card {
    width: 3.43rem;
    height: 1.78rem;
    margin: 0.16rem;
    overflow: hidden;
    background-image: url("../../../../assets/image/active/enrollAggregate/bg-card.png");
    background-size: 100%;
    border-radius: 0.1rem;
    position: relative;
    .talk {
      position: absolute;
      right: 0.24rem;
      top: 0.1rem;
      height: 0.2rem;
      color: rgba(51, 51, 51, 1);
      font-size: 0.1rem;
    }
    .edit {
      position: absolute;
      right: 0;
      top: 0;
      width: 0.6rem;
      height: 0.6rem;
      background: url("../../../../assets/image/teacherKit/<EMAIL>") no-repeat;
      background-size: 100%;
    }
    .img {
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 50%;
      float: left;
      margin-right: 0.06rem;
      overflow: hidden;
      background-color: #fff;
      img {
        width: 100%;
        margin: 0;
      }
    }
    .txt {
      color: rgba(51, 51, 51, 1);
      line-height: 0.24rem;
      margin: 0.3rem 0 0 0.2rem;
      h3 {
        font-weight: bold;
        font-size: 0.22rem;
        line-height: 0.3rem;
      }
    }
    .tel {
      width: 2.1rem;
      height: 0.42rem;
      float: left;
      color: rgba(51, 51, 51, 1);
      margin-left: 0.2rem;
      margin-top: 0.2rem;
      span {
        /*width:1.95rem;*/
        /*white-space: nowrap;*/
        /*overflow: hidden;*/
        /*text-overflow: ellipsis;*/
        &.active {
          width: 1.9rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        display: block;
        line-height: 0.22rem;
        &:first-child {
          float: left;
        }
      }
      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 2.05rem;
        height: 0.25rem;
        font-size: 0.14rem;
        line-height: 0.25rem;
        overflow: hidden;
        i {
          display: inline-block;
          width: 0.12rem;
          height: 0.25rem;
          margin-left: 0.03rem;
          img {
            width: 100%;
            margin-top: 0.06rem;
          }
        }
      }
    }
    .code {
      width: 0.7rem;
      height: 0.94rem;
      float: right;
      margin-top: 0.15rem;
      margin-right: 0.3rem;
      overflow: hidden;
      /*background-color: #fff;*/
      img {
        width: .7rem;
        height: 0.7rem !important;
        overflow: hidden;
      }
      span {
        display: block;
        width: 0.7rem;
        height: 0.24rem;
        color: #363636;
        font-size: 0.12rem;
        background-size: 28%;
        text-align: center;
        font-weight: bold;
      }
    }
  }
  .img_wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 9999;
    .bg {
      position: fixed;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 999;
    }
    img {
      width: 2.5rem;
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      margin: auto;
      z-index: 99999;
    }
  }
</style>
