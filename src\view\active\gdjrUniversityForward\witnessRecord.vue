<template>
  <div class="list" 
    v-infinite-scroll="loadMore"
    infinite-scroll-disabled="isLoading"
    infinite-scroll-distance="50"
    :infinite-scroll-immediate="false"
  >
    <div class="item" v-for="(item,index) in witnessLogList" :key="index">
      <p>
        活动类型：<span>广东金融学院奖学金</span>
      </p>
      <p>
        见证人：<span>{{item.witnessName}}</span>
      </p>
      <p>
        工作单位：<span>{{item.workUnit}}</span>
      </p>
      <p>
        上进点评：<span>{{item.comment}}</span>
      </p>
      <p>
        见证时间：<span>{{item.createTime | formatDate('yyyy-MM-dd')}}</span>
      </p>
      <div class="stamp">
        <img src="../../../assets/image/active/gccScholarship/stamp.png" alt="">
      </div>
    </div>
    <div class="empty" v-if="witnessLogList.length==0">
        暂无见证
    </div>
  </div>
</template>
<script>
export default {
  data(){
    return {
      pageNum: 0,
      pageSize:10,
      witnessLogList:[],
    }
  },
  mounted(){
    // this.getWitnessLogList();
  },
  methods:{
    loadMore(){
      this.pageNum+=1;
      this.getWitnessLogList();
    },
    //获取见证记录
    getWitnessLogList(){
      let parmas = {
        pageNum:this.pageNum,
        pageSize:this.pageSize
      }
      this.$http.post('/mkt/getAdvanceWitnessListByWUserId/1.0/',parmas).then(res=>{
        if(res.code=='00'){
          const datas = (res.body || []);
          this.witnessLogList.push(...res.body);
          // this.$nextTick(()=>{
          //   this.allLoaded = datas.length === 0;
          //   this.isLoading = this.allLoaded;
          // })
        }
      })
    },
  }
}
</script>
<style lang="less" scoped>
.list{
    background-color: white;
    height: auto;
    overflow: hidden;
    min-height: 100vh;
  .item {
    width: 3.55rem;
    padding:0.14rem;
    background: #FFFFFF;
    box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    margin-top: 0.15rem;
    margin:0.15rem 0.1rem;
    font-size:0.14rem;
    position: relative;
    p{
      span {
        color:#6E1002;
        font-weight: bold;
      }
    }
    p + P{
      margin-top: 0.1rem;
      
    }
    .stamp {
      width: 1.36rem;
      height: 0.68rem;
      right: 0.1rem;
      top: 0.14rem;
      position: absolute;
      img {
        width: 100%;
      }
    }
  }
  .empty{
      position: fixed;
      top:0;
      bottom:0;
      left:0;
      right: 0;
      margin: auto;
      width: 1rem;
      height: 1rem;
      padding-top: .83rem;
      background-image: url("../../../assets/image/active/sprintBeforeExam/no_sprint.png");
      background-size: 100%;
      background-repeat: no-repeat;
      text-align: center;
      color: rgba(54, 54, 54, 1);
  }
}

</style>