    <template>
    <div class="yz-invite">
      <div class="yz-invite__tab" >
        <div class="item" v-for="(item, index) in tabs" :key="index" @click='tabChange(index)'>
          <span :class='{active: item.isChecked}'>{{item.text}}</span>
        </div>
      </div>
      <!-- 邀约5月一周活动 -->
      <div class="yz-invite__yy5m-top" v-if='isInvite5MonthActive && showIndex == 0'>
      8月9日19:00 - 9月10日16:00期间，邀请好友报读缴费，额外奖励<span>15000智米</span>
      </div>
      <div class="yz-invite__active-rule" v-if='isInvite5MonthActive && showIndex == 0' @click='showActiveRule=true'>活动规则</div>
      <div class="invite" v-if='showIndex == 0'>
        <div class="content">
          <img src="../../../assets/image/active/newDreamBuild/invite_gift_big.png" alt="">
          <!-- <div class="title">·  邀请朋友上进还能拿智米  ·</div> -->
          <!--<div class="bag1">-->
            <!--<ul>-->
              <!--<li><span>1</span>邀请好友参与远智教育的上进计划</li>-->
              <!--<li class="line"></li>-->
              <!--<li><span>2</span>好友注册成功</li>-->
              <!--<li class="line"></li>-->
              <!--<li><span>3</span>获得100智米</li>-->
              <!--&lt;!&ndash;<li><span>3</span>自己获得100智米</li>&ndash;&gt;-->
            <!--</ul>-->
          <!--</div>-->
          <!-- <div class="bag2">
            <div class="txt1">
              <span class="rl">好友</span>
              <p>成功报读任一学历教育</p>
            </div>
            <div class="line"></div>
            <div class="line2"></div>
            <div class="txt2">
              <span class="rl">好友</span>获得
              <span class="rl">好友</span>
              <p class="big">同等缴纳金额数智米</p>
              <p >获得同等缴纳金额<br />的智米</p>
            </div>
            <div class="txt3">
              <span class="rl">自己</span>获得
              <span class="rl">自己</span>
              <p>
                好友支付的</p>
              <p class="big">同等金额数智米</p>
              <p>
                获得好友实缴费用<br />
                同等金额的5倍智米
              </p>
            </div>
          </div> -->
          <div class="txt">
            <router-link :to="{name:'newHome'}">* 智米可以在远智教育 <span>智米中心</span>兑换京东卡等礼品</router-link>
            <router-link style="margin-top: .1rem;" :to="{name:'zmRaiders'}" >* <span style="text-decoration: none;">智米是什么？</span></router-link>
          </div>
        </div>
      </div>

      <enrollList v-if='showIndex == 1' @invite='enrollListInvite'></enrollList>
      <myInvite v-if='showIndex == 2' @share='remindShare'></myInvite>

      <div class="footer" v-if='showIndex != 1'>
        <button @click="openPoster">生成邀请专属海报</button>
        <button @click="openDefault">立即邀请</button>
      </div>
      <div class="cover" ref="cover" @click="inviteFlag=false" v-if="inviteFlag">
        <div class="bg"></div>
        <div class="J-weixin-tip weixin-tip" ref="wexin_tip">
          <div class="weixin-tip-content">
            请点击右上角<br />
            将它发送给指定朋友<br/>
            <span v-if='!remindText'>或者分享到朋友圈</span>
          </div>
          <div class="airport">
          </div>
        </div>
           <!--<div style="color:#fff;font-size:.15rem;text-align:center;position:fixed;top:35%;z-index:3000;left:25.5%;">-->
            <!--每邀请一名好友报读缴费,<br>-->
            <!--可获得被邀请人缴纳学费的<br>-->
            <!--同等金额数智米奖励-->
          <!--</div>       -->
        <div class="J-weixin-tip-img weixin-tip-img" ref="wexin_tip_img"></div>
      </div>
      <shareposter v-if="posterFlag" @cancel="posterFlag=false" :sex="sex" :isAppopen="isAppOpen" :from="from"></shareposter>
      <share :title="shareObj.title" :desc="shareObj.desc" regOrigin='2'
             :link="origin+shareObj.shareLink" :imgUrl="shareObj.imgUrl" :showShare="false" ref="share"  />

      <van-popup v-model="showActiveRule">
        <div class="yz-invite__yy5m-dialog">
          <img src="../../../assets/image/closeX.png" class="close" @click='showActiveRule = false' alt="">
          <p class="title">活动规则</p>
          <p class="t1">活动时间</p>
          <p class="time">8月9日19:00 - 9月10日16:00</p>
          <p class="t1">活动详情</p>
          <p class="t2">
            活动期间，<span class='red'>邀请人</span>每邀请一名新注册用户，在原有智米奖励基础上：
          </p>
          <!-- <p class="t2">
            1、报读自考并缴费，再额外奖励50000智米; <br />
            2、报读成教并缴费,额外奖励10000智米,缴纳第1年学费后,再奖励40000智米; <br />
            3、报读国开并缴第1学期学费，额外奖励10000智米,缴纳第2学期学费后，再奖励40000智米;<br />
            4、若被邀请人退学，则视为无效推荐，自动收回邀请人的相应智米
          </p>
          <p class="t3">最终解释权归远智所有。</p> -->
          <p class="t2">
            1、报读自考并缴费，再额外奖励15000智米； <br />
            2、报读成教并购买成考辅导礼包，再额外奖励15000智米； <br />
            3、报读国开并缴第1学期学费，再额外奖励15000智米；<br />
            4、若被邀请人退学，则视为无效推荐，自动收回邀请人的相应智米；<br>
            5、若被邀请人报读高起本，则以第一次报读缴费记录为准，只赠送一次15000智米;<br>
          </p>
          <p class="t3">最终解释权归远智所有。</p>
        </div>
      </van-popup>

    </div>
</template>

<script>
  import { Toast, Popup } from 'vant';
  import share from "@/components/share";
  import { getIsInTimeByType } from '@/common';
  import appShare from '@/mixins/appShare';
  import shareposter from "./shareposter";
  import enrollList from '@/view/settings/enrollList/home';
  import config from "../../../config";
  import shareJson from "./share";
  import myInvite from "./myInvite";

    export default {
      name: "invite2",
      components:{ share,shareposter, enrollList, shareJson, myInvite, Popup },
      mixins: [appShare],
      data(){
        return{
          shareObj:{
            title:"",
            desc:"",
            shareLink:"",
            imgUrl:""
          },
          inviteId:'',
          cjInviteId:'',
          inviteFlag:false,
          posterFlag:false,
          sex:'1',
          show:true,
          isChange:false,
          from:'',
          origin:window.location.origin,
          remindText: false,
          share:{//全日制暂无招生
            "1":"dreamBuild",
            "2":"openUniversity",
            "3":"dreamBuild",
            "4":"selfTought",
            "5":"graduate"
          },
          defaultShareObj: {},
          showIndex: 0,
          tabs: [
            {
              text: '邀约有礼',
              isChecked: true,
            },
            {
              text: '邀请排行榜',
              isChecked: false,
            },
            // {
            //   text: '我的邀请',
            //   isChecked: false,
            // },
          ],
          showActiveRule: false,
          isInvite5MonthActive: getIsInTimeByType('decisive'), // 邀约2020-5月活动
        };
      },
      computed: {
        appShareParams() {
          return {
            title: this.shareObj.title,
            content: this.shareObj.desc,
            url: this.shareObj.shareLink,
            // image: this.shareImage,
            // scholarship: this.scholarship,
            regOrigin: 2,
          };
        },
      },
      watch: {
        appShareParams() {
          this.setShareParams(this.appShareParams);
        },
      },
      // beforeRouteEnter(to,from,next){
      //   next();
      // },
      created(){
        this.from=this.$route.query.from;
        this.$yzStatistic('marketing.base.browse', '19', '邀请有礼');
        if(!this.from){
          this.getInfo()
        }else{
          this.$set(this.$data,"shareObj",shareJson[this.from])
          this.defaultShareObj = Object.assign({}, shareJson[this.from]);
        }
        this.inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || '')).replace(/ /g, '+');
        this.sex=this.$route.query.sex?this.$route.query.sex:'1';
        if (this.$route.query.tab) {
          this.showIndex = Number(this.$route.query.tab);
          this.tabChange(this.showIndex);
        }
      },
      mounted() {
        this.initAppShare(() => {
          this.setShareParams(this.appShareParams);
        });
      },
      methods:{
        tabChange(index) {
          this.showIndex = index;
          const tabs = this.tabs.map(val => ({ ...val, isChecked: false }));
          tabs[index].isChecked = true;
          this.tabs = [].concat(tabs);
          Toast.clear();
          //我的邀请腾讯统计事件
          if(index == 2) {
            MtaH5.clickStat("lookmyinvitie")
          }

        },
        openPoster() {
          this.posterFlag = true;
          this.$yzStatistic('marketing.base.click', '20', '邀请有礼-生成海报');
        },
        openDefault() {
          this.$yzStatistic('marketing.base.click', '21', '邀请有礼-立即邀请');
          this.inviteFlag = true;
          this.remindText = false;
          this.shareObj = Object.assign({}, this.defaultShareObj);
        },
        enrollListInvite() {
          this.tabChange(0);
        },
        remindShare(item) {
          this.inviteFlag = true;
          this.remindText = true;
          this.shareObj.shareLink = `/student?isOpenGift=${item.recruitType == 1 ? 1 : 0}`;
          switch (Number(item.recruitType)) {
            case 1:
            case 3:
              this.shareObj.title = '你已成功报读成考啦~快来参加辅导课，提高成考通过率！';
              this.shareObj.desc = '参加成考辅导课后，98%的学员都被心仪的高校专业录取了，并且学费支出都大大减少了哦！';
              break;
            case 2:
              this.shareObj.title = '你已成功报读国开啦~快来入学，争取早日拿证哦！';
              this.shareObj.desc = '不用参加全国成考，即刻入学，证书学信网可查！';
              break;
            case 4:
              this.shareObj.title = '你已成功报读自考啦~快来参加培训班，提高自考通过率！';
              this.shareObj.desc = '上进自我，自考报读，提高自考通过率，学历提升无忧虑！';
              break;
            case 5:
              this.shareObj.title = '你已成功报读研究生啦~快来参加培训班，提高自考通过率！';
              this.shareObj.desc = '上进自我，研究生报读，提高研究生通过率，学历提升无忧虑！';
              break;
            default:
              break;
          }
          this.shareObj = Object.assign({}, this.shareObj);
        },
        getInfo(){
          this.$http
            .post("/mkt/stdLearnInfo/1.0/")
            .then(res => {
              this.$indicator.close();
              if(res.code=='00'){
                // 过滤退学学员或者非成教
                if(res.body){
                  const learnInfos = res.body.learnInfos.filter(item => {
                    if (item.stdStage !== "10" ) {
                      return item;
                    }
                  });
                  if(learnInfos.length){
                    // this.from=this.share[learnInfos[0].recruitType]
                    this.from = 'newMain2021';
                  }
                }
                this.from?this.$set(this.$data,"shareObj",shareJson[this.from]):this.$set(this.$data,"shareObj",shareJson["dreamBuild"])
                this.defaultShareObj = Object.assign({}, this.shareObj);
                this.$nextTick(() => {
                  this.$refs.share.openNO(true);
                });
              }
            })
        },
      },
    }
</script>

<style lang="less" scoped>
  .yz-invite{

    .enroll_wrap {
      .banner_swiper{
        top: 0.55rem!important;
      }
      .method{
        top: 0.9rem;
      }
    }
  }
  .yz-invite__tab{
    display: flex;
    align-items: center;
    height: 0.5rem;
    background: #fff;
    .item{
      width: 50%;
      font-size: 0.15rem;
      text-align: center;
      display: inline-block;
      span{
        padding: 0.05rem 0.1rem;
        &.active{
          color: #FF6467;
          border-bottom: 2px solid #FF6467;
        }
      }

    }
  }
  .yz-invite__yy5m-top{
    background: #FFFAED;
    padding: 0.1rem 0.15rem;
    font-size: 0.14rem;
    color:#FF9256;
    &>span{
      font-weight: bold;
      font-size: 0.16rem;
    }
  }
  .yz-invite__active-rule{
    position: absolute;
    color: #fff;
    top: 1.25rem;
    right: 0.15rem;
    text-decoration: underline;
    z-index: 2;
  }
  .van-popup{
    border-radius: 0.1rem;
  }
  .yz-invite__yy5m-dialog{
    padding: 0.22rem 0.15rem;
    background: #fff;
    width: 3rem;
    font-size: 0.12rem;
    border-radius: 0.1rem;
    .close{
      width: 0.15rem;
      position: absolute;
      top: 0.12rem;
      right: 0.18rem;
    }
    .title{
      font-size: 0.17rem;
      text-align: center;
      margin-bottom: 0.2rem;
      font-weight:bold;
    }
    .red{
      color: #FF6467;
      font-weight:bold;
    }
    .t1{
      font-size: 0.14rem;
      font-weight:bold;
    }
    .time{
      color: #FF6467;
      margin:0.06rem 0 0.15rem;
    }
    .t2{
      line-height: 0.24rem;
    }
    .t3{
      color: #A29B9B;
      margin-top: 0.06rem;
    }
  }
  @keyframes switchImg {
    0%{
      background: url("../../../assets/image/active/newDreamBuild/0.png") no-repeat .05rem 0.05rem;
      background-size: .5rem;
      background-color: rgba(253, 177, 6, .2);
    }
    50%{
      background: url("../../../assets/image/active/newDreamBuild/1.png") no-repeat .05rem 0.05rem;
      background-size: .5rem;
      background-color: rgba(253, 177, 6, .2);
    }
    100%{
      background: url("../../../assets/image/active/newDreamBuild/0.png") no-repeat .05rem 0.05rem;
      background-size: .5rem;
      background-color: rgba(253, 177, 6, .2);
    }
  }
  .top{
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    height:.44rem;
    /*background: url("../../../assets/image/active/newDreamBuild/0.png") no-repeat .05rem 0.05rem;*/
    /*background-size: .5rem;*/
    background-color: rgba(253, 177, 6, .2);
    /*animation: switchImg 1.5s infinite;*/
    img{
      display: block;
      float: left;
      width: .44rem;
      margin-top: .05rem;
      margin-left: .05rem;
    }
    p{
      color: rgba(253, 116, 6, 1);
      line-height: .44rem;
      float: left;
      font-size: .14rem;
    }
    span{
      float: right;
      /*margin-left:.1rem ;*/
      display: block;
      color: rgba(253, 116, 6, 1);
      line-height: .44rem;
      width: .8rem;
      .ic-right{
        display: block;
        margin-top: .1rem;
        float: right;
        width: .24rem;
        height: .24rem;
        background: url("../../../assets/image/active/newDreamBuild/ic_rightback.png") no-repeat;
        background-size: .24rem;
      }
    }
  }
  .invite{
    // background: url("../../../assets/image/active/newDreamBuild/invite_gift_big.png") no-repeat ;
    // background-size:100% 100%;
    // padding: .1rem;
    // // height: 6rem;
    // // max-width: 640px;
    // background-color: white;
    // margin-bottom: .8rem;
   
    .content{
      // margin-top: 5.66rem;
      // border-radius: .04rem;
      // background-color: #fff;
      // padding: .25rem 0rem;
      img {
        height: 100%;
        width: 100%;
      }
      .title{
        text-align: center;
        font-weight: 800;
        background-repeat: no-repeat;
        background-size: 100%;
        font-size: .17rem;
        background-position-y: .08rem;
        margin-bottom: .15rem;
        color: #EE5D4C;
        position: relative;
        &:before{
           content: '';
           width: .71rem;
           height: .02rem;
           background-color: #EE5D4C;
           position: absolute;
           left:-0.01rem;
           top:.12rem;
         }
        &:after{
          content: '';
          width: .71rem;
          height: .02rem;
          background-color: #EE5D4C;
          position: absolute;
          right:-0.01rem;
          top:.12rem;
        }
      }
     .bag1{
       background: url("../../../assets/image/active/dreamBuild/bag1.png") no-repeat;
       background-size: 100%;
       margin-bottom: .1rem;
       width:3.56rem;
       height: 1.45rem;
       ul{
         width: 100%;
         height: 1rem;
         padding-top: .38rem;
         li{
           margin-left: .42rem;
           padding-left: .24rem;
           font-size: .14rem;
           font-weight: 800;
           span{
             display: block;
             font-size: .12rem;
             float: left;
             font-weight: 300;
             color: #fff;
             text-align: center;
             line-height: .15rem;
             width: .15rem;
             height: .15rem;
             margin-top: .03rem;
             background: url("../../../assets/image/active/dreamBuild/round.png") no-repeat;
             background-size: 100%;
             margin-right: .1rem;
           }
         }
         li:last-child{
           color: #EE5D4C;
         }
         .line{
           width: 0.03rem;
           height: .13rem;
           margin-left: .73rem;
           border-left: dashed 1px #EE5D4C;
         }
       }
      }
     .bag2{
       background: url("../../../assets/image/active/dreamBuild/bag2.png")no-repeat;
       /*background: url("../../../assets/image/active/dreamBuild/bag1.png")no-repeat;*/
       background-size: 100%;
       font-size: .14rem;
       width:3.56rem;
       height: 1.75rem;
       line-height: 0.17rem;
       padding: 0.13rem .27rem;
       font-weight: 600;
       .rl{
         display: inline-block;
         width: .29rem;
         height: .17rem;
         line-height: .17rem;
         color: white;
         background-color: #EE5D4C;
         border-radius: .02rem;
         font-size: .12rem;
         text-align: center;
         margin-right: .02rem;
       }
       .txt1{
         width: 100%;
         margin-left: 0.6rem;
         padding-top: .3rem;
         p{
           display: inline-block;
         }
       }
       .txt2{
         float: left;
         text-align: center;
       }
       .txt3{
         float: right;
         text-align: center;
       }
       .big{
         color:#EE5D4C;
         font-weight: bold;
         line-height: .22rem;
       }
       .line{
         width: 0.03rem;
         height: .10rem;
         margin-left: 1.43rem;
         border-left: dashed 1px #EE5D4C;
         margin-top: .1rem;
       }
       .line2{
         width: 1.76rem;
         height: .10rem;
         margin-left: .59rem;
         border-left: dashed 1px #EE5D4C;
         border-top: dashed 1px #EE5D4C;
         border-right: dashed 1px #EE5D4C;
         margin-bottom: .1rem;
       }
      }
      .txt{
        position: relative;
        top: -0.83rem;
        width: 100%;
        color: #EE5D4C;
        height: .62rem;
        // line-height: .31rem;
        font-size: .12rem;
        font-weight: 800;
        text-align: left;
        margin-bottom: .25rem;
        padding-left: .2rem;
        margin-top: .2rem;
        a{
          display: block;
          span{
            text-decoration: underline;
            color:#EE5D4C ;
          }
          /*color: rgba(26, 26, 26, .6);*/
        }
      }

    }
  }
  .footer{
    position: fixed;
    width: 100%;
    max-width: 640px;
    height:.55rem;
    left:0;
    right:0;
    margin: auto;
    bottom:0;
    filter: progid:DXImageTransform.Microsoft.Gradient(startColorStr='#FF7966',endColorStr='#EE5C4D',gradientType='1');
    background-image: linear-gradient(to bottom,#FF7966,#EE5C4D);
    background-image: -webkit-liner-gradient(to bottom,#FF7966,#EE5C4D);
    background-image: -moz-linear-gradient(to bottom,#FF7966,#EE5C4D);
    display: flex;
    padding: .14rem 0;
    button{
      text-align: center;
      width: 50%;
      height: 100%;
      color: white;
      font-size: .15rem;
      font-weight: 800;
      background: transparent;
      border: none;
    }
    button:first-child{
      border-right: solid 1px #ccc;
    }
  }
  .activitInvit{
    background: url("../../../assets/image/active/newDreamBuild/invite_gift2.png") no-repeat;
  }
  .cover{
    width: 100%;
    max-width: 640px;
    height: 100%;

    position: fixed;
    top:0;
    z-index: 2002;
    .bg{
      width: 100%;
      max-width: 640px;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      overflow: hidden;
      z-index: 2003;
      position: fixed;
      top:0;
    }
    .weixin-tip {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      position: fixed;
      top: 15px;
      right: 20px;
      width: 285px;
      padding: 55px 0 0;
      text-align: left;
      background: url("../../../assets/image/icon_arrow.png") no-repeat right top;
      background-size:.74rem auto;
      z-index: 3000;
      color:#fff
    }

    .weixin-tip-img {
      display: none;
      padding: 110px 0 0
    }
    .weixin-tip-content{
      padding-left: 20px;
      text-align: center;
      margin-top:20px;
      line-height: .22rem;
      font-size: .16rem;
    }
  }
</style>
