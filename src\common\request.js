import axios from '@/plugins/axios';

/**
 * 获取活动详情
 * @param {String | Number} scholarship 优惠类型
 * @returns 返回接口信息
 */
export const getActivityInfo = (scholarship) => axios.post("/mkt/getActivityInfo/1.0/", { scholarship });

/**
 * 获取个人学历信息;
 * @returns 返回个人学历信息
 */
export const getStdLearnInfo = (params = {}) => axios.post("/mkt/stdLearnInfo/1.0/", params);

/**
 * 获取用户信息
 * @returns 返回用户信息
 */
export const getUserInfo = () => axios.post('/us/getUserInfoById/1.0/');


export const getLocationData = (cirLocationData) => axios.post("/us/selCircleOtherData/1.0/", { cirLocationData })

