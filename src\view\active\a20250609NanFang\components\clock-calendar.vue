<template>
  <calendar @change="dateChange" v-if="calendarShow">
    <template slot-scope="scope">
      <span :class="isClock(scope.date)">
        {{ dayText(scope.date) }}
      </span>
    </template>
  </calendar>
</template>

<script>
import calendar from './calendar'
export default {
  components: {
    calendar
  },
  data() {
    return {
      readPlanId: '',
      clockDay: [],
      calendarShow: false,
      nowNum: new Date().getUTCDate(),
      year: null,
      month: null
    };
  },
  created() {
  },
  mounted() {
    this.readPlanId = this.$route.query.readPlanId || '';
    this.getCalenDay(this.getCurrentDate());
  },
  methods: {
    isClock(date) {
      //将空白位置，直接返回
      if(!date.day) {
        return {}
      }
      // 判断xx天是否打卡
      if (this.clockDay.indexOf(date.day)!== -1) {
        return {
          pink: true
        }
      } else {
        let now = new Date().getTime();
        let day = new Date(date.year + '/' + date.month + '/' + date.day).getTime();
        if (now >= day) {
          return {
            // point: now >= day
          }
        } else {
          return {}
        }
      }
    },
    getCurrentDate() {
      let date = new Date();
      this.year = date.getFullYear();
      this.month = date.getMonth() + 1;
      let month = this.month < 10 ? '0'+ this.month : this.month
      return this.year + '-' + month;
    },
    dayText(date) {
      if (this.year === date.year &&
        this.month === date.month &&
        this.nowNum === date.day
      ) {
        return '今';
      } else {
        return date.day;
      }
    },
    dateChange(date) {
      this.getCalenDay(date);
    },
    // 获取用户打卡日历信息
    getCalenDay(date) {
      let params = {
        readPlanId: this.readPlanId,
        month: date,
      }
      this.$http.post('/pu/getClockCalendar/1.0/', params)
        .then(res=>{
          const { code, body } = res;
          if (code === '00') {
            if (Array.isArray(body)) {
              this.clockDay = body;
              this.calendarShow = true;
            }
          }
        })
    },
  }
};
</script>

<style lang='scss' scoped>
.point {
  border-radius: 50%;
  background: #DCDCDE;
}

.pink {
  border-radius: 50%;
  background: #F0716F;
  color: #fff;
}
</style>
