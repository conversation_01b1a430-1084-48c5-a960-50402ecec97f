<template>
  <div class="details">

    <div class="certificate">
      <!-- 头像 -->
      <div class="avatar">
        <!-- <img src="../../../assets/image/active/gccScholarship/avatar.png" alt=""> -->
        <img :src="params.coverWitnessHead | defaultAvatar" >
      </div>
      <div class="title">
        <p>我是上进青年{{params.coverWitnessName}}</p>
        <p>邀请你为我上进读书做见证！</p>
      </div>
      <!-- 宣言 -->
      <div class="vow">
        本人 （姓名：{{params.coverWitnessName}}）正在申请远智教育2022级广东机电职业技术学院奖学金，本人承诺秉承上进精神，爱国、爱家、爱企业、将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。
      </div>
      <!-- 见证人 -->
      <div class="witness">
        <p>
          见证人：<span>{{params.witnessName}}</span>
        </p>
        <p>
          职务：<span>{{params.workUnit}}</span>
        </p>
        <p>
          见证时间：<span>{{params.createTime | formatDate('yyyy-MM-dd')}}</span>
        </p>
        <p>
          上进点评：<span class="bold">{{params.comment}}</span>
        </p>
      </div>

      <div class="stamp">
        <img src="../../../assets/image/active/gccScholarship/stamp.png" alt="">
      </div>
    </div>
    
    <!-- 动态 -->
    <div class="dynamic">
      <witnessRecord @see="seeDynamic" />
    </div>

    <!-- <div class="footer" @click="$router.push({path:'/active/jdProfressionUniversityForward',query:{inviteId:inviteId}})">马上了解活动👉</div> -->
    <div class="footer" @click="$InviteJump('/active/jdProfressionUniversityForward/index')">马上了解活动👉</div>
    
  </div>
</template>
<script>
import witnessRecord from './components/witnessRecord'
import invite from './invite'
export default {
  components:{
    witnessRecord,
  },
  mixins:[ invite ],
  created(){
    this.params = this.$route.query;
    this.inviteId = this.$route.query.inviteId || ""
  },
  data(){
    return {
      params:{},
      inviteId:'',
    }
  },
  methods:{
    seeDynamic(item){
      this.params = item;
      if(document.documentElement.scrollTop){
        document.documentElement.scrollTop = 0;
      }else if(document.body.scrollTop){
        document.body.scrollTop=0;
      }
      // let top = document.documentElement.scrollTop = 0 || 
      // top = 0;
    }
  }
}
</script>
<style lang="less" scoped>
.details{
  background: #fff;
  margin-bottom: 0.65rem;
  .certificate {
    width: 100%;
    height: auto;
    min-height: 5.95rem;
    background-image: url('../../../assets/image/active/gccScholarship/frame.png');
    background-size: 100% 100%;
    position: relative;
    overflow: hidden;
    padding-top: 0.12rem;
    
    .avatar{
      text-align:center;
      margin-top: 0.34rem;
      img{
        display: inline-block;
        width: 0.68rem;
        height: 0.68rem;
        border-radius: 50%;
      }
    }

    .title{
      p{
        font-size: 0.16rem;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #453838;
        margin-top: 0.12rem;
        text-align: center;
      }
    }

    .vow {
      font-size: .12rem;
      color: #746A6A;
      background-image: url(../../../assets/image/active/gccScholarship/dottedframe.png);
      background-repeat: no-repeat;
      background-size: inherit;
      width: 2.83rem;
      margin:0.24rem 0.42rem 0.2rem 0.42rem;
      padding: 0.16rem;
    }

    .witness{
      margin-left: 0.42rem;
      margin-right: 0.3rem;
      p{
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color:#453838;
        
        .bold{
          color: #6E1002;
          font-weight: bold;
        }
      }
      p + p {
        margin-top: 0.1rem;
      }
    }
    
    .stamp {
      width: 1.36rem;
      height: 0.68rem;
      right: 0.22rem;
      top: 0.54rem;
      position: absolute;
      img{
        width: 100%;
      }
    }
  }

  .dynamic{
    margin-top:0.15rem;
  }

  .footer{
    position: fixed;
    bottom: 0;
    text-align: center;
    width: 100%;
    font-size: 0.16rem;
    font-family: PingFang-SC-Bold, PingFang-SC;
    font-weight: bold;
    color: #DA282F;
    background: #FBC565;
    height: 0.6rem;
    line-height: 0.6rem;
  }
}

</style>