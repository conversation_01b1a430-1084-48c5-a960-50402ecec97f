@import "./variable";

.widget-list{
  > .item{
    display:flex; align-items:center; position:relative; min-height:.48rem; padding:.06rem 0; background-color:#fff;
    &:after{ .borderBottom }
    &:last-child{
      &:after{ border-bottom:none; }
    }
    &.disabled{
      pointer-events:none;
      > .rt{ padding-right:.13rem; }
      .icons{ display:none; }
    }
    > .lf{ padding:0 .1rem 0 .12rem; white-space:nowrap; }
    > .rt{ flex:1; text-align:right; color:#888; }
    .arr-r{ width:.36rem; height:.36rem; background-image:url(../image/public_ico_open_right.png); }
  }
  .ipt{
    width:100%; height:.36rem; padding-right:.13rem; text-align:right; font-size:.14rem; color:#808080; border:none; background-color:transparent;
    &::-webkit-input-placeholder{ color:#c9c9c9; }
  }
  .fc1{ color:#f00; }
}
.tables{
  position:relative; width:100%; color:#666; font-size:.12rem;
  &:before{ .borderTop }
  &:after{ .borderLeft }
  th,
  td{
    position:relative; padding:.1rem .06rem; vertical-align:middle;
    &:before{ .borderBottom }
    &:after{ .borderRight }
  }
  th{ text-align:left; }
}
.ps{
  padding:.03rem 0 .5rem .12rem; color:#999; font-size:.13rem;
  p{ margin-top:.04rem; }
}
