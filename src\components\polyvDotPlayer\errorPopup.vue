<template>
  <div v-if="show" class="popup my-popup">
    <div class="popup-mask"></div>
    <div class="popup-content">
      <p class="title">哎呀，网络出错了</p>
      <p class="tip">（点击下方按钮,联系客服反馈）</p>
      <a class="button" @click="TO_WX_CS">联系客服</a>
    </div>
  </div>
</template>

<script>
import wxCustomerService from '@/view/aspirantUniversity/mixins/wxCustomerService.js'
export default {
  mixins: [ wxCustomerService ],
  data() {
    return {
      show: false,
    };
  },
};
</script>

<style lang="less" scoped>
.popup {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  .popup-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 1;
    // z-index: 1000 !important;
  }
  .popup-content {
    position: relative;
    // z-index: 1001 !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    .title {
      font-size: 0.14rem;
      font-weight: bold;
      color: #ffffff;
      line-height: 0.2rem;
    }
    .tip {
      font-size: 0.12rem;
      color: #a9a9a9;
      line-height: 0.16rem;
      margin: 0.04rem 0 0.16rem;
    }
    .button {
      width: 1.02rem;
      text-align: center;
      height: 0.32rem;
      line-height: 0.32rem;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      border-radius: 0.16rem;

      font-size: 0.14rem;
      font-weight: bold;
      color: #ffffff;
    }
  }
}
</style>
