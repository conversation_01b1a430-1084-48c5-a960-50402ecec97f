<template>
  <dialog-container
    v-model="show"
    :title='title'
    @close='close'
    close-on-click-overlay
  >
    <div class="yz-get-changes">
      <ul class="yellow-table one-line">
        <li class="head">通过以下方式可以获得机会</li>
        <li>
          <span>1、成功邀请<span class="red">1次</span>学历报读并缴费</span>
          <red-btn class="btn" @click='invite'>邀请</red-btn>
        </li>
        <li>
          <span>2、完成<span class="red">1次</span>学历报读缴费</span>
          <red-btn class="btn" @click='goDreamBuild'>前往</red-btn>
        </li>
        <li>
          <span>3、购买<span class="red">1门</span>上进学社课程</span>
          <red-btn class="btn" @click='go("classSelect")'>前往</red-btn>
        </li>
        <li>
          <span>4、智米商城兑换<span class="red">1个</span>物品</span>
          <red-btn class="btn" @click='goZhimi'>兑换</red-btn>
        </li>
      </ul>
    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container';
import RedBtn from './red-btn';

export default {
  components: { DialogContainer, RedBtn },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    isAppOpen: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '获得抽奖机会'
    },
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    value() {
      this.show = this.value;
    },
  },
  methods: {
    close() {
      this.$emit('input', false);
    },
    invite() {
      this.$emit('invite');
    },
    goDreamBuild() {
      if (!this.isAppOpen) {
        this.go('newDreamBuild');
        return;
      }
      this.go('dreamBuild'); // app-h5里面的链接
    },
    goZhimi() {
      if (!this.isAppOpen) {
        this.go('home');
        return;
      }
      window.location.href = 'yuanzhiapp://yzwill.cn/ZmCenter';
    },
    go(name) {
      const query = this.$route.query;
      this.$router.push({
        name,
        query: { ...query },
      });
    },
  },
};
</script>

<style lang="less">
  .yz-get-changes{
    padding: 0 0.15rem 0.2rem;
    .red{
      color: #FF1E1E;
    }
  }
</style>
