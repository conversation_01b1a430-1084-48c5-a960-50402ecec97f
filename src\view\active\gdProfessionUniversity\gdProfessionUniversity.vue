<template>
    <div class="wrap" :class="{active:isIphoneX}">
        <!--<div class="invite" v-if="showInvite">-->
            <!--<img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" alt="">-->
            <!--<div class="rightView">-->
                <!--<p>您的好友 <span>{{invite.nickName}}</span></p>-->
                <!--<p>邀请您一起来提升学历</p>-->
            <!--</div>-->
        <!--</div>-->
      <inviteTop  :inviteId="inviteId"/>
        <div class="banner">
            <img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt="">
        </div>
        <div class="content" :class="{'bg-g':tabName!='recruit'}">
            <div class="tab-3">
                <p :class="{'active':tabName=='recruit'}" @click="tabName='recruit'"><span>招生主页</span></p>
                <p :class="{'active':tabName=='introduce'}" @click="tabName='introduce'"><span>学校介绍</span></p>
                <p :class="{'active':tabName=='common'}"
                 @click="tabName='common'"><span>常见问题</span></p>
            </div>
        </div>
        <transition name="fade2">
            <div v-if="tabName=='recruit'">
                <div class="bigPackage">
                    <img class="yellowLeft" src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt="">
                    <img class="yellowRight" src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt="">
                    <img class="titleImg" src="../../../assets/image/active/gdProfessionUniversity/group.png" alt="">
                    <div class="contentText">
                      <div class="contentOne" v-for="item in content">
                      <div class="leftBox"><img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt=""></div>
                      <p class="rightBox">{{item}}</p>
                      </div>
                        <!-- <div class="contentOne" style="margin-top:1rem">
                            <div class="leftBox"><img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt=""></div>
                            <div>
                            <p class="rightBox" ref="testText">{{contentTextFirst}}</p>
                            </div>
                        </div>
                        <div class="contentOne">
                            <div class="leftBox"><img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt=""></div>
                            <p class="rightBox">三本教材(礼包价100)</p>
                        </div>
                        <div class="contentOne">
                            <div class="leftBox"><img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt=""></div>
                            <p class="rightBox">成考过295分, 减三年学费</p>
                        </div>
                        <div class="contentOne">
                            <div class="leftBox"><img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt=""></div>
                            <p class="rightBox">报读并购买礼包, 可获得1500元第二年学费抵扣券并奖励第三年学费</p>
                        </div> -->
                        <!-- <img class="priceClass" src="../../../assets/image/app/<EMAIL>" alt=""> -->
                        <!-- <p class="TipsText">(上述考分不含政策性加分)</p> -->
                        <div v-if="started" style="position: absolute;left: 30%;bottom: .7rem;">
                            <button v-if="!isPay" @click="toPayment">立即购买</button>
                            <!--<router-link v-else-->
                                     <!--:to="jumpUrl">-->
                                     <button v-if="isPay" @click="toPaymentBag">立即购买</button>
                        <!--</router-link>-->
                        </div>
                        <button style="position: absolute;left: 30%;bottom: .5rem;" v-else @click="toPayment">立即购买</button>
                    </div>
                </div>
                <div class="charge">
                    <div class="chargetop">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <p>院校专业及收费</p>
                    </div>

                </div>
                <div class="highUniversityBox">
                    <div class="highUniversity">
                        <div class="title">
                            <span class="text">- 高起专 -</span>
                        </div>
                        <div class="contentThree">
                            <div class="titleText">
                               <p>文史</p>
                            </div>
                            <div class="flLeft">
                                <p class="contentText">
                                   市场营销
                            </p>
                            </div>
                            <div class="flRight">
                                <p class="top">2300</p>
                                <p class="bottom">元/学年</p>
                            </div>
                        </div>
                        <div class="contentThree">
                            <div class="titleText">
                               <p>理工类</p>
                            </div>
                            <div class="flLeft">
                                <p class="contentText">
                            现代纺织技术
                            </p>
                            </div>
                            <div class="flRight">
                                <p class="top">2645</p>
                                <p class="bottom">元/学年</p>
                            </div>
                        </div>
                    </div>
                    <p style="padding-left:.2rem;margin-top:.05rem;color: rgba(23, 6, 6, 0.6)"><span style="color:#F06E6C">•</span> 书杂费:&ensp;400元/学年(按2.5年收取)</p>
                    <p style="padding-left:.2rem;color: rgba(23, 6, 6, 0.6)"><span style="color:#F06E6C">•</span> 学制:&ensp;3年</p>

                    <!-- <p  style="color: rgba(23, 6, 6, 0.6);padding-left:.2rem;float:left;">
                    <span style="color:#F06E6C">•</span> 考区:</p>
                    <p style="width:75%;float:left;color:rgba(23,6,6,.6);    margin-left: .07rem;"> 惠州、广州、深圳、东莞、阳江、梅州、肇庆、湛江、汕尾、潮州、韶关、清远、汕头、河源、佛山、茂名、江门</p> -->
                </div>
                <div class="story">
                    <div class="top">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <span>上进故事</span>
                    </div>
                    <div class="content">
                        <a @click="toStory(item.scholarshipId,inviteId,item.resourcesUrl,item.createTime)" class="item" v-for="(item,index) in storyList" :key="index">
                            <img :src= item.articlePicUrl+storyImgLimit|imgBaseURL>
                            <p class="text">{{item.articleTitle}}</p>
                            <p class="date">{{item.createTime.substring(0,11)}}</p>
                        </a>
                    </div>
                </div>
                <!-- <div class="userMessage">
                    <div class="top">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <span class="topTitle">留言区</span>
                    </div>
                    <div class="textContent">
                        <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                            <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                                <div class="fl">
                                    <img :src= item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2 alt="">
                                </div>
                                <div class="fr">
                                    <p class="userName">{{item.nickname|hideNickname}}</p>
                                    <div class="bottom" v-if= item.msgReply>
                                        <p class="uesrQuestion">{{item.msgContent}}</p>
                                        <div class="content" >
                                            <div class="line"></div>
                                            <p class="answer"><span>回复:&nbsp;</span>{{item.msgReply}}</p>
                                        </div>
                                    </div>
                                    <div class="bottom2" v-if= !item.msgReply style="margin-top:.16rem">
                                        <p class="uesrQuestion">{{item.msgContent}}</p>
                                    </div>
                                </div>
                                <div class="line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="userMessageContent">
                        <div class="fl">
                            <img :src= userImg?userImg+headLimit:userImg|defaultAvatar2 alt="" >
                        </div>
                        <div class="fr">
                            <p class="userName">{{userName}}</p>
                            <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                            <span>{{message.length}}/50</span>
                            <button @click="enrollMsg()">提交</button>
                        </div>
                    </div>
                </div> -->
            </div>
        </transition>
        <transition name="fade2">
            <div v-if="tabName=='introduce'">
                <div class="schoolDetails" :class="{showall:true,active:showall}">
                    <img src="../../../assets/image/active/gdProfessionUniversity/<EMAIL>" alt="">
                    <p class="schoolDetailsText">
                      广东职业技术学院是由广东省人民政府举办的全日制普通高等职业院校，行政主管部门是广东省教育厅。其前身为广东省纺织工业学校，筹建于1984年,1985年正式招生，1993年被评为省级重点中专学校，2000年被评为国家级重点中专学校，2001年升格为广东纺织职业技术学院。2012年，经广东省人民政府批准，学校更名为广东职业技术学院。
                    </p>
                  <p class="schoolDetailsText">
                    学校现有禅城、高明、南海三个校区，校园总面积1227亩，建筑面积23万多平方米，固定资产总值约达6.8亿元，图书馆各类藏书达到80余万册。目前设有8系1院2部，即纺织系、轻化工程系、服装系、艺术设计系、机电工程系、信息工程系、经济管理系、应用外语系、马克思主义学院、公共课教学部、继续教育部。学校开设了64多个专业方向，校内外实训基地208个，在校生1万3千多人。
                  </p>
                </div>
                <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="!showall">
                    <a :class="{active:showall}" @click="lookMore">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <span>查看更多</span>
                    </a>
                </div>
                <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="showall">
                    <a :class="{active:showall}" @click="lookMoreCancle">
                        <span class="down"></span>
                        <span>收起</span>
                    </a>
                </div>
                <div class="studentStory">
                    <div class="top">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <span class="topTitle">学员风采</span>
                    </div>
                  <div class="swiper">
                    <swiper :options="swiperOption">
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/1.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/9.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/2.png"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/3.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/4.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/5.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/6.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/7.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/8.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/10.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/11.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/12.jpg"
                        >
                      </swiper-slide>
                      <swiper-slide>
                        <img
                          class="swiperImg"
                          src="../../../assets/image/active/fullTimeSystem/student/13.jpg"
                        >
                      </swiper-slide>
                    </swiper>
                  </div>
                </div>
                <!-- <div class="userMessage">
                    <div class="top">
                        <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
                        <span class="topTitle">留言区</span>
                    </div>
                    <div class="textContent">
                        <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                            <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                                <div class="fl">
                                    <img :src= item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2 alt="">
                                </div>
                                <div class="fr">
                                    <p class="userName">{{item.nickname|hideName}}</p>
                                    <p class="uesrQuestion">{{item.msgContent}}</p>
                                    <div class="content" v-if= item.msgReply>
                                        <div class="line"></div>
                                        <p class="answer"><span>回复:&nbsp;</span>{{item.msgReply}}</p>
                                    </div>
                                </div>
                                <div class="line"></div>
                            </div>
                        </div>
                    </div>
                    <div class="userMessageContent">
                        <div class="fl">
                            <img :src= userImg?userImg+headLimit:userImg|defaultAvatar2 alt="" >
                        </div>
                        <div class="fr">
                            <p class="userName">{{userName}}</p>
                            <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                            <span>{{message.length}}/50</span>
                            <button @click="enrollMsg">提交</button>
                        </div>
                    </div>
                </div> -->
            </div>
        </transition>
        <transition name="fade2">
            <div v-if="tabName=='common'" style="padding-bottom: .6rem">
                 <div class="texta">
                     <van-collapse v-model="activeName" accordion>
                            <van-collapse-item name="1">
                                <div slot="title"><span class="indexText">Q1</span>自考会不会比较好</div>
                                    自考与成考都属于成人教育系列，均是国家承认的学历，证书学信网可查。
                            </van-collapse-item>
                            <van-collapse-item name="2">
                                <div slot="title"><span class="indexText">Q2</span>有课上吗？</div>
                                    有的。购买了上进礼包后，会包含辅导课在里面的。
                            </van-collapse-item>
                            <van-collapse-item name="3">
                                <div slot="title"><span class="indexText">Q3</span>怎么查询是否报考成功呀？</div>
                                    您可以在公众号-学员服务-远智学堂中查看状态，状态为“考前确认”即表示报考成功。
                            </van-collapse-item>
                            <van-collapse-item name="4">
                                <div slot="title"><span class="indexText">Q4</span>考不上怎么办？</div>
                                    考试不难的，只要去考，认真答完试卷基本都能通过。
                            </van-collapse-item>
                            <van-collapse-item name="5">
                                <div slot="title"><span class="indexText">Q5</span>应该很难考的吧，没考上可以不读吧？</div>
                                    考试不难的，在远智辅导的学员，参加成考的通过率高达93.8%。若真的没考上，可以选择不读。
                            </van-collapse-item>
                            <van-collapse-item name="6">
                                <div slot="title"><span class="indexText">Q6</span>本科毕业的也能报吗，搞个双学位</div>
                                    可以的。
                            </van-collapse-item>
                            <van-collapse-item name="7">
                                <div slot="title"><span class="indexText">Q7</span>一共考几科？</div>
                                    成人高考一共考3科，专科考“语文、数学、英语”，本科考“政治、英语、专业科”。
                            </van-collapse-item>
                            <van-collapse-item name="8">
                                <div slot="title"><span class="indexText">Q8</span>可以自选居住地的考点吗？</div>
                                    选择了哪个城市就在哪个城市参考。
                            </van-collapse-item>
                            <van-collapse-item name="9">
                                <div slot="title"><span class="indexText">Q9</span>有什么专业可选？</div>
                                    可以在链接中点击自己所在的城市查看。
                            </van-collapse-item>
                            <van-collapse-item name="10">
                                <div slot="title"><span class="indexText">Q10</span>户口不在广东了可以考吗？</div>
                                    在广东工作就可以，不受户口限制。
                            </van-collapse-item>
                            <van-collapse-item name="11">
                                <div slot="title"><span class="indexText">Q11</span>已经报名，然后咧？</div>
                                    报名后只需要利用碎片时间复习即可，另外会有助学老师跟进你的学习情况和对你入学前的指导。
                            </van-collapse-item>
                            <van-collapse-item name="12">
                                <div slot="title"><span class="indexText">Q12</span>报名了，要缴费吗？</div>
                                    筑梦计划是免费报名的。可以自愿选择是否购买299元的上进礼包。
                            </van-collapse-item>
                            <van-collapse-item name="13">
                                <div slot="title"><span class="indexText">Q13</span>免费报名的没辅导课辅导书，自己复习？</div>
                                    是的。
                            </van-collapse-item>
                            <van-collapse-item name="14">
                                <div slot="title"><span class="indexText">Q14</span>要交考试费啊？</div>
                                    考试费是由教育局收取的，37元/科，3科合计111元，在9月初现场确认时缴纳。
                            </van-collapse-item>
                            <van-collapse-item name="15">
                                <div slot="title"><span class="indexText">Q15</span>中专可以升大专吗？</div>
                                     中专已取得毕业证的同学可以报读成人大专。
                            </van-collapse-item>
                            <van-collapse-item name="16">
                                <div slot="title"><span class="indexText">Q16</span>我在读大专，明年拿证，可以报本科吗？</div>
                                    如果是在明年3月1日前能取得大专毕业证的话，那么可以以应届生的身份参加今年的成人高考。
                            </van-collapse-item>
                            <van-collapse-item name="17">
                                <div slot="title"><span class="indexText">Q17</span>报名要交什么资料的吗？</div>
                                    报名需要填写相关的个人报读信息以及提供身份证正反面、毕业证复印件。
                            </van-collapse-item>
                            <van-collapse-item name="18">
                                <div slot="title"><span class="indexText">Q18</span>在职工作是不是真的可以报读？</div>
                                    可以。
                            </van-collapse-item>
                            <van-collapse-item name="19">
                                <div slot="title"><span class="indexText">Q19</span>去哪里上课的？必须要去学校吗？</div>
                                     课程我们都是安排在周末或者晚上，通过手机直播上课或者是通过我们的远程上课平台学习即可。无需到学校去。
                            </van-collapse-item>
                            <van-collapse-item name="20">
                                <div slot="title"><span class="indexText">Q20</span>考试是闭卷的还是开卷考？</div>
                                   闭卷考。
                            </van-collapse-item>
                            <van-collapse-item name="21">
                                <div slot="title"><span class="indexText">Q21</span>初中毕业能报名吗？</div>
                                    抱歉，初中学历不符合报读资格。需要中专或者高中以上学历才可以。
                            </van-collapse-item>
                            <van-collapse-item name="22">
                                <div slot="title"><span class="indexText">Q22</span>你好，教了资料费后怎么领资料？</div>
                                    会有老师在2个工作日内联系你，跟你确认报读信息以及收件地址。
                            </van-collapse-item>
                            <van-collapse-item name="23">
                                <div slot="title"><span class="indexText">Q23</span>如考试未达295，那学费是多少？</div>
                                   如未达295分，学费按全价收取，相关院校专业的费用请在链接中查看。
                            </van-collapse-item>
                            <van-collapse-item name="24">
                                <div slot="title"><span class="indexText">Q24</span>报名成功了，没说什么时候考试的？</div>
                                   报名成功后，在今年9月初现场确认办理准考证，10月中下旬参加成人高考。
                            </van-collapse-item>
                            <van-collapse-item name="25">
                                <div slot="title"><span class="indexText">Q25</span>什么是筑梦计划？</div>
                                   筑梦计划是远智教育联合各合作高校为广大上进青年提供的一个提升学历的公益项目。
                            </van-collapse-item>
                        </van-collapse>
                 </div>
            </div>
        </transition>
    <!--<div class="fixBottom">-->
      <!--<div class="leftBox">-->
        <!--<img src="../../../assets/image/active/newDreamBuild/default_photo.png" alt />-->
        <!--<span class="textOne">远智教育</span>-->
        <!--<span class="textTwo">来远智0元读大专本科</span>-->
      <!--</div>-->
      <!--<div class="rightBox">-->
        <!--<div class="phoneIcon" @click="phoneNumber()">-->
          <!--<img src="../../../assets/image/active/newDreamBuild/icon_iphone.png" alt />-->
          <!--<p>咨询</p>-->
        <!--</div>-->
        <!--<div class="line"></div>-->
        <!--<router-link :to="jumpUrl" v-if="started">-->
          <!--<div class="signUpIcon">-->
            <!--<img src="../../../assets/image/active/newDreamBuild/icon_signup.png" alt />-->
            <!--<p>免费报读</p>-->
          <!--</div>-->
        <!--</router-link>-->
        <!--<a v-else @click="tips">-->
          <!--<div class="signUpIcon">-->
            <!--<img src="../../../assets/image/active/newDreamBuild/icon_signup.png" alt />-->
            <!--<p>免费报读</p>-->
          <!--</div>-->
        <!--</a>-->
      <!--</div>-->
    <!--</div>-->
      <o-footer tabName="newintroduce" :Expired="enrollEnd" @isIphoneX="isIphoneX=true" :actName="actName" from="gz" :unvs="unvs" :scholarship="scholarship"></o-footer>
      <share :title="shareObj.title" :isActivity="true" :desc="shareObj.desc" :link="shareLink" :scholarship="scholarship" :imgUrl="shareObj.img" ref="share"/>
    </div>
</template>

<script>


import { swiper, swiperSlide } from 'vue-awesome-swiper';
import {toLogin, isEmployee, getIsInTimeByType} from '../../../common';
import share from '@/components/active/lotteryShare';
import { Collapse, CollapseItem } from 'vant';
import {imgBaseURL,activityTime} from "../../../config";
import oFooter from "@/components/activePage/footer"
import inviteTop from "../enrollAggregate/components/invite-top"
let scholarship = '164'; //优惠类型

export default {
    data() {
        return {
            tabName:'recruit',
            activeName: '',
            animatePraise:false,
            animate:false,
            isIphoneX:false,
            message:'',
            shareLink:'',
            isLogin: null,
            scholarship:scholarship,
            inviteId:'',
            isPay:true,
            started:true,
            learnInfo:'',
            userName:'',
            showall:false,
            userImg:'',
            showInvite:false,
            items:[],
            invite:{},
            enrollMsgList:[],
            enrollEnd:false,
            storyList:[],
            hiddleLotteryTime:1548680400000, //2019/1/28/21:00 隐藏抽奖入口
            swiperOption:{
                initialSlide:1,
                autoplay: 2000,
                centeredSlides: true,
                loop:true,
                slidesPerView: "auto",
                loopedSlides: 1,
                autoplayDisableOnInteraction : false
            },
            contentTextFirst:'考前辅导课(礼包价99)',
            shareObj:{title:'读学历来广东职业技术学院，免费报名，学信网可查！',desc:'',img:''},
            content:["考前辅导课（礼包价199）","三本辅导教材（礼包价100）"],
            storyImgLimit:'?x-oss-process=image/resize,m_fixed,h_122,w_155',
            loadingFlag:true,
            headLimit:"?x-oss-process=image/resize,m_fixed,h_38,w_38",
            actName:'',
            activityContent:{},
            isEmployee:isEmployee(),
            unvs:{
              unvsName:'广东职业技术学院',
              unvsId:'155132322430854822',
            },
            isInvite5MonthActive: getIsInTimeByType('decisive'), // 邀约2020-5月活动
        }
    },

    created() {
        let nowTime = new Date().getTime();
        //let startTime = 1558087200000 // 4月1日切换
        let startTime = activityTime // 4月1日切换
        if(nowTime > startTime) {
              scholarship='164';
              this.scholarship ='164';
        }
        this.getContent();
        document.title = '广东职业技术学院';
        this.isLogin = !!this.storage.getItem('authToken');
        this.inviteId = this.$route.query.inviteId || '',
        this.userName = this.storage.getItem("realName") || this.storage.getItem('zmcName')||this.storage.getItem('mobile');
        this.userImg = this.storage.getItem("headImg") || '';
        this.action = this.$route.query.action || '';
        this.shareLink = `${window.location.origin}/active/gdProfessionUniversity`;
        setInterval(this.scroll,3000);
        // setInterval(this.scrollText,3000);
        if(Date.now()>1552190400000&&Date.now()<1552644000000){//2019年3月10日12点
          this.flag=false;
        }
        this.getActivityInfo();
    },
    components:{
        Collapse,
        CollapseItem,
        swiper,
        swiperSlide,
        share,
      oFooter,
      inviteTop
    },
    computed: {
        jumpUrl: function () {
            let url = {
                name:'adultExamEnrollCheck',
                query: {
                    activityName: 'scholarship',
                    inviteId: this.inviteId,
                    action: 'login',
                    scholarship: this.scholarship,
                    actName:this.actName,
                    recruitType:'1',
                    unvs:JSON.stringify({
                        unvsName:'广东职业技术学院',
                        unvsId:'155132322430854822',})
                }
            };
            return url;
        }
    },
    mounted:function () {
        window.addEventListener('scroll',this.handleScroll,true);
        //获取邀约人信息
        if(!!this.inviteId) {
            this.getInviteInfo();
        }
    },
    methods: {
    getActivityInfo() {
      this.$http
        .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarship })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          this.actName = body.actName;
          if(Date.now() > body.EndTime){
            this.enrollEnd=true
          }
        });
    },

      //监听滑动的距离
      handleScroll() {
        let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
        if(this.loadingFlag) {
            if(scrollTop > 330) {
              this.getEnrollMsgList();
              this.getStoryList();
              this.loadingFlag = false;
          }
        }
      },
      toStory(id,inviteId,resourcesUrl,createTime){
        if(window.__wxjs_environment === 'miniprogram') {
          wx.miniProgram.navigateTo({
            url: '/pages/scholarshipStory/main?id='+id+"&inviteId="+inviteId
          })
          return;
        }else{
          this.$router.push({name:'scholarshipStoryInfo',query:{id:id,inviteId:inviteId,resourcesUrl,createTime}})
        }
      },
      //获取礼包和分享内容
      getContent(){
          // this.$http.post('/bds/getActivityContentConfig/1.0/',{type:2,scholarship:scholarship,unvsId:'155132322430854822'}).then(res=>{
          //   if(res.code=='00'){
          //     this.activityContent=res.body;
          //   }
          // })
        this.$http.post('/bds/getActivityOtherConfig/1.0/',{typeCode:'2_guangzhi'}).then(res=>{
          if(res.code==='00'){
            // this.shareObj.title=res.body.title;
            this.shareObj.desc=res.body.content;
            this.shareObj.img='http:' + imgBaseURL+res.body.picUrl;
            this.$nextTick(res=>{
              this.$refs.share.openNO();
            })
          }
        })
        // .then(res=>{
        //   this.$http.post('/bds/getActivityOtherConfig/1.0/',{typeCode:'1_guangzhi'}).then(res=>{
        //     if(res.code==='00'){
        //       if(res.body.content){
        //         this.content=res.body.content.split("\n");
        //       }
        //     }
        //   })
        // })
      },
        // 获取学员基础信息
        getStdLearnInfo: function () {
            if (!this.isLogin) {
            return
            }
            let param = {grade:'2020',scholarship:this.scholarship};
            this.$http.post('/mkt/stdLearnInfo/1.0/',param).then(res => {
            let {code, body} = res;
            this.userName = body.std_name;
            this.userImg = body.headImg;
            if (code !== '00') return;
            let learnInfos = body.learnInfos.find(item => (item.scholarship == scholarship && item.grade == '2020')) || {};
            this.learnInfo = learnInfos;
            if (!!learnInfos.scholarship) {
                this.getPayInfo(learnInfos.learnId);
            }
            });
        },

        // 获取邀约人信息
        getInviteInfo() {
            let inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || this.getQueryString(this.redirect, 'inviteId') || '')).replace(/ /g, '+');
            if(inviteId){
            this.$http.post('/us/getInviteInfo/1.0/', {inviteToken: inviteId}).then(res => {
                let {code, body} = res;
                if (code !== '00') return;
                this.invite = body||{};
                this.showInvite = true;
            });
            }
        },
        // 点击查看更多
        lookMore:function () {
            this.showall = true;
        },
        lookMoreCancle :function () {
            this.showall = false;
        },

        // 获取学员基础信息
        getPayInfo: function (learnId) {
            let data = {
            learnId: learnId,
            itemCode: 'Y0'
            }
            this.$http.post('/mkt/selectTutionPaidCountByLearnId/1.0/', data).then(res => {
            let {code, body} = res;
            if (code !== '00') return;
            this.isPay = body.ifPay == '1' ? true : false;
            });
        },

        //购买教材
        toPayment: function () {
            this.$router.push({
            name: 'stuPayment',
            query: {learnId: this.learnInfo.learnId, activityName: 'dreamBuildScholarship', unvsid: this.learnInfo.unvsId}
            });
        },
      toPaymentBag: function() {
        this.$router.push({
          name: "adultExamEnrollCheck",
          query: {
            activityName: "scholarship",
            inviteId: this.inviteId,
            action: "login",
            scholarship: this.scholarship,
            actName:this.actName,
            recruitType:'1',
            unvs:JSON.stringify({
              unvsName:'广东职业技术学院',
              unvsId:'155132322430854822',})
          }
        });
      },
        //邀请好友报名
        openShare: function () {
          this.$refs.share.open(null, 'login', `${this.$route.fullPath}${this.$route.fullPath.includes('?') ? '&' : '?'}action=share`);
        },
        tips() {
            this.$modal({message: '活动暂未开始！', icon: 'warning'});
        },
        lotterytips(){
            let now = new Date().getTime();
            let stop = 1548680400000;// 2019/1/28 21:00 抽奖入口关闭
            if(now > stop) {
                this.$modal({message: '抽奖活动已经下线！', icon: 'warning'});
            }else {
                this.$router.push({path:'/active/iphoneXLotteryThr'})
            }

        },

        // 获取留言列表
        getEnrollMsgList: function () {
            this.$http.post('/mkt/getEnrollMsgList/1.0/', {scholarship: scholarship}).then(res => {
            let {code, body} = res;
            if (code === '00') {
              this.enrollMsgList = body;
           }
         });
        },
      // 评论
      enrollMsg: function () {
        if (!this.isLogin) {
          toLogin.call(this, null);
          return
        }
        if (!this.message) {
          this.$modal({message: '请输入评论内容', icon: 'warning'});
          return
        }
        if (this.isEmployee) {
          this.$modal({message: '招生老师不能评论！', icon: 'warning'});
          return
        }
        this.$http.post('/mkt/enrollMsg/1.0/', {scholarship: scholarship, msgContent: this.message}).then(res => {
          let {code, body} = res;
          if (code === '00') {
            this.$modal({
              message: '提交成功', beforeClose: (action, instance, done) => {
                done();
                this.message = '';
                this.getEnrollMsgList();
              }
            });
          }
        });
      },

       // 获取故事列表
      getStoryList() {
        this.$http.post('/mkt/scholarshipStoryList/1.0/',{informationType:1}).then(res => {
          const {code, body} = res;
          if (code === '00') {
            this.storyList = body;
          }
        });
      },

      // 轮播用户评论
      scroll() {
            if(!this.enrollMsgList.length || this.enrollMsgList.length <= 3){
                return
            }
            this.animatePraise = true;
            setTimeout(() =>{
                this.enrollMsgList.push(this.enrollMsgList[0]);
                this.enrollMsgList.shift();
                this.animatePraise = false;
            },500)
        },

      // 轮播文字
      scrollText() {
        this.animate = true;
        setTimeout(() => {
          this.items.push(this.items[0]);
          this.items.shift();
          this.animate = false;
        }, 500)
      },
      // 获取最新注册用户列表
      getNewRegList: function () {
        this.$http.post('/us/getNewRegList/1.0/').then(res => {
          let {code, body} = res;
          if (code === '00') {
            this.items = body;
          }
        });
      },
    },
  beforeDestroy(){
    window.removeEventListener('scroll',this.handleScroll,true);
  }
}
</script>


<style lang="less" scoped>
    .texta {
        .van-collapse {
            .van-collapse-item {
                .van-cell {
                    line-height: .54rem;
                    height: auto;
                    .van-cell__right-icon {
                        margin-top: 3%;
                    }
                    .indexText {
                        width: .15rem;
                        height: .17rem;
                        background:rgba(240,110,108,0.1513);
                        border-radius:8px 8px 8px 0px;
                        color: #F06E6C;
                        font-size: .12rem;
                        margin-left:.17rem;
                        margin-right: .09rem;
                    }
                }
                .van-collapse-item__wrapper {
                        .van-collapse-item__content {
                        padding: .13rem .56rem .13rem .44rem;
                        color:rgba(23,6,6,0.8);
                        }
                    }

                }
            }
        }
    .wrap {
        position: relative;
        background: rgb(246, 246, 246);
      &.active{
        padding-bottom: 34px;
      }
        .invite {
            background-image: url('../../../assets/image/active/enrollmentHomepage/invitBg2.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
            height: .68rem;
            position: relative;
            margin-bottom: -.1rem;
            z-index: 99;
            img {
                width: .48rem;
                height: .48rem;
                float: left;
                border-radius: 50%;
                margin-top: 1.8%;
                margin-left: .12rem;
                margin-right: .07rem;
            }
            .rightView {
                float: left;
                margin-top: 2.3%;
                p {
                    font-size: .16rem;
                span {
                    color: #e15443;
                    font-size: .13rem;
                }
                &:first-of-type {
                    font-size: .13rem;
                }
                }
            }
        }
        .banner {
            img {
                height: 1.11rem;
                width: 3.75rem;
            }
        }
        .content {
            &.bg-g {
                background-image: none;
            }
            .tab {
                height: .5rem;
                margin-top: -.02rem;
                background:rgba(246,246,246,1);
            p {
                float: left;
                height: .38rem;
                width: 25%;
                margin-top: .06rem;
                text-align: center;
                padding: .10rem 0;
            span {
                display: inline-block;
                width: 100%;
                border-right: .01rem rgba(23, 6,6, 0.09);
                font-size: .14rem;
                color:rgba(23,6,6,0.8);
            }
            &.active {
                padding: .0rem;
                line-height: .38rem;
                span {
                position: relative;
                width: auto;
                border-right: none;
                color: #F06E6C;
                border-radius: .02rem;
                &:before {
                    content: '';
                    height: .02rem;
                    background-color: #F06E6C;
                    width: .3rem;
                    top:.42rem;
                    transform: translateX(-50%);
                    left: 50%;
                    position: absolute;
                    border-radius: 2px;
                }
            }
        }
            &:last-of-type {
                span {
                    border-right: none;
                }
              }
            }
         }

         .tab-3 {
                height: .5rem;
                margin-top: -.02rem;
                background:rgba(246,246,246,1);
            p {
                float: left;
                height: .38rem;
                width: 33.3%;
                margin-top: .06rem;
                text-align: center;
                padding: .10rem 0;
            span {
                display: inline-block;
                width: 100%;
                border-right: .01rem rgba(23, 6,6, 0.09);
                font-size: .14rem;
                color:rgba(23,6,6,0.8);
            }
            &.active {
                padding: .0rem;
                line-height: .38rem;
                span {
                position: relative;
                width: auto;
                border-right: none;
                color: #F06E6C;
                border-radius: .02rem;
                &:before {
                    content: '';
                    height: .02rem;
                    background-color: #F06E6C;
                    width: .3rem;
                    top:.42rem;
                    transform: translateX(-50%);
                    left: 50%;
                    position: absolute;
                    border-radius: 2px;
                }
            }
        }
            &:last-of-type {
                span {
                    border-right: none;
                }
              }
            }
         }
        }

        .lottery {
            position: absolute;
            left: 2.76rem;
            float: left;
            width: .94rem;
            height: .86rem;
            top: .73rem;
            border: 0;
        }

        .bigPackage {
            position: relative;
            width: 100%;
            height: 4.5rem;
            background-color: #fff;
            // background:linear-gradient(360deg,rgba(0,99,213,1) 0%,rgba(25,128,213,1) 100%);
            text-align: center;
            .titleImg {
                margin: 0 auto;
                width: 2.3rem;
            }
            .yellowLeft {
                position:absolute;
                left: 0;
                top: .51rem;
                width: .32rem;
            }
            .yellowRight {
                position:absolute;
                right: .11rem;
                top: .76rem;
                width: .4rem;
            }
            .contentText {
                width: 3.45rem;
                height: 3.7rem;
                margin: 0 auto;
                background: url('../../../assets/image/active/gdProfessionUniversity/contentBg.png') no-repeat center center;
                background-size: 3.85rem;
                margin-top: -.8rem;
                box-shadow:0px 4px 20px 0px rgba(23,6,6,0.1);
                border-radius: .05rem;
                .contentOne {
                    position: relative;
                    display: inline-block;
                    width: 2.57rem;
                    background-color: #fff;
                    margin-top: .25rem;
                    min-height: .3rem;
                    border-radius:.04rem;
                    border:1px solid rgba(255,184,13,1);
                    .leftBox {
                        width: .32rem;
                        position: absolute;
                        background:rgba(255,184,13,1);
                        top:0;
                        left: 0;
                        height: 100%;
                        img {
                            width: .12rem;
                            vertical-align: middle;
                            padding-top: .06rem;
                        }
                    }
                    .rightBox {
                        width: 2.25rem;
                        float:right;
                        text-align: left;
                        padding-left: .1rem;
                        padding-right: .1rem;
                        // border-radius:0 .04rem .04rem 0;
                        padding-top: .06rem;
                        padding-bottom: .04rem;

                    }
                }
              .contentOne:first-child{
                margin-top: 1.1rem;
              }
                .TipsText {
                    font-size: .14rem;
                    width: 2.57rem;
                    margin: 0 auto;
                    text-align: left;
                    color:rgba(23,6,6,.8);
                }
                .priceClass {
                    position:absolute;
                    width: 1.1rem;
                    height: 1.19rem;
                    bottom: .3rem;
                    right: .18rem;
                }
                button {
                    // background: url('../../../assets/image/active/gdProfessionUniversity/buttonbg.png') no-repeat center center;
                    background-size: 1.84rem;
                    width: 1.45rem;
                    height: .45rem;
                    margin: 0 auto;
                    border: 0;
                    color:#fff;
                    font-size: .18rem;
                    font-weight:bold;
                    background:rgba(255,184,13,1);
                    box-shadow:0px 2px 4px 0px rgba(255,184,13,0.45);
                    border-radius:23px;
                    margin-top: .1rem;
                }
            }

        }
        .textContentJoin {
          left: .05rem;
          box-shadow:0px .04rem .2rem 0rem rgba(0,0,0,0.2);
          bottom: 0.29rem;
          width: 2.35rem;
          height: .3rem;
          overflow: hidden;
          background: rgb(255, 255, 255);
          position:fixed;
          z-index: 999;
          border-radius: .19rem;
          .gdBox {
            width: 100%;
            background-color: rgba(0,0,0,0);
            :first-child {
            }
            p {
                font-size: .1rem;
                height: .3rem;
                line-height: .28rem;
                display: flex;
                justify-content: center;
                -webkit-justify-content: center;
                position: relative;
                box-shadow:0rem .04rem .2rem 0rem rgba(0,0,0,0.2);
                border-radius:.19rem;
              img {
                width: .3rem;
                height: .3rem;
                border-radius: 50%;
                margin-top: 0;
                position:absolute;
                left: 0;
              }
              span {
                margin-left: .1rem;
                display: inline-block;
                font-size: .12rem;
                float: left;
                color: black;
                &:first-of-type {
                  max-width: .7rem;
                  overflow: hidden;
                  height: .2rem;
                  text-overflow: ellipsis;
                }
              }
              .name {
                  width: .55rem;
                  margin-left: .25rem;
                  color: #F06E6C;
              }
              .mobile {
                  margin-left: 0rem;
                  color: #F06E6C
              };
            }
            &.anim {
              transition: all 1s;
              margin-top: -.38rem;

            }
          }
        }

        .charge {
            background: rgb(255, 255, 255);
            .chargetop {
                background: #fff;
                width: 3.75rem;
                height: .6rem;
                line-height: .6rem;
                img {
                    width: .32rem;
                    height: .32rem;
                    vertical-align:middle;
                    margin-left: .1rem;
                    margin-top: -.05rem;
                }
                p {
                    display:inline-block;
                    font-size: .17rem;
                    height: .24rem;
                    line-height: .24rem;
                    color:#170606
                }
            }

             .region {
                 margin-top: .05rem;
                .regionTop {
                    position: relative;
                    height: .24rem;
                    line-height: .24rem;
                    .line {
                        display: inline-block;
                        width: .04rem;
                        height: .16rem;
                        background-color: #F06E6C;
                        margin-left: .4rem;
                    }
                    .regionTitle {
                        position:absolute;
                        display: inline-block;
                        font-size:14px;
                        color:rgba(23,6,6,1);
                        font-weight:400;
                        left: .54rem;
                        top: -0.02rem;
                    }
                }
                .regionText {
                    p {
                        margin-left: .54rem;
                        display: inline-block;
                        width: 2.94rem;
                        font-size: 14px;
                    }
                }
            }
        }

        .highUniversityBox {
            padding-top: .08rem;
            background: #fff;
            padding-bottom: .1rem;
            overflow: hidden;
            .title {
                background: #fff;
                width: 100%;
                height: .48rem;
                .fl {
                    width: 50%;
                    height: .48rem;
                    float: left;
                    position: relative;
                    .flTop {
                        height: .21rem;
                        line-height: .21rem;
                         .line {
                            position: absolute;
                            display: inline-block;
                            width: .04rem;
                            height: .04rem;
                            background-color: #F06E6C;
                            top: .05rem;
                            left: 23%;
                        }
                        .schoolType {
                            position:absolute;
                            display: inline-block;
                            font-size:14px;
                            color:rgba(23,6,6,.4);
                            font-weight:400;
                            left: .54rem;
                            top: -0.02rem;
                        }
                    }
                    .year {
                        padding-left: .54rem;
                        font-size: .2rem;
                        color:rgba(23,6,6,.6);
                    }
                    span {
                        color: #F06E6C;
                        color:rgba(23,6,6,.6);
                    }
                }
                .fr {
                    width: 50%;
                    height: .48rem;
                    float: left;
                    position: relative;
                    .frTop {
                        height: .21rem;
                        line-height: .21rem;
                         .line {
                            position: absolute;
                            display: inline-block;
                            width: .04rem;
                            height: .04rem;
                            background-color: #F06E6C;
                            top: .05rem;
                            left: 14%;
                        }
                        .bookPrice {
                            position:absolute;
                            display: inline-block;
                            font-size:14px;
                            color:rgba(23,6,6,.4);
                            font-weight:400;
                            left: .35rem;
                            top: -0.02rem;
                            span {
                                font-size: 12px;
                                color:rgba(23,6,6,0.4);
                            }
                        }
                    }
                    .bookPriceYear {
                        padding-left: .35rem;
                        font-size: .2rem;
                        color:rgba(23,6,6,.6);
                    }
                    span {
                        color: #F06E6C;
                        color:rgba(23,6,6,.6);
                    }

                }
            }
             .highUniversity {
                margin: auto .1rem;
                background-color: #fff;
                border-radius: .1rem;
                border:1px solid rgba(240,110,108,.4);
                .title {
                    // width: 3.35rem;
                    height: .5rem;
                    line-height: .5rem;
                    text-align: center;
                    border-radius:.1rem .1rem 0rem 0rem;
                    background:rgba(240,110,108,.0798);
                    .lineLeft {
                        width: .1rem;
                        height: .02rem;
                        border-radius: .01rem;
                        background: #F06E6C;
                    }
                    .text {
                        font-size: .17rem;
                        color: #F06E6C;
                        font-weight: 600;
                    }
                }
                .content {
                    border-top: 1px solid rgba(240,110,108,.4);
                    overflow: hidden;
                    position: relative;
                    .titleText {
                        width: .7rem;
                        height: .8rem;
                        float: left;
                        border-right: 1px solid rgba(240,110,108,.4);
                        display: inline-block;
                        p {
                            display: inline-block;
                            width: .7rem;
                            height: .42rem;
                            text-align: center;
                            margin-top: 55.5%;
                            font-weight: 600;
                            font-size: .15rem;
                            color:rgba(23,6,6,.61);
                        }
                    }
                    .flLeft {
                        width: 2.11rem;
                        height: .8rem;
                        float: left;
                        border-right: 1px solid rgba(240,110,108,.4);
                        .contentText {
                            display: inline-block;
                            width: 1.86rem;
                            margin: .1rem .17rem .1rem .13rem;
                            font-size: .14rem;
                            color:rgba(23,6,6,.8);
                        }
                    }
                    .flRight {
                        float: left;
                        width: .5rem;
                        text-align: center;
                        line-height: 100%;
                        position:absolute;
                        left: 82%;
                        top: 32.5%;
                        .content {
                            width: 0.42rem;
                            height: 0.38rem;
                            background-color: aquamarine;
                        }
                        p {
                            margin-top: .04rem;
                        }
                        .top {
                            color: #F06E6C;
                            font-weight: 600;
                            font-size: .15rem;
                        }
                        .bottom {
                            font-size: .12rem;
                            color: #F06E6C;
                        }
                    }
                }
                .contentTwo {
                    border-top: 1px solid rgba(240,110,108,.4);
                    overflow: hidden;
                    position: relative;
                    .titleText {
                        width: .7rem;
                        float: left;
                        display: inline-block;
                        p {
                            display: inline-block;
                            width: .7rem;
                            height: .42rem;
                            text-align: center;
                            margin-top: 110%;
                            font-weight: 600;
                            font-size: .15rem;
                            color:rgba(23,6,6,.61);
                        }
                    }
                    .flLeft {
                        width: 2.11rem;
                        float: left;
                        border-right: 1px solid rgba(240,110,108,.4);
                        border-left: 1px solid rgba(240,110,108,.4);
                        .contentText {
                            display: inline-block;
                            width: 1.86rem;
                            margin: .1rem .17rem .1rem .13rem;
                            font-size: .14rem;
                            color:rgba(23,6,6,.8);
                        }
                    }
                    .flRight {
                        float: left;
                        width: .5rem;
                        text-align: center;
                        line-height: 100%;
                        position:absolute;
                        left: 82%;
                        top: 40.5%;
                        .content {
                            width: 0.42rem;
                            height: 0.38rem;
                            background-color: aquamarine;
                        }
                        p {
                            margin-top: .04rem;
                        }
                        .top {
                            color: #F06E6C;
                            font-weight: 600;
                            font-size: .15rem;
                        }
                        .bottom {
                            font-size: .12rem;
                            color: #F06E6C;
                        }
                    }
                }
                .contentThree {
                    border-top: 1px solid rgba(240,110,108,.4);
                    overflow: hidden;
                    position: relative;
                    .titleText {
                        width: .7rem;
                        height: .58rem;
                        float: left;
                        border-right: 1px solid rgba(240,110,108,.4);
                        display: inline-block;
                        p {
                            display: inline-block;
                            width: .7rem;
                            height: .58rem;
                            text-align: center;
                            line-height: .58rem;
                            font-weight: 600;
                            font-size: .15rem;
                            color:rgba(23,6,6,.61);
                        }
                    }
                    .flLeft {
                        width: 2.11rem;
                        height: .58rem;
                        float: left;
                        border-right: 1px solid rgba(240,110,108,.4);
                        line-height: .58rem;
                        .contentText {
                            display: inline-block;
                            width: 1.86rem;
                            font-size: .14rem;
                            color:rgba(23,6,6,.8);
                            padding-left: .1rem;
                        }
                    }
                    .flRight {
                        float: left;
                        width: .5rem;
                        text-align: center;
                        line-height: 100%;
                        position:absolute;
                        left: 82%;
                        top: 20.5%;
                        .content {
                            width: 0.42rem;
                            height: 0.38rem;
                            background-color: aquamarine;
                        }
                        p {
                            margin-top: .04rem;
                        }
                        .top {
                            color: #F06E6C;
                            font-weight: 600;
                            font-size: .15rem;
                        }
                        .bottom {
                            font-size: .12rem;
                            color: #F06E6C;
                        }
                    }
                }
            }
        }

        .clear {
            clear: both;
        }
        .story {
            background: rgb(255, 255, 255);
            margin-top: .1rem;
            height: 2.74rem;
            .top {
                width: 3.75rem;
                height: .5rem;
                line-height: .5rem;
                img {
                    width: 0.32rem;
                    height: 0.32rem;
                    vertical-align: middle;
                    margin-top: -.05rem;
                    margin-left: .1rem;
                }
                span {
                    font-size: .17rem;
                }
            }

            .content {
                overflow-x: scroll;
                overflow-y: hidden;
                white-space: nowrap;
                position: relative;
                height: 2.26rem;
                .item {
                    display: inline-block;
                    width: 1.55rem;
                    margin-left: .1rem;
                    box-shadow:0px 1px 2px 0px rgba(0,0,0,0.04),0px 1px 2px 0px rgba(0,0,0,0.04),0px 2px 6px 0px rgba(0,0,0,0.04);
                    border-radius:5px;
                    height: 2.16rem;
                    position: relative;
                    img {
                        width: 1.55rem;
                        height: 1.22rem;
                        border-radius:5px 5px 0px 0px;
                        object-fit: cover;
                    }
                    p {
                        margin-left: .05rem;
                    }
                    .text {
                        width: 1.39rem;
                        font-size: .13rem;
                        margin-top: .08rem;
                        color: rgba(23,6,6,0.8);
                        white-space:normal;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 3;
                        overflow: hidden;

                    }
                    .date {
                        position:absolute;
                        font-size: .12rem;
                        color:rgba(23,6,6,0.4);
                        bottom: .02rem;
                    }
                }

            }
        }
        .userMessage {
            background: rgb(255, 255, 255);
            margin-top: .1rem;
            margin-bottom: .6rem;
            .top {
                width: 3.75rem;
                height: .7rem;
                line-height: .7rem;
                img {
                    width: 0.32rem;
                    height: 0.32rem;
                    margin-left: .1rem;
                    vertical-align: middle;
                    margin-top: -.05rem;
                }
                .topTitle {
                    font-size: .17rem;
                    color:rgba(23,6,6,0.8);
                }
            }
            .textContent {
                 overflow: hidden;
                 height: 4.1rem;
                .userMessageContentList {
                    width: 3.75rem;
                    &.anim {
                        transition: all 1s;
                        margin-top: -1.7rem;
                    }
                .content {
                    overflow: hidden;
                    height: 1.33rem;
                    position: relative;
                    .line {
                        position:absolute;
                        bottom: .01rem;
                        height: 1px;
                        width: 2.76rem;
                        left: .64rem;
                        background-color: rgba(23,6,6,0.08);

                    }
                    .fl {
                        width: .64rem;
                        img {
                            width: 0.38rem;
                            height: 0.38rem;
                            float: right;
                            border-radius: 50%;
                            margin-top: .11rem;
                        }
                    }
                    .fr {
                        width: 3.11rem;
                        height: 1.14rem;
                        .userName {
                            margin-left: .1rem;
                            margin-top: .12rem;
                            font-size: .14rem;
                            color:rgba(23,6,6,0.8);
                        }
                        .uesrQuestion {
                            display: inline-block;
                            width: 2.76rem;;
                            margin-top: .03rem;
                            margin-left: .1rem;
                            font-size: .14rem;
                            color:rgba(23,6,6,0.4);
                        }
                        .content {
                            position: relative;
                            margin-bottom: .09rem;
                            height: auto;

                            .line {
                                position:absolute;
                                display: inline-block;
                                width: 0.02rem;
                                height: 0.14rem;
                                background-color: #F06E6C;
                                top: .17rem;
                                left: .01rem;
                            }
                            .answer {
                                display: inline-block;
                                background:rgba(248,248,248,1);
                                border-radius:3px 3px 3px 0px;
                                width: 2.86rem;
                                font-size: .14rem;
                                color:rgba(23,6,6,0.8);
                                padding: .13rem .07rem .13rem .1rem;
                            }
                        }
                    }
                }
            }
        }

        .userMessageContent {
            width: 3.75rem;
            overflow: hidden;
            .fl {
                width: 0.64rem;
                img {
                    width: 0.38rem;
                    height: 0.38rem;
                    float: right;
                    border-radius: 50%;
                    margin-top: .11rem;
                }
            }
            .fr {
                width: 3.11rem;
                position: relative;
                .userName {
                    margin-left: .1rem;
                    margin-top: .12rem;
                    font-size: .14rem;
                    color:rgba(23,6,6,0.8);
                    display: inline-block;
                    width: 2.48rem;
                    height: .24rem;
                }
                textArea {
                    width: 2.87rem;
                    height: .86rem;
                    border-radius:0px 5px 5px 5px;
                    opacity:0.4;
                    padding: .13rem .1rem .13rem .1rem;
                    margin-top: .03rem;
                }
                span {
                    position: absolute;
                    font-size: .09rem;
                    color:rgba(23,6,6,0.4);
                    top: 1.07rem;
                    right: .305rem;
                }
                button {
                    width: 1rem;
                    height: .35rem;
                    background:rgba(240,110,108,1);
                    border: 0;
                    margin-top: .1rem;
                    margin-left: .89rem;
                    margin-bottom: .2rem;
                    border-radius: .2rem;
                    color: #FFFFFF;
                    font-size: .16rem;
                    font-weight: 500;
                }
            }
        }
    }
        .InvitationBg {
            position: relative;
            img {
                width: 3.75rem;
                height: 1.53rem;
                // margin-top: .05rem;
            }
        }
        .fixBtn {
            position: fixed;
            bottom: 0.01rem;
            right: .01rem;
            width: 1.25rem;
            height: 1.27rem;
            line-height: .4rem;
            text-align: center;
            color: #fff;
            z-index: 999;
            background-image: url('../../../assets/image/active//gdProfessionUniversity/<EMAIL>');
            background-size: 100%;
            border-radius: 10px;
            a {
                display: block;
            height: 100%;
            color: #fff;
            font-size: 21px;
            text-decoration: none;
            }
        }
        .bottomBox {
            background: rgb(255, 255, 255);
            width: 3.75rem;
            height: .5rem;
            line-height: .5rem;
            text-align: center;
            a {
                color: #F06E6C;
                font-size: .14rem;
            }
            span {
                color: #F06E6C;
                display: inline-block;
                margin-top: .01rem;
            }
            .callUs {
                margin-right: .1rem;
            }
            .official {
                margin-left: .1rem;
            }
        }
        .showall {
            height: 4.2rem;
        }
        .showall.active {
            height: auto;
        }
        .schoolDetails {
            width: 3.75rem;
            background: rgb(255, 255, 255);
            overflow: hidden;
            img {
                width: 3.55rem;
                height: 1.79rem;
                border-radius: 5px;
                margin: .1rem .1rem 0rem .1rem;
            }
            p {
                display: inline-block;
                width: 3.15rem;
                margin: .2rem .3rem 0rem .3rem;
                padding: 0rem .05rem 0rem .05rem
            }
        }
        .lookMore {
            background: rgb(255, 255, 255);
            padding-top: .06rem;
            text-align: center;
            height: .54rem;
            line-height: .54rem;
            position: relative;
            span {
                color:rgba(23,6,6,0.4);

            }
            img {
                position: absolute;
                top: -.03rem;
                left: 1.7rem;
                width: .32rem;
                height: .32rem;
            }
        }
        .studentStory {
            background: rgb(255, 255, 255);
            height: 2.41rem;
            width: 3.75rem;
            margin-top: .1rem;
            .top {
                height: .7rem;
                width: 3.75rem;
                line-height: .7rem;
                img {
                    width: 0.32rem;
                    height: 0.32rem;
                    margin-left: .1rem;
                    vertical-align: middle;
                    margin-top: -.05rem;
                }
                span {
                    font-size: .17rem;
                    color:rgba(23,6,6,0.8);
                }
            }
        }
  }

    .swiper {
        width:100%;
        height: 1.6rem;
        overflow: hidden
    }
    .swiper-slide {
        width:80%;
        height: 160px;
    }
    .swiper-slide-active img {
        margin-top: 0;
        width: 100%;
        height: 100%;
    }
    .swiperImg {
        display: block;
        margin: 0 auto;
        margin-top: 3.5%;
        width: 85%;
        height: 85%;
        border-radius:5px;
    }

    .van-cell {
        height: .54rem;
        line-height: .54rem;
    }

.textBox {
    position: relative;
    display: inline-block;
    width: 2.57rem;
    background-color: #fff;
    margin-top: .15rem;
    line-height: .3rem;
}
.fixBottom {
  position: fixed;
  bottom: 0;
  height: 0.6rem;
  z-index: 9999;
  .leftBox {
    display: inline-block;
    width: 1.96rem;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;
    border-top: 1px solid #f06e6c;
    img {
      width: 0.39rem;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }
    span {
      display: inline-block;
    }
    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }
    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.13rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }
  .rightBox {
    display: inline-block;
    width: 1.79rem;
    height: 0.6rem;
    background: #f06e6c;
    float: left;
    position: relative;
    .line {
      position: absolute;
      display: inline-block;
      width: 1px;
      height: 0.25rem;
      background: rgba(255, 255, 255, 0.4);
      top: 0.16rem;
      left: 0.88rem;
    }
    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;
      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }
      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;
      img {
        opacity: 0.8;
        width: 0.24rem;
        margin-top: 0.1rem;
      }
      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
  }
}
.aleft {
    width: .32rem;
    position: absolute;
    background:rgba(255,184,13,1);
    top:0;
    left: 0;
    height: 100%;
    img {
        width: .12rem;
        vertical-align: middle;
    }
}
.aright {
    width: 2.25rem;
    float:right;
    p {
        text-align: left;
        padding-left: .1rem;
        padding-right: .1rem;
    }
}

</style>


