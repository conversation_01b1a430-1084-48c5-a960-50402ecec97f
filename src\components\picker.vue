<template>
  <mt-popup v-model="isShow" position="bottom">
    <div class="commonPop-box">
      <div class="commonPop-content">
        <mt-picker :class="{disabled:disabled}" :slots="slots" :itemHeight="50" :visibleItemCount="3" :showToolbar="!!title" @change="onValuesChange" ref="picker">{{title}}</mt-picker>
      </div>
      <div class="commonPop-title">
        <span @click="close">取消</span>
        <span @click="confirm" :class="{disabled:disabled}">确定</span>
      </div>
    </div>
  </mt-popup>
</template>

<script>
  export default {
    props: {
      title: null,
      slots: {
        type: Array,
        default: [],
        require: false
      },
      disabledSlots:{
        type: Array,
        default: function () {
          return []
        },
        require: false
      },
      isSetDefaultVal: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isShow: false,
        newVal: [],
      }
    },
    computed: {
      selectedValue: function () {
        // debugger
        let val = [];
        for (let i in this.newVal) {
          val.push({index: this.slots[i].values.indexOf(this.newVal[i]), val: this.newVal[i]});
        }
        if (val.length === 0) {
          val = [{}];
        }
        return val;
      }
    },
    created() {
      this.setDefaultVal();// 设置禁选项
    },
    methods: {
      init: function () {
        this.slots = [{flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex}];
        this.$nextTick(()=>{
          // 设置禁选项
          if(this.disabledSlots.length>0){
            debugger
            if(this.disabledSlots.includes(values[0])){
              this.disabled=true;
            }else {
              this.disabled=false;
            }
          }
        })
      },
      open: function () {
        this.isShow = true;
      },
      close: function () {
        this.isShow = false;
      },
      setDefaultVal: function () {
        if (this.isSetDefaultVal && this.slots.length > 0) {
          let vals = [];
          for (let item of this.slots) {
            const index = item.defaultIndex || 0;
            const val = item.values[index];
            vals.push({index: index, val: val});
          }
          if (vals.length === 0) {
            vals = [{}];
          }
          this.$emit('input', vals);
        }
      },
      onValuesChange: function (picker, values) {
        for (let i in values) {
          if (values[i] === undefined) {
            values[i] = this.slots[i].values[0];
          }
        }
        picker.setValues(values);

        // 设置禁选项
        if(this.disabledSlots.includes(values[0])){
          this.disabled=true;
        }else {
          this.disabled=false;
        }

        this.newVal = values;
      },
      confirm: function () {
        this.isShow = false;
        this.$emit('input', this.selectedValue);
      },
      setValues: function (vals) {
        this.$refs.picker.setValues(vals);
        this.$nextTick(() => {
          this.confirm();
        });
      }
    },
  }
</script>


<style lang="less" scoped>
  @import "../assets/less/commonPopBox";
</style>
<style lang="less">
  @import "../assets/less/variable";

  .mint-popup-bottom{ width:100%; }
  .picker-items{
    .picker-slot{ font-size:.16rem; }
  }
  .picker-toolbar{ height:.5rem; line-height:.5rem; text-align:center; font-size:.16rem; }

  .title {
    padding: .1rem .1rem .04rem .1rem;
    font-weight: bold;
    font-size: 15px;
  }
  .tip {
    padding: 0 .1rem;
    color: red;
    font-size: 13px;
  }
</style>
