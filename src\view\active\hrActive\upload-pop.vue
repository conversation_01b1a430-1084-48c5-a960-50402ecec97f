<template>
  <div>
    <van-popup v-model="show" position="bottom" class="upload-popup">
      <div class="popup-head">
        <div class="close-box" @click='show = false;'>
          <img src="../../../assets/image/closeX.png" class='close' alt="">
        </div>
      </div>
      <div class="container">
        <img src="../../../assets/image/active/hrActive/pic.png" class="pic"  alt="">
        <div class="text-box">报名<span class="red">7天内</span>, 需上传<span class="red">「加盖公章」后</span>的在职证明模板哦！</div>
        <div class="btn-box">
          <div class="pre">
            <webuploader :multiple='false' @upload='uploadSuccess'>
              <div class="btn red">
                <p>上传证明</p>
                <p class="s2">* 只支持上传图片或扫描件</p>
              </div>
            </webuploader>
          </div>
          <button class="btn" @click='down'>下载模版</button>
        </div>
      </div>
    </van-popup>
    <photoswipe ref='photoswipe' />
  </div>

</template>

<script>
import bridge from '@/plugins/bridge';
import { transformImgToBase64 } from "@/common";
import { Toast, Popup } from 'vant';
import photoswipe from '@/components/photoswipe';
import webuploader from "@/components/uploader";
import templateImg from '@/assets/image/active/hrActive/template.jpg';

export default {
  components: {
    Toast, Popup, webuploader, photoswipe
  },
  props: {
    learnId: {
      type: String,
      default: '',
    },
  },
  data () {
    return {
      show: false,
      isAppOpen: false,
    }
  },
  mounted () {
    bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        this.isAppOpen = true;
      }
    });
  },
  methods: {
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    down() {
      if (this.isAppOpen) {
        const img = new Image();
        img.crossOrigin = "";  // 支持跨域图片
        img.setAttribute("crossOrigin",'Anonymous');//跨域在前
        img.src = templateImg;
        img.onload = () => {
          const base64 = transformImgToBase64(img, 'image/jpeg');
          const imgdata = base64.split(",")[1];
          bridge.callHandler('receiptImage', { imgdata });
        };
        return;
      }
      this.$refs.photoswipe.open([{title: '长按图片保存模板', url: templateImg, type: 'base64'}]);
    },
    async uploadSuccess(path) {
      if (this.learnId) {
        const res = await this.$http.post('/mkt/updateLearnAnnex/1.0/', { learnId: this.learnId, url: path });
        if (res.code == '00') {
          Toast.success('上传成功');
          this.$emit('success');
        }
      }
    },
  },
};
</script>

<style lang="less">
  .upload-popup{
    color: #000000;
    border-radius: 0.1rem 0.1rem 0 0;
    .container{
      text-align: center;
      padding: 0 0.52rem;
      .pic{
        width: 0.93rem;
        height: 1.23rem;
        vertical-align: middle;
      }
      .text-box{
        font-size:0.17rem;
        padding: 0.43rem 0;
        .red{
          color: #BA171A;
        }
      }
      .pre{
        position: relative;
      }
      .btn-box{
        margin-bottom: 0.26rem;
        .btn{
          width: 100%;
          height: 0.4rem;
          text-align: center;
          background: none;
          color: #BA171A;
          font-size: 0.17rem;
          font-weight: 600;
          border: 1px solid #EE5D4C;
          border-radius: 100px;
          p{
            line-height: 1;
          }
          .s2{
            font-size: 0.09rem;
            font-weight: 400;
            width: 100%;
            margin-top: -0.08rem;
          }
          &.red{
            background: linear-gradient(180deg, #DB3135, #A50609);
            border: 0;
            box-shadow: 0px 0.02rem 0.02rem 0px rgba(166, 19, 8, 0.4);
            margin-bottom: 0.15rem;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;

          }
        }
      }
    }
  }
  .popup-head{
    height: 0.54rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    text-align: right;
    margin-bottom: 0.33rem;
    padding-right: 0.11rem;
    .close-box{
      padding: 0.1rem;
      display: inline-block;
      margin-top: 0.1rem;
      img{
        width: 0.14rem;
        height: 0.14rem;
        vertical-align: middle;
        opacity: 0.7;
        float: left;
      }
    }
  }
</style>
