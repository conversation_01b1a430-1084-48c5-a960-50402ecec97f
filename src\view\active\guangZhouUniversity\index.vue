<template>
  <div>
    <div class="oldWrap">
      <inviteTop :inviteId="inviteId"></inviteTop>
      <div class="banner">
        <img src="../../../assets/image/schoolpage/gzschool.png" alt />
      </div>

      <div class="content" :class="{'bg-g':tabName!='recruit'}">
        <div class="tab-3">
          <p :class="{'active':tabName=='recruit'}" @click="tabName='recruit'">
            <span>招生主页</span>
          </p>
          <p :class="{'active':tabName=='introduce'}" @click="tabName='introduce'">
            <span>学校介绍</span>
          </p>
          <p :class="{'active':tabName=='common'}" @click="tabName='common'">
            <span>常见问题</span>
          </p>
        </div>
      </div>
      <transition name="fade2">
        <div v-if="tabName=='recruit'">
          <div class="schoolAdvantage">
            <div class="content" >
              <p class="contentText">299元  </p>
              <ul>
                <li class="item"><i><img src="../../../assets/image/active/SouthChinaAgriculturalUniversity/leaf.png" alt=""></i><p>考前辅导课(礼包价199)</p></li>
                <li class="item"><i><img src="../../../assets/image/active/SouthChinaAgriculturalUniversity/leaf.png" alt=""></i><p>三本辅导教材(礼包价100)</p></li>
                <!-- <li class="item"><i><img src="../../../assets/image/active/SouthChinaAgriculturalUniversity/leaf.png" alt=""></i><p>成考过295分, 奖3年学费等值智米</p></li> -->
              </ul>
              <!-- <p class="supplement">(上述考分不含政策性加分)</p> -->
              <div class="btn" @click="enroll">立即购买</div>
            </div>
          </div>
          <div class="charge">
            <div class="chargetop">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <p>院校专业及收费</p>
            </div>
          </div>
          <div class="highUniversityBox">
            <div class="highUniversity" v-if='pfsnLevel == 5 || !pfsnLevel'>
              <div class="title">
                <span class="text">- 高起专 -</span>
              </div>
              <table>
                <tr>
                  <td width="20%">文史类</td>
                  <td>工商企业管理、电子商务</td>
                  <td width="20%" class="rictext">
                    2300
                    <br />元/学年
                  </td>
                </tr>
              </table>
            </div>
            <div class="highUniversity" v-if='pfsnLevel == 1 || !pfsnLevel' style="margin-top:.12rem">
              <div class="title">
                <span class="text">- 专升本 -</span>
              </div>
              <table>
                <tr>
                  <td width="20%" style="text-align:left;padding-left:.13rem">
                    文史类
                  </td>
                  <td>汉语言文学（秘书方向）</td>
                  <td width="20%" class="rictext">
                    2500
                    <br />元/学年
                  </td>
                </tr>
                <tr>
                  <td>理工类</td>
                  <td>工程管理、会计学、行政管理</td>
                  <td width="20%" class="rictext">
                    2500
                    <br />元/学年
                  </td>
                </tr>
                <tr>
                  <td>理工类</td>
                  <td>土木工程、给排水科学与工程</td>
                  <td width="20%" class="rictext">
                    2875
                    <br />元/学年
                  </td>
                </tr>
              </table>
            </div>
            <p style="padding-left:.2rem;margin-top:.15rem;color: rgba(23, 6, 6, 0.6)">
              <span style="color:#F06E6C">•</span> 书杂费:&ensp;400元/学年(多退少补),按3年收取
            </p>
            <p style="padding-left:.2rem;color: rgba(23, 6, 6, 0.6);margin:0.08rem 0">
              <span style="color:#F06E6C">•</span> 学制:&ensp;3年
            </p>

            <!-- <p style="color: rgba(23, 6, 6, 0.6);padding-left:.2rem;float:left;width:20%">
              <span style="color:#F06E6C">•</span> 考区:
            </p>
            <p
              style="width:75%;float:left;color:rgba(23,6,6,.6); margin: 0 0 0.08rem -.05rem;"
            >广州、深圳</p> -->
          </div>
          <div class="story">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span>上进故事</span>
            </div>
            <div class="content">
              <a
                @click="toStory(item.scholarshipId,inviteId,item.resourcesUrl,item.createTime)"
                class="item"
                v-for="(item,index) in storyList"
                :key="index"
              >
                <img :src="item.articlePicUrl+storyImgLimit|imgBaseURL" />
                <p class="text">{{item.articleTitle}}</p>
                <p class="date">{{item.createTime.substring(0,11)}}</p>
              </a>
            </div>
          </div>
          <!-- <div class="userMessage">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">留言区</span>
            </div>
            <div class="textContent">
              <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                  <div class="fl">
                    <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2" alt />
                  </div>
                  <div class="fr">
                    <p class="userName">{{(item.realName|| item.nickname)|hideName}}</p>
                    <div class="bottom" v-if="item.msgReply">
                      <p class="uesrQuestion">{{item.msgContent}}</p>
                      <div class="content">
                        <div class="line"></div>
                        <p class="answer">
                          <span>回复:&nbsp;</span>
                          {{item.msgReply}}
                        </p>
                      </div>
                    </div>
                    <div class="bottom2" v-if="!item.msgReply" style="margin-top:.16rem">
                      <p class="uesrQuestion">{{item.msgContent}}</p>
                    </div>
                  </div>
                  <div class="line"></div>
                </div>
              </div>
            </div>
            <div class="userMessageContent">
              <div class="fl">
                <img :src="userImg?userImg+headLimit:userImg|defaultAvatar2" alt />
              </div>
              <div class="fr">
                <p class="userName">{{userName}}</p>
                <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                <span>{{message.length}}/50</span>
                <button @click="enrollMsg()">提交</button>
              </div>
            </div>
          </div> -->
        </div>
      </transition>
      <transition name="fade2">
        <div v-if="tabName=='introduce'">
          <div class="schoolDetails" :class="{showall:true,active:showall}">
            <img src="../../../assets/image/schoolpage/gzschooldetail.png" alt />
            <p class="schoolDetailsText">
              &nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; 广州大学，是以国家重要中心城市“广州”命名的综合性大学，于2000年合并组建，有着90多年的办学传统。学校紧紧抓住国家推进“双一流”建设，是广东省高水平大学重点学科建设高校，广州市高水平大学建设高校。
              <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; 学校现有大学城、桂花岗两个校区，学校学科门类齐全、综合性强，设有26个学院，涵盖哲学、经济学、法学、教育学、文学、历史学、理学、工学、管理学、艺术学等十大学科门类。学校不断优化学科结构,服务区域创新驱动发展特色更加鲜明，逐步构建起文理工相互支撑、交叉融合、协同发展的学科体系，形成由四个省“冲一流”重点建设学科、14个省级重点学科和16个市级重点学科和重点扶持学科构成的重点建设学科体系。
              <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;毕业生就业质量稳步提升，近3年平均初次就业率为90.92%，用人单位总体满意度达98.7%。坚持把创新创业教育融入人才培养全过程，学生在“互联网+”“挑战杯”“创青春”等国家创新创业竞赛成绩位居广东高校乃至全国同类高校前列。
            </p>
          </div>
          <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="!showall">
            <a :class="{active:showall}" @click="lookMore">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span>查看更多</span>
            </a>
          </div>
          <div class="lookMore" :class="{active:showall}" style="text-align:center" v-if="showall">
            <a :class="{active:showall}" @click="lookMoreCancle">
              <span class="down"></span>
              <span>收起</span>
            </a>
          </div>
          <div class="studentStory">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">学员风采</span>
            </div>
            <div class="swiper">
              <swiper :options="swiperOption">
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/1.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/9.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/2.png"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/3.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/4.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/5.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/6.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/7.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/8.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/10.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/11.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/12.jpg"
                  />
                </swiper-slide>
                <swiper-slide>
                  <img
                    class="swiperImg"
                    src="../../../assets/image/active/fullTimeSystem/student/13.jpg"
                  />
                </swiper-slide>
              </swiper>
            </div>
          </div>
          <!-- <div class="userMessage">
            <div class="top">
              <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt />
              <span class="topTitle">留言区</span>
            </div>
            <div class="textContent">
              <div class="userMessageContentList" :class="{anim:animatePraise==true}">
                <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                  <div class="fl">
                    <img :src="item.headImg?item.headImg+headLimit:item.headImg|defaultAvatar2" alt />
                  </div>
                  <div class="fr">
                    <p class="userName">{{item.nickname}}</p>
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                    <div class="content" v-if="item.msgReply">
                      <div class="line"></div>
                      <p class="answer">
                        <span>回复:&nbsp;</span>
                        {{item.msgReply}}
                      </p>
                    </div>
                  </div>
                  <div class="line"></div>
                </div>
              </div>
            </div>
            <div class="userMessageContent">
              <div class="fl">
                <img :src="userImg?userImg+headLimit:userImg|defaultAvatar2" alt />
              </div>
              <div class="fr">
                <p class="userName">{{userName}}</p>
                <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
                <span>{{message.length}}/50</span>
                <button @click="enrollMsg">提交</button>
              </div>
            </div>
          </div> -->
        </div>
      </transition>
      <transition name="fade2">
        <div v-if="tabName=='common'">
          <questionList></questionList>
        </div>
      </transition>
    </div>
    <!-- <o-footer
      tabName="newintroduce"
      :Expired="enrollEnd"
      @isIphoneX="isIphoneX=true"
      :actName="actName"
      :scholarship="scholarship"
      from="gz"
      :unvs="unvs"
    ></o-footer> -->
    <second-footer @enroll='enroll' />
    <share
      :title="title"
      :desc="desc"
      :link="shareLink"
      :isActivity="true"
      :scholarship="scholarship"
      :imgUrl="shareImg"
      :regOrigin='regOrigin'
      ref="share"
      :levelId="pfsnLevel"
    />
  </div>
</template>

<script>
import appShare from '@/mixins/appShare';
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { toLogin, isEmployee, isIOS,getIsInTimeByType} from "../../../common";
import oFooter from "@/components/activePage/footer";
import share from "@/components/share";
import DigitRoll from "@huoyu/vue-digitroll";
import { imgBaseURL, activityTime } from "../../../config";
import countdown from "@/components/active/countdown5";
import questionList from "@/components/activePage/questionList";
import inviteTop from "../enrollAggregate/components/invite-top";
import SecondFooter from "@/view/active/2021NewMain/components/second-footer";
import statistic from '@/view/active/2021NewMain/statistic.json';

let scholarship = "164"; //优惠类型
export default {
  mixins: [appShare],
  components: {
    swiper,
    swiperSlide,
    share,
    countdown,
    DigitRoll,
    questionList,
    oFooter,
    inviteTop,
    SecondFooter
  },

  data() {
    return {
      tabName: "recruit",
      isInvite5MonthActive: getIsInTimeByType('decisive'),
      activeName: "",
      animatePraise: false,
      animate: false,
      message: "",
      shareLink: "",
      isLogin: null,
      scholarship: scholarship,
      inviteId: "",
      content: [],
      isPay: true,
      started: true,
      learnInfo: "",
      isEmployee: isEmployee(),
      userName: "",
      showall: false,
      userImg: "",
      showInvite: false,
      items: [],
      invite: {},
      enrollMsgList: [],
      storyList: [],
      unvs: {
        unvsName: "广州大学",
         unvsId: "158529180291064815"
      },
      enrollEnd: false,
      isIphoneX: true,
      swiperOption: {
        initialSlide: 1,
        autoplay: 2000,
        centeredSlides: true,
        loop: true,
        slidesPerView: "auto",
        loopedSlides: 1,
        autoplayDisableOnInteraction: false
      },
      storyImgLimit: "?x-oss-process=image/resize,m_fixed,h_122,w_155",
      loadingFlag: true,
      headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
      actName: "",
      learnCount: 0,
      showCard: false,
      cardHeadUrl: "",
      cardWechatQrcode: "",
      propagateUrl: "",
      flag: false,
      sprintList: [],
      pageNum: 0,
      pageSize: 10,
      isLoading: false,
      allLoaded: false,
      activityInfo: {},
      title: "读学历来广州大学，名校录取，学信网可查！",
      desc: "教育部备案的高等院校，超2万名同学等你一起来上进！",
      relation: 0,
      pfsnLevel: '',
      shareImg: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/gzxh.png',
      regOrigin: 42,
    };
  },

  created() {
    document.title = "广州大学";
    this.tabName = this.$route.query.tab || "recruit";
    if (this.$route.query.tab) {
      this.toSprint();
    }
    this.isLogin = !!this.storage.getItem("authToken");
    this.userName =
      this.storage.getItem("realName") ||
      this.storage.getItem("zmcName") ||
      this.storage.getItem("mobile");
    this.storage.getItem("realName") || this.storage.getItem("zmcName");
    this.relation = this.storage.getItem("relation") || 0;
    this.userImg = this.storage.getItem("headImg") || "";
    (this.inviteId = this.$route.query.inviteId || ""),
    (this.action = this.$route.query.action || "");
    this.pfsnLevel = this.$route.query.pfsnLevel;
    this.shareLink = `${window.location.origin}/active/guangZhouUniversity`;

    // this.getContent();
    setInterval(this.scroll, 3000);
    setInterval(this.scrollText, 3000);
    this.getNewRegList();
    this.getActivityInfo();
    // this.getCouponGiveAllNum();
    // this.isGetCoupon();
    if (this.pfsnLevel) {
      this.$yzStatistic('marketing.base.browse', statistic.schoolPage[this.pfsnLevel].main.id, statistic.schoolPage[this.pfsnLevel].main.name);
    }
    this.initAppShare(() => {
      this.setShareParams({
        title: this.title,
        content: this.desc,
        url: '/active/guangZhouUniversity',
        image: this.shareImg,
        regOrigin: this.regOrigin,
      });
    });
  },

  computed: {
    regChannel() {
      return this.$route.query.regChannel || '';
    },
    jumpUrl: function() {
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName: this.actName,
          recruitType: "1",
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
          unvs: JSON.stringify({
            unvsName: "广州大学",
            unvsId: "158529180291064815"
          })
        }
      };
      return url;
    }
  },
  mounted: function() {
    window.addEventListener("scroll", this.handleScroll, true);
    //获取邀约人信息
    if (!!this.inviteId) {
      this.getInviteInfo();
    }

  },
  methods: {
    enroll() {
      if(!this.isOpenAppLogin()) {  // 该方法来自appShare.js
        return;
      }
      this.$router.push(this.jumpUrl);
    },
    getSptintList() {
      this.$http
        .post("/mkt/getAdvanceWitnessList/1.0/", {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          scholarship: this.scholarship
        })
        .then(res => {
          if (res.code == "00") {
            const datas = res.body || [];
            this.sprintList.push(...res.body);
            this.$nextTick(() => {
              this.allLoaded = datas.length === 0;
              this.isLoading = this.allLoaded;
            });
            //this.sprintListFlag=true
          }
        });
    },
    toMethod() {
      this.$router.push({ name: "sprintProcess" });
    },
    loadMore() {
      this.pageNum++;
      this.getSptintList();
    },
    // getInviteInfo() {
    //   let inviteId = (
    //     window.sessionStorage.getItem("inviteId") ||
    //     this.$route.query.inviteId ||
    //     decodeURIComponent(
    //       this.$route.query.inviteId ||
    //       this.getQueryString(this.redirect, "inviteId") ||
    //       ""
    //     )
    //   ).replace(/ /g, "+");
    //   if (inviteId) {
    //     this.$http
    //       .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
    //       .then(res => {
    //         let { code, body } = res;
    //         if (code !== "00") return;
    //         this.invite = body || {};
    //         this.showInvite = true;
    //         if (!this.articleId) {
    //           if (this.invite.relation == "2" || this.invite.relation == "6") {//取自己的名片
    //             this.getArtile(this.invite.userId);
    //           } else {//其他情况取跟进人
    //             this.getCard(this.invite.userId);
    //           }
    //         }
    //       });
    //   }
    // },
    toActivity() {
      this.$router.push({
        name: "enrollmentHomepage",
        query: { inviteId: this.$route.query.inviteId }
      });
    },
    //监听滑动的距离
    handleScroll() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (this.loadingFlag) {
        if (scrollTop > 330) {
          this.getEnrollMsgList();
          this.getStoryList();
          this.loadingFlag = false;
        }
      }
    },
    toStory(id, inviteId, resourcesUrl, createTime) {
      this.$router.push({
        name: "scholarshipStoryInfo",
        query: {
          id: id,
          inviteId: inviteId,
          resourcesUrl: resourcesUrl,
          createTime: createTime
        }
      });
    },

    // 获取邀约人信息
    getInviteInfo() {
      let inviteId = (
        window.sessionStorage.getItem("inviteId") ||
        decodeURIComponent(
          this.$route.query.inviteId ||
            this.getQueryString(this.redirect, "inviteId") ||
            ""
        )
      ).replace(/ /g, "+");
      if (inviteId) {
        this.$http
          .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
          .then(res => {
            let { code, body } = res;
            if (code !== "00") return;
            this.invite = body || {};
            this.showInvite = true;
            // this.$refs.inviteRef.style.top = "1.31rem";
            // this.$refs.inviteRef.style.visibility = "visible";
            if (!this.articleId) {
              if (this.invite.relation == "2" || this.invite.relation == "6") {
                //取自己的名片
                this.getArtile(this.invite.userId);
              } else {
                //其他情况取跟进人
                this.getCard(this.invite.userId);
              }
            }
          });
      } else {
        this.$refs.inviteRef.style.top = ".73rem";
        this.$refs.inviteRef.style.visibility = "visible";
      }
    },
    getArtile(id) {
      this.$http
        .post("/mkt/getSpreadCardByRelationId/1.0/ ", { relationId: id })
        .then(res => {
          if (res.code === "00") {
            if (res.body === null) {
              this.articleId = "";
            } else {
              this.showCard = true;
              Object.assign(this.$data, res.body);
              if (this.cardHeadUrl) {
                this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
              }
              if (this.cardWechatQrcode) {
                this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
              }
              this.propagateUrl = this.propagateUrl + "&articleId=" + id;
            }
          }
        });
    },
    getCard(userId) {
      this.$http
        .post("/mkt/getFollowCardByUserId/1.0/", { userId: userId })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          if (res.body === null) {
            this.articleId = "";
          } else {
            this.showCard = true;
            Object.assign(this.$data, res.body);
            if (this.cardHeadUrl) {
              this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
            }
            if (this.cardWechatQrcode) {
              this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
            }
            //this.propagateUrl = this.propagateUrl + "&articleId=" + id;
          }
        });
    },
    getActivityInfo(scholarshipDream = scholarship) {
      this.$http
        .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarshipDream })
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          if (scholarshipDream == "67") {
            if (Date.now() < body.EndTime && Date.now() > body.StartTime) {
              this.isMarch = true;
            }
            return;
          } else {
            this.getActivityInfo("67");
          }
          if (Date.now() > body.EndTime) {
            this.enrollEnd = true;
          }
          this.startTime = body.StartTime;
          this.isShowEnd = res.body.isShowEnd;
          this.endTime = body.EndTime;
          this.actName = body.actName;
          this.EnrolmentCount = parseInt(body.learnCount);
        });
    },
    // 点击查看更多
    lookMore: function() {
      this.showall = true;
    },
    lookMoreCancle: function() {
      this.showall = false;
    },
    phoneNumberMobile() {
      window.location.href = "tel:" + this.cardMobile;
    },
    phoneNumber() {
      if (this.articleId) {
        window.location.href = "tel:" + this.cardMobile;
        return;
      }
      window.location.href = "tel:4008336013";
    },
    // 获取学员基础信息
    getPayInfo: function(learnId) {
      let data = {
        learnId: learnId,
        itemCode: "Y0"
      };
      this.$http
        .post("/mkt/selectTutionPaidCountByLearnId/1.0/", data)
        .then(res => {
          let { code, body } = res;
          if (code !== "00") return;
          this.isPay = body.ifPay == "1" ? true : false;
        });
    },

    //购买教材
    toPayment: function() {
      this.$router.push({
        name: "stuPayment",
        query: {
          learnId: this.learnInfo.learnId,
          activityName: "dreamBuildScholarship",
          unvsid: this.learnInfo.unvsId
        }
      });
    },
    //邀请好友报名
    openShare: function() {
      this.$refs.share.open(
        null,
        "login",
        `${this.$route.fullPath}${
          this.$route.fullPath.includes("?") ? "&" : "?"
        }action=share`
      );
    },
    tips() {
      this.$modal({ message: "活动暂未开始！", icon: "warning" });
    },
    lotterytips() {
      let now = new Date().getTime();
      let stop = 1548680400000; // 2019/1/28 21:00 抽奖入口关闭
      if (now > stop) {
        this.$modal({ message: "抽奖活动已经下线！", icon: "warning" });
      } else {
        this.$router.push({ path: "/active/iphoneXLotteryThr" });
      }
    },

    // 获取留言列表
    getEnrollMsgList: function() {
      this.$http
        .post("/mkt/getEnrollMsgList/1.0/", { scholarship: scholarship })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.enrollMsgList = body;
          }
        });
    },
    // 评论
    enrollMsg: function() {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      if (this.isEmployee) {
        this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
        return;
      }
      if (!this.message) {
        this.$modal({ message: "请输入评论内容", icon: "warning" });
        return;
      }

      this.$http
        .post("/mkt/enrollMsg/1.0/", {
          scholarship: scholarship,
          msgContent: this.message
        })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.$modal({
              message: "提交成功",
              beforeClose: (action, instance, done) => {
                done();
                this.message = "";
                this.getEnrollMsgList();
              }
            });
          }
        });
    },

    // 获取故事列表
    getStoryList() {
      this.$http
        .post("/mkt/scholarshipStoryList/1.0/", { informationType: 1 })
        .then(res => {
          const { code, body } = res;
          if (code === "00") {
            this.storyList = body;
          }
        });
    },

    // 轮播用户评论
    scroll() {
      if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
        return;
      }
      this.animatePraise = true;
      setTimeout(() => {
        this.enrollMsgList.push(this.enrollMsgList[0]);
        this.enrollMsgList.shift();
        this.animatePraise = false;
      }, 500);
    },

    // 轮播文字
    scrollText() {
      this.animate = true;
      setTimeout(() => {
        this.items.push(this.items[0]);
        this.items.shift();
        this.animate = false;
      }, 500);
    },
    // 获取最新注册用户列表
    getNewRegList: function() {
      this.$http.post("/us/getNewRegList/1.0/").then(res => {
        let { code, body } = res;
        if (code === "00") {
          this.items = body;
          if (this.items.length == 1) {
            this.items.push(body[0]);
          }
        }
      });
    }
  },
  beforeDestroy() {
    window.removeEventListener("scroll", this.handleScroll, true);
  }
};
</script>


<style lang="less" scoped>
@themeColor: rgba(39, 89, 164, 1);

.oldWrap {
  position: relative;
  background: rgb(246, 246, 246);
  .fixBottom {
    position: fixed;
    bottom: 0;
    height: 0.6rem;
    z-index: 9999;
    .leftBox {
      display: inline-block;
      width: 1.96rem;
      height: 0.6rem;
      float: left;
      background: #fff;
      position: relative;
      border-top: 1px solid #f06e6c;
      img {
        width: 0.39rem;
        margin-left: 0.1rem;
        margin-top: 0.12rem;
      }
      span {
        display: inline-block;
      }
      .textOne {
        margin-top: 0.12rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
      }
      .textTwo {
        position: absolute;
        left: 0.52rem;
        top: 0.3rem;
        font-size: 0.13rem;
        color: rgba(54, 54, 54, 0.6);
      }
    }
    .rightBox {
      display: inline-block;
      width: 1.79rem;
      height: 0.6rem;
      background: #f06e6c;
      float: left;
      position: relative;
      .line {
        position: absolute;
        display: inline-block;
        width: 1px;
        height: 0.25rem;
        background: rgba(255, 255, 255, 0.4);
        top: 0.16rem;
        left: 0.88rem;
      }
      .phoneIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;
        img {
          width: 0.24rem;
          margin-top: 0.1rem;
        }
        p {
          color: rgba(255, 255, 255, 1);
          font-size: 0.14rem;
        }
      }
      .signUpIcon {
        text-align: center;
        display: inline-block;
        float: left;
        width: 50%;
        height: 100%;
        img {
          opacity: 0.8;
          width: 0.24rem;
          margin-top: 0.1rem;
        }
        p {
          color: rgba(255, 255, 255, 1);
          font-size: 0.14rem;
        }
      }
    }
  }
  .banner {
    img {
      height: 1.11rem;
      width: 3.75rem;
    }
  }
  .content {
    &.bg-g {
      background-image: none;
    }
    .tab {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: rgba(246, 246, 246, 1);
      p {
        float: left;
        height: 0.38rem;
        width: 25%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;
        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }
        &.active {
          padding: 0rem;
          line-height: 0.38rem;
          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;
            &:before {
              content: "";
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }
        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }

    .tab-3 {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: #fff;
      p {
        float: left;
        height: 0.38rem;
        width: 33.3%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;
        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }
        &.active {
          padding: 0rem;
          line-height: 0.38rem;
          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;
            &:before {
              content: "";
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }
        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }
  }

  .lottery {
    position: absolute;
    left: 2.76rem;
    float: left;
    width: 0.94rem;
    height: 0.86rem;
    top: 0.73rem;
    border: 0;
  }

  .schoolAdvantage {
    width: 3.75rem;
    height: 3.56rem;
    background: url("../../../assets/image/schoolpage/gzgift2.png");
    background-size: 100% 100%;
    overflow: hidden;
    .content {
      .contentText{
        color: #FFFFFF;
        text-align: center;
        font-size: .16rem;
        span{
          text-decoration: line-through;
        }
      }
      ul{
        margin-top: 0.4rem;
      }
      .item{
        width: 2.7rem;
        margin: 0.01rem auto 0rem;
        height: auto;
        overflow: hidden;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: .02rem;
        padding: .05rem .08rem 0rem .05rem;
        margin-top: .1rem;
        i{
          font-style: normal;
          float: left;
          width: .26rem;
          margin-left: .1rem;
          img{
            float: left;
            width: 0.14rem;
            height: 0.13rem;
            margin-top: 0.07rem;
            margin-right: 0.12rem;
          }
        }
        p{
          width: 2.21rem;
          float: left;
          color: rgba(54, 54, 54, 0.8);
          font-size: .14rem;
          text-align: left;
          margin-top: .03rem;
          margin-left: 0;
        }
      }
      .supplement{
        margin-left: .65rem;
        color: rgba(23, 6, 6, 0.4);
        margin-top: 0.1rem;
      }
      .btn{
        width: 1.48rem;
        height: .44rem;
        text-align: center;
        margin: .5rem auto 0;
        background: url("../../../assets/image/schoolpage/btnbg.png") no-repeat center;
        background-size: 100% 100%;
        color: #B82926;
        font-size: .16rem;
        line-height: .44rem;
        font-weight: bold;
    }
      padding-top: 0.82rem;
      // width: 2.95rem;
      // height: 3.36rem;
      // margin: 0 auto;
      // background: url("../../../assets/image/schoolpage/gzgift.png") no-repeat
      //   center;
      // background-size: 100% 100%;
      .contentTile {
        width: 1.45rem;
        height: 0.32rem;
        position: absolute;
        background: linear-gradient(
          318deg,
          rgba(240, 110, 108, 1) 0%,
          rgba(240, 145, 144, 1) 100%
        );
        box-shadow: 0px 2px 4px 0px rgba(240, 110, 108, 0.2),
          0px 1px 1px 0px rgba(255, 255, 255, 0.5);
        border-radius: 0px 0px 10px 10px;
        font-size: 0.17rem;
        font-weight: 600;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        margin-left: 1rem;
        margin-top: -0.05rem;
      }
      .title {
        width: 1.43rem;
        height: 0.2rem;
        background: url("../../../assets/image/active/guangzhouOpenUniversity/priceBg.png")
          no-repeat center;
        margin: 0 auto;
        margin-top: 0.7rem;
        background-size: 100% 100%;
        text-align: center;
        color: rgba(23, 6, 6, 0.8);
        span {
          text-decoration: line-through;
        }
      }
      .detail {
        margin-top: 1.2rem;
        .items {
          width: 2.7rem;
          margin: 0.01rem auto 0rem;
          height: auto;
          overflow: hidden;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 0.02rem;
          padding: 0.05rem 0.08rem 0rem 0.05rem;
          &:last-of-type {
            margin-bottom: 0;
          }
          i {
            font-style: normal;
            float: left;
            width: 0.26rem;
            margin-left: 0.1rem;
            img {
              float: left;
              width: 0.14rem;
              height: 0.13rem;
              margin-top: 0.07rem;
              margin-right: 0.12rem;
            }
          }
          p {
            width: 2.21rem;
            float: left;
            color: rgba(54, 54, 54, 0.8);
            font-size: 0.14rem;
            text-align: left;
            margin-top: 0.03rem;
            margin-left: 0;
          }
        }
        p {
          margin-left: 0.35rem;
          color: rgba(23, 6, 6, 0.4);
          margin-top: 0.08rem;
        }
      }
      button {
        border: none;
        width: 1.48rem;
        height: 0.44rem;
        background: url("../../../assets/image/active/SouthChinaAgriculturalUniversity/btnbg.png")
          no-repeat center;
        background-size: 100% 100%;
        border-radius: 0.17rem;
        font-weight: bold;
        font-size: 0.16rem;
        color: rgba(236, 14, 14, 1);
        margin: 0.43rem auto 0;
        display: block;
      }
    }
  }
  .charge {
    background: rgb(255, 255, 255);
    .chargetop {
      background: #fff;
      width: 3.75rem;
      height: 0.6rem;
      line-height: 0.6rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-left: 0.1rem;
        margin-top: -0.05rem;
      }
      p {
        display: inline-block;
        font-size: 0.17rem;
        height: 0.24rem;
        line-height: 0.24rem;
        color: #170606;
      }
    }

    .region {
      margin-top: 0.05rem;
      .regionTop {
        position: relative;
        height: 0.24rem;
        line-height: 0.24rem;
        .line {
          display: inline-block;
          width: 0.04rem;
          height: 0.16rem;
          background-color: #f06e6c;
          margin-left: 0.4rem;
        }
        .regionTitle {
          position: absolute;
          display: inline-block;
          font-size: 14px;
          color: rgba(23, 6, 6, 1);
          font-weight: 400;
          left: 0.54rem;
          top: -0.02rem;
        }
      }
      .regionText {
        p {
          margin-left: 0.54rem;
          display: inline-block;
          width: 2.94rem;
          font-size: 14px;
        }
      }
    }
  }

  .highUniversityBox {
    background: #fff;
    padding-bottom: 0.1rem;
    overflow: hidden;
    .title {
      background: #fff;
      width: 100%;
      height: 0.48rem;
      .fl {
        width: 50%;
        height: 0.48rem;
        float: left;
        position: relative;
        .flTop {
          height: 0.21rem;
          line-height: 0.21rem;
          .line {
            position: absolute;
            display: inline-block;
            width: 0.04rem;
            height: 0.04rem;
            background-color: #f06e6c;
            top: 0.05rem;
            left: 23%;
          }
          .schoolType {
            position: absolute;
            display: inline-block;
            font-size: 14px;
            color: rgba(23, 6, 6, 0.4);
            font-weight: 400;
            left: 0.54rem;
            top: -0.02rem;
          }
        }
        .year {
          padding-left: 0.54rem;
          font-size: 0.2rem;
          color: rgba(23, 6, 6, 0.6);
        }
        span {
          color: #f06e6c;
          color: rgba(23, 6, 6, 0.6);
        }
      }
      .fr {
        width: 50%;
        height: 0.48rem;
        float: left;
        position: relative;
        .frTop {
          height: 0.21rem;
          line-height: 0.21rem;
          .line {
            position: absolute;
            display: inline-block;
            width: 0.04rem;
            height: 0.04rem;
            background-color: #f06e6c;
            top: 0.05rem;
            left: 14%;
          }
          .bookPrice {
            position: absolute;
            display: inline-block;
            font-size: 14px;
            color: rgba(23, 6, 6, 0.4);
            font-weight: 400;
            left: 0.35rem;
            top: -0.02rem;
            span {
              font-size: 12px;
              color: rgba(23, 6, 6, 0.4);
            }
          }
        }
        .bookPriceYear {
          padding-left: 0.35rem;
          font-size: 0.2rem;
          color: rgba(23, 6, 6, 0.6);
        }
        span {
          color: #f06e6c;
          color: rgba(23, 6, 6, 0.6);
        }
      }
    }
    .highUniversity {
      margin: auto 0.1rem;
      background-color: #fff;
      border-radius: 0.1rem;
      .title {
        // width: 3.35rem;
        height: 0.5rem;
        line-height: 0.5rem;
        text-align: center;
        border-radius: 0.1rem 0.1rem 0rem 0rem;
        background: rgba(240, 110, 108, 0.0798);
        border: 1px solid rgba(240, 110, 108, 0.4);
        .lineLeft {
          width: 0.1rem;
          height: 0.02rem;
          border-radius: 0.01rem;
          background: #f06e6c;
        }
        .text {
          font-size: 0.17rem;
          color: #f06e6c;
          font-weight: 600;
        }
      }
      table {
        width: 3.55rem;
        border-collapse: separate;
        td {
          text-align: center;
          vertical-align: middle;
          padding: 0.1rem 0;
        }
      }
      table tr th:first-child,
      table tr td:first-child {
        /*设置table左边边框*/
        border-left: 1px solid pink;
      }
      table tr th:last-child,
      table tr td {
        /*设置table右边边框*/
        border-right: 1px solid pink;
      }

      table tr td:first-child,
      table tr td:nth-child(2),
      table tr td:nth-child(3),
      table tr td:last-child {
        /*设置table表格每列底部边框*/
        border-bottom: 1px solid pink;
      }
      table tr th {
        background: pink;
      }
      table tr:first-child th:first-child {
        border-top-left-radius: 0.1rem;
      }

      table tr:first-child th:last-child {
        border-top-right-radius: 0.1rem;
      }
      table tr:last-child td:first-child {
        border-bottom-left-radius: 0.1rem;
      }

      table tr:last-child td:last-child {
        border-bottom-right-radius: 0.1rem;
      }

      .content {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          height: 0.8rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.42rem;
            text-align: center;
            margin-top: 55.5%;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          height: 0.8rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          .contentText {
            display: inline-block;
            width: 1.86rem;
            margin: 0.1rem 0.17rem 0.1rem 0.13rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 32.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
      .contentTwo {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          float: left;
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.42rem;
            text-align: center;
            margin-top: 110%;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          border-left: 1px solid rgba(240, 110, 108, 0.4);
          .contentText {
            display: inline-block;
            width: 1.86rem;
            margin: 0.1rem 0.17rem 0.1rem 0.13rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 40.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
      .contentThree {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        position: relative;
        .titleText {
          width: 0.7rem;
          height: 0.58rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          display: inline-block;
          p {
            display: inline-block;
            width: 0.7rem;
            height: 0.58rem;
            text-align: center;
            line-height: 0.58rem;
            font-weight: 600;
            font-size: 0.15rem;
            color: rgba(23, 6, 6, 0.61);
          }
        }
        .flLeft {
          width: 2.11rem;
          height: 0.58rem;
          float: left;
          border-right: 1px solid rgba(240, 110, 108, 0.4);
          line-height: 0.58rem;
          .contentText {
            display: inline-block;
            width: 1.86rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
            padding-left: 0.1rem;
          }
        }
        .flRight {
          float: left;
          width: 0.5rem;
          text-align: center;
          line-height: 100%;
          position: absolute;
          left: 82%;
          top: 20.5%;
          .content {
            width: 0.42rem;
            height: 0.38rem;
            background-color: aquamarine;
          }
          p {
            margin-top: 0.04rem;
          }
          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
          }
          .bottom {
            font-size: 0.12rem;
            color: #f06e6c;
          }
        }
      }
    }
  }

  .clear {
    clear: both;
  }
  .story {
    background: rgb(255, 255, 255);
    margin-top: 0.1rem;
    height: 2.74rem;
    .top {
      width: 3.75rem;
      height: 0.5rem;
      line-height: 0.5rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-top: -0.05rem;
        margin-left: 0.1rem;
      }
      span {
        font-size: 0.17rem;
      }
    }

    .content {
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      position: relative;
      height: 2.26rem;
      .item {
        display: inline-block;
        width: 1.55rem;
        margin-left: 0.1rem;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04),
          0px 1px 2px 0px rgba(0, 0, 0, 0.04),
          0px 2px 6px 0px rgba(0, 0, 0, 0.04);
        border-radius: 5px;
        height: 2.16rem;
        position: relative;
        img {
          width: 1.55rem;
          height: 1.22rem;
          border-radius: 5px 5px 0px 0px;
          object-fit: cover;
        }
        p {
          margin-left: 0.05rem;
        }
        .text {
          width: 1.39rem;
          font-size: 0.13rem;
          margin-top: 0.08rem;
          color: rgba(23, 6, 6, 0.8);
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }
        .date {
          position: absolute;
          font-size: 0.12rem;
          color: rgba(23, 6, 6, 0.4);
          bottom: 0.02rem;
        }
      }
    }
  }
  .userMessage {
    background: rgb(255, 255, 255);
    margin-top: 0.1rem;
    margin-bottom: 0.6rem;
    .top {
      width: 3.75rem;
      height: 0.7rem;
      line-height: 0.7rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }
      .topTitle {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }
    .textContent {
      overflow: hidden;
      height: 4.1rem;
      .userMessageContentList {
        width: 3.75rem;
        &.anim {
          transition: all 1s;
          margin-top: -1.7rem;
        }
        .content {
          overflow: hidden;
          height: 1.33rem;
          position: relative;
          .line {
            position: absolute;
            bottom: 0.01rem;
            height: 1px;
            width: 2.76rem;
            left: 0.64rem;
            background-color: rgba(23, 6, 6, 0.08);
          }
          .fl {
            width: 0.64rem;
            img {
              width: 0.38rem;
              height: 0.38rem;
              float: right;
              border-radius: 50%;
              margin-top: 0.11rem;
            }
          }
          .fr {
            width: 3.11rem;
            height: 1.14rem;
            .userName {
              margin-left: 0.1rem;
              margin-top: 0.12rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }
            .uesrQuestion {
              display: inline-block;
              width: 2.76rem;
              margin-top: 0.03rem;
              margin-left: 0.1rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }
            .content {
              position: relative;
              margin-bottom: 0.09rem;
              height: auto;

              .line {
                position: absolute;
                display: inline-block;
                width: 0.02rem;
                height: 0.14rem;
                background-color: #f06e6c;
                top: 0.17rem;
                left: 0.01rem;
              }
              .answer {
                display: inline-block;
                background: rgba(248, 248, 248, 1);
                border-radius: 3px 3px 3px 0px;
                width: 2.86rem;
                font-size: 0.14rem;
                color: rgba(23, 6, 6, 0.8);
                padding: 0.13rem 0.07rem 0.13rem 0.1rem;
              }
            }
          }
        }
      }
    }

    .userMessageContent {
      width: 3.75rem;
      overflow: hidden;
      .fl {
        width: 0.64rem;
        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }
      .fr {
        width: 3.11rem;
        position: relative;
        .userName {
          margin-left: 0.1rem;
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
          display: inline-block;
          width: 2.48rem;
          height: 0.24rem;
        }
        textarea {
          width: 2.87rem;
          height: 0.86rem;
          border-radius: 0px 5px 5px 5px;
          // opacity:0.4;
          padding: 0.13rem 0.1rem 0.13rem 0.1rem;
          margin-top: 0.03rem;
        }
        span {
          position: absolute;
          font-size: 0.09rem;
          color: rgba(23, 6, 6, 0.4);
          top: 1.07rem;
          right: 0.305rem;
        }
        button {
          width: 1rem;
          height: 0.35rem;
          background: rgba(240, 110, 108, 1);
          border: 0;
          margin-top: 0.1rem;
          margin-left: 0.89rem;
          margin-bottom: 0.2rem;
          border-radius: 0.2rem;
          color: #ffffff;
          font-size: 0.16rem;
          font-weight: 500;
        }
      }
    }
  }
  .InvitationBg {
    position: relative;
    img {
      width: 3.75rem;
      height: 1.53rem;
      // margin-top: .05rem;
    }
    button {
      position: absolute;
      width: 1.2rem;
      height: 0.35rem;
      background: rgba(255, 255, 255, 1);
      border: 0;
      border-radius: 0.2rem;
      color: #f06e6c;
      font-size: 0.15rem;
      font-weight: 500;
      top: 0.87rem;
      left: 0.32rem;
    }
  }
  .QRCode {
    position: relative;
    width: 3.75rem;
    height: 2.08rem;
    background: rgb(255, 255, 255);
    .bg {
      width: 3.75rem;
      height: 1.66rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .QRCodeImg {
      position: absolute;
      width: 0.75rem;
      height: 0.74rem;
      left: 2.02rem;
      top: 0.6rem;
    }
    .topText {
      position: absolute;
      font-size: 0.12rem;
      color: rgba(23, 6, 6, 0.8);
      left: 1.99rem;
      top: 0.35rem;
      font-weight: 600;
    }
    .bottomText {
      position: absolute;
      font-size: 0.12rem;
      color: rgba(23, 6, 6, 0.8);
      left: 1.99rem;
      top: 1.42rem;
      font-weight: 600;
    }
  }
  .fixBtn {
    position: fixed;
    bottom: 0.01rem;
    right: 0.01rem;
    width: 1.25rem;
    height: 1.27rem;
    line-height: 0.4rem;
    text-align: center;
    color: #fff;
    z-index: 999;
    background-image: url("../../../assets/image/active/enrollmentHomepage/<EMAIL>");
    background-size: 100%;
    border-radius: 10px;
    a {
      display: block;
      height: 100%;
      color: #fff;
      font-size: 21px;
      text-decoration: none;
    }
  }
  .bottomBox {
    background: rgb(255, 255, 255);
    width: 3.75rem;
    height: 0.5rem;
    line-height: 0.5rem;
    text-align: center;
    a {
      color: #f06e6c;
      font-size: 0.14rem;
    }
    span {
      color: #f06e6c;
      display: inline-block;
      margin-top: 0.01rem;
    }
    .callUs {
      margin-right: 0.1rem;
    }
    .official {
      margin-left: 0.1rem;
    }
  }
  .showall {
    height: 4.2rem;
  }
  .showall.active {
    height: auto;
  }
  .schoolDetails {
    width: 3.75rem;
    background: rgb(255, 255, 255);
    overflow: hidden;
    img {
      width: 3.55rem;
      height: 1.79rem;
      border-radius: 0.01rem;
      margin: 0.1rem 0.1rem 0rem 0.1rem;
    }
    p {
      display: inline-block;
      width: 3.15rem;
      margin: 0.2rem 0.3rem 0.1rem 0.3rem;
      padding: 0rem 0.05rem 0rem 0.05rem;
    }
  }
  .lookMore {
    background: rgb(255, 255, 255);
    padding-top: 0.06rem;
    text-align: center;
    height: 0.54rem;
    line-height: 0.54rem;
    position: relative;
    span {
      color: rgba(23, 6, 6, 0.4);
    }
    img {
      position: absolute;
      top: -0.03rem;
      left: 1.7rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }
  .studentStory {
    background: rgb(255, 255, 255);
    height: 2.41rem;
    width: 3.75rem;
    margin-top: 0.1rem;
    .top {
      height: 0.7rem;
      width: 3.75rem;
      line-height: 0.7rem;
      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }
      span {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }
  }
}

.swiper {
  width: 100%;
  height: 160px;
  overflow: hidden;
  margin-top: 0.22rem;
}
.swiper-slide {
  width: 80%;
  height: 160px;
}
.swiper-slide-active img {
  margin-top: 0;
  width: 100%;
  height: 100%;
}
.swiperImg {
  display: block;
  margin: 0 auto;
  margin-top: 3.5%;
  width: 85%;
  height: 85%;
  border-radius: 5px;
}

.van-cell {
  height: 0.54rem;
  line-height: 0.54rem;
}
.lotteryRed {
  position: fixed;
  bottom: 1rem;
  right: 0.1rem;
  width: 0.87rem;
  transition: all 1s;
  z-index: 99;
}
.rictext {
  font-size: 0.15rem;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: rgba(240, 110, 108, 1);
}
</style>


