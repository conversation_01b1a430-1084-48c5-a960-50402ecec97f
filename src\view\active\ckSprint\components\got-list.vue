<template>
  <dialog-container v-model="show" @close='close'>
    <div class="yz-ck-gotList">
      <div class="t1"></div>
      <ul class="list" v-infinite-scroll="loadMore" :infinite-scroll-disabled="loading" infinite-scroll-distance="10">
        <li v-for="(item, index) in list" :key="index">
          <span>{{drawNames[item.drawType]}}</span>
          <span>{{item.createTime | formatTimeByMoment('YYYY.MM.DD')}}</span>
          <span class="share" @click='shareDraw(item)'>分享</span>
        </li>
      </ul>
    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container.vue';

export default {
  components: { DialogContainer },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },

  data () {
    return {
      show: false,
      drawNames: ['状元签', '上上签', '上上签', '上上签', '万能签', '锦鲤签', '幸运签', '读书签', '奋斗签', '奋斗签', '梦想签', '拼搏签', '拼搏签', '上进签', '上进签', '上进签', '上进签'],
      drawDec: [
        '金榜题名，光宗耀祖',
        '梦想成真，前程似锦',
        '水到渠成，马到成功',
        '逢考必过，顺风顺水',
        '一帆风顺，得心应手',
        '锦鲤现身，神运加持',
        '你有多努力，便有多幸运',
        '学而时习之，不亦说乎',
        '星光不问赶路人，时光不负有心人',
        '宝剑锋从磨砺出，梅花香自苦寒来',
        '以梦为马，不负韶华',
        '将来的你，一定会感谢现在拼搏的自己',
        '长风破浪会有时，直挂云帆济沧海',
        '常读书，将是你未来成功的伏笔',
        '读书破万卷，下笔方能如有神助',
        '欲穷千里目，更上一层楼',
        '你若盛开，蝴蝶自来',
      ],
    };
  },
  watch: {
    value (val) {
      this.show = val;
    },
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '11', '抽签记录');

    this.show = this.value
  },
  methods: {
    close () {
      this.show = false;
      this.$emit('input', false);
      this.$emit('changShare', false, {});
    },
    loadMore () {
      this.$emit('loadMore');
    },
    shareDraw (item) {
      const shareObj = {
        link: '/active/ckSprint?drawType=' + item.drawType,
        drawType: item.drawType,
        title: `我今天抽到了「${this.drawNames[item.drawType]}」，快来一起测测2021成考走势吧～`,
        shareDesc: `签文解读：您将在今年成考过后，${this.drawDec[item.drawType]}！`,
      }
      this.$emit('changShare', true, shareObj);
    }
  },
};
</script>

<style lang="less">
.yz-ck-gotList {
  text-align: center;
  padding-top: 0.3rem;
  line-height: 1.2;
  .t1 {
    background: url(../../../../assets/image/active/ckSprint/c1.png) no-repeat;
    background-size: 100% 100%;
    width: 0.93rem;
    height: 0.3rem;
    margin: 0 auto 0.13rem;
  }
  .list {
    overflow: auto;
    height: 2.68rem;
    width: 100%;
    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #fff;
      font-size: 0.14rem;
      padding: 0 0.26rem;
      margin-bottom: 0.1rem;
      .share {
        font-weight: 600;
        color: #f9e08a;
        text-decoration: underline;
      }
    }
  }
}
</style>
