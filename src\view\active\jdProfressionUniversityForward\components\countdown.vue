<template>
  <div class="countdown" v-if="isShow" :style="{color:txt_color}">
    <span class="text">{{text}}</span>
    <span class="num day">{{dayFirst}}</span><span class="num day">{{daySecond}}</span><p style="display:inline-block;line-height:.22rem">&thinsp;天</p>
    <span class="num hour">{{hourFirst}}</span><span class="num hour">{{hourSecond}}</span>&thinsp;时
    <span class="num minute">{{minuteFirst}}</span><span class="num minute">{{minuteSecond}}</span>&thinsp;分
    <span class="num second">{{secondFirst}}</span><span class="num minute">{{secondTwo}}</span>&thinsp;秒
    <span class="num big millisecond">{{millisecond}}</span>
  </div>
</template>

<script>
  export default {
    name: 'countdown',
    props: ['startTime','endTime','txt_color','text'],
    data() {
      return {
        isShow: false,
        timer: null,
        day: '00',
        dayFirst:'0',
        daySecond:'0',
        hour: '00',
        hourFirst:'0',
        hourSecond:'0',
        minute: '00',
        minuteFirst:'0',
        minuteSecond:'0',
        secondFirst: '0',
        secondTwo:'0',
        millisecond: '0',
        // startTime: 1529510400000 ,   // 开始时间：2018-06-21 00:00:00
        // endTime: 1530374400000 ,  // 结束时间：2018-06-01 00:00:00
      }
    },
    beforeDestroy() {
      this.clearTimer();
    },
    created() {
        console.log(this.startTime);
        console.log(this.endTime);
    },
    methods: {
      countdown: function () {
        const now = new Date().getTime();
        const surplus = this.endTime - now;
        const end = now - this.startTime;
        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;

        this.day = this.complement(s / oneDay).toString();
        var dayIndex = this.day.split('');
        this.dayFirst = dayIndex[0];
        this.daySecond = dayIndex[1];

        this.hour = this.complement(s % oneDay / oneHour).toString();
        var hourIndex = this.hour.split('');
        this.hourFirst = hourIndex[0];
        this.hourSecond = hourIndex[1];


        this.minute = this.complement(s % oneDay % oneHour / 60).toString();
        var minuteIndex = this.minute.split('');
        this.minuteFirst = minuteIndex[0];
        this.minuteSecond = minuteIndex[1];

        this.second = this.complement(s % oneDay % oneHour % 60).toString();
        var secondIndex = this.second.split('');
        this.secondFirst = secondIndex[0];
        this.secondTwo = secondIndex[1];

        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      countdown2: function () {
        const now = new Date().getTime();
        const surplus = this.startTime - now;

        if (surplus <= 0) {
          this.clearTimer();
          return;
        }
        const oneHour = 60 * 60;
        const oneDay = oneHour * 24;
        const s = parseInt(surplus / 1000);
        const ms = surplus % 1000;

        this.day = this.complement(parseInt(s / oneDay));
        this.hour = this.complement(parseInt(s % oneDay / oneHour));
        this.minute = this.complement(parseInt(s % oneDay % oneHour / 60));
        this.second = this.complement(parseInt(s % oneDay % oneHour % 60));
        this.millisecond = ms > 100 ? (ms + '').substring(0, 1) : 0;
      },
      complement: function (num) {
        return num < 10 ? '0' + num : num;
      },
      clearTimer: function () {
        clearInterval(this.timer)
      },
      // 获取服务器时间，判断活动是否已结束
      getSystemDateTime: function (now) {
        // debugger
        //console.log("getSystemDateTime=>" + now);
        this.isShow = true;
        if ( now < this.startTime) {
          this.timer = setInterval(this.countdown2, 100);
        }else {
          this.timer = setInterval(this.countdown, 100);
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  .mb5{
    margin-bottom: .5rem;
  }
  .text{
    font-size: .12rem;
    font-weight: bold;
    color:#FF443F;
   }
  .countdown{
    position: relative;
    z-index: 1;
    text-align: center;
    font-size: .12rem;
    color:#FF443F;
    font-weight: bold;
    background-size:80%;background-position-x: .45rem;
    .num {
      display:inline-block;
      width:.16rem;
      height:.26rem;
      line-height: .26rem;
      border-radius: .04rem;
      vertical-align:bottom;
      background-color:#FF443F;
      margin: 0 .01rem;
      color: #fff;
      font-size:.14rem;
      font-weight: bold;
      // &.big{
      //   width:.11rem;
      // }
    }
  }
</style>
