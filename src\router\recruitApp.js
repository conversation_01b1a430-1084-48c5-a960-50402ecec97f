import { loadPcdJson } from '../common';

export default [
  {
    path: '/payment/student',
    name: 'recruit',
    component: () => import('../view/recruit/home'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/payment/transferPage',
    name: 'transferPage',
    component: () => import('../view/recruit/transferPage'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/home',
    name: 'searchStudent',
    component: () => import('../view/recruit/payment/searchStudent'),
    meta: { title: '招生应用', keepAlive: true, loadDict: true }
  },
  {
    path: '/recruit/addEnrollStuList',
    name: 'addEnrollStuList',
    component: () => import('../view/recruit/addEnrollStuList.vue'),
    meta: { title: '学员网报申请', loadDict: true }
  },
  {
    path: '/recruit/addEnrollStu',
    name: 'addEnrollStu',
    component: () => import('../view/recruit/addEnrollStu.vue'),
    meta: { title: '新增学员网报申请', loadDict: true }
  },
  {
    path: '/recruit/addRecruitStudentDes',
    name: 'addRecruitStudentDes',
    component: () => import('../view/recruit/addRecruitStudentDes.vue'),
    meta: { title: '新增学员网报申请', loadDict: true }
  },
  {
    path: '/recruit/stuTransaction',
    name: 'stuTransaction',
    component: () => import('../view/recruit/stuTransaction.vue'),
    meta: { title: '学员网报异动申请', loadDict: true }
  },
  {
    path: '/recruit/stuTransactionDes',
    name: 'stuTransactionDes',
    component: () => import('../view/recruit/stuTransactionDes.vue'),
    meta: { title: '学员网报异动详情', loadDict: true }
  },
  {
    path: '/payment/options',
    name: 'paymentOptions',
    component: () => import('../view/recruit/payment/paymentOptions'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/payment/qr',
    name: 'paymentQR',
    component: () => import('../view/recruit/payment/paymentQR'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/login',
    name: 'recruitLogin',
    component: () => import('../view/recruit/login'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/student',
    name: 'recruitStudent',
    component: () => import('../view/recruit/student/list'),
    meta: { title: '招生应用', keepAlive: true, loadDict: true }
  },
  {
    path: '/recruit/student/add',
    name: 'recruitStudentAdd',
    component: () => import('../view/recruit/student/type'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/student/add/:type',
    name: 'recruitStudentType',
    component: () => import('../view/recruit/student/add'),
    beforeEnter: loadPcdJson,
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/student/detail/:id',
    name: 'recruitStudentDetail',
    component: () => import('../view/recruit/student/detail'),
    meta: { title: '招生应用', loadDict: true }
  },
  {
    path: '/recruit/searchSite',
    name: 'searchSite',
    component: () => import('../view/recruit/searchSite/searchSite'),
    meta: { title: '查询现场确认点', loadDict: true, }
  },
  {
    path: '/recruit/searchSiteDes',
    name: 'searchSiteDes',
    component: () => import('../view/recruit/searchSite/searchSiteDes'),
    meta: { title: '查询现场确认点指引', loadDict: true, }
  },
  {
    path: '/recruit/searchSiteMap',
    name: 'searchSiteMap',
    component: () => import('../view/recruit/searchSite/searchSiteMap'),
    meta: { title: '指引导航', loadDict: true, }
  },
  {
    path: '/recruit/student/detail',
    name: 'studentDetail',
    component: () => import('../view/recruit/payment/studentDetail'),
    meta: { title: '学员查询', loadDict: true, }
  },
  {
    path: '/recruit/replyMessage',
    name: 'replyMessage',
    component: () => import('../view/recruit/replyMessage/home'),
    meta: { title: '学员评论管理', loadDict: true, }
  },
  {
    path: '/recruit/messageList',
    name: 'messageList',
    component: () => import('../view/recruit/replyMessage/messageList'),
    meta: { title: '', loadDict: true, }
  },
  {
    path: '/recruit/message',
    name: 'allReplymessage',
    component: () => import('../view/recruit/replyMessage/message'),
    meta: { title: '', loadDict: true, }
  }, {
    path: '/recruit/problem/SearchProblem',
    name: 'SearchProblem',
    component: () => import('../view/recruit/problem/SearchProblem'),
    meta: { title: '常见问题' }
  },
  {
    path: '/recruit/problem/Search',
    name: 'searchPage',
    component: () => import('../view/recruit/problem/Search'),
    meta: { title: '员工知识库' }
  },
  {
    path: '/recruit/problem/details',
    name: 'SearchProblemDetails',
    component: () => import('../view/recruit/problem/details'),
    meta: { title: '常见问题' }
  }, {
    path: '/recruit/problem/webResults',
    name: 'webResults',
    component: () => import('../view/recruit/problem/webResults'),
    meta: { title: '常见问题' }
  }, {
    path: '/recruit/problem/webResults/detail',
    name: 'webResults.detail',
    component: () => import('../view/recruit/problem/articleDetails'),
    meta: { title: '常见问题', loadDict: true }
  }, {
    path: '/recruit/forwarding/pending',
    name: 'pending',
    component: () => import('../view/recruit/forwarding/pending'),
    meta: { title: '学员转报审批', loadDict: true }
  }, {
    path: '/recruit/forwarding/director',
    name: 'director',
    component: () => import('../view/recruit/forwarding/director'),
    meta: { title: '学员转报审批', loadDict: true }
  }, {
    path: '/recruit/forwarding/management',
    name: 'management',
    component: () => import('../view/recruit/forwarding/management'),
    meta: { title: '学员转报审批', loadDict: true }
  }, {
    path: '/recruit/forwarding/submit',
    name: 'forwarding.submit',
    component: () => import('../view/recruit/forwarding/submit'),
    meta: { title: '转报学员', loadDict: true }
  }, {
    path: '/recruit/forwarding/applyDetail',
    name: 'forwarding.applyDetail',
    component: () => import('../view/recruit/forwarding/applyDetail'),
    meta: { title: '转报申请', loadDict: true }
  },
  {
    path: '/recruit/callTask',
    name: 'callTask',
    component: () => import('../view/recruit/callTask/index'),
    meta: { title: '学员外呼管理', loadDict: true, }
  },
  {
    path: '/recruit/callTask/detail',
    name: 'callTaskDetail',
    component: () => import('../view/recruit/callTask/detail'),
    meta: { title: '学员外呼管理', loadDict: true, }
  },
  {
    path: '/recruit/callTask/follow',
    name: 'callTaskFollow',
    component: () => import('../view/recruit/callTask/follow'),
    meta: { title: '学员外呼管理', loadDict: true, }
  },
  {
    path: '/recruit/callTask/followRecord',
    name: 'callTaskFollowRecord',
    component: () => import('../view/recruit/callTask/followRecord'),
    meta: { title: '跟进记录', loadDict: true, }
  },
];
