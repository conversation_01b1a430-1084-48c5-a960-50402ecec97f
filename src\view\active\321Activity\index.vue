<template>
    <div class="activity_wrap">
      <div class="banner">
        <router-link :to="{name:'My321ActivityCert'}" class="cert" ><img src="../../../assets/image/active/321Activity/cert.png" alt=""></router-link>
        <router-link :to="{name:'321ActivityRule'}" class="rule" ><img src="../../../assets/image/active/321Activity/rule.png" alt=""></router-link>
        <!-- <div class="toSay" @click="beginUpload"><img src="../../../assets/image/active/321Activity/btn.png" alt=""></div> -->
        <img class="advance" src="../../../assets/image/active/321Activity/advance.png" alt="">
        <div class="num">
          已有{{joinNum}}人参与 </div>
        <!-- <img src="../../../assets/image/active/321Activity/321.png" alt="" class="txt" > -->
        <div class="txt_war">
          <div class="banner_swiper" >
            <div class="gdBox gdBox1" :style="{width:width+'px'}">
              <div class="gdBox_wrap1 gdBox_wrap" v-for="item,i in audioList.slice(0,5)" @click="bannerStop(i)">
              <div class="img">
                <img :src="item.headImg|defaultAvatar">
              </div>
              <div class="txt_fr">
                <span>{{item.txt}}</span>
                <!-- <span><audio :src="item.url" :ref="'audio'+i"></audio></span> -->
              </div>
            </div>
              <div class="gdBox_wrap1 gdBox_wrap" v-for="item,i in audioList.slice(0,5)" @click="bannerStop(i)">
                <div class="img">
                  <img :src="item.headImg|defaultAvatar">
                </div>
                <div class="txt_fr">
                  <span>{{item.txt}}</span>
                  <!-- <span><audio :src="item.url" :ref="'audio'+i"></audio></span> -->
                </div>
              </div>
            </div>
            <div class="gdBox gdBox2" :style="{width:width2+'px'}">
              <div class="gdBox_wrap2 gdBox_wrap" v-for="item,i in audioList.slice(5,10)" @click="bannerStop(i+5)">
                <div class="img">
                  <img :src="item.headImg|defaultAvatar">
                </div>
                <div class="txt_fr">
                  <span>{{item.txt}}</span>
                  <!-- <span><audio :src="item.url" :ref="'audio'+(i+5)"></audio></span> -->
                </div>
              </div>
              <div class="gdBox_wrap2 gdBox_wrap" v-for="item,i in audioList.slice(5,10)" @click="bannerStop(i+5)">
                <div class="img">
                  <img :src="item.headImg|defaultAvatar">
                </div>
                <div class="txt_fr">
                  <span>{{item.txt}}</span>
                  <!-- <span><audio :src="item.url" :ref="'audio'+(i+5)"></audio></span> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="videoSwiper">
        <div class="content">
          <a class="item">
            <video src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/1-1.mp4"  ref="video0" @click="playVideo(0)"></video>
            <template v-if="showPlay[0].state!='play'">
              <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/1feng.png" alt="">
              <div class="bg"></div>
              <p class="text"> 32.1W</p>
              <p class="play" @click="playVideo(0)"></p>
            </template>
          </a>
          <a class="item">
            <video   ref="video1" @click="playVideo(1)">
              <source src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/2-2.mp4">
              </source>
            </video>
            <template v-if="showPlay[1].state!='play'">
            <div class="bg"></div>
              <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/2feng.png" alt="">
            <p class="text">29.9W</p>
            <p class="play" @click="playVideo(1)"></p>
            </template>
          </a>
          <a class="item">
            <video src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/3-3.mp4"   ref="video2" @click="playVideo(2)"></video>
             <template v-if="showPlay[2].state!='play'">
               <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/3feng.png" alt="">
               <div class="bg"></div>
               <p class="text">30.9W</p>
               <p class="play" @click="playVideo(2)"></p>
             </template>
          </a>
          <a class="item">
            <video src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/4-4.mp4"  ref="video3" @click="playVideo(3)"></video>
            <template v-if="showPlay[3].state!='play'">
              <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/4feng.png" alt="">
              <div class="bg"></div>
              <p class="text">28.6W</p>
              <p class="play" @click="playVideo(3)"></p>
            </template>
          </a>
          <a class="item">
            <video src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/5-5.mp4"   ref="video4" @click="playVideo(4)"></video>
            <template v-if="showPlay[4].state!='play'">
              <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/5feng.png" alt="">
              <div class="bg"></div>
              <p class="text">26.6W</p>
              <p class="play" @click="playVideo(4)"></p>
            </template>
          </a>
          <a class="item">
            <video src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/6-6.mp4"   ref="video5" @click="playVideo(5)"></video>
            <template v-if="showPlay[5].state!='play'">
              <img src="http://yzims.oss-cn-shenzhen.aliyuncs.com/321/6feng.png" alt="">
              <div class="bg"></div>
              <p class="text">21.3w</p>
              <p class="play" @click="playVideo(5)"></p>
            </template>
          </a>
        </div>
      </div>
      <div class="all_txt">
        <div class="top"><img src="../../../assets/image/active/321Activity/top.png" alt=""></div>
         <img src="../../../assets/image/active/321Activity/text.png" alt="" class="txt">
        <div class="xiaozhi">
          <p>上进小智</p>
          <img src="../../../assets/image/active/321Activity/xiaozhi.png" alt="">
        </div>
        <div class="list">
          <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
          <ul>
            <li v-for="item,i in allList" >
                <div class="info">
                  <img :src="item.headImg |defaultAvatar" alt="">
                  <div class="name">
                    <h3>第{{item.udNum}}位上进者</h3>
                    <p>{{item.nikeName||''|b64DecodeUnicode}}发布了一条上进宣言</p>
                  </div>
                </div>
              <div class="audio" v-if="item.udType==1" @click="playAudio(item.udId,i)">{{item.resourcesTime}}
                <img src="../../../assets/image/active/321Activity/''@2x.png" alt="">
                <img src="../../../assets/image/active/321Activity/audio_icon.png" alt="" v-if="item.state!='play'">
                <img src="../../../assets/image/active/321Activity/audio.gif" alt="" v-else>
                <!-- <audio :src="item.resourcesUrl |imgBaseURL" :ref="'media'+i"></audio> -->
              </div>

              <div class="video" v-if="item.udType==2"  @click="toShow(item.udId,i)">
                <img :src="item.resourcesUrl+'?x-oss-process=video/snapshot,t_1000,f_jpg,ar_auto'|imgBaseURL" alt="">
                <img src="../../../assets/image/active/321Activity/play_black.png" alt="">
                <!--<video :src="item.resourcesUrl |imgBaseURL" ></video>-->
              </div>
              <p class="heart" :class="{active:item.isPraise}" @click="heart(item.udId,item.isPraise,i)">{{item.praiseNum||0}}</p>

            </li>
          </ul>
            <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="allList.length"></load-bar>
          </div>
        </div>
      </div>
      <div class="footer" @click="beginUpload">
        <h3>发表我的上进宣言</h3>
        <p>已有{{joinNum}}人在坚持上进</p>
      </div>
      <div class="media_wrap" v-if="show">
        <div class="bg" @click="show=false"></div>
        <div class="makeMedia" >
          <router-link class="item" :to="{name:'321ActivityAudio'}" v-if="isWeixin">
            <button>录制语音</button>
            <p>（时长保持在10s-60s之间哦）</p>
          </router-link>
          <!--<fileupload accept="video/*" udType="video" @resource="resource" @publish="publishsuccess" ref="video" v-model="video">-->
            <!--<div class="item">-->
            <!--<button>上传视频</button>-->
            <!--<p>（时长保持在10s-60s之间哦）</p>-->
          <!--</div>-->
          <!--</fileupload>-->
          <div class="item">
            <webuploader accept="video/*" udType="video" picker="videoPicker" @resource="resource" @publish="publishsuccess" ref="video" />
            <button>上传视频</button>
            <p>（时长保持在10s-30s之间哦）</p>
          </div>
          <!--<fileupload accept="audio/*" udType="audio" @resource="resource" @publish="publishsuccess" ref="audio" v-model="audio">-->
          <!--<div class="item">-->
            <!--<button>上传语音</button>-->
            <!--<p>（时长保持在10s-60s之间哦）</p>-->
          <!--</div>-->
          <!--</fileupload>-->
          <div class="item" v-if="!isWeixin">
            <webuploader accept="audio/*" udType="audio" picker="audioPicker" @resource="resource" @publish="publishsuccess" ref="audio" />
            <button>上传语音</button>
            <p>（时长保持在10s-60s之间哦）</p>
          </div>
        </div>
      </div>

      <div class="success" v-if="showCert">
        <div class="bg"></div>
        <h3>发布成功！</h3>
        <p v-if="info.udOwnNum==1">您是第{{info.udNum}}位上进者</p>
        <p v-else>快和朋友们一起分享你的上进吧~</p>
        <img  src="../../../assets/image/active/321Activity/sunshine.png" alt="">
        <div class="cert_img" id="cert_img">

          <img src="../../../assets/image/active/321Activity/my-cert.png" alt="">
            <!--<span>{{info.udNum}}</span>-->
            <!--<span>{{nickName}}</span>-->

          <p class="txt4"><span>证书编号：</span>{{code}}</p>
          <p class="txt1">第 <span>{{info.udNum}}</span> 位上进者 <span>{{nickName}}</span> ，在「321上进日」活动中发表《上进宣言》，并郑重承诺今后努力实现上进目标，传递时代正能量，展现上进新风貌，现已通过【上进协会】评估认证。</p>
          <p class="txt2">特此发证，以资鼓励。</p>
          <p class="txt3">{{Date.now() | formatDate('yyyy.MM.dd')}}</p>
          <div id="qrcode" ref="qrcode"></div>
        </div>
        <button @click="shareCert">分享证书</button>
        <button @click="toMycert">分享上进宣言</button>
        <div class="close" @click="cancelCert">
          <img src="../../../assets/image/active/321Activity/close.png" alt="">
        </div>
      </div>
      <div class="img_wrap" v-if="isShow">
        <div class="bg" @click="isShow=false"></div>
        <img :src="imgBase" alt="">
        <p>长按保存图片</p>
      </div>
      <success :type="type" @upload="upload" :showPic="showPic" :duration="duration" :video="video" :audio="audio"  @publish="publish" v-if="success" />
      <share title="我在参加321上进日活动，快来围观我的上进宣言，为上进发声！" :imgUrl="imgUrl" desc="3月21日，发布上进宣言，干一件上进的事，影响更多的人一起上进。" :link="shareLink" />
    </div>
</template>

<script>
    import fileupload from "@/components/fileUpload-oss"
    import success from "./success"
    import share from "@/components/share"
    import loadBar from '@/components/loadBar';
    import QRCode from "qrcodejs2";
    import * as html2canvas from "html2canvas";
    import {toLogin,isWeixin} from "../../../common"
    import webuploader from "@/components/uploader-oss"
    import {Toast} from 'vant'
    export default {
        name: "index",
      components:{fileupload,success,share,loadBar,webuploader},
      data(){
          return{
            videoList:[{articlePicUrl:'website/scholarship/903115056FDC47BDA839F16A6ACFEA12.png'},{articlePicUrl:'website/scholarship/BB2639881B964B33B7DE7C6C1A89BD5E.jpg'},{articlePicUrl:'website/scholarship/23B24F0CE8914EB0A1EC7C2141DEC380.png'}],
            allList:[],
            enrollMsgList:[],
            headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
            show:false,
            showCert:false,
            width:225,
            width2:230,
            timer:null,
            isWeixin:isWeixin(),
            i:0,
            j:0,
            video:'',
            audio:'',
            type:'',
            success:false,
            pageSize:10,
            pageNum:0,
            joinNum:0,
            publishNum:0,
            duration:0,
            showPic :'',
            nickName:'',
            shareLink:'',
            imgUrl:'http://yzims.oss-cn-shenzhen.aliyuncs.com/logo/321act/321logo.png',
            isLoading:false,
            allLoaded:false,
            imgBase:'',
            isShow:false,
            showInvite:false,
            invite:{},
            endTime:new Date(2020,2,28,0,0,0).getTime(),
            showPlay:[{state:''},{state:''},{state:''},{state:''},{state:''},{state:''}],
            audioList:[
              {txt:'改掉拖延症，学习一门乐器',url:'',headImg:'../../../../static/headImg/10.png'},
              {txt:'坚持读书也是上进',url:'',headImg:'../../../../static/headImg/1.png'},
              {txt:'3月21日，影响更多人上进',url:'',headImg:'../../../../static/headImg/2.png'},
              {txt:'我要考上中级，完成半马',url:'',headImg:'../../../../static/headImg/3.png'},
              {txt:'我要每年多成长365分钟',url:'',headImg:'../../../../static/headImg/4.png'},
              {txt:'我要每天不少于30分钟的运动时间',url:'',headImg:'../../../../static/headImg/5.png'},
              {txt:'321，做一件好人好事',url:'',headImg:'../../../../static/headImg/6.png'},
              {txt:'上进日，我要做1件有意义的事',url:'',headImg:'../../../../static/headImg/7.png'},
              {txt:'我要提高工作技能和管理水平',url:'',headImg:'../../../../static/headImg/8.png'},
              {txt:'我要完成一次全马42.19公里！',url:'',headImg:'../../../../static/headImg/9.png'},
            ],
              ShortUrlId:'',
              flag:true
          }
      },
      computed:{
          code(){
            let date=new Date()
            let str='SJYZ-'+date.getFullYear()+((date.getMonth()+1)>9?date.getMonth()+1:'0'+(date.getMonth()+1));
            str+=date.getDate()>9?date.getDate():'0'+date.getDate();
            let num=(this.info.udNum.toString().split("").reverse().join("")+'0000').substr(0,5).split("").reverse().join("")
            return str+num
        }
      },
      beforeRouteEnter(to, from, next) {
        next(vm=>{          //  这里的vm指的就是vue实例，可以用来当做this使用
          localStorage.setItem("fromUrl",from.fullPath);
        })
      },
      created(){
        this.getActivityInfo()
        this.getPublishNum()
        this.nickName=this.storage.getItem("zmcName")
        this.shareLink=`${window.location.origin}/active/321Activity`;
        if(this.$route.query.publishInfo){
          this.info=JSON.parse(this.$route.query.publishInfo)
           if(this.storage.getItem("fromUrl").indexOf("audio")!=-1){
            this.showCert=true;
            this.$nextTick(()=>{
              this.transferShortUrl()
            })
          }
        }
        if(this.$route.query.code=='1'){
          MtaH5.clickStat('2')
        }
      },
      mounted(){
        this.scrollMsg()
      },
      methods:{
        getInviteInfo() {
          let inviteId = (
            window.sessionStorage.getItem("inviteId") ||
            this.$route.query.inviteId ||
            decodeURIComponent(
              this.$route.query.inviteId ||
              ""
            )
          ).replace(/ /g, "+");
          if (inviteId) {
            this.$http
              .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
              .then(res => {
                let { code, body } = res;
                if (code !== "00") return;
                this.invite = body || {};
                this.showInvite = true;
              });
          }
        },
        cancelCert(){
          this.showCert=false
          //window.location.href=`${window.location.origin}/active/321Activity`
        },
        playVideo(i){
          if(this.$refs["video"+i].paused){
            let video=document.getElementsByTagName('video');
            for (let i=0;i<video.length;i++){
              video[i].pause()
            }
            let audio=document.getElementsByTagName('audio');
            for (let i=0;i<audio.length;i++){
              audio[i].pause();
            }
            for (let j=0;j<this.showPlay.length;j++){
              this.showPlay[j].state='stop'
            }

            this.$refs["video"+i].play()
            this.showPlay[i].state='play'
          }else{
            this.$refs["video"+i].pause()
            this.showPlay[i].state='stop'
          }
        },
        playAudio(udId,i){
          if(this.$refs["media"+i][0].paused){
            let audio=document.getElementsByTagName('audio');
            for (let i=0;i<audio.length;i++){
              if(!audio[i].paused){
                audio[i].pause();
              }
            }
            let video=document.getElementsByTagName('video');
            for (let i=0;i<video.length;i++){
              if(!video[i].paused){
                video[i].pause();
              }
            }
            this.$refs["media"+i][0].play()
            var item=this.allList[i]
            item.state='play'
            this.$set(this.allList,i,item)
            this.$refs["media"+i][0].addEventListener('pause',()=>{
              var item=this.allList[i]
              item.state='stop'
              this.$set(this.allList,i,item)
            })
          }else{
            this.$refs["media"+i][0].pause()
            var item=this.allList[i]
            item.state='stop'
            this.$set(this.allList,i,item)
          }
          this.$http.post('/mkt/playUpDeclaration/1.0/',{udId:udId}).then(res=>{
            if(res.code=='00'){

            }
          })
        },
        shareCert(){
          this.$indicator.open()
          this.$nextTick(() => {
            html2canvas(document.getElementById('cert_img'), {width: document.getElementById('cert_img').offsetWidth, height: document.getElementById('cert_img').tHeight, useCORS: true}).then(canvas => {
              this.$indicator.close();
              this.imgBase=canvas.toDataURL();
              this.showCert=false;
              this.isShow=true
            });
          });
          MtaH5.clickStat('3')
        },
        getActivityInfo(){
            this.$http.post('/mkt/getUpDeclarationActInfo/1.0/').then(res=>{
                if(res.code=='00'){
                    this.joinNum=res.body.joinNum
                }
            })
          },
        useqrcode(){
          // this.inviteUrl = `${window.location.origin}/active/321Activity?inviteId=${this.storage.getItem('ykAuthtoken')}&code=1`;
          this.inviteUrl = `${window.location.origin}/active/321Activity/urlTransfer?u=${this.ShortUrlId}`
          let qrcode = new QRCode('qrcode', {
            width: 72,  // 设置宽度
            height: 72, // 设置高度
            text: this.inviteUrl,
            correctLevel:QRCode.CorrectLevel.L,
            backgoround: '#fff'
          })
          this.$nextTick(()=>{
            let qrcode=document.getElementById('qrcode');
            qrcode.getElementsByTagName("img")[0].style.width="100%"
          })
        },

      transferShortUrl(){
        let url = `${window.location.origin}/active/321Activity?inviteId=${this.storage.getItem('ykAuthtoken')}&code=1`
        this.$http.post('/proxy/commitShortUrl/1.0/',{url:url}).then((res=>{
            const {code,body} = res;
            if(code !=='00') return;
            this.ShortUrlId = body;
            this.$nextTick(()=>{
                this.useqrcode();
            })
        }))
      },

        getPublishNum(){
         if(!this.storage.getItem('ykAuthtoken')){
            return;
          }
          this.$http.post('/mkt/getUpDeclarationPublishCount/1.0/').then(res=>{
            if(res.code=='00'){
              this.publishNum=res.body
            }
          })
        },
        loadMore(){
            this.pageNum++;
          this.getList();
        },
        toMycert(){
          this.$router.push({name:'My321ActivityCert'})
        },
          getList(reload){
          if(reload){
            this.pageNum=1;
            this.allList=[];
          }
            this.isLoading=true
            this.$http.post('/mkt/getUpDeclarationList/1.0/',{pageSize:this.pageSize,pageNum:this.pageNum}).then(res=>{
              this.allList.push(...res.body);
              this.$nextTick(() => {
                this.allLoaded = res.body.length === 0;
                this.isLoading = this.allLoaded;
              });
            })
          },
        publishsuccess(info){
          this.show=false
          this.success=false;
          this.info=info
          this.showCert=true;
          this.getList('reload')
          this.$nextTick(()=>{
            this.transferShortUrl()
          })
        },
        resource(url,duration,type){
          console.log(url);
          if(type=='audio'){
            this.audio=url;
            this.duration=duration
            this.type='audio'
          }else{
            this.video=url;
            this.showPic=duration
            this.type='video'
          }
        },
        upload(){
          console.log(this.$refs);
          this.type=='audio'?this.$refs.audio.$refs.input.click():this.$refs.video.$refs.input.click()
        },
        publish(){
          this.type=='audio'?this.$refs.audio.putBlob():this.$refs.video.putBlob()
        },
        toShow(udId,i){
            this.storage.setItem('mediaList',this.allList)
            this.$router.push({name:'321ActivityShowMedia',query:{udId:udId,index:i,pageNum:this.pageNum}})
        },
        modal(){
            this.$modal({message:'今年321上进日活动已经结束了哦~欢迎您明年赶早来参与上进啦！',icon:'warning'})
        },
        beginUpload(){
          if(this.endTime<Date.now()){
            this.modal();
            return;
          }
          if(!this.storage.getItem('ykAuthtoken')){
            toLogin.call(this)
            return;
          }
          if(this.publishNum>4){
            this.$modal({message:`您已经发布过5条上进宣言啦！\n上进目标太多了哦~`,icon:'warning'})
            return;
          }
          this.show=true;
        },
        bannerStop(i){
         // cancelAnimationFrame(this.timer)
          if(this.$refs["audio"+i][0].paused){
            let audio=document.getElementsByTagName('audio');
            for (let i=0;i<audio.length;i++){
              audio[i].pause();
            }
            this.$refs["audio"+i][0].play()
            // this.$refs["audio"+i][0].addEventListener('pause',()=>{
            //   if(!this.timer){
            //     this.reddown
            //   }
            // })
          }
        },
        scrollMsg:function(){
            let gdBox1= document.querySelectorAll('.gdBox_wrap1')
            gdBox1.forEach((item)=>{
              this.width+=item.offsetWidth;
            })
            let gdBox2= document.querySelectorAll('.gdBox_wrap2')
            gdBox2.forEach((item)=>{
              this.width2+=item.offsetWidth;
            })
            // this.reddown()
         this.reddown()
        },
        heart(praiseId,isPraise,i){
          if(this.endTime<Date.now()){
            this.modal();
            return;
          }
          if(this.flag) {
            this.flag = false;
            let fabulousNum=isPraise?-1:1;
            this.$http.post('/mkt/praiseUpDeclaration/1.0/',{praiseType:4,praiseId:praiseId,fabulousNum:fabulousNum}).then(res=>{
              if(res.code=='00'){
                this.allList[i].isPraise=!isPraise;
                isPraise?this.allList[i].praiseNum--:this.allList[i].praiseNum++;
                this.flag = true;
              }
            })
          }else {
            Toast('手速慢一些!')
          }

        },
        reddown(){
         //  this.i++;
         //  var that=this;
         //  let gdBox=document.querySelectorAll('.gdBox');
         //  gdBox.forEach(item=>{
         //    item.style.transform=`translate3d(${-1.04*this.i+375}px,0,0)`
         //    if(1.5*this.i>=Math.abs((item.offsetWidth+document.getElementsByClassName('banner_swiper')[0].offsetWidth))) {
         //      item.style.transform = `translate3d(0px,0,0)`
         //      this.i = 0
         //    }
         // })
          this.i++;
          this.j++;
          let gdBox=document.querySelectorAll('.gdBox');
          gdBox.forEach((item,index)=>{
            if(index==0){
              item.style.transform=`translate3d(${-1.05*this.i}px,0,0)`
              if(1.05*this.i>=(item.offsetWidth/2)){
                item.style.transform=`translate3d(0px,0,0)`
                this.i=0
              }
              item.style.left=`${10*this.i}px`
            }else{
              item.style.transform=`translate3d(${-1.05*this.j}px,0,0)`
              if(1.05*this.j>=(item.offsetWidth/2)){
                item.style.transform=`translate3d(0px,0,0)`
                this.j=0
              }
              item.style.left=`${10*this.j}px`
            }

          })
          //  document.querySelectorAll('.gdBox_wrap').forEach(item=>{
          //    item.style.transform=`translate3d(${-1.5*this.i}px,0,0)`
          //  })
          this.timer=requestAnimationFrame(this.reddown);
        },
      },
      beforeDestroy(){
        cancelAnimationFrame(this.timer)
      },
      watch:{
        audio:function(newValue){
          console.log(newValue);
          this.success=true;
          this.type='audio'
          //this.$router.push({name:"321ActivitySuccess",query:{type:"audio",fileUrl:newValue}})
        },
        video:function(newValue){
          this.success=true
          this.type='video'
        },
      }
    }
</script>

<style scoped lang="less">
.activity_wrap /deep/ #qrcode {
  img {
    width: .72rem !important;
    height: .72rem !important;
  }
}
  .activity_wrap{
    width: 100%;
    height: auto;
    overflow: hidden;
    max-width: 640px;
    margin: 0 auto;
    padding-bottom: 1.04rem;
    background-color: rgba(126, 25, 30, 1);
    .banner{
      width: 100%;
      height: 5.62rem;
      position: relative;
      background-image: url("../../../assets/image/active/321Activity/banner.jpg");
      background-size: 100% 100%;
      .txt{
      width: 3.47rem;
        height: 1.89rem;
        position: absolute;
        left:.26rem;
        top:2.2rem
    }
      .cert{
        width: .75rem;
        height: .28rem;
        position: absolute;
        top:.17rem;
        right: 0;
        img{
          width: 100%;
        }
      }
      .rule{
        width: .27rem;
        height: .85rem;
        position: absolute;
        right: 0;
        top:2.06rem;
        z-index: 88;
        img{
          width: 100%;
        }
      }
      .advance{
        width: .66rem;
        height: .24rem;
        position: absolute;
        left:.1rem;
        top:.09rem
      }
      .num{
        color: white;
        font-size: .12rem;
        line-height: .17rem;
        position: absolute;
        left: .1rem;
        top:.34rem
      }
      .toSay{
        width: 3.17rem;
        height: .45rem;
        position: absolute;
        top:4.26rem;
        left:.24rem;
        img{
          width: 100%;
        }
      }
      .txt_war{
        width: 100%;
        height: .7rem;
        position: absolute;
        top:4.5rem;
        overflow: hidden;
        .banner_swiper {
          width: 100%;
          height: 100%;
          .gdBox {
            width: auto;
            height: .25rem;
            overflow: hidden;
          //  animation: move 20s linear infinite;
            transform: translate3d(100%,0,0);
            -webkit-overflow-scrolling:touch;
            &:first-of-type{
              margin-bottom: 0.2rem;
            }
          }
          .gdBox_wrap {
            float: left;
            height: 0.25rem;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 0.25rem;
            margin-left: .2rem;
            padding-right: .1rem;
            .img {
              float: left;
              width: 0.25rem;
              height: 0.25rem;
              border-radius: 50%;
              img {
                width: 100%;
                border-radius: 50%;
              }
            }
          }
          .txt_fr {
            float: left;
            margin-left: 0.05rem;
            height: 0.25rem;
            color: white;
            font-size: 0.12rem;
            span {
              float: left;
              line-height: 0.25rem;
              /*max-width: 1.1rem;*/
              /*overflow: hidden;*/
              /*text-overflow: ellipsis;*/
              /*white-space: nowrap;*/
            }
            span:last-child {
              float: left;
              width: .16rem;
              height: .16rem;
              background-image: url("../../../assets/image/active/321Activity/play_yellow.png");
              background-size: 100% 100%;
              margin-left: 0.04rem;
              margin-top: .04rem;
            }
          }
        }
      }
    }
    .videoSwiper {
      margin-top: .25rem;
      height: 2.15rem;
      background-color: rgba(126, 25, 30, 1);
      .content {
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;
        position: relative;
        height: 2.26rem;
        .item {
          display: inline-block;
          width: 1.31rem;
          margin-left: .1rem;
          height: 2.13rem;
          position: relative;
          border-radius: .05rem;
          border: 2px solid RGBA(255, 191, 129, 1);
          position: relative;
          .video-js{
            width: 100%;
            height: 100%;
          }
          .bg{
            width: 100%;
            height: 100%;
            position: absolute;
            left:0;
            top:0;
            background-color: rgba(0, 0, 0, .1);
          }
          img{
            position: absolute;
            width: 100%;
            height: 100%;
            left:0;
            top:0;
          }
          .text{
            color: white;
            font-size: .12rem;
            /*line-height: .17rem;*/
            padding-top: .05rem;
            position: absolute;
            bottom:.1rem;
            left:.06rem;
            background-image: url("../../../assets/image/active/321Activity/fire.png");
            background-size: .16rem .19rem;
            background-repeat: no-repeat;
            padding-left: .2rem;
          }
          .play{
            width: .32rem;
            height: .32rem;
            position: absolute;
            right: .06rem;
            bottom: .1rem;
            background-image: url("../../../assets/image/active/321Activity/play.png");
            background-size: .32rem;

          }
          video {
            width: 1.27rem;
            height: 2.09rem;
            border-radius:5px 5px 0px 0px;
            object-fit: cover;
          }
        }

      }
    }
    .footer{
      position: fixed;
      width: 100%;
      max-width: 640px;
      height: .6rem;
      left:0;
      right: 0;
      bottom:0;
      margin: auto;
      background-color: rgba(237, 152, 66, 1);
      z-index:88;
      h3{
        font-weight: bold;
        font-size: .16rem;
        line-height: .22rem;
        color: white;
        text-align: center;
        margin-top: .12rem;
      }
      p{
        font-size: .1rem;
        color: rgba(255, 255, 255, .59);
        text-align: center;
      }
    }
    .all_txt{
      width: 3.5rem;
      height: auto;
      overflow: hidden;
      background-color: white;
      margin: .24rem auto 0;
      border-radius: .1rem;
      border: 2px solid RGBA(255, 191, 129, 1);
      position: relative;
      .xiaozhi{
        position: absolute;
        right: .16rem;
        z-index: 22;
        top:2.25rem;
        p{
          float: left;
          color: white;
          margin-right: .14rem;
          line-height: .52rem;
        }
        img{
          float: left;
          width: .52rem;
          height: .52rem;
        }
      }
      .top{
        width: 3.32rem;
        height: .47rem;
        margin: .09rem 0 0 .07rem;
        img{
          width: 100%;
        }
      }
      .txt{
        width: 3.46rem;
        height: 2rem;
        margin-top: .09rem;
        margin-bottom: .15rem;
      }
      .list{
        width: 100%;
        // max-height: 8.4rem;
        height: auto;
        // overflow: scroll;
        ul{
          li{
            width: 100%;
            padding:.15rem;
            height: auto;
            overflow: hidden;
            border-bottom: solid 1px rgba(238, 238, 238, 1);
            position: relative;
            .info{
              width: 100%;
              margin-bottom: .15rem;
              height:.4rem ;
              overflow: hidden;
              img{
                float: left;
                width: .4rem;
                height: .4rem;
                margin-right: .07rem;
              }
              .name{
                float: left;
                padding-top: .07rem;
                h3{
                  color: rgba(247, 116, 83, 1);
                  font-size: .12rem;
                  line-height: .17rem;
                }
                p{
                  color: rgba(102, 102, 102, 1);
                  font-size: .11rem;
                  line-height: .16rem;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
            .audio{
              float: left;
              width: 2.24rem;
              height: .32rem;
              border-radius: .04rem;
              background-color: rgba(132, 201, 102, 1);
              font-size: .16rem;
              line-height: .32rem;
              padding-left: .13rem;
              img:first-of-type{
                width: .06rem;
                height: .06rem;
                margin-top: .04rem;
              }
              img:last-of-type{
                width: .11rem;
                height: .14rem;
                margin-top: .1rem;
                margin-left: .05rem;
              }
            }
            .video{
              float: left;
              width: 1.42rem;
              height: 1.72rem;
              position: relative;
              &:before{
                content: "";
                width: 100%;
                height: 100%;
                position: absolute;
                left:0;
                top:0;
                background-color: rgba(0, 0, 0, .1);
                z-index: 2;
              }
              img:first-of-type{
                width: 100%;
                height: 100%;
                position: absolute;
              }
              video{
                width: 100%;
                height: 100%;
              }
              img:last-of-type{
                position: absolute;
                width: .32rem;
                height: .32rem;
                left: 0;
                right: 0;
                top:0;
                bottom:0;
                margin: auto;
              }
            }
            .heart{
              /*float: right;*/
              right: .15rem;
              bottom:.15rem;
              position: absolute;
              font-size: .12rem;
              line-height: .14rem;
              margin-top: .17rem;
              background-image: url("../../../assets/image/active/321Activity/heart.png");
              background-size: .14rem .14rem;
              background-repeat: no-repeat;
              padding-left: .17rem;
              &.active{
                background-image: url("../../../assets/image/active/321Activity/heart_sel.png");
              }
            }
          }
        }
      }
    }
    .media_wrap{
      position: fixed;
      width: 100%;
      height: 100vh;
      bottom:0;
      top:0;
      z-index: 99;
      .bg{
        width: 100%;
        height: 100%;
        position: absolute;
        left:0;
        bottom:0;
        background-color: rgba(0,0,0,.3);
      }
      .makeMedia{
        position: absolute;
        bottom:0;
        width: 100%;
        left:0;
        z-index: 10;
        background-color: white;
        animation: top 1s forwards;
        max-width: 640px;
        .item{
          width: 100%;
          height: .6rem;
          display: block;
          position: relative;
          button{
            line-height: .2rem;
            margin-top: .12rem;
            width: 100%;
            background-color: white;
            color: rgba(51, 51, 51, 1);
          }
          p{
            color: rgba(153, 153, 153, 1);
            font-size: .12rem;
            text-align: center;
          }
        }
      }
    }
    .success{
      width: 100%;
      height: 100vh;
      position: fixed;
      z-index: 99;
      bottom:0;
      left:0;
      padding-top: .6rem;
      .bg{
        width: 100%;
        height: 100%;
        background-color: rgba(3, 3, 3, .8);
        position: absolute;
        top:0;
        left:0;
        z-index: -1;
      }
      h3{
        font-size: .22rem;
        line-height: .3rem;
        color: white;
        text-align: center;
        margin-bottom: .07rem;
      }
      p{
        color: rgba(255, 247, 201, .69);
        margin-bottom: .12rem;
        text-align: center;
      }
      >img:first-of-type{
        width:100% ;
        height: 3.73rem;
        left:0;
        margin-left: 0;
        margin-top: -1rem;
        position: absolute;
      }
      .cert_img{
        width: 3.2rem;
        height: 2.72rem;
        position: relative;
        margin-left: .29rem;
        .txt1{
          position: absolute;
          color: rgba(51, 51, 51, 1);
          font-size: .1rem;
          text-indent: 2em;
          width: 2.8rem;
          top:.9rem;
          left:.2rem;
          text-align: left;
          word-break: break-all;
          span{
            color: RGBA(183, 155, 89, 1);
          }
        }
        .txt2{
          position: absolute;
          color: rgba(51, 51, 51, 1);
          font-size: .1rem;
          text-indent: 2em;
          width: 2.8rem;
          top:1.45rem;
          left:.2rem;
          text-align: left;
        }
        .txt3{
          width: 1rem;
          position: absolute;
          color: rgba(51, 51, 51, 1);
          font-size: .1rem;
          text-indent: 2em;
          top:2.35rem;
          right:.2rem;
          text-align: left;
        }
        .txt4{
          width:100%;
          text-align: center;
          position: absolute;
          color: rgba(51, 51, 51, 1);
          font-size: .1rem;
          text-indent: 2em;
          top:.7rem;
          right:0rem;
          span{
            color: RGBA(174, 147, 102, 1);
          }
        }
        #qrcode{
          position: absolute;
          width: .72rem;
          height: .72rem;
          bottom:.2rem;
          left:.3rem;
          img {
            width: .72rem !important;
            height: .72rem !important;
          }
        }
        img{
          width: 3.18rem;
          height: 100%;
          position: absolute;

        }
          /*span{*/
            /*position: absolute;*/
            /*font-size: .1rem;*/
            /*right: 1.15rem;*/
            /*top:.92rem;*/
            /*width: .4rem;*/
            /*text-align: center;*/
            /*overflow: hidden;*/
            /*word-break: break-all;*/
            /*white-space: nowrap;*/
            /*color:rgba(102, 102, 102, 1) ;*/
            /*&:last-of-type{*/
              /*width: .5rem;*/
              /*right:.27rem*/
            /*}*/
          /*}*/
      }
      button{
        width: 1.7rem;
        height: .4rem;
        display: block;
        color: white;
        text-align: center;
        font-size: .16rem;
        background-image: linear-gradient(to bottom,rgba(252, 190, 95, 1),rgba(248, 137, 49, 1));
        border-radius: .28rem;
        margin: .26rem auto 0;
        box-shadow: 0 0 .09rem 0 rgba(235, 83, 0, 1);
        border-bottom:solid .03rem rgba(235, 83, 0, 1) ;
        border-right:solid .02rem rgba(235, 83, 0, 1) ;
        &:last-of-type{
          margin: .15rem auto 0;
        }
      }
      .close{
        width: .32rem;
        height: .32rem;
        margin: .3rem auto 0;
        img{
          width: 100%;
        }
      }
    }
  }
  .invite {
    padding: 0.16rem 0;
    background-image: url("../../../assets/image/active/dreamBuild/content-bg.png");
    background-size: 100%;
    img {
      width: 0.48rem;
      height: 0.48rem;
      border-radius: 50%;
      float: left;
      margin-left: 0.24rem;
    }
    .headTxt {
      float: left;
      margin-left: 0.12rem;
      p {
        font-size: 0.14rem;
        span {
          color: #e15443;
          font-size: 0.14rem;
        }
        &:first-of-type {
          font-size: 0.14rem;
        }
      }
    }
  }
  .invite-top {
    background-color: #f3b422;
    height: 0.72rem;
    position: relative;
    z-index: 10;
  }
  .img_wrap{
    position: fixed;
    width: 100%;
    height:100%;
    overflow: hidden;
    left:0;
    right: 0;
    top:0;
    bottom:0;
    margin: auto;
    z-index:999;
    .bg{
      position: fixed;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.4);
      z-index: 999;
    }
    img{
      width: 2.5rem;
      position: absolute;
      left:0;
      right: 0;
      top:0;
      bottom:0;
      margin: auto;
      z-index:99999;
    }
    p{
      color: white;
      width: 100%;
      text-align: center;
      position: absolute;
      left:0;
      bottom:1.5rem;
      margin: auto;
      z-index:99999;
    }
  }
  @keyframes move {
    from{
      left:100%;
      transform: translate3d(3.75rem,0,0);
    }
    to{
      left:-100%;
      transform: translate3d(-100%,0,0);
    }
  }
  @keyframes top {
    from{
      bottom:-100%;
      transform: translate3d(0,-100%,0);
    }
    to{
      bottom:0;
      transform: translate3d(0,0,0);
    }
  }
</style>
