<template>
  <div class="yz-growth-start">
    <div class="img1-box">
      <img src="../../../assets/image/active/growthSystem/1.png" alt="">
    </div>
    <p class="text">上进的人生在于没有荒废时间，凡事努力，敢想敢试。趁现在，跟随远智盘点生活细节，开启上进之旅！</p>
    <el-row class="three-box" type='flex' justify="space-between">
      <div class="item" :class='{rotate: s1Show}'>
        <img src="../../../assets/image/active/growthSystem/s1.png" :class="{z3: s1Index==0}" alt="">
        <img src="../../../assets/image/active/growthSystem/s4.png" :class="{z3: s1Index==1}" alt="">
        <img src="../../../assets/image/active/growthSystem/s7.png" :class="{z3: s1Index==2}" alt="">
        <img src="../../../assets/image/active/growthSystem/s10.png" :class="{z3: s1Index==3}" alt="">
      </div>
      <div class="item" :class='{rotate: s2Show}'>
        <img src="../../../assets/image/active/growthSystem/s2.png" :class="{z3: s2Index==0}" alt="">
        <img src="../../../assets/image/active/growthSystem/s5.png" :class="{z3: s2Index==1}" alt="">
        <img src="../../../assets/image/active/growthSystem/s8.png" :class="{z3: s2Index==2}" alt="">
        <img src="../../../assets/image/active/growthSystem/s8.png" :class="{z3: s2Index==3}" alt="">
        <img src="../../../assets/image/active/growthSystem/s11.png" :class="{z3: s2Index==3}" alt="">
      </div>
      <div class="item" :class='{rotate: s3Show}'>
        <img src="../../../assets/image/active/growthSystem/s3.png" :class="{z3: s3Index==0}" alt="">
        <img src="../../../assets/image/active/growthSystem/s6.png" :class="{z3: s3Index==1}" alt="">
        <img src="../../../assets/image/active/growthSystem/s9.png" :class="{z3: s3Index==2}" alt="">
        <img src="../../../assets/image/active/growthSystem/s9.png" :class="{z3: s3Index==3}" alt="">
        <img src="../../../assets/image/active/growthSystem/s12.png" :class="{z3: s3Index==3}" alt="">
      </div>
    </el-row>
    <img src="../../../assets/image/active/growthSystem/start-btn.png" class="start-btn" alt="" @click='finish'>
  </div>
</template>

<script>
export default {
  data () {
    return {
      s1Index: 0,
      s2Index: 0,
      s3Index: 0,
      s1Show: false,
      s2Show: false,
      s3Show: false,
    };
  },
  props: {
    start: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    start(val) {
      if (val) {
        this.s1Change();
      }
    },
  },
  mounted() {
  },
  methods: {

    s1Change() {
      this.s1Show = true;
      setTimeout(() => {
        if (this.s1Index < 3) {
          this.s1Index += 1;
        } else {
          this.s1Index = 0;
        }
        this.s1Show = false;
        setTimeout(() => {
          this.s2Change();
        }, 500);
      }, 250);
    },
    s2Change() {
      this.s2Show = true;
      setTimeout(() => {
        if (this.s2Index < 3) {
          this.s2Index += 1;
        } else {
          this.s2Index = 0;
        }
        this.s2Show = false;
        setTimeout(() => {
          this.s3Change();
        }, 500);
      }, 250);
    },
    s3Change() {
      this.s3Show = true;
      setTimeout(() => {
        if (this.s3Index < 3) {
          this.s3Index += 1;
        } else {
          this.s3Index = 0;
        }
        this.s3Show = false;
        setTimeout(() => {
          this.s1Change();
        }, 500);
      }, 250);
    },
    finish() {
      this.$emit('finish', { index: 0 });
    },
  }
};
</script>

<style lang="less">
  .yz-growth-start{
    background: url(../../../assets/image/active/growthSystem/bg.png) no-repeat;
    background-size: 100% 100%;
    height: 100vh;
    width: 100%;
    position: absolute;
    z-index: -1;
    .img1-box{
      padding: 0.51rem 0.45rem 0.26rem;
      img{
        width: 100%;
      }
    }
    .text{
      padding: 0 0.65rem;
      text-align: center;
      font-size: 0.14rem;
    }
    .three-box{
      margin-top: 0.5rem;
      padding: 0 0.48rem;
      .item{
        width: 0.72rem;
        height: 0.72rem;
        box-sizing: content-box;
        background: #F8A18C;
        border: 0.03rem solid #000;
        transition: transform 1s;
        position: relative;
        &.rotate{
          transform: rotateY(180deg);
        }
        &.s1{
          // animation: s1Frames 2s ease 1s infinite;
          // timing-function delay iteration-count direction fill-mode;
        }
        img{
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
        }
        .z3{
          z-index: 3;
        }
      }
    }
    .start-btn{
      position: absolute;
      bottom: 0.5rem;
      left: 50%;
      transform: translateX(-50%);
      width: 1rem;
      height: 0.42rem;
    }
  }

  @keyframes s1Frames {
    from{
      transform: rotateY(0deg);
    }
    to{
      transform: rotateY(360deg);
    }
  }
</style>
