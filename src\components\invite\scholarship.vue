<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.dictValue===item.dictValue}" :key="item.dictValue" @click="selected(item)">{{item.dictName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="options.length"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';
  import { domain } from '@/config';

  export default {
    props: ['value', 'datas'],
    data() {
      return {
        options: [],
        type: 'P',
        pfsnId: '',
        grade: '',
        pfsn: '',
        pfsnLevel: '',
        unvsId: '',
        pageNum: 0,
        pageSize: 20,
        isLoading: false,
        allLoaded: false,
        activityName: '',
        cityCode: '',
        cityName: '',
        pfsnNames: [],
        taId:'',
        recruitType:'',
        allShipList: [],
      }
    },
    created() {
      this.activityName = this.datas.activityName;
      this.grade = this.datas.grade.dictValue;
      this.pfsnLevel = this.datas.pfsnLevel.pfsnLevel;
      this.unvsId = this.datas.unvs.unvsId;
      this.pfsnId = this.datas.pfsn.pfsnId || '';
      this.cityCode=this.datas.city.cityCode;
      this.recruitType=this.datas.recruitType.dictValue;
      this.taId=this.datas.ta.taId;
    },
    mounted () {
      console.log(1111111);
      this.getAllList();
    },
    methods: {
      getAllList: function () {
        const url = `${domain}/baseinfo/scholarship.do`;
        this.$http.post(url).then(res => {
          if (res.code !== '00') return;
          this.allShipList = res.body || [];
          this.allLoaded = true;
          this.getList();
        });
      },
      async getList() {
        this.isLoading = true;
        const res = await this.$http.post(`${domain}/recruit/getScholarships.do?pfsnId=${this.pfsnId}&taId=${this.taId}`);
        this.isLoading = false;
        if (res.code == '00') {
          const ops = [];
          const list = res.body || [];
          list.forEach(val => {
            ops.push(this.allShipList.find(item => item.dictValue == val));
          });
          this.options = [].concat(ops);
        }
      },
      loadMore: function () {},
      selected: function (val) {
        this.$emit('input', val);
      },
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
