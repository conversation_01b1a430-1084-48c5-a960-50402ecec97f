<template>
  <div class="imgPopup" v-if="show" @click="close">
    <div class="inner" @click.stop>
      <div class="bd">
        <img :src="imgSrc|basePath" alt="">
      </div>
    </div>
    <div class="delete" @click="delImg">
      <i></i>
    </div>
  </div>
</template>

<script>
  import config from '../config'
  
  export default {
    filters: {
      basePath: function (url) {
        if (!url) return url;
        return config.imgBasePath + url;
      }
    },
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      imgSrc: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        show: this.isShow,
        imgSrcs:this.imgSrcs
      }
    },
    methods: {
      open: function () {
        this.show = true;
      },
      close: function () {
        this.show = false;
      },
      delImg: function () {
        this.$emit('delImg')
      }
    }
  }
</script>

<style lang="less" scoped>
  .imgPopup{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:99999; background-color:rgba(0, 0, 0, .5);
    > .inner{ position:absolute; top:50%; right:.0rem; left:.0rem; max-height:100%; overflow-y:auto; transform:translateY(-50%); border-radius:.04rem; }
    > .inner > .bd{
      padding:0 ;
      max-height:3rem;
      overflow: scroll;
      img{  width: 100%;}
    }
    .delete{
      position: absolute;
      bottom: .8rem;
      left: 50%;
      transform:translateX(-50%);
      i{
        display: inline-block;
        width: .44rem;
        height: .44rem;
        background-image: url("../assets/image/ver_ico_del.png");
        background-size: 100%;
      }
    }
  }
</style>
