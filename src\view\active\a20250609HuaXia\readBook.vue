<template>
  <div class="container">
    <!-- 微信分享组件 -->
    <share
      ref="wxShare"
      :title="share.title"
      :desc="share.content"
      :link="share.url"
      :imgUrl="share.image"
      :regOrigin="share.regOrigin"
    />

    <!-- 邀请进群-->
    <div class="invite" @click="toQRCodePage">
      <div class="group-avatar"></div>
      <div class="words">
        <p class="text-1">还差一步：加入班级群</p>
        <p class="text-2">与同学一起学习</p>
      </div>
      <div class="operating">
        <button class="btn">立即加入</button>
      </div>
    </div>
    <!-- 学习时长 -->
    <div class="study-length" v-if="!startSign">
      <div class="default can" v-if="isSign === 0 && surplusDuration > 0">
        <span class="powder"> 今日再学习&nbsp; </span>
        <van-count-down
          @finish="startSign = true"
          :time="surplusDuration"
          format=" mm 分 ss 秒"
        />
        <span class="powder"> &nbsp;即可打卡</span>
      </div>
      <button class="rule-btn" @click="openRulePopup">打卡规则</button>
    </div>
    <!--播放区域-->
    <div class="player-area">
      <!--播放器-->
      <!-- <div id="player" class="video-block"></div> -->
      <dot-player
        :opts="opts"
        @switchLine="switchLine"
      />
    </div>
    <!--评论区-->
    <div class="comment-block">
      <div class="title">精彩心得</div>
      <div v-if="list.length <= 0" class="empty">
        <img
          src="../../../assets/image/aspirantUniversity/zore.png"
          alt=""
          class="empty-bg"
        />
        <div class="empty-tip">精彩的上课心得总是需要慢慢打磨的</div>
      </div>

      <van-list
        v-else
        class="comment-list"
        v-model="loading"
        :finished="finished"
        finished-text="- 上进是一种动力，要坚持每天学习哦 -"
        @load="onLoad"
      >
        <div class="item" v-for="(item, index) in list" :key="index">
          <div class="comment">
            <div class="head">
              <div class="user-avatar">
                <img
                  :src="item.headImg | defaultAvatar"
                  alt=""
                  width="100%"
                  height="100%"
                />
              </div>
              <div class="user-info">
                <div class="user-name">
                  {{ item.realName }}
                </div>
                <div class="release-time">
                  {{ item.createTime | formatDate("yyyy.MM.dd") }}
                </div>
              </div>
            </div>
            <div class="comment-main">
              <div class="comment-content">
                {{ item.punchCardContents }}
              </div>
            </div>
          </div>
          <van-divider
            :style="{
              borderColor: '#EDEBEB',
              paddingTop: '0.1rem',
              marginTop: '0.1rem',
            }"
          />
        </div>
      </van-list>
    </div>
    <div class="fixed">
      <div class="clock-area">
        <button
          class="btn"
          @click="openClockDialog"
          v-if="isSign === 0 && startSign"
        >
          {{ isPatchCard ? "写心得补卡" : "写心得打卡" }}
        </button>
        <button class="btn" disabled v-if="isSign === 0 && !startSign">
          {{ isPatchCard ? "写心得补卡" : "写心得打卡" }}
        </button>
        <button class="btn" disabled v-if="isSign == 1">已打卡</button>
      </div>
    </div>

    <!-- 打卡规则-->
    <van-popup
      class="rule-popup"
      closeable
      v-model="rulePopup"
      get-container="body"
    >
      <div class="content">
        <div class="haed">打卡规则</div>
        <div class="text">
          {{ ruleText }}
        </div>
      </div>
    </van-popup>

    <van-popup
      class="wx-share"
      closeable
      v-model="wxShare"
      get-container="body"
    >
      <div class="content">
        <img
          width="100%"
          height="100%"
          src="../../../assets/image/aspirantUniversity/wx-share.png"
          alt=""
        />
      </div>
    </van-popup>

    <clock-dialog ref="clock" />
  </div>
</template>

<script>
import comment from "./components/comment";
import calendar from "./components/clock-calendar";
import { Toast } from "vant";
import appShare from "@/mixins/appShare";
import invite from "@/mixins/invite";
import bridge from "@/plugins/bridge";
import { AppShareUrl, player } from "@/config";
import share from "@/components/share";
import { handleBought, notPurchased } from "./common/book-list";
import clockDialog from "./clock-dialog";
import dotPlayer from "@/components/dotPlayer/index";
import { videoListMap } from '@/components/tcDotPlayer/type'
export default {
  components: {
    comment,
    calendar,
    share,
    clockDialog,
    dotPlayer
  },
  mixins: [appShare, invite],
  data() {
    return {
      startSign: false,
      isAppOpen: false, //是否运行在远智app
      plans: false,
      rulePopup: false,
      calendarPopup: false,
      wxShare: false,
      isDisabled: false,
      player: null, //播放器实例
      attendClassId: "", // 上课id
      readPlanId: "", // 读书计划id
      arrangeId: "", //安排id
      currentArrangeId: "", //当前播放的章节安排id
      ruleText:
        "21天每天至少学习5分钟，并在当天北京时间23:59之前，完成当天学习打卡噢~", //打卡规则文本

      durationShow: false, //控制时长显示隐藏
      readingProgress: {}, //阅读进度
      clockBtnText: "",
      clockBtnDisabled: false,
      // progressTimer: null, // 定时器
      lengthStudyStatus: 0, // 0.没有达到打卡时长要求,1.有学习进度达到打卡时长要求,2.已打卡
      targetDuration: 0, // 打卡规则观看时长
      surplusDuration: 0, //今日学习剩余观看时长
      lengthStudy: 0, // 今日学习总学习时长
      watchedDuration: 0, // 今日学习已观看时长
      hasBeenSentDuration: 0, // 已发送给后台的累计时长
      mappingId: null, // 套餐id
      isPlayerDrag: "off", // 保利播放器参数，是否能拖动未观看的进度
      share: {
        title: "",
        content: "",
        url: "",
        image: "",
        regOrigin: "", //邀约类型 不清楚的话去bms系统字典查
      },
      page: 0,
      list: [],
      isSign: 0,
      loading: false,
      finished: false,
      pageShow: false,
      semesterId: null, // 学期id
      punchOrder: "", // 打卡序号
      isPatchCard: null, //是否补卡
      vids: "", // 保利视频id
      updateTimer: null,

      opts: {},
      txyConfigs: null, // 腾讯云白名单配置
      dotPlayer: null, // 点播实例
      attendClassId: "",
    };
  },
  mounted() {
    this.vids = this.$route.query.vids || "";
    this.punchOrder = this.$route.query.punchOrder || "";
    this.isPatchCard = this.$route.query.isPatchCard;
    this.semesterId = this.$route.query.semesterId || "";
    this.attendClassId = this.$route.query.attendClassId || "";
    this.tagTime();
    this.isSign = this.$route.query.isSign || 0;

    if (this.isSign) {
      this.startSign = true;
    }
    // this.loadPlayerScript(this.initPlayer);
    this.judgeUser(this.attendClassId, this.vid)
    this.onLoad();
  },
  methods: {
    // 腾讯云播放失败切换到保利威
    switchLine() {
      if (this.txyConfigs.playerSwitch == 1) {
        this.getPlaySafeToken2(this.vid)
      }
      // 腾讯云报错 不支持切换线路
      else {
        this.opts = { playerType: 'polyv', showError: true }
      }
    },
    // 根据白名单接口判断用户是否在白名单内
    judgeUser(attendClassId, vid) {
      // 如果请求过腾讯白名单接口就根据储存的字段来判断用保利威还是腾讯云
      if (this.txyConfigs) {
        this.handleGrayTest(attendClassId, vid)
        return
      }

      // 如果没有请求过腾讯白名单接口,就根据接口返回的字段来判断用保利威还是腾讯云
      this.$http.post('/bds/selTxyConfigs/1.0/').then(res=>{
        const { code, body: { grayTestSwitch, playerSwitch } } = res
        if (code === '00') {
          this.txyConfigs = { grayTestSwitch, playerSwitch }
          this.handleGrayTest(attendClassId, vid)
        }
      })
    },
    // 处理灰度测试判断
    handleGrayTest(attendClassId, vid) {
      if (this.txyConfigs.grayTestSwitch == 1) {
        this.getTencentCourse(attendClassId, vid)
      }else {
        this.getPlaySafeToken2(vid)
      }
    },
    // 获取该章节对应的保利威信息vid, token, ts, sign
    getPlaySafeToken2(vid) {
      if (!vid) return this.opts = { playerType: 'polyv', showError: true }
      const params = { vid };
      this.$http.post('/us/getTutorCourseToken/1.0/', params).then(res=>{
        const { code, body } = res
        if (code == '00') {
          const { token, ts, sign } = body
          this.opts = { playerType: 'polyv', vid, token, ts, sign }
        }
      })
    },
    // 获取腾讯云课程
    getTencentCourse(businessId, vid) {
      // businessType类型说明
      // 1：成教 2：国开 3：全日制 4：自考 5：研究生 6：成教辅导课 
      // 7：上进学社课程ID类型 8：上进直播 9:上进学社训练营ID类型 
      // 10:上进学社读书计划ID类型 11:单本读书
      //（注：目前1代表成教和全日制,课程模块按第一期课程详情走）
      const params = {
        businessId,
        businessType: '10'
      }
      this.$http.post('/bds/selTxyVideoDetail/1.0/',params).then(res=>{
        const { code, body } = res
        if (code == '00') {
          // 支持腾讯云播放
          if (body.ifSupportTxy == 1) {
            const videoList = videoListMap(body.txyVideos)
            this.opts = { 
              playerType: 'tc', 
              videoList
            }
          }
          // 不支持播放, 切到保利威
          else {
            this.getPlaySafeToken2(vid)
          }
        }
      })
    },
    // 上面是点播版本迁移
    //简单记录观看时间，打卡后自动remove
    tagTime() {
      if (localStorage.getItem(`${this.vids}-surplusDuration`)) {
        this.surplusDuration = localStorage.getItem(
          `${this.vids}-surplusDuration`
        );
      } else {
        // todo 测试的时候, 打开15秒的代码注释, 要上线就注释掉
        this.surplusDuration = this.$route.query.surplusDuration * 1000;
        // this.surplusDuration = this.$route.query.surplusDuration * 15;
      }
      let index = 1;
      this.updateTimer = setInterval(() => {
        if (this.surplusDuration - 5000 * index > 0) {
          localStorage.setItem(
            `${this.vids}-surplusDuration`,
            this.surplusDuration - 5000 * index
          );
          index++;
        } else {
          clearInterval(this.updateTimer);
          localStorage.setItem(`${this.vids}-surplusDuration`, 1000);
        }
      }, 5000);
    },
    onLoad() {
      this.page += 1;
      let data = {
        semesterId: this.semesterId,
        punchCardType: 1,
        pageNum: this.page,
        pageSize: 5,
      };
      this.$http
        .post("/mkt/getSjScholarshipPunchCardList/1.0/", data)
        .then((res) => {
          if (Array.isArray(res.body)) {
            this.list = this.list.concat(res.body);
            this.loading = false;

            if (!this.pageShow) {
              this.pageShow = true;
            }

            if (res.body.length === 0) {
              this.finished = true;
            }
          } else {
            this.pageShow = true;
            this.finished = true;
          }
        });
    },
    openClockDialog() {
      this.$router.replace({
        path: "/active/a20250609HuaXia/release",
        query: {
          punchOrder: this.punchOrder,
          isPatchCard: this.isPatchCard,
          semesterId: this.semesterId,
          vids: this.vids,
        },
      });
    },
    shareCourse() {},
    refreshComment() {
      this.$refs.comment.refresh();
    },

    //加载播放器SDK
    loadPlayerScript(callback) {
      if (!window.polyvPlayer) {
        const $script = document.createElement("script");
        $script.setAttribute("src", player);
        $script.onload = callback;
        document.body.appendChild($script);
      } else {
        callback();
      }
    },
    // 初始化播放器
    initPlayer() {
      const polyvPlayer = window.polyvPlayer;
      this.player = polyvPlayer({
        wrap: "#player",
        width: "100%",
        height: "100%",
        speed: [1, 1.25, 1.5, 1.75, 2], //倍数
        vid: this.vids, //视频id
        useH5Page: true, //开启同层播放，腾讯X5内核的浏览器有效
        hideAudioMode: true,
        forceH5: true,
      });
    },
    toQRCodePage() {
      this.$router.push({
        path: "/aspirantUniversity/groupChat",
        query: {
          readPlanId: this.readPlanId,
          semesterId: this.semesterId,
        },
      });
    },
    openRulePopup() {
      this.rulePopup = !this.rulePopup;
    },
  },
  destroyed() {
    if (this.player) {
      this.player.destroy();
    }
  },
  beforeDestroy() {
    clearInterval(this.updateTimer);
  },
};
</script>

<style lang="scss" scoped>
@import "../../aspirantUniversity/style/common.css";
@import "../../aspirantUniversity/style/book-list.scss";

.container {
  .title {
    font-size: 0.14rem;
    color: #170606;
    line-height: 0.2rem;
    font-weight: bold;
  }

  .powder {
    color: #f0716f;
  }

  .no,
  .clock-yes {
    color: #979798;
  }

  .yes,
  .now {
    color: #f0716f;
  }

  .invite {
    background: #fff;
    padding: 0.09rem 0.1rem;
    background: url("../../../assets/image/aspirantUniversity/invite-group-bg.png")
      no-repeat;

    .group-avatar {
      width: 0.38rem;
      height: 0.38rem;
      display: inline-block;
      background: url("../../../assets/image/aspirantUniversity/group-avatar.png")
        no-repeat;
      background-size: 100% 100%;
    }

    .words {
      margin-left: 0.1rem;
      display: inline-block;
      vertical-align: top;

      .text-1 {
        font-size: 0.15rem;
        font-weight: 600;
        color: #170606;
        line-height: 0.24rem;
      }

      .text-2 {
        font-size: 0.12rem;
        color: #453838;
        line-height: 0.17rem;
      }
    }

    .operating {
      float: right;
      padding: 0.06rem 0;

      .btn {
        width: 0.72rem;
        height: 0.3rem;
        background: linear-gradient(
          135deg,
          #f09190 0%,
          #f07877 66%,
          #f06e6c 100%
        );
        border-radius: 0.2rem;
        color: #fff;
        font-size: 0.14rem;
      }
    }
  }

  .study-length {
    background: #fff;
    padding: 0.15rem 0.15rem 0.15rem 0;
    text-align: right;
    display: flex;
    justify-content: center;
    .default {
      padding: 0.02rem 0.1rem;
      border-radius: 0.2rem;
      display: inline-block;
      color: #f0716f;
      background: rgba(#f0716f, 0.1);
      font-size: 0.12rem;
      vertical-align: top;

      .clock {
        color: #f0716f;
        text-decoration: underline;
      }
    }

    .can {
      color: #000000;
      display: flex;
      align-items: center;
    }

    .rule-btn {
      width: 0.6rem;
      height: 0.21rem;
      background: linear-gradient(90deg, #70c5fe 0%, #51a0eb 100%);
      border-radius: 0.12rem;
      font-size: 0.12rem;
      color: #fff;
      margin-left: 0.18rem;
    }
  }

  .player-area {
    background: #fff;
    padding: 0 0.15rem 0.1rem 0.15rem;
    .dot-player {
      width: 3.45rem;
      height: 1.86rem;
      border: 1px solid #d9d9d9;
    }

    .video-block {
      width: 3.45rem;
      height: 1.86rem;
      border: 1px solid #d9d9d9;

      video {
        width: 100%;
        height: 100%;
      }
    }

    .operating {
      width: 3.45rem;
      height: 0.74rem;
      background: #fef9f9;
      color: #a3a3a5;
      margin: 0.15rem 0;
      border-radius: 0.02rem;

      .label-icon {
        display: block;
        i {
          width: 0.26rem;
          height: 0.26rem;
          display: inline-block;
        }
      }

      .label {
        display: block;
      }

      .icon-share {
        background: url("../../../assets/image/aspirantUniversity/icon-share.png")
          no-repeat;
        background-size: 100%;
      }

      .icon-studyplan {
        background: url("../../../assets/image/aspirantUniversity/icon-studyplan.png")
          no-repeat;
        background-size: 100%;
      }

      .van-col {
        padding-top: 0.14rem;
        text-align: center;
        position: relative;
      }

      .van-col:not(:last-child):after {
        content: "";
        position: absolute;
        top: 0.22rem;
        right: 0;
        width: 0.01rem;
        background: rgba(#f06e6c, 0.4);
        height: 0.3rem;
      }
    }
  }

  .recommend {
    margin-top: 0.1rem;
    background: #fff;
    padding: 0.1rem 0.15rem 0 0.15rem;

    .book {
      border: none;
    }
  }

  .comment-block {
    background: #fff;
    margin-top: 0.1rem;
    padding-top: 0.1rem;
    padding-bottom: 0.5rem;

    .title {
      padding: 0 0.15rem;
    }

    .list {
      padding: 0 0.05rem;
    }
  }

  .fixed {
    position: fixed;
    bottom: 0;
    width: 3.75rem;
    height: 0.5rem;
    //padding: .05rem .1rem;
    background: #fff;
    border-top: 1px solid #edebeb;

    .tip {
      width: 100%;
      height: 0.4rem;
      line-height: 0.4rem;
      background: #edebeb;
      border-radius: 0.2rem;
      padding-left: 0.1rem;
      color: #746a6a;
      font-size: 0.14rem;
    }
  }

  .week-plans {
    /deep/ .van-action-sheet__header {
      text-align: left;
      font-size: 0.14rem;
      height: 0.5rem;
      line-height: 0.5rem;
      padding-left: 0.15rem;
      color: #000000;
      border-bottom: 1px solid #edebeb;
    }

    .catalogue-ul {
      padding: 0.15rem;
      .catalogue-li + .catalogue-li {
        margin-top: 0.2rem;
      }

      .catalogue-li {
        width: 100%;
        font-size: 0.14rem;
        .catalogue-name {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        i {
          vertical-align: text-bottom;
        }
      }
    }
  }

  .speed-block {
    /deep/ .van-action-sheet__description {
      font-size: 0.14rem;
      color: #000000;
    }

    /deep/ .van-action-sheet__item {
      line-height: 0.22rem;
    }

    /deep/ .van-action-sheet__name {
      font-size: 0.14rem;
      color: #000000;
    }

    /deep/ .van-action-sheet__cancel {
      padding: 0.14rem 0.16rem;
      font-size: 0.14rem;
    }
  }
}

.rule-popup {
  border-radius: 0.1rem;

  .content {
    width: 3.45rem;
    height: 4.28rem;
    background: #fff;

    .haed {
      font-size: 0.16rem;
      font-weight: 500;
      color: #000000;
      text-align: center;
      margin: 0 0.15rem;
      padding: 0.2rem 0 0.15rem 0;
      border-bottom: 1px solid #edebeb;
    }

    .text {
      height: 3.36rem;
      overflow: auto;
      margin: 0 0.15rem;
      padding: 0.2rem 0;
    }
  }
}

.calendar-popup {
  border-radius: 0.1rem;

  .content {
    width: 3.45rem;
    min-height: 4.36rem;
    background: #f5f5f5;
    position: relative;

    .head {
      width: 100%;
      height: 1.78rem;
      background: linear-gradient(180deg, #f9a7a5 0%, #f06e6c 100%);
      border-radius: 0.1rem 0.1rem 0 0;
      padding-top: 0.25rem;
      text-align: center;

      .text-1 {
        font-size: 0.2rem;
        font-weight: 500;
        color: #ffffff;
      }

      .text-2 {
        margin-top: 0.05rem;
        font-size: 0.13rem;
        font-weight: 500;
        color: #fcd9d8;
      }
    }

    .date {
      position: absolute;
      top: 0.95rem;
      left: 0.15rem;
      width: 3.15rem;
    }
  }
}

.wx-share {
  border-radius: 0.1rem;
  .content {
    width: 3rem;
    height: 3.4rem;
  }
}

.clock-area {
  text-align: center;
  margin: 0.05rem 0;
  .btn {
    width: 3.45rem;
    height: 0.4rem;
    border-radius: 0.24rem;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    font-size: 0.18rem;
    color: #fff;
    font-weight: 500;
  }

  .btn:disabled {
    background: #c0c0c0;
  }

  .clocked {
    background: linear-gradient(
      135deg,
      #f09190 0%,
      #f07877 66%,
      #f06e6c 100%
    ) !important;
    //opacity: 0.6;
  }

  .grey-btn {
    width: 1rem;
    height: 0.44rem;
    border-radius: 0.24rem;
    font-size: 0.18rem;
    color: #fff;
    font-weight: 500;
    background: #c0c0c0;
    margin-right: 0.2rem;
  }

  .btn-w {
    width: 2rem;
  }
}
.comment-list {
  background: #fff;
  .item {
    padding: 0.1rem 0.1rem 0 0.1rem;

    /deep/ .van-divider {
      margin: 0;
    }

    .head {
      display: flex;

      .user-avatar {
        width: 0.4rem;
        height: 0.4rem;
        border-radius: 50%;
        background: #cccccc;
        flex: 0 0 auto;

        img {
          border-radius: 50%;
        }
      }

      .user-name {
        font-size: 0.14rem;
        color: #170606;

        .featured {
          width: 0.41rem;
          height: 0.15rem;
          vertical-align: text-bottom;
        }
      }

      .user-info {
        margin-left: 0.05rem;
      }
    }

    .comment-main {
      margin-top: 0.15rem;
      margin-left: 0.05rem;

      .imgs {
        margin-top: 0.08rem;
        margin-right: -0.8rem;
        .van-image {
          margin-right: 0.08rem;
          margin-bottom: 0.04rem;
        }
      }

      .release-time {
        font-size: 0.12rem;
        color: #b2acac;
        transform: rotate(0.9);
        margin-top: 0.02rem;
      }

      .comment-content {
        margin-top: 0.08rem;
        word-break: break-all;

        .more {
          color: #f06e6c;
          font-size: 0.14rem;
          font-weight: 600;
          line-height: inherit;
          background: transparent;
        }
      }

      .sub-comment-list {
        border-radius: 0.1rem;
        margin-top: 0.15rem;
        margin-right: 0.19rem;
        background-color: #f8f7f7;
        padding: 0.15rem 0.15rem 0.15rem 0.1rem;

        .sub-comment-user-name {
          display: inline-block;
          font-size: 0.14rem;
          font-weight: 600;
          color: #170606;
          position: relative;

          &:before {
            content: "";
            width: 0.02rem;
            height: 100%;
            background: #f06e6c;
            position: absolute;
            left: -0.1rem;
          }
        }

        .sub-comment-content {
          font-size: 0.14rem;
          word-break: break-all;
          color: #736868;
        }
      }
    }
  }
}
.empty {
  text-align: center;
}
</style>
