<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.unvsId===item.unvsId}" @click="selected(item)">{{item.unvsName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';

  export default {
    props: ['value', 'datas'],
    data() {
      return {
        options: [],
        pageNum: 0,
        pageSize: 20,
        isLoading: false,
        allLoaded: false,
        pfsnName:''
      }
    },
    created() {
      this.pfsnName = this.$route.query.pfsnName || '';
    },
    methods: {
      getUnvsList: function () {
        this.isLoading = true;
        const data = {
          pfsnName:this.pfsnName,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }
        this.$http.post('/bds/getResearchUnvs/1.0/', data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || [];
          this.options.push(...datas);
          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },

      loadMore: function () {
        this.pageNum++;
        this.getUnvsList();
      },
      selected: function (val) {
        this.$emit('input', val);
        this.$emit('onChange','unvs')

      },
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
