<template>
  <div v-if="isShow" @click.stop>
    <mt-swipe class="emotions bc-w" :auto="0" >
      <mt-swipe-item class="cl" v-for="(group,key) of emotions" :key="key">
        <div class="item" v-for="(item,index) of group" :key="index" @click="add(item)">
          <i class="emotion-icon" v-bind:style="{backgroundPosition:item.position}"></i>
        </div>
        <div class="item" @click="del">
          <i class="emotion-icon facedel-icon"></i>
        </div>
      </mt-swipe-item>
    </mt-swipe>
  </div>

</template>

<script>
  import emotions from '../../static/emotion/emotions.json';

  export default {
    props: ['value'],
    data() {
      return {
        isShow: false,
        emotionUrl: '//livestatic.polyv.net/assets/images/em/',
        emotions: []
      }
    },
    created() {
      let emotionJson = [...emotions];
      const len = Math.ceil(emotionJson.length / 20);
      for (let i = 0; i < len; i++) {
        this.emotions.push(emotionJson.splice(0, 20));
      }

      this.addEvent(document.getElementsByTagName('body')[0], 'click', (e) => {
        if (e.target.id !== 'btnEmotion') {
          this.toggleBox(false);
        }
      });
    },
    methods: {
      addEvent: function (element, type, callback) {
        if (element.addEventListener) {
          element.addEventListener(type, callback, false);
        } else if (element.attachEvent) {
          element.attachEvent(`on${type}`, callback);
        }
      },
      toggleBox: function (type) {
        this.isShow = type === undefined ? !this.isShow : type;
        if (this.isShow) {
          this.$nextTick(() => {
            if( document.querySelector("#playerWraper")){

              document.querySelector("#playerWraper").scrollIntoView(false);
            }
          });
        }
      },
      add: function (info) {
        this.$emit('input', `${this.value}[${info.title}]`);
      },
      del: function () {
        let val = this.value;
        if (this.value) {
          if (val.lastIndexOf(']') === val.length - 1) {
            val = val.slice(0, val.lastIndexOf('['));
          } else {
            val = val.slice(0, val.length - 1);
          }
          this.$emit('input', val);
        }
      },
      // 表情转换
      formatEmotions: function (content) {
        if (!content) return content;
        return content.replace(/(\[([^\[^\]]*)\])/g, ($1) => {
          const cur = emotions.find(item => {
            if ($1 === `[${item.title}]`) return item;
          });
          if (cur) {
            return `<img class="emotionimg" src="${this.emotionUrl + cur.fileName}"/>`;
          } else {
            return content;
          }
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .emotions{width: 100%;;height:160px;margin:0 auto;}
  .item{float:left;width:14.2857%;height:40px;text-align:center;}
  .emotion-icon{
    display:inline-block;width:48px;height:48px;margin:-5px 0 0 -5px;transform:scale(.64);background:url(//livestatic.polyv.net/assets/images/em/default.png) no-repeat 0 0;&.facedel-icon{background-position:center;background-image:url(//livestatic.videocc.net/assets/wimages/facedel.png);background-size:contain;}
  }
</style>
<style>
  .emotions .mint-swipe-indicator.is-active{background-color:#000;opacity:0.4;}
</style>
