<template>
  <div class="player">
    <div id="player" class="inner">
      <div class="live-load-wrap" v-if="!isLoaded">
        <mt-spinner class="mint-loadmore-spinner" :size="30" type="fading-circle"></mt-spinner>
      </div>
      <!--<div class="um" v-if="playing">-->
      <!--<div class="exit"></div>-->
      <!--<div class="moreBtn" @click="showMore=true" v-if="lines">-->
      <!--<img src="../../assets/image/student/more.png" alt="">-->
      <!--</div>-->
      <!--<div class="screenBtn" @click="fullScreen">-->
      <!--<img src="../../assets/image/student/fullScreen.png" alt="">-->
      <!--</div>-->
      <!--<div class="rightBg" v-show="showMore">-->
      <!--<div class="closeBtn" @click="showMore=false">-->
      <!--<img src="../../assets/image/close-w.png" alt="">-->
      <!--</div>-->
      <!--<ul>-->
      <!--<li>-->
      <!--<p>多线路<span :class="{active:line==item-1}" @click="switchLine(item-1)" v-for="item in lines">线路{{item}}</span></p>-->
      <!--</li>-->
      <!--</ul>-->
      <!--</div>-->
      <!--</div>-->
    </div>
  </div>
</template>
<script>
import config from "@/config";
import md5 from "js-md5";
export default {
  data() {
    return {
      isLoaded: false,
      isShowText: false,
      showTxt: "网络不给力哦，正全力加载中",
      play: true,
      nickname: "",
      userId: "",
      vid: "",
      nobegin: false,
      isPC: false,
      showMore: false,
      line: "",
      lines: 0,
      liveSdk: null,
      playing: false,
      status: false,
      zhiBO:false,
      param5: { cpId: '', liveFrom: '1' }, // 保利威参数
      param4:{l:'',m:''}
    };
  },
  created() {
    this.loadBase64Js();
    setTimeout(() => {
      this.isShowText = true;
    }, 15000);
  },
  mounted() {},
  props:['cpId'],
  methods: {
    IsPC() {
      var userAgentInfo = navigator.userAgent;
      var Agents = [
        "Android",
        "iPhone",
        "SymbianOS",
        "Windows Phone",
        "iPad",
        "iPod"
      ];
      var flag = true;
      for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
          flag = false;
          break;
        }
      }
      return flag;
    },
    init: function(vid, userId, nickname, sign, timestamp, socket) {
    
      this.vid = vid;
      this.userId = userId;
      this.nickname = nickname;
      let $script = document.createElement("script");
      $script.type = "text/javascript";
      $script.charset = "utf-8";
      $script.src = config.liveplayer;
      $script.onload = () => {
        this.initPlayer(vid, userId, nickname, sign, timestamp, socket);
      };
      document.getElementsByTagName("head")[0].appendChild($script);
    },
    loadBase64Js: function() {
      let $script = document.createElement("script");
      $script.type = "text/javascript";
      $script.charset = "utf-8";
      $script.src = config.liveBase64Js;
      document.getElementsByTagName("head")[0].appendChild($script);
    },
    fullScreen() {
      console.log(this.liveSdk.player);
      if (this.liveSdk.player.fullScreen) {
        this.liveSdk.player.fullScreen.request(
          document.getElementById("player")
        );
      } else {
        let video = document.getElementsByTagName("video")[0];
        if (video) {
          video.webkitEnterFullscreen();
        }
      }
    },
    // 初始化播放器
    // initPlayer: function (vid, userId, nickname) {
    //   // 如果DNS被劫持，2秒后再次执行该方法
    //   if ('undefined' === typeof polyvObject) {
    //     setTimeout(() => {
    //       this.initPlayer(vid, userId, nickname);
    //     }, 2000);
    //     return;
    //   }
    //   this.isLoaded = true;
    //   this.$nextTick(() => {
    //     polyvObject('#player').livePlayer({
    //       'width': '100%',
    //       'height': '100%',
    //       'uid': config.userId,   // 直播频道用户ID
    //       'vid': vid,
    //       'param1': userId,
    //       'param2': nickname,
    //       'param4':this.storage.getItem('learnId')
    //     });
    //   });
    // },
    initPlayer: function(vid, userId, nickname, sign, timestamp, socket) {
      if(this.zhiBO){
        return
      }
      if ("undefined" === typeof PolyvLiveSdk) {
        setTimeout(() => {
          this.initPlayer(vid, userId, nickname, sign, timestamp, socket);
        }, 2000);
        return;
      }
      // return;
      this.param5.cpId=this.cpId
      this.param4.l= this.storage.getItem("learnId")
      this.param4.m= this.storage.getItem("mobile")
      this.isLoaded = true;
      this.$nextTick(() => {
        var liveSdk = new PolyvLiveSdk({
          channelId: vid,
          sign: sign, // 频道验证签名
          timestamp: timestamp, // 毫秒级时间戳
          appId: config.appId, // polyv 后台的appId
          socket: socket,
          user: {
            userId: userId,
            userName: nickname,
            pic:
              this.storage.getItem("headImg") ||
              "//livestatic.videocc.net/v_102/assets/wimages/missing_face.png"
          },
          param4:JSON.stringify(this.param4),
          param5:JSON.stringify(this.param5)
        });
        this.liveSdk = liveSdk;
        this.$emit("live", liveSdk);
        liveSdk.on(liveSdk.EVENTS.CHANNEL_DATA_INIT, (event, data) => {
          liveSdk.setupPlayer({
            pptEl: "",
            el: "#player",
            type: "auto",
            width: "100%",
            height: "100%",
            autoplay: false,
            audioMode: false,
            controller: true,
            barrage: false,
            defaultBarrageStatus: false
          });
          this.getStatus(vid);
          liveSdk.on(this.liveSdk.EVENTS.STREAM_UPDATE, (event, status) => {
            if (status == "live") {
              this.liveSdk.player.play();
              this.$emit("golive",true)
            } else {
              this.liveSdk.player.pause();
                 this.$emit("golive",false)
            }
          });
        });
      });
      this.zhiBO=true
    },
    getStatus(vid){
        let detail = {
        timestamp: Date.now(),
        channelIds: vid
      };
      this.$http
        .get(
          `https://api.polyv.net/live/v2/channels/live-status?appId=${config.appId}&timestamp=${Date.now()}&channelIds=${vid}&sign=${this.getSignValue(detail)}`
        )
        .then(res => {
          // console.log(vid)
         if(res.code==200){
             let item = res.data[0];
                if (item.status == 'live') {
                   this.$emit("golive",true)
                    setTimeout(()=> {
                        this.liveSdk.player.play();
                   }, 500);
              }else{
                  this.$emit("golive",false)
              }
           }
        });
    },
    getSignValue: function(data) {
      data["appId"] = config.appId; //必选参数
      var res = Object.keys(data).sort();
      var str = config.appSecret;
      for (var i = 0; i < res.length; i++) {
        str = str + res[i] + data[res[i]];
      }
      str = str + config.appSecret;
      const sign = md5(str).toUpperCase();
      return sign;
    },
    switchLine(i) {
      this.line = i;
      this.liveSdk.player.switchLine(i);
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../assets/less/variable";
.player {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 100;
  .inner {
    max-width: @maxWidth;
    height: 2.1rem;
    margin: 0 auto;
    background-color: #000;
  }
}
.btn {
  width: 1rem;
  color: rgba(232, 91, 87, 1);
  border: solid 1px rgba(232, 91, 87, 1);
  background-color: transparent;
  height: 0.3rem;
  line-height: 0.3rem;
  text-align: center;
  font-size: 0.14rem;
  margin-top: 0.2rem;
}
</style>
<style lang="less">
.live-load-wrap {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  color: #fff;
  font-size: 0.12rem;
  transform: translate(-50%, -50%);
  .mint-loadmore-spinner {
    position: relative;
    top: 50%;
    margin-top: -30px;
    margin-right: 0;
  }
}
.video {
  .video-wrapper {
    position: relative;
    font-size: 0;
  }
  .video-play {
    .video_btn {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1) url("../../assets/image/student/play.png")
        center/80px 80px no-repeat;
    }
  }
}
.um {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  transform: translate(-50%);
  top: 0;
  z-index: 1000;
  max-width: 640px;
  .moreBtn {
    position: absolute;
    top: 0.1rem;
    right: 0.1rem;
    img {
      width: 0.2rem;
    }
  }
  .screenBtn {
    position: absolute;
    bottom: 0.05rem;
    right: 0.1rem;
    img {
      width: 0.2rem;
    }
  }
  .rightBg {
    padding-top: 0.3rem;
    background-color: rgba(0, 0, 0, 0.7);
    position: absolute;
    width: 70%;
    height: 100%;
    right: 0;
    top: 0;
    animation: move 1s;
    .closeBtn {
      position: absolute;
      width: 0.2rem;
      right: 0.1rem;
      top: 0.1rem;
      img {
        width: 100%;
      }
    }
    ul {
      li {
        width: 100%;
        height: 0.5rem;
        line-height: 0.5rem;
        color: #fff;
        font-size: 0.14rem;
        padding-left: 0.15rem;
        p {
          color: rgba(255, 255, 255, 0.7);
        }
        span {
          display: inline-block;
          margin-left: 0.1rem;
          width: 0.5rem;
          height: 0.3rem;
          line-height: 0.3rem;
          text-align: center;
          color: #fff;
          &.active {
            border: solid 1px #fff;
            border-radius: 0.15rem;
          }
        }
      }
    }
  }
}
@keyframes move {
}
</style>
