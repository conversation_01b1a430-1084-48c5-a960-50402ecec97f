<template>
  <transition name="modal" v-on:after-leave="destroyElement">
    <div
      class="modal"
      :class="customClass"
      v-show="visible"
      @click="handleWrapperClick"
    >
      <div class="inner">
        <div class="bd">
          <div class="txt">
            <slot>{{ message }}</slot>
          </div>
          <p v-if="small_message.length > 0" class="small_txt">
            {{ small_message }}
          </p>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      customClass: "", // 自定义类名
      title: "",
      message: "",
      halfBtn: true, //按钮是否为1/2宽
      duration: 3000,
      yes: null,
      cancel: null,
      smallCancel: false, //取消按钮大小
      beforeClose: null,
      timer: null,
      onClose: null,
      small_message: "",
    };
  },
  mounted() {
    this.startTimer();
  },
  methods: {
    close: function () {
      this.clearTimer();
      this.visible = false;
      if (typeof this.onClose === "function") this.onClose(this.id);
    },
    handleClose: function (action) {
      if ("function" === typeof this.beforeClose) {
        this.beforeClose(action, this, () => {
          this.close();
        });
      } else {
        this.close();
      }
    },
    handleWrapperClick: function () {
      if (this.closeOnClickModal) this.handleClose("cancel");
    },
    clearTimer: function () {
      clearTimeout(this.timer);
    },
    startTimer: function () {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          this.handleClose("cancel");
        }, this.duration);
      }
    },
    destroyElement: function (el) {
      this.$destroy(true);
      el.parentNode.removeChild(el);
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../../assets/less/variable";
.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  text-align: center;
  overflow-y: auto;
  word-break: normal;
  transition: opacity 0.3s ease;
  &:after {
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
    content: "";
  }
  > .inner {
    display: inline-block;
    margin-top: -0.4rem;
    vertical-align: middle;
    text-align: left;
    background-color: rgba(23, 6, 6, 0.5);
    transition: all 0.3s ease;
    border-radius: 0.07rem;
    color: white;
  }
  .bd {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0.09rem 0.1rem;
    font-size: 0.14rem;
  }
  .ft {
    display: flex;
    padding-right: 0.15rem;
  }
  .txt {
    font-size: 0.12rem;
    line-height: 0.17rem;
  }
}
.modal-enter,
.modal-leave-active {
  opacity: 0;
}
.modal-enter .inner,
.modal-leave-active .inner {
  transform: scale(0.8);
}

.small_txt {
  font-size: 0.12rem;
  text-align: center;
  color: #444444;
}
</style>
