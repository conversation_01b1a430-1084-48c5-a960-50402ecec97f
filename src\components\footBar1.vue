<template>
  <div class="footer"  :class='{"no-bg": noBg}'>
    <div class="wrap">
      <div class="inner" @click="submit" :class="{grad:grad}">{{title}}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ""
    },
    grad: {
      type: Boolean,
      default: false
    },
    noBg: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    submit: function() {
      this.$emit("submit");
    }
  }
};
</script>

<style lang="less" scoped>
@import "../assets/less/variable";
.footer {
  position: fixed;
  bottom: 0rem;
   background: white;
   width: 100%;
   z-index: 99;
   &.no-bg{
     background: none;
     .wrap{
       background: none;
     }
   }
  .wrap {
    left: 0.1rem;
    right: 0.1rem;
     bottom: 0.1rem;
     background: white;
     z-index: 99;
  }
  &[disabled] {
    .inner {
      opacity: 0.6;
      pointer-events: none;
    }
  }
  .inner {
    border-radius: 0.25rem;
    line-height: 0.4rem;
    text-align: center;
    height: 0.4rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    font-size: 0.15rem;
    background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
    margin-top: 0.1rem;
    padding: 0;
  }
  &.active {
    .inner {
      background-color: #efe142;
      color: #333;
      background-image: linear-gradient(to bottom right, #efe142, #ffd100);
    }
  }
}
</style>
