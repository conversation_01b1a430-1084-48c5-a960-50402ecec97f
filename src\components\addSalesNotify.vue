<template>
  <div class="footer detail-ft">
    <div class="wrap">
      <div class="inner clearfix">
        <button class="fr btn1 mt" disabled v-if="isAddNotify==='Y'">已提醒</button>
        <button class="fr btn1 mt" v-else @click="addSalesNotify">开始前提醒我</button>
        <div class="lf">
          <span class="fsc2">我的智米：</span>
          <span class="fsc3">{{accAmount}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: ['salesId', 'salesName', 'ifAddNotify', 'planCount', 'salesType',"startTime"],
    data() {
      return {
        isAddNotify: this.ifAddNotify,
        accAmount: '-',
      }
    },
    mounted() {
      this.getMyAccAmount();
    },
    methods: {
      // 添加活动开始提醒
      addSalesNotify: function () {
        let data = {
          salesId: this.salesId,
          salesName: this.salesName,
          planCount: this.planCount || '',
          salesType: this.salesType,
          startTime:this.startTime
        };
        this.$http.post('/gs/addSalesNotify/1.0/', data).then(res => {
          if (res.code !== '00') return;

          this.isAddNotify = 'Y';

          const h = this.$createElement;
          this.$modal({
            message: h(
              'div', {style: 'text-align:center'}, [
                h('div', null, '已收到'),
                h('div', null, '系统将在开始前给您推送消息！')
              ]
            ),
            icon: 'notice'
          });
        });
      },
      // 获取我的智米
      getMyAccAmount: function () {
        this.$http.post('/ats/accountDetail/1.0/', {accType: '2'}).then(res => {
          if (res.code !== '00') return;
          this.accAmount = parseInt(res.body.accAmount || 0);
        });
      },
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/detailPage";
</style>
