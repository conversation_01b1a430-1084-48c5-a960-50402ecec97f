<template>
  <div class="polyv-dot-player-wrap">
    <div id="polyv-dot-player" class="polyv-dot-player"></div>
    <error-popup ref="errorPopup" />
  </div>
</template>

<script>
import errorPopup from './errorPopup.vue'
export default {
  components: {
    errorPopup
  },
  props: {
    // 播放器实例化的配置项
    playerOpts: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 视频资源的配置(加密的视频, 模拟器的移动端播放不了, 真机是可以播放的)
    /**
     * opts参数如下:
     *   ts:      String    必填参数  
     *   vid:     String    必填参数    
     *   sign:    String    必填参数
     *   token:   String    必填参数   
     *   showError: Boolean 非必填参数   显示错误弹框层 
     */    
    opts: {
      type: Object,
      default: () => {
        return {
        };
      },
    },
  },
  data() {
    return {
      vodPlayerJs: "https://player.polyv.net/script/player.js",
      player: null,
    };
  },
  watch: {
    opts: {
      handler(nV, oV) {
        this.reset()
        this.loadPlayer()
        // const { vid, ts, token: playsafe, sign, showError } = nV;
        // if (vid && token && ts && sign) {
        //   this.player.changeVid({ vid, playsafe, ts, sign });
        // }
        // if (showError) {
        //   this.showErrorPopup()
        // }
      },
      deep: true,
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    // 显示错误弹框层
    showErrorPopup() {
      this.$refs.errorPopup.show = true;
    },
    // 重置
    reset() {
      this.$refs.errorPopup.show = false;
    },
    init() {
      this.loadPlayerScript(this.loadPlayer);
    },
    loadPlayerScript(callback) {
      if (!window.polyvPlayer) {
        const myScript = document.createElement("script");
        myScript.setAttribute("src", this.vodPlayerJs);
        myScript.onload = callback;
        document.body.appendChild(myScript);
      } else {
        callback();
      }
    },
    loadPlayer() {
      const { vid, token: playsafe, ts, sign, showError } = this.opts;
      if (showError) return this.showErrorPopup()
      if (!vid && !playsafe && !ts && !sign) return;
      if (this.player) return this.player.changeVid({ vid, playsafe, ts, sign })
      
      const polyvPlayer = window.polyvPlayer;
      this.player = polyvPlayer({
        wrap: "#polyv-dot-player",
        width: "100%",
        height: "100%",
        speed: [1, 1.25, 1.5, 1.75, 2],
        useH5Page: true, //开启同层播放，腾讯X5内核的浏览器有效
        forceH5: true,
        vid,
        playsafe,
        ts,
        sign,
        ...this.playerOpts
      });
      this.$emit("inited", this.player);
      this.player.on("s2j_onPlayerError", () => {
        this.$emit("onError");
        this.showErrorPopup()
      });
      this.player.on("s2j_onVideoPlay", () => {
        this.$emit("onPlay");
      });
      this.player.on("s2j_onVideoPause", () => {
        this.$emit("onPause");
      });
      this.player.on("s2j_onPlayOver", () => {
        this.$emit("onEnded");
      });
      // inited 下面写播放器监听代码
    },
  },
  destroyed() {
    if (this.player) {
      this.player.destroy();
    }
  },
};
</script>

<style lang="less" scoped>
.polyv-dot-player-wrap {
  width: 100%;
  height: 100%;
}
.polyv-dot-player {
  // max-width: 640px;
  // height: 2.1rem;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background-color: #000;
}
</style>
