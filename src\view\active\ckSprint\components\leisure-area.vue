<template>
  <div class="yz-ck-leisureArea">
    <div class="title-img"></div>
    <ul class="list-box clearfix">
      <li v-for="(item, index) in list" :key="index">
        <img :src="item.img" class="list-img" alt="">
        <div class="liner"></div>
        <red-btn class='btn-abs' :disabled='item.btnDisabled' :text='index == 3 ? "查看更多":"我也参加"'
                 @click='btnClick(item)' />
      </li>
    </ul>
  </div>
</template>

<script>
// import a1Img from '../../../../assets/image/active/ckSprint/a1.jpg';
import a3Img from '../../../../assets/image/active/ckSprint/a3.jpg';
import a4Img from '../../../../assets/image/active/ckSprint/a4.jpg';
// import a5Img from '../../../../assets/image/active/ckSprint/a5.jpg';
import a6Img from '../../../../assets/image/active/ckSprint/a6.jpg';
import a7Img from '../../../../assets/image/active/ckSprint/a7.jpg';
import RedBtn from './red-btn.vue';

export default {
  components: {
    RedBtn,
  },
  data () {
    return {
      list: [
        {
          name: '十月国庆，文化推荐官',
          img: a7Img,
          btnDisabled: false,
          appUrl: 'yuanzhiapp://yzwill.cn/Activity/Detail?params={"actId":"252"}',

        },
        // {
        //   name: '我爱原创，展现多才多艺的你',
        //   img: a5Img,
        //   btnDisabled: false,
        //   appUrl: 'yuanzhiapp://yzwill.cn/Activity/Detail?params={"actId":"245"}',
        // },
        {
          name: '晒最炫的证，立最酷的 flag',
          img: a4Img,
          btnDisabled: false,
          appUrl: 'yuanzhiapp://yzwill.cn/Activity/Detail?params={"actId":"247"}',
        },
        {
          name: '你跑起来真好看',
          img: a3Img,
          btnDisabled: false,
          appUrl: 'yuanzhiapp://yzwill.cn/Activity/Detail?params={"actId":"246"}',
        },
        {
          name: '第2期学员领读计划',
          img: a6Img,
          btnDisabled: false,
          // appUrl: 'yuanzhiapp://yzwill.cn/Activity/Detail?params={"actId":"250"}',
          appUrl: 'yuanzhiapp://yzwill.cn/Home?params={"tab": 2, "circleTopItems": 1}',

        },
        // {
        //   name: '开学季，神秘来信请签收',
        //   img: a1Img,
        //   btnDisabled: false,
        //   appUrl: 'yuanzhiapp://yzwill.cn/Home?params={"tab": 2, "circleTopItems": 1}',

        // },

      ],
    };
  },
  props: {},
  mounted () {

  },
  methods: {
    btnClick (item) {
      this.$yzStatistic('sprintAct.base.click', '14', '我也参加');
      window.location.href = item.appUrl;
    },
  },
};
</script>

<style lang="less" scoped>
.yz-ck-leisureArea {
  background: #e1f1f4;
  border-top: 0.02rem solid #84ccc9;
  padding: 0.27rem 0.17rem 0.1rem;
  .title-img {
    background: url(../../../../assets/image/active/ckSprint/title-play.png) no-repeat;
    background-size: 100% 100%;
    width: 1.7rem;
    height: 0.2rem;
    margin: 0 auto 0.18rem;
  }
  .list-box {
    li {
      border-radius: 0.1rem;
      // background: rgba(0, 0, 0, 0.2);
      float: left;
      width: 1.62rem;
      height: 2.17rem;
      margin-bottom: 0.15rem;
      box-shadow: 0px 5px 5px 0px rgba(15, 130, 125, 0.55);
      position: relative;
      overflow: hidden;
      .liner {
        background: linear-gradient(rgba(250, 205, 137, 0), rgba(250, 205, 137, 1));
        position: absolute;
        width: 100%;
        height: 1.04rem;
        left: 0;
        right: 0;
        bottom: 0;
      }
      .list-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      &:not(:nth-child(2n)) {
        margin-right: 0.16rem;
      }
      .btn-abs {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0.14rem;
        z-index: 2;
      }
    }
  }
}
</style>
