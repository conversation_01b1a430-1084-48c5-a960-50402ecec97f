<template>
  <div class="ranking">
    <div class="title-box">
      <img src="../../../../assets/image/active/signAct/ti-left.png" alt />
      <h3>同期学员挑战进度</h3>
      <img src="../../../../assets/image/active/signAct/t-right.png" alt />
    </div>
    <div class="record-box">
      <table style="table-layout: fixed" width="100%" cellspacing="0" cellpadding="2">
        <tr>
          <th class="header" style="width: 25%">排名</th>
          <th class="header" style="width: 25%">用户</th>
          <th class="header" style="width: 25%">学习打卡</th>
          <th class="header" style="width: 25%">跑步打卡</th>
        </tr>
        <tr class="item-list">
          <td class="bgc item-index" style="vertical-align: middle">{{ myData.order }}</td>
          <td class="bgc item-name">
            <img
              :src="
                myData.head_img ||
                require('../../../../assets/image/active/signAct/chick.png')
              "
              alt
            />我
          </td>
          <td class="bgc item-bonus" style="vertical-align: middle">{{ myData.learnCount }}/21</td>
          <td class="bgc item-bonus" style="vertical-align: middle">{{ myData.runningCount }}/21</td>
        </tr>
        <tr
          :class="index > 2 ? 'item-list item-last' : 'item-list'"
          v-for="(item, index) in rankingList"
          :key="item.userId"
        >
          <td v-if="index > 2" class="bgc item-index">{{ index + 1 }}</td>
          <td v-else class="bgc item-index" style="vertical-align: middle">
            <img
              :src="
                require(`../../../../assets/image/active/signAct/li-${index + 1
                  }.png`)
              "
              alt
            />
          </td>
          <td class="bgc item-name">
            <img
              :src="
                item.headImg ||
                require('../../../../assets/image/active/signAct/chick.png')
              "
              alt
            />
            {{ item.realName | nameFormat }}
          </td>
          <td class="bgc item-bonus" style="vertical-align: middle">{{ item.learnCount }}/21</td>
          <td class="bgc item-bonus" style="vertical-align: middle">{{ item.runningCount }}/21</td>
        </tr>
      </table>
      <div class="load-more" v-if="!showNoMore" @click="$emit('handelLoad')">
        <span>查看更多</span>
        <van-icon name="arrow" color="#FFF0D0" />
      </div>
      <div class="load-more" v-else>
        <span>已经到底了!!!</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "LeaderBoard",
  props: {
    rankingList: {
      type: Array,
      required: false,
    },
    myData: {
      type: Object,
      required: false,
    },
    showNoMore: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {};
  },
  filters: {
    nameFormat(item) {
      if (!item) return "";
      let str = item;
      if (str.length == 2) {
        str = str
          .toString()
          .replace(/^([^\x00-\xff])([^\x00-\xff]{0,})([^\x00-\xff])/g, "$1*");
      } else if (str.length >= 3) {
        str = str
          .toString()
          .replace(/^([^\x00-\xff])([^\x00-\xff]{0,})([^\x00-\xff])/g, "$1*$3");
      } else if (str.length == 4) {
        str = str
          .toString()
          .replace(
            /^([^\x00-\xff])([^\x00-\xff]{0,2})([^\x00-\xff])/g,
            "$1*$3"
          );
      } else if (str.length > 4) {
        str = str
          .toString()
          .replace(
            /^([^\x00-\xff])([^\x00-\xff]{0,3})([^\x00-\xff])/g,
            "$1*$3"
          );
      }
      return str;
    },
  },
  created() { },
  methods: {},
};
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.ranking {
  margin-top: 0.2rem;
  .title-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6 * @rem;
    img {
      margin: 0 6 * @rem;
      width: 10 * @rem;
      height: 5 * @rem;
    }
    h3 {
      font-size: 14 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #8c4d0e;
      line-height: 20 * @rem;
    }
  }
  .record-box {
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    background: linear-gradient(180deg, #fdca65 0%, #f8ae48 100%);
    box-shadow: inset 0 * @rem 2 * @rem 4 * @rem 0 * @rem
        rgba(255, 255, 255, 0.5),
      inset 0 * @rem -3 * @rem 4 * @rem 0 * @rem rgba(245, 154, 50, 0.57);
    border-radius: 6 * @rem;
    padding: 0 2 * @rem 10 * @rem 2 * @rem;
    .header {
      padding: 9 * @rem 0;
      font-size: 12 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #8c4d0e;
    }
    .item-list {
      background: #fef3e1;
      text-align: center;
      .item-name {
        display: flex;
        align-items: center;
        font-size: 14 * @rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #8c4d0e;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        img {
          margin-right: 6 * @rem;
          border-radius: 50%;
          width: 32 * @rem;
          height: 32 * @rem;
        }
      }
      .item-index {
        font-size: 16 * @rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ddb96d;
        img {
          width: 24 * @rem;
          height: 24 * @rem;
        }
      }
      .item-bonus {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #a87744;
      }
      .bgc {
        padding: 14 * @rem 0;
      }
    }
    .item-last {
      background-color: #f9e4c2;
    }
    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10 * @rem;
      span {
        font-size: 14 * @rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fff0d0;
      }
    }
  }
}
</style>
