import axios from 'axios'
import md5 from 'js-md5'
import { Base64 } from 'js-base64'
import { removeCookie,getRawCookie } from 'tiny-cookie'
import key from '@/view/account/key'
import { isWeibo,isWxwork } from '@/common'
import MobileDetect from '@/common/mobile-detect'
import { app_key, app_secret, loginUrl } from '@/config'
import router from '@/router'
import Modal from '@/components/modal'
import storage from './storage'
import { Toast } from 'vant'
const cancelToken = axios.CancelToken
const source = cancelToken.source()

var transferIdMap = {}
var phoneModel = ''
var phoneSys = ''

// axios拦截器
axios.interceptors.request.use(
  (config) => {
    if (config.params) {
      if (config.method === 'get' && config.params.begin) {
        var http = window.location.protocol === 'http:' ? 'http:' : 'https:'
        // config.url = http + config.url;
        return config
      }
    }
    if (config.url.startsWith('https://ocr-api.ccint.com')) {
      config.headers['app-key'] = app_key
      config.headers['app-secret'] = app_secret
      config.headers['content-Type'] = 'application/json'
    }
    if (!config.url.startsWith('http')) {
      const authToken = storage.getItem('authToken')
      const ykAuthtoken = storage.getItem('ykAuthtoken')
      if(config.url.includes('/qyWechatLogin')){
        config.url = config.url
      }else{
        config.url = '/proxy' + config.url
      }  
      authToken && (config.headers.authtoken = authToken)
      if (document.URL.indexOf('321Activity') != -1) {
        ykAuthtoken && (config.headers.ykAuthtoken = ykAuthtoken)
      }   
      if(config.url.includes('/product-server')||config.url.includes('/cp')){
        if(config.url.includes('/product-server')){
          config.data.client = {
            platform:'H5'
          }
          }
        config.headers['content-Type'] = 'application/json'
      }else{
        config.headers['content-Type'] = 'text/yzedu+;charset=utf-8'
      }
      if (config.url.includes('/uploadFile')) return config // 上传文件
      if (config.data === undefined) {
        config.data = {}
      }
      getPhoneNameAndSystemName()
      // 特殊统一：当请求头类型不一致时，可在此处自定义设置
      if (config.proxyName == 'yzapp') {
        // 'Access-Control-Expose-Headers': '*' // 显示自定义类型值
        config.headers['content-Type'] = 'application/json; charset=utf-8'
      } else {
        let timeStamp = Date.now()
        let sign = md5(`${timeStamp}${key.key}`).toUpperCase()
        //发送时间
        let sendTime = new Date().getTime()
        //批次调用唯一标识
        let transferId = uuid()
        //在当次调用本次调用的顺序
        let transferSeq = '1'
        //当次调用的地址
        let uri = `${document.URL}`
        //当次调用的备注
        let title = document.title
        let frontTrace = JSON.stringify({
          sendTime,
          transferId,
          transferSeq,
          uri,
          title,
          phoneModel,
          phoneSys,
        })
        let data ={}
        if(config.url.includes('/product-server')||config.url.includes('/cp')){
           data = config.data
        }else{
        config.data['sign'] = sign
        config.data['timeStamp'] = timeStamp
         data = {
          header: { appType: isWeibo() ? '7' : '2', frontTrace: frontTrace },
          body: config.data || {},
        }       
      }
        if (config.url.trim().endsWith('/1.0/')) {
          data = Base64.encode(JSON.stringify(data))
        } else {
          data = JSON.stringify(data)
        }
        config.data = data
      }
    }

    if (config.url.includes('question')) {
      config.cancelToken = source.token
      config.withCredentials = true
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

axios.interceptors.response.use(
  (response) => {
    let { data } = response
    // 错误请求时，不需要提示的处理
    data['showTips'] = response.config?.url !== '/proxy/api/open/common'
    try {
      if (response.config.url.indexOf('.do') == -1) {
        responseCode(
          data,
          JSON.parse(Base64.decode(response.config.data) || '{}').body || {}
        )
      } else {
        responseCode(data, JSON.parse(response.config.data || '{}') || {})
      }
    } catch (e) {
      responseCode(data, {})
    }
    return data
  },
  (error) => {
    if (!error.message === 'E000034') {
      Modal({ message: '服务网络有异常，请稍后再试！', icon: 'warning' })
    }
    return Promise.reject(error)
  }
)

//生成UUID
function uuid() {
  var type = window.event && window.event.type
  if (type != 'click') {
    var e = document.URL
    if (transferIdMap[e]) return transferIdMap[e]
  }
  var s = []
  var hexDigits = '0123456789abcdef'
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = ''
  var uuid = s.join('')
  if (type != 'click') {
    var e = document.URL
    transferIdMap[e] = uuid
  }
  return uuid
}

//获取手机型号和系统
function getPhoneNameAndSystemName() {
  // 测试获取手机型号和手机系统
  var device_type = navigator.userAgent //获取userAgent信息
  var md = new MobileDetect(device_type) //实例化mobile-detect
  var os = md.os() //获取系统
  var model = ''
  if (os == 'iOS') {
    //ios系统的处理
    os = md.os() + md.version('iPhone')
    model = md.mobile()
  } else if (os == 'AndroidOS') {
    //Android系统的处理
    os = md.os() + md.version('Android')
    model = md.mobile()
  }
  phoneModel = model
  phoneSys = window.navigator.userAgent
  //打印系统版本和手机型号
  // console.log(model + "---" + window.navigator.userAgent);
}

// 去登录页
function toLogin() {
  console.log('=>router.history', router.history)
  console.log('=>router.history.current.query', router.history.current.query)
  const {
    inviteId = '',
    action = '',
    regOrigin = '',
    regChannel = window.sessionStorage.getItem('regChannel'),
  } = router.history.current.query

  let query = {
    redirect: router.history.current.fullPath,
    inviteId,
    action,

    // 新增 2024-01-05 解决上进学社领取代金券去登录页面丢失邀约参数
    regOrigin,
    regChannel,
  }
  if (router.history.current.fullPath.indexOf('scholarship') == '-1') {
    query.scholarship = router.history.current.query.scholarship
  }

  //   if (router.history.current.fullPath.indexOf('regOrigin') == '-1') {
  //     query.regOrigin = router.history.current.query.regOrigin || ''
  //   }
  //   if (router.history.current.fullPath.indexOf('regChannel') == '-1') {
  //     query.regChannel =
  //       router.history.current.query.regChannel ||
  //       window.sessionStorage.getItem('regChannel')
  //   }
  router.push({
    name: 'login',
    query: query,
  })
}
//去钉钉登录页
function toRecruitLogin() {
  router.push({
    name: 'recruitLogin',
    query: {
      redirect: router.history.current.fullPath,
      inviteId: router.history.current.query.inviteId || '',
    },
  })
}
/** 员工知识库登录 */
function ygzskToLogin() {
  source.cancel('E000034')
  Modal.confirm('当前页面需要登录确认身份，即将进入登录界面。', undefined, {
    customClass:
      document.querySelector('body').clientWidth > 750 ? 'ygzsk-modal' : '',
    confirmButtonText: '继续访问',
    showCancelButton: false,
  })
    .then((action) => {
      console.log(/Mobi|Android|iPhone/i.test(navigator.userAgent),isWxwork(),'/Mobi|Android|iPhone/i.test(navigator.userAgent)');
      if (/Mobi|Android|iPhone/i.test(navigator.userAgent)) {
        // 企微 走 recruit Login
        if(isWxwork()){
          router.replace({
            name: 'transferPage',
            query: {
              redirect: router.history.current.fullPath,
              type:'ZSKYD',
              inviteId: router.history.current.query.inviteId || '',
            },
          })
  
        }else{
        router.replace({
          name: 'recruitLogin',
          query: {
            redirect: router.history.current.fullPath,
            inviteId: router.history.current.query.inviteId || '',
          },
        })
        }
      } else {
        // 浏览器走 url
        if(isWxwork()){
          router.replace({
            name: 'transferPage',
            query: {
              redirect: router.history.current.fullPath,
              type:'ZSKPC',
              inviteId: router.history.current.query.inviteId || '',
            },
          })
        }else{
          const url = loginUrl + '?redirect=' + encodeURIComponent(location.href)
          window.location.replace(url)
          }
      }
    })
    .catch(() => {})
}
// 状态码判断
function responseCode(data, fromData) {
  if (data.code == 200) {
    return
  }
  // 如果在证书页面 不走下面的逻辑
  if (
    router.history.current.fullPath.includes('progressCertificate') &&
    (data.code === 'E00001' ||
      data.code === 'E000034' ||
      data.code === 'E00010')
  ) {
    Toast.fail(data.message || data.msg || data.code)
    storage.clear()
    window.sessionStorage.setItem('loginPopup', '1')
    setTimeout(() => {
      if (router.history.current.name === 'progressCertificate') {
        router.go(0)
      } else {
        router.push({
          name: 'progressCertificate',
          query: {
            certId: router.history.current.query.certId || '',
            targetUserId: router.history.current.query.targetUserId || '',
            certRecordId: router.history.current.query.certRecordId || '',
            name: router.history.current.query.name || '',
            inviteId: router.history.current.query.inviteId || '',
          },
        })
      }
    }, 500)
    return
  }
  // 自考成绩单错误码
  if (
    [
      'E000334',
      'E000335',
      'E000336',
      'E000337',
      'E000338',
      'E000339',
      'E000340',
      'E000341',
    ].includes(data.code)
  ) {
    return
  }
  // 如果是APP访问并且登录失效时，跳转到app路由页
  if (
    window.sessionStorage.getItem('browser') === 'app' &&
    ['E00001', 'E00010'].includes(data.code)
  ) {
    yz.login()
  } else if (data.code === 'E00001') {
    // 登录失效或未登录
    storage.clear()
    toLogin()
  } else if (data.code === 'E000034'||data.code === 'E000035') {
    // 登录失效或未登录
    removeCookie('empId')
    removeCookie('userId')
    if (router.history.current.fullPath.includes('problem')) {
      ygzskToLogin()
    } else {
      toRecruitLogin()
    }
  } else if (data.code === 'E00010') {
    // 异地登录错误码
    storage.clear()

    if (window.history.length === 1) {
      toLogin()
    } else {
      Modal.closeAll()
      Modal.confirm('您的帐号在另一地点登录，您已被迫下线', undefined, {
        confirmButtonText: '重新登录',
      })
        .then((action) => {
          toLogin()
        })
        .catch(() => {})
    }

  } else if (data.code !== '00' && !fromData.notPrompt && data.showTips) {
    if(data.code == '0'){
      return false
    }
    Modal({ message: data.message || data.msg || data.code, icon: 'warning' })
  }
}

export default axios
