export default {
  data() {
    return {
      redBooks: {}
    }
  },
  methods: {
    // URL转换为Object
    urlToObj(search) {
      const news = {}
      const list = search?.split('?')[1]?.split('&')
      if (list && list.length) {
        for (let i = 0; i < list.length; i++) {
          const key = list[i]?.split('=')
          if ([key[0]]) news[key[0]] = key[1]
        }
      }
      return news
    },
    // 获取访问权限
    getAccessToken() {
      this.$http
        .post(
          '/api/open/common',
          {
            advertiser_id: this.redBooks.advertiser_id,
            method: 'oauth.getAccessToken' // 固定
          },
          { proxyName: 'yzapp' }
        )
        .then((res) => {
          console.log('获取访问权限-res', res?.data)
          const token = res?.data?.access_token
          const types = this.redBooks?.event_type?.split(',')
          console.log('获取访问权限-event_type', types)
          if (token && types?.length) {
            for (let m = 0; m < types.length; m++) {
              types[m] && this.getBackData(types[m], token)
            }
          }
        })
    },
    // 获取回传数据
    getBackData(_type, _token) {
      this.$http.post(
        '/api/open/common',
        {
          advertiser_id: this.redBooks.advertiser_id,
          method: 'aurora.leads', // 固定
          access_token: _token,
          event_type: _type,
          click_id: this.redBooks.click_id,
          conv_time: new Date().getTime() // 固定
        },
        { proxyName: 'yzapp' }
      )
    }
  }
}
