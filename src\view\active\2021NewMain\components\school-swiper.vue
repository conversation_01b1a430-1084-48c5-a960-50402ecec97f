<template>
  <div class="yz-school-swiper">
    <!-- 轮播组件 -->
    <big-swiper
      :list='list'
      swiperClass='swiper-class'
      swiperSlideClass='swiper-slide-class'
      pageId='school-swiper-page'
    >
      <div class="img-shadow" slot-scope="scope">
        <img :src="scope.item.image" alt="">
      </div>
    </big-swiper>
  </div>
</template>

<script>
import BigSwiper from './big-swiper';

export default {
  components: { BigSwiper },
  data() {
    return {
      show: false,
      list: [
        { image: require("../../../../assets/image/active/2021NewMain/s1.png") },
        { image: require("../../../../assets/image/active/2021NewMain/s2.png") },
        { image: require("../../../../assets/image/active/2021NewMain/s3.png") },
      ],
    }
  },
  mounted() {
  },
};
</script>

<style lang="less">
  .yz-school-swiper{
    margin-top: 0.1rem;
    .img-shadow{
      height: 100%;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border-radius: 0.05rem;
      img{
        width: 100%;
        height: 100%;
        // vertical-align: middle;
      }
    }
    .swiper-class{
      height: 1.66rem;
    }
    .swiper-slide-class{
      width: 82%;
      height: 1.16rem;
      margin-top: 0.23rem;
    }
    .swiper-slide-active{
      width: 2.9rem;
      height: 1.58rem;
      margin-top: 0.03rem;
    }
  }
</style>
