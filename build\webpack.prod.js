//# webpack 生产环境配置

let data = {
  "build:testing": "打包测试环境",
  "build:pre": "打包预发布环境",
  "build:production": "打包生产环境",
};
console.log(`--------------- ${data[process.env.NODE_ENV]} ---------------`);

const { merge } = require("webpack-merge");
const TerserPlugin = require("terser-webpack-plugin");
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");
const baseConfig = require("./webpack.base");

const webpackProdConfig = {
  mode: "production",
  // devtool: 'source-map',
  optimization: {
    moduleIds: "deterministic", // 长期缓存
    runtimeChunk: true,
    // 代码分割按需加载、提取公共代码
    splitChunks: {
      chunks: "all",
      minSize: 30000, // 构建出来的chunk大于30000才会被分割  单位字节 30000/1024＝29.296875KB
      minChunks: 1, // 引用了一次的进行分割
      maxInitialRequests: 6, // 入口点的最大并行请求数
      maxAsyncRequests: 6, // 按需加载时的最大并行请求数。
      cacheGroups: {
        vendors: {
          // node_modules里的代码
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          // filename: 'js/vendor-[contenthash].js',
          chunks: "all",
          minChunks: 2,
          priority: 10, // 优先级
          enforce: true,
        },
      },
    },
    minimizer: [
      new CssMinimizerPlugin(), // 压缩css
      "...", // 在 optimization.minimizer 中可以使用 '...' 来访问默认值
    ],
  },
  // cache: {
  //   type: 'filesystem', //开发环境下默认为memory类型，生产环境 cache 配置默认是关闭的不会生效。
  //   buildDependencies: {
  //     config: [__filename]
  //   }
  // }
};

if (process.env.NODE_ENV === "production") {
  // 删除console.log代码 注意要 要比 '...'选项前否则不会生效
  webpackProdConfig.optimization.minimizer.unshift(
    new TerserPlugin({
      exclude: /node_modules/,
      // 多进程
      parallel: true,
      terserOptions: {
        ecma: undefined,
        warnings: false,
        parse: {},
        compress: {
          drop_console: false,
          drop_debugger: true,
          pure_funcs: ["console.log"],
        },
      },
    })
  );
}

module.exports = merge(baseConfig, webpackProdConfig);
