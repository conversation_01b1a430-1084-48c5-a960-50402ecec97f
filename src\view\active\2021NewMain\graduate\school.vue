<template>
  <div class="yz-graduate-school">
    <!-- 学校 -->
    <ul class='tab-content'>
      <li class="MBALi" @click="getScoolList('MBA')">
        <p class="major">工商管理硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
      <li class="MEMLi" @click="getScoolList('MEM')">
        <p class="major">工程管理硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
      <li class="MPALi" @click="getScoolList('MPA')">
        <p class="major">公共管理硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
      <li class="JYGLI" @click="getScoolList('JYGL')">
        <p class="major">教育管理硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
      <li class="MPAcc" @click="getScoolList('MPAcc')">
        <p class="major">会计硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
      <li class="MTA" @click="getScoolList('MTA')">
        <p class="major">旅游管理硕士</p>
        <button class="lookSchool">查看院校 <img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
      </li>
    </ul>
    <!-- 专业弹窗 -->
    <van-popup round v-model="showPop" position="bottom"  class="graduatePop">
      <div class="head_box">
        <div class="line"></div>
        <p class="pfName">{{selectPsfnName}}</p>
        <img src="../../../../assets/image/active/enrollAggregate/graduate/close.png" @click="showPop = false">
      </div>
      <ul class="enrollSchoolUl">
        <li v-for="(item, index) in schoolList" :key='index' @click="toForm(item.unvsName,item.unvsId,item.unvsCode,item.pfsnId)">
          <img class="back" :src="'http://yzims.oss-cn-shenzhen.aliyuncs.com/university/backgroundForH5/'+ item.unvsCode+'.png'" alt />
          <img class="icon" :src="'http://yzims.oss-cn-shenzhen.aliyuncs.com/university/logo/'+ item.unvsCode+'.png'" alt />
          <p class="school_name">{{ item.unvsName }}</p>
          <button>报读<img src="../../../../assets/image/active/enrollAggregate/graduate/whiteArrow.png" alt=""></button>
        </li>
      </ul>
    </van-popup>
  </div>
</template>

<script>
import { isLogin, getIsAppOpen } from '@/common';
import { toAppLogin } from '@/common/jump';

export default {
  data() {
    return {
      schoolList: [],
      selectPsfnName: '',
      showPop: false,
      isLogin: isLogin(),
      isAppOpen: false,
      schoolInfo: {
        MBA: '工商管理硕士（MBA）',
        MEM: '工程管理硕士（MEM）',
        MPA: '公共管理硕士（MPA）',
        JYGL: '教育管理',
        MPAcc: '会计硕士（MPAcc）',
        MTA: '旅游管理硕士（MTA）'
      },
    };
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
  },
  methods: {
    getScoolList(id) {
      this.$http.post('/bds/getResearchUnvs/1.0/',{
        pfsnName: this.schoolInfo[id],
        pageSize: 99,
        pageNum: 0,
      }).then(res=>{
        const {code,body} = res;
        if (code !== '00') return;
        this.schoolList = body;
        this.$nextTick(()=>{
          this.selectPsfnName = id == 'JYGL'? '教育管理硕士（MED）' : this.schoolInfo[id];
          this.showPop = true;
        });
      });
    },
    toForm(unvsName, unvsId, unvsCode,pfsnId) {
      if (this.isAppOpen && !this.isLogin) {
        toAppLogin(); // 调起app登录
        return;
      }
      if(this.recruitType != 5) {
        const current = {unvsName,unvsId,unvsCode}
        this.$router.push({name:'graduateForm',query:{unvs:JSON.stringify(current), pfsnName:this.selectPsfnName,pfsnId}});
        return;
      }
      this.$router.push({name:'stuInfo'});
    },
  },
};
</script>

<style lang="less" scoped>
  .yz-graduate-school{
    padding-top: 0.1rem;
    background: #fff;
    .tab-content {
      width: 100%;
      text-align: center;
      padding: 0.1rem 0 0.15rem;
      li {
        position:relative;
        width: 3.45rem;
        height: 1.25rem;
        margin: 0 auto;
        box-shadow: 1px 3px 10px 0px rgba(0, 0, 0, 0.35);
        border-radius: 9px;
        .en-title {
          text-align:left;
          padding-left:.25rem;
          color: rgba(234, 52, 113, 1);
          font-size: 0.24rem;
          padding-top: .24rem;
        }
        .major {
          text-align:left;
          padding-left:.2rem;
          font-size:.17rem;
          color:#363636;
          padding-top: .4rem;
          font-weight: 600;
        }
        .lookSchool {
          width:.75rem;
          height: .25rem;
          font-size: .12rem;
          color:#fff;
          border-radius:12px;
          background: #E93470;
          position: absolute;
          left: .2rem;
          bottom: .26rem;
          img {
            width:5px;
            vertical-align: baseline;
          }
        }
      }
      li:not(:first-child) {
        margin-top: 0.15rem;
      }
      .MBALi{
        background:url('../../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
      .MEMLi {
        background:url('../../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
      .MPALi {
        background:url('../../../../assets/image/active/enrollAggregate/graduate/<EMAIL>') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
      .JYGLI {
        background:url('../../../../assets/image/active/enrollAggregate/graduate/bg4.png') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
      .MPAcc {
        background:url('../../../../assets/image/active/enrollAggregate/graduate/MPAcc.png') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
      .MTA {
        background:url('../../../../assets/image/active/enrollAggregate/graduate/MTA.png') no-repeat center;
        background-size: 3.54rem 1.35rem;
      }
    }
    .graduatePop {
      z-index: 10;
      max-height:80%;
      .head_box {
        line-height: .6rem;
        background: #fff;
        border-bottom:1px solid rgba(54, 54, 54, .15);
        position: sticky;
        top: 0;
        z-index: 10;
        .line {
          position: absolute;
          top: .2rem;
          width:.05rem;
          height: .2rem;
          background: #E93470;
        }
        .pfName {
          color: #363636;
          font-size: .18rem;
          font-weight: 600;
          padding-left: .1rem;
        }
        img {
          width: .12rem;
          position: absolute;
          top: .24rem;
          right: .2rem;
        }

      }
      .enrollSchoolUl {
        overflow: hidden;
        background: #fff;
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        flex-wrap: wrap;
        padding: .1rem .12rem;
        li {
          box-shadow: 0px 1px 5px 0px rgba(0, 0, 0, 0.35);
          width:100%;
          padding: .1rem;
          border-radius: 5px;
          margin-top: .15rem;
          display:flex;
          position: relative;
          align-items: center;
            .icon {
              width: .42rem;
              height: .42rem;
              z-index:2;
            }
            .back {
              position: absolute;
              top:0;
              left:0;
              width:100%;
              height:100%;
              z-index:1;
            }
            .school_name {
              font-size: .16rem;
              font-weight: 600;
              margin: 0 .1rem;
              z-index:2;
            }
            button {
              width: .55rem;
              height: .28rem;
              background: #E93470;
              border-radius:14px;
              color: #fff;
              margin-left: auto;
              flex-shrink:0;
              z-index:2;
              img {
                width: 4px;
                height:auto;
                margin-top: .05rem;
                margin-left:3px;
              }
          }
        }
      }
    }
  }
</style>
