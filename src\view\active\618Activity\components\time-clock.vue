<template>
  <div class="finish-time">
    <img src="../../../../assets/image/active/618Activity/clock.png" class="clock" alt="">
    <span class="t1">距活动结束时间</span>
    <span class="white" v-for="(item, index) in day" :class='{m1: index == 0}'>{{item}}</span>
    <!-- <span class="white">9</span> -->
    <span class="t2"> 天 </span>
    <span class="white" v-for="(item, index) in hour" :class='{m1: index == 0}'>{{item}}</span>
    <span class="t2"> 时 </span>
    <span class="white" v-for="(item, index) in min" :class='{m1: index == 0}'>{{item}}</span>
    <span class="t2"> 分 </span>
    <span class="white" v-for="(item, index) in second" :class='{m1: index == 0}'>{{item}}</span>
    <span class="t2"> 秒</span>
  </div>
</template>

<script>
import { transformSecond } from "@/common";

export default {
  props: {
    endTime: {
      type: [String, Number],
      default: '',
    }
  },
  data() {
    return {
      day: '00',
      hour: '00',
      min: '00',
      second: '00',
    };
  },
  watch: {
    endTime() {
      this.setTimeClock();
    },
  },
  mounted() {
    this.setTimeClock();
  },
  methods: {
    setTimeClock() {
      if (!this.endTime) {
        return;
      }
      const timer = setInterval(() => {
        const now = Date.now();
        const cha = (new Date(this.endTime).getTime() - now) / 1000;
        if (cha <= 0) {
          this.day = '00';
          this.hour = '00';
          this.min = '00';
          this.second = '00';
          clearInterval(timer);
          return;
        }
        const obj = transformSecond(parseInt(cha));
        this.day = this.tranfromNum(obj.day);
        this.hour = this.tranfromNum(obj.hour);
        this.min = this.tranfromNum(obj.min);
        this.second = this.tranfromNum(obj.second);
      }, 1000);
      
    },
    tranfromNum(num) {
      return num <= 9 ? `0${num}` : `${num}`;
    },
  },
};
</script>

<style lang="less" scoped>
  .finish-time{
    margin: 0 0.24rem;
    background: linear-gradient(180deg, #FFF2F2 0%, #FFEE86 100%);
    box-shadow: 0px 2px 4px 0px rgba(166, 19, 8, 0.4);
    display: flex;
    align-items: center;
    padding-left:0.1rem;
    color: #333333;
    line-height: 1;
    height: 0.36rem;
    border-radius: 0.05rem;
    font-size: 0.13rem;
    font-weight: 500;
    .t2{
      margin: 0 0.03rem;
    }
    .m1{
      margin-right: 1px;
    }
    .clock{
      width: 0.16rem;
      height: 0.16rem;
      margin: 0 0.05rem;
    }
    .white{
      display: inline-block;
      border-radius: 0.02rem;
      background: #fff;
      height: 0.24rem;
      line-height: 0.24rem;
      text-align: center;
      width: 0.14rem;
      color: #FA5C36;
      font-size: 0.16rem;
    }
  }
</style>
