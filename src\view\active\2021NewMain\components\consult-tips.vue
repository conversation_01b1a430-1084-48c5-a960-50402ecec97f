<template>
  <div class="yz-consult-tips" v-if='show' :style='{bottom: bottom, right: right}'>
    <img src="../../../../assets/image/active/2021NewMain/ic_close.png" class="close" @click='close' alt="">
    <p>您好！我是{{tipName}}，欢迎向我咨询～</p>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      defaule: false,
    },
    bottom: {
      type: String,
      default: '0.64rem',
    },
    right: {
      type: String,
      default: '0.33rem',
    },
    tipName: {
      type: String,
      default: '小Y客服',
    },
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    value(val) {
      this.show = val;
    },
  },
  mounted() {
    this.show = this.value;
  },
  methods: {
    close() {
      this.$emit('close');
      this.$emit('input', false);
    },
  },
};
</script>

<style lang="less" scoped>
  .yz-consult-tips{
    position: absolute;
    bottom: 0.64rem;
    right: 0.31rem;
    // background: rgba(0, 0, 0, 0.7);
    background: #4C4C4C;
    color: #fff;
    padding: 0.1rem;
    border-radius: .05rem;
    box-shadow: 0px 0px 8px 0px rgba(23, 6, 6, 0.15);
    font-size: 0.12rem;
    z-index: 7;
    .close{
      width: 0.24rem;
      height: 0.24rem;
      position: absolute;
      right: -0.12rem;
      top: -0.12rem;
    }
  }
</style>
