@import "./variable";
@import '../ysbh/ysbh.css';
@import '../moneyFont/index.css';
@import '../jxzk/index.css';
@import '../ruiFont/index.css';

*{ -webkit-box-sizing:border-box; box-sizing:border-box;  -webkit-text-size-adjust: none; -webkit-tap-highlight-color: rgba(0,0,0,0);}
html{ font-size:100px; font-size:calc(100vw / 3.75); font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif; }
body{
  -webkit-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  line-height:1.5;
  font-size:.14rem;
  color:#444;
  overflow-y:auto;
  // background-color:#f5f6f8;
  background-color:#F8F7F7;
  -webkit-overflow-scrolling:touch;
  // &::-webkit-scrollbar{width:6px;}
  // &::-webkit-scrollbar-thumb{border-radius:3px;background:#c1c1c1;}
}
html, body{ height:100%; }
.wraper{ position:relative; min-height:100vh; max-width:@maxWidth; margin:0 auto; overflow:hidden; }

// 公共
body, dd, dl, dt, form, h1, h2, h3, h4, h5, h6, html, li, ol, p, select, ul{ margin:0; padding:0 }
h1, h2, h3, h4, h5, h6{ font-weight:normal; font-size:.14rem; }
ul{list-style:none;}
a{ color:#444; text-decoration:none; }
a,img{ border:0; vertical-align:top; }
:focus, :active{ outline:0; -webkit-tap-highlight-color:rgba(0, 0, 0, 0); }
input{
  &::-webkit-input-placeholder{ color:#888; }
}
button,
input,
textarea{ padding:0; resize:none; font-family:TrebuchetMS, Rotobo, "Microsoft YaHei", sans-serif; vertical-align:top; -webkit-appearance:none; -moz-appearance:none; -webkit-border-image:none; border-image:none; -webkit-border-radius:0; border-radius:0; }
button,
input[type=button],
input[type=reset],
input[type=submit]{ cursor:pointer; -webkit-appearance:button }
a:active, button:active{opacity:.6;}
table{ border-collapse:collapse; border-spacing:0 }
td{ vertical-align:top }
.bc-w{ background-color:#fff; }
.img{ width:100%; height:100%; object-fit: contain; }
.icons{ display:inline-block; vertical-align:top; background-size:100% 100%; }
.mt{ margin-top:.08rem; }
.mt2 {margin-top:.04rem;}
.mt-10{ margin-top:.1rem; }
.mt-12{ margin-top:.12rem; }
.vam{ vertical-align:middle; }
.fl{ float:left; }
.fr{ float:right; }
.txt-r{ text-align:right; }
.txt-c{ text-align:center; }
.c-red{color:#e85b57;}
.c-blue{ color: #00AEEF;}
.rela{position: relative;}
.ab{position: absolute}
.cl,.clearfix{
  zoom:1;
  &:after,
  &:before{ content:""; display:table }
  &:after{ clear:both }
}
.hidden{ overflow:hidden; }

.row1{ overflow:hidden; white-space:nowrap; text-overflow:ellipsis; }
.row2{ display:-webkit-box; overflow:hidden; word-break:break-all; -webkit-box-orient:vertical; -webkit-line-clamp:2; }

// 按钮
.btn1{
  min-width:1rem; height:.33rem; line-height:.33rem; text-align:center; border:none; color:#fff; background-color:#f26662; background-image:-webkit-gradient(linear, left top, right bottom, from(#f86f6b), to(#ef425c)); background-image:linear-gradient(to bottom right, #f86f6b, #ef425c); border-radius:.02rem;
  &:disabled{ opacity:.6; }
}

.tab-nav{
  display:-webkit-box; display:flex; position:relative; line-height:.44rem;
  &:after{ .borderBottom }
  .item{
    display:block; -webkit-box-flex:1; flex:1; position:relative; z-index:1; text-align:center; color:#444;
    &.active{
      color:@color;
      &:after{ position:absolute; right:.18rem; bottom:0; left:.18rem; height:2px; background-color:@color; content:''; }
    }
  }
}

.footer{
  height:.5rem;
  .wrap{ position:fixed; right:0; bottom:0; left:0; z-index:2000; }
  .inner{
    position:relative; max-width:@maxWidth; height:.5rem; margin:0 auto; padding:0 .1rem; overflow:hidden; background-color:#fff;
    &.plr{ padding:0; }
    &:after{ .borderTop }
  }
}

// 子页面
.children-page{
  position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; overflow-y:auto; background-color:#fff; -webkit-overflow-scrolling:touch;
  // &::-webkit-scrollbar{width:6px;}
  // &::-webkit-scrollbar-thumb{border-radius:3px;background:#c1c1c1;}
  > .inner{ max-width:@maxWidth; margin:0 auto; padding:.12rem; }

  &.bg{
    background-color:#f5f6f8;
    > .inner{ padding:0; }
  }
}
.rich-text{
  word-wrap:break-word;
  img{ max-width:100%; height:auto; }
  table{ width:100%; }
}

// 暂无数据
.no-data{ padding:.6rem 0; text-align:center; color:#666; }

// mint-ui
.mint-toast{ z-index:10000 !important; }
.mint-indicator{ position:relative; z-index:10000; }
/*.mint-msgbox{ width:90%; font-size:.14rem; border-radius: .04rem; }
.mint-msgbox-content{
  padding:0; border-bottom:none;
  .gap1{ padding:.6rem 0 .5rem; }
}
.mint-msgbox-message{ color:#444; }
.mint-msgbox-btns{ height:auto; padding:.15rem .15rem .15rem 0; line-height:1; }
.mint-msgbox-btn{ height:.44rem; margin-left:.15rem; line-height:.44rem; color:#666; border:1px solid #efefef; border-radius:.02rem; }
.mint-msgbox-confirm{ color:#fff; border:none; background-image:linear-gradient(to right, #f86f6b, #ef425c); }*/

// element-ui
/*.el-message-box__wrapper{
  position:fixed; top:0; bottom:0; left:0; right:0; text-align:center; padding:0 .16rem;
  &:after{ display:inline-block; width:0; height:100%; vertical-align:middle; content:''; }
}
.el-message-box{ display:inline-block; width:100%; margin-top:-.4rem; vertical-align:middle; text-align:left; backface-visibility:hidden; background-color:#fff; border-radius:.04rem; }
.el-message-box__content{
  display:-webkit-box; display:flex; flex-direction:column; justify-content:center; min-height:1.4rem; padding:0 .1rem; text-align:center; font-size:.16rem;
  > div{ width:100%; flex-shrink:0; }
  .el-message-box__input{ padding:0 .15rem; }
  .el-input__inner{ width:100%; height:.4rem; padding:0 .1rem; color:#444; font-size:.14rem; border:1px solid #dfdfdf; border-radius:.02rem; }
}
.el-message-box__btns{
  display:-webkit-box; display:flex; padding-right:.15rem;
  .el-button{
    -webkit-box-flex:1; flex:1; height:.44rem; margin-left:.15rem; margin-bottom:.18rem; color:#666; font-size:.17rem; border:1px solid #efefef; background-color:#fff; border-radius:.02rem;
    &.el-button--primary{ color:#fff; border:none; background-image:linear-gradient(to right, #f86f6b, #ef425c); }
  }
}*/


@media only screen and (min-width:640px){
  html{ font-size:calc(640px / 3.75) !important; }
}
/*@supports not (font-size:calc(100vw / 3.75)){
  html{ font-size:100px; }
}*/
button {
  border: none;
}
