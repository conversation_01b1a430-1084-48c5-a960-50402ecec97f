<template>
  <van-popup v-model="show" :overlay="false">
    <div class="main">
      <van-nav-bar>
        <template #left>
          <van-icon class="icon" name="cross" @click="close" />
        </template>
        <template #right>
          <div
            class="btn"
            :class="{disabled : btnDisabeld}"
            @click="submit"
          >
            {{ isDayClock ? '打卡': '发布'}}
          </div>
        </template>
      </van-nav-bar>
      <div class="input-area">
        <van-field
          v-model="comment"
          class="textarea"
          rows="1"
          type="textarea"
          :autosize="{ maxHeight: 250, minHeight: 50 }"
          placeholder="本节课的内容、讲师等方面给你留下了怎样的印象？是否值得推荐给朋友听？留下10字以上的听课心得吧~"
        />
        <van-uploader
          class="upload"
          v-model="fileList"
          multiple
          :max-count="9"
          :max-size="10000 * 1024"
          :after-read="afterRead"
          @oversize="onOversize"
        >
          <template #default>
            <div class="upload-placeholder">
              <van-icon name="plus" />
            </div>
          </template>
        </van-uploader>
        <div class="book" v-if="bookInfo">
          <div class="book-img">
            <van-image
              class="book-img"
              :src="bookInfo.bookImg | imgOssURL"
            />
          </div>
          <div class="book-name">{{ bookInfo.bookName }}</div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Toast } from 'vant';
export default {
  components: {},
  data() {
    return {
      show: false,
      comment: '',
      fileList: [],
      bookInfo: null,// 书籍信息
      mappingId: '', // 套餐id,
      semesterId: '', // 学期id
      readPlanId: '', // 读书计划id
      arrangeId: '', //
      bookName: '',
      isDayClock: false, // 是否今天打卡
    };
  },
  computed: {
    btnDisabeld() {
      if (this.comment.trim().length <= 9) {
        return true
      } else {
        return false;
      }
    }
  },
  methods: {
    open() {
      this.show = true;
      this.searchBook();
    },
    close() {
      this.show = false;
      // 重置数据
      Object.assign(this.$data, this.$options.data());
    },
    onOversize() {
      Toast('文件大小不能超过 10M！');
    },
    // 上传文件
    afterRead(files) {
      // 多张同时上传
      if (Array.isArray(files)) {
        for (let i = 0; i< files.length; i++) {
         let item = files[i];
         this.uploadFile(item);
        }
      } else {
        // 单张
        this.uploadFile(files);
      }
      console.log(this.fileList,'fileList')
    },
    uploadFile(fileObj) {
      fileObj.status = 'uploading';
      fileObj.message = '上传中...';
      let formData = new FormData();
      formData.append('file', fileObj.file)
      formData.append('name', fileObj.file.name);
      this.$http.post('/uploadFileNew', formData)
        .then(res => {
          const { code, body } = res;
          if (code === '00') {
            fileObj.status = 'done';
            fileObj.message = '上传完成';
            fileObj.url = body;
          }
        })
    },
    // 发布前验证
    submitVerify() {
      let isPass = true;
      for (let i = 0; i < this.fileList.length; i++) {
        let file = this.fileList[i];
        if (file.status !== 'done') {
          isPass = false;
          Toast('图片上传中，请稍后！');
        }
      }

      if (this.btnDisabeld) {
        isPass = false;
        Toast('需要写满10字以上的心得评论喔😲')
      }

      return isPass;
    },
    // 发布
    submit() {
      // 校检
      if (this.submitVerify()) {
        // 书本信息
        let extContent = null;
        if (this.bookInfo) {
          extContent = {
            bookName: this.bookInfo.bookName,
            bookUrl: this.bookInfo.bookImg,
            readPersonNum:  this.bookInfo.readPersonNum
          }
          extContent = JSON.stringify(extContent);
        }

        // 拼接图片
        let picture = '';
        this.fileList.forEach(item => {
          picture += item.url + ','
        });
        picture = picture.substring(0, picture.length - 1);

        // 发布评论
        let params = {
          mappingId: this.mappingId, // 套餐id
          comment: this.comment, // 内容
          synCircleStatus: true, // 打卡记录同步到app圈子
          type: 'readPlan',
          picture: picture,
          readPlanId: this.readPlanId,
          arrangeId: this.arrangeId, // 读书计划安排id
          semesterId: this.semesterId, //学期id
          bookId: this.bookInfo && this.bookInfo.id, // 圈子书籍id
          extContent: extContent
        }

        this.$http.post('/pu/userExperienceComment/1.0/', params)
          .then(res => {
            const { code } = res;
            if (code === '00') {
              // 符合今日章节，且打卡需求
              if (this.isDayClock) {
                this.$parent.getClockDay(); // 更新打卡数
                this.$parent.calendarPopup = true;
              }
              this.$parent.refresh(); // 刷新
              this.close();
            }
          })
      }
    },
    searchBook() {
      let params = {
        type: 9,
        keyWords: this.bookName, //书本关键字
        pageNum: 1,
        pageSize: 1,
      };

      this.$http.post('/search/getSearchResultList/1.0/', params)
        .then(res => {
          const { code, body } = res;
          if (code === '00') {
            if (Array.isArray(body)) {
              if (body.length > 0) {
                this.bookInfo = body[0];
              }
            }
          }
        })
    },
  },
  beforeDestroy() {}
};
</script>

<style lang='scss' scoped>
.main {
  width: 3.75rem;
  min-height: 100vh;
  background: #fff;

  .btn {
    width: .5rem;
    height: .3rem;
    line-height: .3rem;
    background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
    border-radius: .05rem;
    font-weight: 600;
    color: #FFFFFF;
  }

  .disabled {
    background: #B9B4B4;
  }


  .icon {
    font-size: .18rem;
    color: #333333;
  }

  .input-area {
    padding: 0 .16rem;
  }

  .textarea {
    font-size: .14rem;
    padding: .1rem 0;

    &:after {
      border: none;
    }
  }

  .upload {
    padding-top: .1rem;

    .upload-placeholder {
      width: 1.1rem;
      height: 1.1rem;
      background: #F7F7F7;
      border-radius: .05rem;
      text-align: center;
      display: inline-flex;
      justify-content: center;
      align-items: center;

      i {
        color: #333333;
        font-size: .16rem;
        font-weight: 600;
      }
    }

    /deep/ .van-uploader__preview {
      border-radius: .05rem;
      overflow: hidden;
      margin: 0 .02rem;
    }

    /deep/ .van-uploader__preview-image {
      width: 1.1rem;
      height: 1.1rem;
    }
  }

  .book {
    width: 100%;
    height: .94rem;
    margin-top: .24rem;
    background: #FFFFFF;
    box-shadow: 0 .04rem .2rem 0 rgba(23, 6, 6, 0.1);
    border-radius: .06rem;
    display: flex;
    padding: .12rem .15rem;

    .book-img {
      width: .48rem;
      height: .68rem;
    }

    .book-name {
      margin-left: .16rem;
    }

  }


}
</style>
