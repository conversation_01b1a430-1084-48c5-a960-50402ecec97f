<template>
  <div>
    <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50" class="mt17">
      <div v-for="(item, index) in list" class="grid-li">
        <!-- <p class="task-push-time" v-if="item.finish_time&&item.taskStatus==='1'"> {{item.finish_time | timestampFormat}}</p> -->
        <p class="task-push-time" v-if="item.create_time && taskStatus === '0'"> {{ item.create_time }}</p>
        <div class="myOrder-item mt" :class="item.taskType | taskClass" :key="index">
          <img v-if="item.isSticky == '1'" src="../../assets/image/top-img.png" class="g-bg">
          <img v-if="item.taskStatus === '0' && item.isRead == '0'" src="../../assets/image/no-read.png"
            class="no-read-bg">
          <div class="myOrder-item-t" :class="{ 'pt12': item.isSticky == '1' }">
            <p class="m-i-nav-p">
              <span v-if="taskStatus != '2'" class="s-t bs-c">
                {{ item.taskType | taskType }}</span>
              <span v-else class="dis-ac bs-c">
                {{ item.invalidType }}</span>
              <span>{{ item.taskTitle }}</span>
            </p>
            <p v-if="item.taskContent" class="task-content">{{ item.taskContent }}</p>
          </div>
          <div class="myOrder-item-b cl">
            <div class="fl">
              <p class="grid-flex-b">
                <img src="../../assets/image/student/myTask/start.png" alt="" class="time-icon">
                <span>{{ item.startTime | formatDate('yyyy年MM月dd日 hh:mm:ss') }}</span>
              </p>
              <p class="grid-flex-b">
                <img src="../../assets/image/student/myTask/end.png" alt="" class="time-icon">
                <span>{{ item.endTime | formatDate('yyyy年MM月dd日 hh:mm:ss') }}</span>
              </p>
              <p>
                <span>客服热线：</span>
                <span>4008336013</span>
              </p>
            </div>
            <template>
              <div class="fr" v-if="item.taskType === '-1'">
                <template v-if="item.taskTemplateType == '1'">
                  <router-link v-if="item.taskStatus == '0'"
                    :to="{ name: 'noticeConfirm', query: { taskId: item.taskId, learnId: item.learnId, taskStatus: item.taskStatus, recruitType: recruitType2 } }">
                    去完成</router-link>
                  <router-link v-if="item.taskStatus == '1'"
                    :to="{ name: 'noticeConfirm', query: { taskId: item.taskId, learnId: item.learnId, taskStatus: item.taskStatus } }">
                    去查看</router-link>
                </template>
                <template v-else-if="item.taskTemplateType == '2'">
                  <router-link v-if="item.taskStatus == '0'"
                    :to="{ name: 'informationCheck', query: { taskId: item.taskId, learnId: item.learnId, taskStatus: item.taskStatus } }">
                    去完成</router-link>
                  <router-link v-if="item.taskStatus == '1'"
                    :to="{ name: 'informationCheck', query: { taskId: item.taskId, learnId: item.learnId, taskStatus: item.taskStatus } }">
                    去查看</router-link>
                </template>
                <template v-else-if="item.taskTemplateType == '3'">
                  <router-link v-if="item.taskStatus == '0'"
                    :to="{ name: 'certificatesCollect', query: { taskId: item.taskId, learnId: item.learnId } }">
                    去完成</router-link>
                  <router-link v-if="item.taskStatus == '1'"
                    :to="{ name: 'certificatesCollect', query: { taskId: item.taskId, learnId: item.learnId } }">
                    去查看</router-link>
                </template>
                <template v-else-if="item.taskTemplateType == '4'">
                  <span v-if="item.taskStatus == '0'" @click="question(item)">去做答</span>
                  <span v-if="item.taskStatus == '1'" @click="question(item)">去查看</span>
                  <!-- <router-link v-if="item.taskStatus=='0'" :to="{name:'questionlist',query:{taskId:item.taskId,learnId:item.learnId,taskTitle:item.taskTitle}}">去做答</router-link>
                <router-link v-if="item.taskStatus=='1'" :to="{name:'questionlist',query:{taskId:item.taskId,learnId:item.learnId,taskTitle:item.taskTitle}}">去查看</router-link>-->
                </template>
                <!-- 自考延长服务提醒 -->
                <template v-else-if="item.taskTemplateType == '5'">
                  <router-link :to="item.taskToUrl">{{ item.mActionString }}</router-link>
                </template>
                <template v-else-if="item.taskTemplateType == '6'">
                  <router-link v-if="item.taskStatus == '0'"
                    :to="{ name: 'materialDetail', query: { taskId: item.taskId, learnId: item.learnId } }">
                    去完成</router-link>
                  <router-link v-if="item.taskStatus == '1'"
                    :to="{ name: 'materialDetail', query: { taskId: item.taskId, learnId: item.learnId } }">
                    去查看</router-link>
                </template>
              </div>
              <div class="fr" v-if="item.taskType === '1'">
                <span v-if="item.taskStatus == '0'" @click="updateTaskStatus(item.taskId, item.learnId)">知道啦</span>
              </div>
              <div class="fr" v-else-if="item.taskType === '2'">
                <template v-if="item.taskId != 155851804431666483">
                  <span v-if="item.taskStatus == '0'"
                    @click="updateTaskStatus(item.taskId, item.learnId, item.taskUrl)">去完成</span>
                  <span v-if="item.taskStatus == '1'" @click="taskJump(item.taskUrl, item.learnId)">去查看</span>
                </template>
                <template v-else>
                  <span v-if="item.taskStatus == '0'" @click="taskJump(item.taskUrl, item.learnId)">开始测试</span>
                  <span v-if="item.taskStatus == '1'" @click="taskJump(item.taskUrl, item.learnId)">查看结果</span>
                </template>
              </div>
              <div class="fr" v-else-if="item.taskType === '3'">
                <div class="check" v-if="item.taskStatus == '0' && item.isTaskOverdue == '1'"
                  @click="expireTips(item.endTime)">已过期
                </div>
                <router-link v-else-if="item.taskStatus == '0'"
                  :to="{ name: 'selectAddressTask', query: { taskId: item.taskId, eyId: item.eyId } }">选择地址
                </router-link>
                <div class="check" v-if="item.taskStatus == '1'" @click="ViewAddress(item.taskId)">查看地址</div>
              </div>
              <div class="fr" v-else-if="item.taskType === '4'">
                <span v-if="item.taskStatus == '0'" @click="toChooseExamRoom(item)">选择考场</span>
                <router-link
                  :to="{ name: 'confirmExamRInfo', query: { learnId: item.learnId, taskId: item.taskId, eyId: item.eyId, status: 'disabled' } }"
                  v-else>查看考场</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '5'">
                <template v-if="item.eyId.indexOf('2019春【国开现场') == -1">
                  <router-link v-if="item.taskStatus == '0'"
                    :to="{ name: 'examRoomInfo', query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId } }">
                    去完成</router-link>
                  <router-link v-if="item.taskStatus == '1'"
                    :to="{ name: 'examRoomInfo', query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, status: '0' } }">
                    去查看</router-link>
                </template>
                <template v-else>
                  <span v-if="item.taskStatus == '0'"
                    @click="taskJump1(item.taskId, item.learnId, item.eyId)">去完成</span>
                  <span v-if="item.taskStatus == '1'"
                    @click="taskJump1(item.taskId, item.learnId, item.eyId, '0')">去查看</span>
                </template>
              </div>
              <div class="fr" v-else-if="item.taskType === '6'">
                <span v-if="item.taskStatus == '0'"
                  @click="updateTaskStatus(item.taskId, item.learnId, { name: 'submitGraduateData', query: { taskId: item.taskId, learnId: item.learnId, isApply: item.ifApply } })">去完成</span>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'submitGraduateData', query: { taskId: item.taskId, learnId: item.learnId, isApply: item.ifApply } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '7'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'isInfoCheck', query: { taskId: item.taskId, learnId: item.learnId } }">去完成
                </router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'isInfoCheck', query: { taskId: item.taskId, learnId: item.learnId, status: '0' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '8'">
                <template v-if="item.taskStatus == '0'">
                  <router-link
                    :to="{ name: 'BachelorEnglish', query: { taskId: item.taskId, learnId: item.learnId } }">去查看
                  </router-link>
                </template>
                <template v-if="item.taskStatus == '1'">
                  <router-link v-if="item.isEnroll == '0'"
                    :to="{ name: 'BachelorEnglish', query: { taskId: item.taskId, learnId: item.learnId } }">去查看
                  </router-link>
                  <router-link v-if="item.isEnroll == '1'"
                    :to="{ name: 'BachelorResult', query: { taskId: item.taskId, learnId: item.learnId, sign: '1', enrollNo: item.enrollNo } }">
                    已报名</router-link>
                  <router-link v-if="item.isEnroll == '2'"
                    :to="{ name: 'BachelorResult', query: { taskId: item.taskId, learnId: item.learnId, sign: '0' } }">
                    不报名</router-link>
                </template>
              </div>
              <div class="fr" v-else-if="item.taskType === '9'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'thesisNotice', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, taskType: item.taskType, eyId: item.eyId } }">
                  去完成</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'thesisNotice', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, taskType: item.taskType, eyId: item.eyId } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '10' || item.taskType == 27">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'courseInform', query: { taskId: item.taskId, learnId: item.learnId } }">去查看
                </router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'courseInform', query: { taskId: item.taskId, learnId: item.learnId, status: '1' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '11'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'newbornData', query: { taskId: item.taskId, learnId: item.learnId, taskType: item.taskType, eyId: item.eyId } }">
                  去查看</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'newbornData', query: { taskId: item.taskId, learnId: item.learnId, taskType: item.taskType, eyId: item.eyId, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '12'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'qingshuGuide', query: { taskId: item.taskId, learnId: item.learnId } }">去完成
                </router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'qingshuGuide', query: { taskId: item.taskId, learnId: item.learnId, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '13'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'TestSite', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle } }">
                  选择城市</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'TestSite', query: { taskId: item.taskId, learnId: item.learnId, status: '1', taskTitle: item.taskTitle } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '14'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'bkExamSign', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, eyId: item.eyId } }">
                  去完成</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'bkExamSign', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, eyId: item.eyId } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '15'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'diplomaReceive', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, recruitType: recruitType2, eyId: item.eyId, fileHostLink: item.fileHostLink, isAllowFileHost: item.isAllowFileHost } }">
                  去完成</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'diplomaReceive', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '16'">
                <router-link v-if="item.taskStatus == '0' && nowTime < formDate(item.endTime)"
                  :to="{ name: 'examConfirm', query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, taskTitle: item.taskTitle } }">
                  去缴费</router-link>

                <span v-if="item.taskStatus == '0' && nowTime > formDate(item.endTime)"
                  @click="toExamConfirm(item)">未完成</span>

                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'examConfirm', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '17'">
                <span v-if="item.taskStatus == '0'" @click="toSpotInvite(item)">去预约</span>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'spotInvite', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '18'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'informationCheck', query: { learnId: item.learnId, taskId: item.taskId, taskStatus: item.taskStatus } }">
                  去完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '19'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'receivingGoodsConfirm', query: { learnId: item.learnId, taskId: item.taskId, taskStatus: item.taskStatus } }">
                  去完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '20'">
                <span @click="openacademicDegree(item)">{{ item.taskStatus == 0 ? '去完成' : '已完成' }}</span>
                <!-- <router-link v-if="item.taskStatus=='0'" :to="{name:'openacademicDegree',query:{learnId:item.learnId,taskId:item.taskId,taskStatus:item.taskStatus}}">去完成</router-link> -->
              </div>
              <div class="fr" v-else-if="item.taskType === '21'">
                <router-link :to="{ name: 'guokaiExam', query: { learnId: item.learnId, taskId: item.taskId } }">去预约
                </router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '22'">
                <router-link :to="item.taskToUrl">去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '23'">
                <router-link
                  :to="{ name: 'schooldata', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, eyId: item.eyId, checkStatus: item.checkStatus, eyId: item.eyId } }">
                  {{ item.checkStatus == 0 ? "去完成" : item.checkStatus == 1 ? "审核中" : item.checkStatus == 2 ? "已驳回" :
      "已通过" }}
                </router-link>
                <!-- <router-link v-if="item.taskStatus=='1'" :to="{name:'schooldata',query:{taskId:item.taskId,learnId:item.learnId,taskTitle:item.taskTitle,eyId:item.eyId}}">去查看</router-link> -->
              </div>
              <div class="fr" v-else-if="item.taskType === '24'">
                <router-link v-if="item.taskStatus == '0'"
                  :to="{ name: 'eduCompleteMaterial', query: { taskId: item.taskId, learnId: item.learnId } }">
                  去完成</router-link>
                <router-link v-if="item.taskStatus == '1'"
                  :to="{ name: 'eduCompleteMaterial', query: { taskId: item.taskId, learnId: item.learnId } }">去查看
                </router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '26'">
                <a :class='{ blue: item.checkStatus == 4 }' @click='taskType26Click(item)'>{{ item.checkStatus |
      taskType26Text }}</a>
                <!-- <router-link v-if="item.taskStatus=='1'" :to="{name:'schooldata',query:{taskId:item.taskId,learnId:item.learnId,taskTitle:item.taskTitle,eyId:item.eyId}}">去查看</router-link> -->
              </div>
            </template>
            <template v-if="taskStatus === '33333'">
              <div class="fr" v-if="item.taskType === '2'">
                <span @click="taskJump(item.taskUrl, item.learnId)">去查看</span>
              </div>
              <div class="fr" v-else-if="item.taskType === '4'">
                <router-link
                  :to="{ name: 'confirmExamRInfo', query: { learnId: item.learnId, taskId: item.taskId, eyId: item.eyId, status: 'disabled' } }">
                  查看考场</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '5'">
                <router-link
                  :to="{ name: 'examRoomInfo', query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, status: '0' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '6'">
                <router-link
                  :to="{ name: 'submitGraduateData', query: { taskId: item.taskId, learnId: item.learnId, isApply: item.ifApply } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '7'">
                <router-link
                  :to="{ name: 'isInfoCheck', query: { taskId: item.taskId, learnId: item.learnId, status: '0' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '8'">
                <router-link v-if="item.isEnroll == '0'"
                  :to="{ name: 'BachelorEnglish', query: { taskId: item.taskId, learnId: item.learnId } }">去查看
                </router-link>
                <router-link v-if="item.isEnroll == '1'"
                  :to="{ name: 'BachelorResult', query: { taskId: item.taskId, learnId: item.learnId, sign: '1', enrollNo: item.enrollNo } }">
                  已报名</router-link>
                <router-link v-if="item.isEnroll == '2'"
                  :to="{ name: 'BachelorResult', query: { taskId: item.taskId, learnId: item.learnId, sign: '0' } }">
                  不报名</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '9'">
                <router-link
                  :to="{ name: 'thesisNotice', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '10' || item.taskType == 27">
                <router-link
                  :to="{ name: 'courseInform', query: { taskId: item.taskId, learnId: item.learnId, status: '1' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '11'">
                <router-link
                  :to="{ name: 'newbornData', query: { taskId: item.taskId, learnId: item.learnId, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '12'">
                <router-link
                  :to="{ name: 'qingshuGuide', query: { taskId: item.taskId, learnId: item.learnId, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '13'">
                <router-link
                  :to="{ name: 'TestSite', query: { taskId: item.taskId, learnId: item.learnId, status: '1', taskTitle: item.taskTitle } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '14'">
                <router-link
                  :to="{ name: 'bkExamSign', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, eyId: item.eyId } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '15'">
                <router-link
                  :to="{ name: 'diplomaReceive', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  去查看</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '16'">
                <router-link
                  :to="{ name: 'examConfirm', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  已完成</router-link>
              </div>
              <div class="fr" v-else-if="item.taskType === '17'">
                <router-link
                  :to="{ name: 'spotInvite', query: { taskId: item.taskId, learnId: item.learnId, taskTitle: item.taskTitle, status: '1' } }">
                  去查看</router-link>
              </div>
            </template>
          </div>
        </div>
      </div>
      <load-bar :isLoading="isLoading" :allLoaded="allLoaded" v-if="taskStatus === '0'"></load-bar>
      <van-popup v-model="show" round :style="{ minHeight: '2.08rem' }">
        <div class="information">
          <p class="dialog-text">你选择的教材收货信息是:</p>
          <div class="grey">
            {{ selectedAddress.saName }} {{ selectedAddress.mobile }}
            <br />
            {{ selectedAddress.provinceName }}{{ selectedAddress.cityName }}{{ selectedAddress.districtName }}{{
      selectedAddress.streetName }}
            {{ selectedAddress.address }}
          </div>
        </div>
        <div class="btn">
          <a href="tel:4008336013">联系客服</a>
          <p @click="show = false; selectedAddress = {}">确认</p>
        </div>
      </van-popup>
    </div>
    <img class="no-data-img" v-if="isNoDate" src="../../assets/image/bg-nodata.png" />
    <div class="no-data" v-if="isNoDate">暂无记录~</div>
  </div>
</template>

<script>
import loadBar from '@/components/loadBar';
import { Dialog, Toast } from 'vant';
export default {
  props: ['taskStatus', 'active'],
  data() {
    return {
      learnId: this.storage.getItem('learnId'),
      pageNum: 0,
      pageSize: 100,
      list: [],
      isLoading: true,
      allLoaded: false,
      nowTime: '',
      show: false,
      selectedAddress: {},
      grade: "",
      recruitType: "",
      recruitType2: ''
    }
  },
  computed: {
    isNoDate: function () {
      let noDate = false;
      if (this.list && this.list.length === 0) {
        noDate = true;
      }
      return noDate;
    }
  },
  filters: {
    taskType26Text(val) {
      if (!val) {
        return "";
      }
      const text = {
        0: '未完成',
        1: '审核中',
        2: '已驳回',
        3: '已完成',
        4: '反馈中'
      };
      return text[val];
    },
    taskType(code) {
      const obj = { '3': '地址确认', '4': '考场确认', '5': '考场确认', '13': '考点确认', '7': '跳转', '20': '跳转', '8': '报名通知', '16': '考试费缴费', '17': '现场确认' };
      return obj[code] || '通知';
    }
  },
  created() {
    this.recruitType2 = this.$route.query.recruitType
    // const learnId = this.$route.query.learnId;
    this.recruitType = this.storage.getItem('recruitType') || '';
    this.grade = this.storage.getItem('grade') || '';
    this.getSystemDateTime();
    // if (!!learnId) {
    //   this.learnId = learnId;
    //   this.storage.setItem('learnId', learnId);
    // }
    this.loadMore();
    // this.getAddress();
  },
  methods: {
    expireTips(date) {
      Dialog.alert({
        message: `已超过任务截止时间${date}，地址如未填写，请联系客服进行处理`,
        confirmButtonText: '联系客服',
        cancelButtonText: '确定',
        showCancelButton: true,
        confirmButtonColor: 'red',
        cancelButtonColor: '#000'
      }).then(() => {
        window.location.href = "tel:4008336013";
      })
    },
    openacademicDegree(item) {
      this.$router.push({
        name: 'openacademicDegree', query: { learnId: item.learnId, taskId: item.taskId, taskStatus: item.taskStatus }
      })
    },
    ViewAddress(val) {
      this.show = true;
      if (!val) return
      this.getAddress(val)
    },
    //获取已提交的教材地址
    getAddress(el) {
      this.$http.post('/us/getAddressByTaskId/1.0/', { taskId: el }).then(res => {
        if (!res.body) return
        this.selectedAddress = res.body;
      })
    },
    // 获取任务列表
    getMyTasks: function (type) {
      this.isLoading = true;
      let data = {
        learnId: this.learnId,
        taskStatus: this.taskStatus == '2' ? '' : this.taskStatus,
        tabType: this.taskStatus,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      this.$http.post('/mkt/myTasks/1.0/', data).then(res => {
        if (res.code !== '00') return;
        let datas = res.body || {};
        if (type === 'reload') {
          this.list = datas.list || [];
        } else {
          this.list.push(...(datas.list || []));
        }

        this.$emit('update:listDate', this.list);

        this.allLoaded = true;
        this.isLoading = true;

      });
    },
    formDate(time) {
      return new Date(time.replace(/-/g, '/')).getTime()
    },

    loadMore: function () {
      if (!this.active) return;
      this.pageNum++;
      this.getMyTasks();
    },
    reload: function () {
      this.pageNum = 1;
      this.getMyTasks('reload');
    },
    // 更新任务状态
    updateTaskStatus: function (taskId, learnId, to) {
      this.$http.post('/mkt/updateTaskStatus/1.0/', { taskId: taskId, learnId: learnId }).then(res => {
        if (res.code !== '00') return;

        if ('string' === typeof to) {
          this.taskJump(to, learnId);
        } else if ('object' === typeof to) {
          this.$router.push(to);
        }
        this.$emit('reload');
        this.reload()
      });
    },
    // 跳转任务
    taskJump: function (url, learnId) {
      if (this.$route.query.recruitType && url.indexOf('student/mytask/payInfoPage')!=-1) {
        Toast({
          type: 'html',
          message: '<div>需前往【远智公众号-学堂-我的任务】完成</div><div>即将自动跳转</div>'
        });
        setTimeout(() => {
          window.location.href = 'https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=MzU4NDY5NDI4Ng==#wechat_redirect'

        }, 3000);
      } else {
        if (url) {
          const origin = window.location.origin;
          if (url.startsWith(origin)) {
            this.$router.push({ path: url.replace(origin, ''), query: { learnId } });
          } else {
            window.location.href = url;
          }
        }
      }
    },
    taskJump1: function (taskId, learnId, eyId, status) {
      if (status == '0') {
        this.$router.push({ name: 'examRoomInfo', query: { taskId: taskId, learnId: learnId, eyId: eyId, status: '0' } })
      } else {
        const datas = { taskId: taskId, learnId: learnId, eyId: eyId, isRead: 1 }
        this.$http.post('/mkt/updateStudentExamGkIsRead/1.0/', datas).then(res => {
          if (res.code === '00') {
            // this.$modal({
            //   message: '确认成功，祝您考试顺利！',
            //   beforeClose: (action, instance, done) => {
            //     done();
            //     this.$router.replace({name: 'myTask', query: {active: 'completed'}});
            //   }
            // });
            this.$router.push({ name: 'examRoomInfo', query: { taskId: taskId, learnId: learnId, eyId: eyId } })
          }
        });
      }
    },
    question(item) {
      // if(this.nowTime >new Date(item.endTime).getTime()){
      //       this.$modal({message: '当前任务已经过期，无法操作。如有疑问，请致电远智教育热线：4008336013。', icon: 'warning'});
      //     return;
      // }
      // if(this.nowTime < new Date(item.startTime).getTime()){
      //       this.$modal({message: '当前任务还没开始，无法操作。如有疑问，请致电远智教育热线：4008336013。', icon: 'warning'});
      //     return;
      // }
      this.$router.push({
        name: 'questionlist',
        query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, taskTitle: item.taskTitle, taskStatus: item.taskStatus, recruitType: this.$route.query.recruitType }
      });
    },
    // 去选择考场
    toChooseExamRoom: function (item) {
      if (item.taskType === '4') {
        if (item.curTime > new Date(item.endTime).getTime()) {
          this.$modal({ message: '当前任务已经过期，无法操作。如有疑问，请致电远智教育热线：4008336013。', icon: 'warning' });
          return;
        }
        if (item.ifTeacherOper === 'Y') {
          this.$modal({ message: '无法确认考场，如有疑问，请联系班主任！', icon: 'warning' });
          return;
        }
      }

      this.$router.push({
        name: 'examRChoose',
        query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, recruitType: this.recruitType2 }
      });
    },
    // 报考信息确认
    toExamConfirm: function (item) {
      if (this.nowTime > item.endTime) {
        this.$modal({ message: '当前任务已经过期，无法操作。如有疑问，请致电远智教育热线：4008336013。', icon: 'warning' });
        return;
      }
      // this.$router.push({
      //   name: 'examConfirm',
      //   query: {taskId: item.taskId, learnId: item.learnId, eyId: item.eyId,taskTitle:item.taskTitle}
      // });
    },
    // 去预约现场确认
    toSpotInvite: function (item) {
      if (this.nowTime > item.endTime) {
        this.$modal({ message: '当前任务已经过期，无法操作。如有疑问，请致电远智教育热线：4008336013。', icon: 'warning' });
        return;
      }
      this.$router.push({
        name: 'spotInvite',
        query: { taskId: item.taskId, learnId: item.learnId, eyId: item.eyId, taskTitle: item.taskTitle }
      });
    },
    // 获取服务器时间，判断任务是否已结束
    getSystemDateTime: function () {
      // this.$http.post('/mkt/getSystemDateTime/1.0/').then(res => {
      //   if (res.code === '00') {
      //     const now = new Date(res.body.systemDateTime.replace(/-/g, '/')).getTime();
      this.nowTime = Date.now();

      //   }
      // });
    },
    taskType26Click(item) {
      switch (Number(item.checkStatus)) {
        case 0:
        case 2:
          this.$router.push({
            name: 'schooldata2',
            query: {
              taskId: item.taskId,
              learnId: item.learnId,
              checkStatus: item.checkStatus,
              taskTitle: item.taskTitle,
              eyId: item.eyId,
            },
          });
          break;
        case 1:
          Toast('您的信息审核中，请耐心等待');
          break;
        case 3:
          this.$router.push({
            name: 'schooldata.finish',
            query: {
              taskId: item.taskId,
              learnId: item.learnId,
            },
          });
          break;
        case 4:
          Toast('您的信息调整中，请隔段时间再来处理');
          break;

        default:
          break;
      }
    },
  },
  watch: {
    active: function () {
      this.isLoading = !(this.active && !this.allLoaded);
    }
  },
  components: { loadBar }
}
</script>

<style lang="less" scoped>
@import '../../assets/less/variable';

.mt17 {
  margin-top: 0.17rem;
}

.task-push-time {
  text-align: center;
  font-size: 0.12rem;
  font-family: PingFangSC-Light, PingFang SC;
  font-weight: 300;
  color: #A29B9B;
  margin-top: 0.04rem;
}

.no-data {
  margin-top: .1rem;
  padding-top: 0;
}

.no-data-img {
  width: 1.33rem;
  margin-left: 33%;
  margin-top: 1.6rem;
}

.myOrder-item {
  padding: 0.14rem 0.12rem 0.12rem;
  background-color: #fff;
  margin: 0.04rem 0.21rem;
  position: relative;
  border-radius: 0.05rem;

  .pt12 {
    padding-top: 0.12rem;
  }

  &.router {
    .myOrder-item-t {
      p {
        span {
          &:first-child {
            color: #69bcf3;
            border: 1px solid #69bcf3;
            background: #fff;
          }
        }
      }

      .task-content {
        background-color: #f8f7f7;
        padding: 0.1rem;
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
        opacity: 0.8;
        line-height: 0.2rem;
      }
    }
  }

  &.chose {
    .myOrder-item-t {
      p {
        span {
          &:first-child {
            color: #aed98c;
            border: 1px solid #aed98c;
            background: #fff;
          }
        }
      }
    }
  }

  .g-bg {
    width: 0.41rem;
    position: absolute;
    top: 0;
    left: 0;
  }

  .no-read-bg {
    width: 0.24rem;
    position: absolute;
    top: 0;
    right: 0.2rem;
  }
}

.myOrder-item-t {
  position: relative;

  &:after {
    .borderBottom;
  }

  p {
    padding-bottom: 0.06rem;
    font-size: 0.12rem;
    color: #888;

    span {
      color: #444;
      font-size: 0.15rem;
      font-weight: 600;
      color: #170606;
      line-height: 0.21rem;
      // &:first-child {
      //   padding: 0 0.02rem;
      //   vertical-align: text-bottom;
      //   color: #f67470;
      //   font-size: 0.12rem;
      //   border: 1px solid #f67470;
      //   border-radius: 0.02rem;
      // }
    }
  }

  .m-i-nav-p {
    .bs-c {
      padding: 0.03rem 0.05rem;
      border-radius: 0.02rem;
      font-size: 0.13rem;
      vertical-align: bottom;
      font-weight: 400;
      margin-right: 0.05rem;
    }

    .s-t {
      color: #EA5A59;
      border-radius: 0.02rem;
      border: 1px solid #F06E6C;
      // background: #FEE4E3;
    }

    .dis-ac {
      color: #666666 !important;
      border: 1px solid #666666 !important;
    }
  }

}

.myOrder-item-b {
  position: relative;

  .grid-flex-b {
    display: flex;
    align-items: center;
  }

  .fl {
    p {
      padding-top: 0.04rem;

      span {
        color: #888;
        font-size: 0.13rem;

        &:last-child {
          font-size: 0.13rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #170606;
          opacity: 0.6;
        }
      }

      &:last-child {
        span {
          color: #999;
          font-size: 0.12rem;
        }
      }

      a.blue {
        background: #3F9DF2;
        border: 0;
      }
    }
  }

  .fr {
    padding-top: 0.16rem;

    span,
    a:first-child {
      display: block;
      width: 0.8rem;
      text-align: center;
      color: #fff;
      font-size: 0.14rem;
      background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
      border-radius: 0.15rem;
      font-size: 0.14rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      height: 0.3rem;
      line-height: 0.3rem;

      i {
        font-style: normal;
      }
    }

    a {
      display: block;
      width: 0.8rem;
      margin-top: 0.08rem;
      text-align: center;
      color: #999;
      font-size: 0.12rem;
    }
  }

  .check {
    position: absolute;
    right: 0rem;
    bottom: 0.25rem;
    padding: 0 0.12rem;
    background: rgba(240, 110, 108, 1);
    border-radius: 2px;
    font-size: 0.14rem;
    color: #ffffff;
    height: 0.3rem;
    line-height: 0.3rem;
  }

  .time-icon {
    width: 0.24rem;
    height: 0.24rem;
  }

}

.information {
  width: 2.86rem;
  // height: 2.08rem;
  padding: 0.3rem 0.26rem 0.28rem;

  .dialog-text {
    color: #170606;
    font-size: 0.17rem;
  }

  .grey {
    margin-top: 0.04rem;
    color: rgba(23, 6, 6, 0.4);
    line-height: 0.2rem;
    font-size: 0.14rem;
  }
}

.btn {
  width: 100%;
  height: 0.5rem;
  line-height: 0.5rem;
  // color: #F06E6C;
  // text-align: center;
  position: absolute;
  bottom: 0;
  border-top: 1px solid rgba(23, 6, 6, 0.08);
  overflow: hidden;

  a {
    display: inline-block;
    width: 50%;
    border-right: 1px solid rgba(23, 6, 6, 0.08);
    text-align: center;
  }

  p {
    color: #f06e6c;
    width: 49%;
    float: right;
    text-align: center;
  }
}

.van-popup--center.van-popup--round {
  border-radius: 0.15rem;
}

.grid-li {
  margin-bottom: 0.15rem;
}
</style>
