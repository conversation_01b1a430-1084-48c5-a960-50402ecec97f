<template>
  <div v-show="show" class="popup">
    <div class="popup-mask">
      <img
        class="audio-bg"
        src="https://static.yzou.cn/h5/20231018Tencent/audio-bg.png"
        alt=""
      />
    </div>
    <div class="popup-content">
      <img
        class="logo"
        src="https://static.yzou.cn/h5/20231018Tencent/audio-logo.png"
        alt=""
      />
      <div class="button" @click="openFrame"><span>打开画面</span></div>
      <p class="tip">仅音频播放中</p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 父组件的上下文this
    parentCom: {
      type: Object,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    openFrame() {
      this.parentCom.openFrame();
    },
  },
};
</script>

<style lang="less" scoped>
/***
  * 1.弹框层组件不要使用rem或者vw
  * 2.如果弹框层组件使用rem和vw, 在全屏模式下, 文字和样式会特别大, 因为rem和vw是根据视宽来自动变化的, 当横屏的时候, 视宽很大, 自然样式和字体也变大了
  * 3.可以使用px或者em, 根据播放器的父元素使用em, 全屏就不会变得很大了
  */
.popup {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 97;
  .popup-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    .audio-bg {
      width: 100%;
      height: 100%;
    }
  }
  .popup-content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    .logo {
      width: 17.5em;
      height: 6.4em;
    }
    .button {
      margin: 3em 0 1.4em;
      width: 9em;
      height: 3.5em;
      line-height: 3.5em;
      border-radius: 2.3em;
      border: 0.1em solid #dddddd;
      text-align: center;
      background: #3d3c3c;
      color: #ffffff;
      span {
        font-size: 1.4em;
      }
    }
    .tip {
      font-size: 1.2em;
      line-height: 1.7em;
      color: #ffffff;
    }
  }
}
</style>
