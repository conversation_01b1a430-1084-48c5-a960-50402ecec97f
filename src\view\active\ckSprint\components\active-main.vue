<template>
  <div class="active-bg">
    <div class="countdown">距离理想大学还有{{lastDate}}天</div>
    <video-swiper class="video-swiper" :list="videoList" />
    <swiper-murquee class='swiper-murquee1' :class='{invite: hasInviteId}' :time='30' :list='murquee1List' />
    <swiper-murquee class='swiper-murquee2' :class='{invite: hasInviteId}' :time='20' :list='murquee2List' />
    <div class="btn">
      <red-btn class='btn-abs' text='测考运' @click="$emit('toTestFate')" />
      <red-btn class='btn-abs' text='许心愿' @click="toWish" />
    </div>

    <wish-popup :wishShow="wishShow" ref="questionEditBox" key="questionEditBox" :change="true" :isSocketConnect="true"
                @cancel="wishShow=false" @load="getMruqueeList" />
  </div>
</template>

<script>
import videoSwiper from './video-swiper.vue';
import SwiperMurquee from "./swiper-murquee";
import RedBtn from './red-btn.vue';
import WishPopup from './wish-popup.vue';

export default {
  components: {
    videoSwiper,
    SwiperMurquee,
    RedBtn,
    WishPopup
  },
  props: {
    lastDate: {
      type: [Number, String],
      default: 0,
    },
    loginMethod: {
      type: Function,
      default: () => { },
    },
  },
  data () {
    return {
      hasInviteId: false,
      murquee1List: [],
      murquee2List: [],
      wishShow: false,
      videoList: [],
      // videoId: 0, // 视频id
    };
  },
  computed: {},
  watch: {
  },
  methods: {
    // 获取走马灯数据
    async getMruqueeList () {
      const { code, body } = await this.$http.post("/mkt/findWishList/1.0/", {
        scholarship: '2522',
        pageNum: 1,
        pageSize: 40,
        // videoId: vId || this.videoId
      });
      if (code == "00") {
        this.murquee1List = []
        this.murquee2List = []
        for (let index = 0; index < body.data.length; index++) {
          if (body.data[index].wishComment.length > 15) {
            body.data[index].wishComment = body.data[index].wishComment.substring(0, 15);
            body.data[index].wishComment += '...'
          }

          if (index < 20) {
            this.$set(this.murquee1List, this.murquee1List.length, body.data[index])
          } else {
            this.$set(this.murquee2List, this.murquee2List.length, body.data[index])
          }
        }
      }
    },
    async getVideoList () {
      const { code, body } = await this.$http.post("/mkt/findVideoByScholarship/1.0/", {
        scholarship: '2522',
        pageNum: 0,
        pageSize: 7,
      });
      if (code == "00") {
        this.videoList = body.data
        // this.videoId = this.videoList[0].videoId

      }
    },
    toWish () {
      this.$yzStatistic('sprintAct.base.click', '4', '首页-许心愿');

      if (!this.loginMethod()) {
        return;
      }
      this.wishShow = true;
      this.$nextTick(() => {
        this.$refs.questionEditBox.editFocus();
      })
    },
    // activeIndex (val) {
    //   this.videoId = this.videoList[val].videoId
    // }
  },
  created () {
    this.getMruqueeList()
    this.getVideoList();

  },
  mounted () {

  },

}
</script>
<style lang='less' scoped>
.active-bg {
  background-image: url('../../../../assets/image/active/ckSprint/bg.png');
  background-size: contain;
  background-repeat: no-repeat;
  height: 4.54rem;
  width: 100%;
  position: relative;
  .countdown {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 0.3rem;
    background: #ffffff;
    border-radius: 0px 0px 0.1rem 0.1rem;
    font-size: 0.16rem;
    line-height: 0.3rem;
    font-family: PingFang SC;
    padding: 0 0.12rem;
    font-weight: 400;
    color: #d92424;
    min-width: 2rem;
    text-align: center;
  }
  .video-swiper {
    position: relative;
    top: 1.5rem;
  }
  .swiper-murquee1 {
    left: 0.43rem;
    top: 3.31rem;
    &.invite {
      top: 3.91rem;
    }
  }
  .swiper-murquee2 {
    top: 3.6rem;
    &.invite {
      top: 4.2rem;
    }
  }
  .btn {
    position: absolute;
    bottom: 0.2rem;
    left: 0.66rem;
    .btn-abs {
      width: 1.09rem;
      height: 0.41rem;
      font-size: 0.18rem;
      margin-right: 0.2rem;
      font-family: PingFang SC;
      font-weight: 600;
      :last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
