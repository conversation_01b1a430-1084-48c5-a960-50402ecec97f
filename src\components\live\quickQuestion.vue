<template>
    <div class="quickQuestion" v-if="isShow">
      <div class="bg"></div>
      <div class="question">
          <div class="title">
            <span>答题卡</span>
            <div class="close"><img src="../../assets/image/student/ic_close.png" alt="" @click="isShow=false"></div>
          </div>
          <div class="content">
            <ul v-if="quickQuestion.type=='R'">
              <li v-for="item,o in options"   @click="select(o)" :class="{active:singleselect==o}" >{{item}}</li>
              <!--<li @click="select('B')" :class="{active:singleselect=='B'}"><span>B. </span>{{data.option2}}</li>-->
            </ul>
            <ul v-else>
              <li v-for="item,o in options"   @click="select(o)" :class="{active:manyselect.indexOf(o)!=-1}" >{{item}}</li>
            </ul>
          </div>
          <div class="btn">
            <button v-on:click="submit" >提交</button>
          </div>
      </div>
    </div>
</template>

<script>
    export default {
      props:['quickQuestion'],
      data(){
          return{
            isShow:false,
            singleselect:'',
            manyselect:[],
            options:{}
          }
      },
      created(){

      },
      methods:{
        open(){
          this.isShow=true
        },
        select(data){
          //R表示单选，C表示多选
          if(this.quickQuestion.type=='R'){
            this.singleselect=data
          }else{
            if(this.manyselect&&this.manyselect.indexOf(data)==-1){
              let an=[...this.manyselect,data]
              this.manyselect=an;
            }else if (this.manyselect&&this.manyselect.indexOf(data)!=-1) {
              let an=[]
              for(let i=0;i<this.manyselect.length;i++){
                if(data!=this.manyselect[i]){
                  an.push(this.manyselect[i])
                }
              }
              this.manyselect=an;
            }else{
              this.manyselect.push(data)
            }
          }
        },
        submit(){
          let answer;
          if(this.quickQuestion.type=='R'){
            answer=this.singleselect;
          }else{
            answer=this.manyselect.join("");
          }
          this.$emit('sendAnswer',answer,this.quickQuestion.questionId);
          this.isShow=false
          this.$modal2({message:'答案已提交'})
        }
      },
      watch:{
        quickQuestion:function(newValue){
          this.options={};
          this.manyselect=[];
          this.singleselect=''
          for(let o in newValue){
              if(o.indexOf('option')!=-1){
                let obj={};
                obj[o]=newValue[o];
                // this.options=[...this.options,this.data[o]];
                let detail;
                switch (o) {
                  case 'option1':detail='A';break;
                  case 'option2':detail='B';break;
                  case 'option3':detail='C';break;
                  case 'option4':detail='D';break;
                  case 'option5':detail='E';break;
                  default:break;
                }
                this.$set(this.options,detail,newValue[o])
              }
            }
        }
      }
    }
</script>

<style scoped lang="less">
  .quickQuestion{
    position: fixed;
    width: 100%;
    max-width: 640px;
    height: 100%;
    min-height: 100vh;
    z-index:992;
    top:0;
    left:0;
    bottom:0;
    right: 0;
    margin: auto;
  .bg{
    width: 100%;
    max-width: 640px;
    min-height: 100vh;
    height: 100%;
    position: fixed;
    top:0;
    left:0;
    bottom:0;
    right: 0;
    margin: auto;
    z-index:210;
    background-color: rgba(0,0,0,.3);
  }
  .question{
    position:absolute;
    z-index:400;
    width:100%;
    background-color: white;
    bottom:0;
  }
    .title{
      height: auto;
      overflow: hidden;
      border-bottom: solid 1px rgba(23,6,6,.1);
      margin-left: .2rem;
      margin-right: .15rem;
      padding-bottom: .05rem;
      span{
        display: block;
        float: left;
        background-size: .24rem .24rem;
        font-size: .15rem;
        margin-left: .2rem;
        margin-top: .17rem;
      }
      .close{
        float: right;
        width: .32rem;
        height: .32rem;
        margin-top: .11rem;
        margin-right: .15rem;
        img{
          width: 100%;
        }
      }
    }
  .content{
    width: 100%;
    margin: 0 auto;
  ul{
    display: flex;
    width: 100%;
    flex-direction: row;
    justify-content:space-around;
  li{
    float: left;
    font-size: .15rem;
    width: .45rem;
    height: .45rem;
    border-radius: 50%;
    border: solid 1px #666666;
    text-align: center;
    line-height: .45rem;
    margin-top: .26rem;
    margin-bottom: .42rem;
  &.active{
    background-color: #3F9DF2 ;
    color: white;
    border: none;
   }
  }
  }
  }
  button{
    display: block;
    margin: .2rem auto 0;
    line-height: .4rem;
    border: none;
    font-size: .14rem;
    color: #fff;
    background: #E85B57;
    padding: 0 0.1rem;
    width: 100%;
    cursor: pointer;
  }
  }
</style>
