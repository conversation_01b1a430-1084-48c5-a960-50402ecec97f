<template>
    <div class="content">
        <div class="pic-text Succes">恭喜购买成功</div>
        <img :src="imgUrl | imgOssURL" alt="" class="pic">
        <div class="pic-text">{{text}}</div>
    </div>
</template>
<script>
export default {
    data(){
        return{
         imgUrl:"",
         mapId:"",
         text:"",
        }
    },
    created(){
        this.mapId=this.$route.query.mapId
        this.getActivity(this.mapId)
    },
    methods:{
       getActivity(item){
           this.$http.post('/pu/getMsgDeploy/1.0/',{actId:item}).then(res=>{
                if(res.code!="00"){
                   return
                }
                this.imgUrl=res.body.imgUrl
                this.text=res.body.actExplain
                // this.mebId=this.detail.mebInfo.mebId
           })
       }, 
    }
}
</script>
<style lang="less" scoped>
.content{
    width: 3.75rem;
    min-height: 100vh;
    .pic{
        display: block;
        width: 1.49rem;
        height: 1.49rem;
        margin: 0 auto;
    }
    .pic-text{
        width: 2.10rem;
        height: 0.2rem;
        font-size: 0.14rem;
        white-space: nowrap;
        margin: 0 auto;
        margin-top: 0.3rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 0.2rem;
        text-align: center;
    }
    .Succes{
        margin-bottom: 0.3rem;
        margin-top: 0.9rem;
    }
}
</style>