<template>
  <div class="footer comment-bar">
    <div class="wrap">
      <div class="inner" @click="show">
        <input class="input1" type="text" disabled placeholder="我要评论..."/>
      </div>

      <div class="comment-box" v-if="isShow" @click="hide">
        <div class="wrap bc-w" @click.stop>
          <div class="head-box clearfix">
            <div class="tit fl">{{title}}</div>
            <div class="txt-r fr">
              <button class="btn" v-if="isSubmiting">评论中…</button>
              <button class="btn" :disabled="!content" @click="submit" v-else>发表</button>
            </div>
          </div>
          <slot></slot>
          <div class="textarea-box">
            <textarea class="textarea1" rows="5" v-bind:placeholder=textareaContent v-model.trim="content" v-focus></textarea>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    directives: {
      focus: {
        inserted: function (el) {
          el.focus();
        }
      }
    },
    props: {
      title: {
        type: String,
        default: '评论'
      },
      textareaContent:{
        type: String,
        default: '我要评论...'
      },
      isSubmiting: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isShow: false,
        content: ''
      }
    },
    methods: {
      show: function () {
        this.content = '';
        this.isShow = true;
      },
      hide: function () {
        this.isShow = false;
      },
      submit: function () {
        this.$emit('submit', this.content);
      },
      // 评论成功弹窗
      successModal: function () {
        const h = this.$createElement;
        this.$modal({
          message: h(
            'div', {style: 'text-align:center'}, [
              h('div', {style: 'font-size:.17rem'}, '评论成功！'),
              h('div', {style: 'color:#999;font-size:.13rem'}, '（审核通过后将公开显示）')
            ]
          ),
          icon: 'comment'
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .comment-bar{
    .input1{
      width:100%; height:.4rem; margin-top:.05rem; padding:0 .12rem; color:#888; font-size:.12rem; border:0; background-color:#f7f7f7; border-radius:.05rem;
      &:disabled{ background: #edebeb }
      &::placeholder{
        color:rgba(23,6,6,0.6);
        font-size: 0.14rem;
      }
    }
    .btn{
      background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
      border-radius: 0.05rem;
      padding: 0.05rem 0.1rem;
      line-height: 0.2rem;
      font-weight:600;
      color: #fff;
      margin-top: 0.1rem;
    }
    .head-box{
      height: 0.5rem;
      line-height: 0.5rem;
      border-bottom: 1px solid rgba(23, 6, 6, 0.08);
      padding: 0 0.1rem;
    }
    .textarea-box{
      padding: 0.1rem;
    }
  }
  .comment-box{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; background-color:rgba(0, 0, 0, .50);
    .wrap{ position:absolute; right:0; bottom:0; left:0; }
    .tit{ text-align:center; color:@color; font-size:.16rem; }
    .textarea1{ width:100%; padding:.1rem; border:none; font-size:.14rem; border: 0; background: rgba(23,6,6,0.03); border-radius:.05rem; }
  }
</style>
