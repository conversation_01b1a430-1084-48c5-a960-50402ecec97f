<template>
  <div class="top-bar" v-if="isShowTopbar">
    <div class="wrap">
      <div class="inner">
        <div class="lf">
          <button class="icons btn-back" @click="goBack" v-if="showBackButton"></button>
        </div>
        <div class="mid row1" :class="{'font-m':title.length>='13'}">{{title}}</div>
        <div class="rt"><slot name="rt"></slot></div>
      </div>
    </div>
  </div>
</template>

<script>
  import {setDocumentTitle} from '../common';
  
  export default {
    props: {
      title: {
        type: String,
        default: ''
      },
      showBackButton: {
        type: Boolean,
        default: true
      },
      fnGoBack: {
        type: Function
      }
    },
    data() {
      return {
        redirect: '',
        isShowTopbar: true
      }
    },
    mounted() {
      this.redirect = this.$route.query.redirect;

      // app访问时隐藏topbar
      if (this.$route.query.topbar === 'no' || this.$route.query.browser === 'app' || window.sessionStorage.getItem('browser') === 'app') {
        this.isShowTopbar = false;
        window.sessionStorage.setItem('browser', 'app');
  
        // app设置title
        setDocumentTitle(this.title);
      }
    },
    methods: {
      goBack: function () {
        // app返回
        if ('undefined' !== typeof yz) {
          yz.goBack();
          return;
        }

        // web返回
        if ('function' === typeof this.fnGoBack) {
          this.fnGoBack();
        } else {
          if (!this.redirect) {
            if (window.history.length === 1) {
              const routerName = this.$route.fullPath.startsWith('/student') ? 'stuInfo' : 'home';
              this.$router.push({name: routerName});
            } else {
              this.$router.go(-1);
            }
          } else {
            this.$router.push(decodeURIComponent(this.redirect));
          }
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .top-bar{
    height:.5rem;
    .wrap{ position:fixed; top:0; right:0; left:0; z-index:2000; height:.5rem; }
    .inner{
      background-color:rgba(232,91,87,1);
      display:flex; align-items:center; position:relative; height:100%; max-width:@maxWidth; margin:0 auto; overflow:hidden; 
      &:after{ .borderBottom }
    }
    .mid{ flex:1; padding:0 .1rem; line-height:.44rem; text-align:center; font-size:.17rem;
    color:rgba(255,255,255,1);
    &.font-m{
      font-size: .14rem;
      }
    }
    .lf,
    .rt{ min-width:.5rem; }
    .rt{ text-align:right; }
  }
  .btn-back{ width:.44rem; height:.44rem; border:none; background-color:transparent; background-image:url(../assets/image/backTwo.png); }
</style>
