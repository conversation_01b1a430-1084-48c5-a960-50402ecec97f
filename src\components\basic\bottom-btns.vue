<template>
  <div class="complete-bottom-btns">
    <button
      v-if="isOnlyOne == 'all' || isOnlyOne == 'left'"
      :class="[{ block: isOnlyOne == 'left', plain: isleftPlain, 'btn-disabled': leftDisabled }]"
      :disabled="leftDisabled"
      @click="leftClick">
      {{ leftBtnText }}
    </button>
    <button
      v-if="isOnlyOne == 'all' || isOnlyOne == 'right'"
      :class="[{ block: isOnlyOne == 'right', plain: isRightPlain }]"
      :disabled="rightDisabled"
      @click="rightClick">
      {{ rightBtnText }}
    </button>
  </div>
</template>

<script>
export default {
  data() {
    return {}
  },
  props: {
    leftBtnText: {
      type: String,
      default: '上一步',
    },
    rightBtnText: {
      type: String,
      default: '下一步',
    },
    isOnlyOne: {
      // 是否只有1个按钮
      type: String,
      default: 'all', // 传入left，则保留左边，传入right则保留右边
    },
    leftDisabled: {
      type: Boolean,
      default: false,
    },
    rightDisabled: {
      type: Boolean,
      default: false,
    },
    isleftPlain: {
      // 左朴素按钮
      type: Boolean,
      default: false,
    },
    isRightPlain: {
      // 右朴素按钮
      type: Boolean,
      default: false,
    },
  },
  methods: {
    leftClick() {
      this.$emit('leftClick')
    },
    rightClick() {
      this.$emit('rightClick')
    },
  },
}
</script>

<style
  lang="less"
  scoped>
.complete-bottom-btns {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.05rem 0.2rem;
  background: #fff;
  button {
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    height: 0.4rem;
    width: 1.62rem;
    text-align: center;
    color: #fff;
    border: 0;
    border-radius: 100px;
    font-size: 0.14rem;
    font-weight: 600;
    padding: 0 5px;
    box-sizing: border-box;
    &.block {
      width: 100%;
      display: block;
      color: #fff !important;
    }
    &:not(.block):first-child {
      margin-right: 0.2rem;
    }
    &:disabled {
      background: rgba(185, 180, 180, 1);
      opacity: 1;
    }
    &.plain {
      border: 1px solid #f06e6c;
      background: #fff;
      color: #f06e6c;
      &:disabled {
        opacity: 1;
        border: 1px solid #a29b9b;
        color: rgba(23, 6, 6, 0.4);
      }
    }
  }
  .btn-disabled{
    background: #dad8d8 !important;
    opacity: 0.5 !important;
  }
}
</style>
