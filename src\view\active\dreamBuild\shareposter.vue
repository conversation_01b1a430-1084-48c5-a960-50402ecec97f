<template>
    <div class="signShare">
      <div class="bg" @click="cancel"></div>
     <div class="sign">
       <div class="close" @click="cancel">
         <img  src="../../../assets/image/active/dreamBuild/invite_close.png" alt="">
       </div>
       <div style="opacity: 0">
         <div class="shareImg" >
           <div class="head">
             <div class="headImg">
               <img :src="headImg" alt="" v-if="headImg" ref="shareHeadImg">
               <img :src="sex | avatar2" alt="" v-else ref="shareHeaddefault">
             </div>
             <div class="headInfo">
               <h2>{{stdName}}</h2>
               <div class="headTxt">
                 <!-- dreamBuild -->
                 <p v-if="from=='newMain2021'">邀请你一起来在线报读拿学历</p>
                 <p v-else-if="from=='openUniversity'||'selfTought'">邀请你一起来提升大专本科学历</p>
                 <p v-else>邀请你一起来提升学历拿全日制文凭</p>
               </div>
             </div>
           </div>
           <div class="img_wrap">
             <img src="../../../assets/image/active/dreamBuild/postershare.png" alt="" v-if="defaultFlag" ref="shareImgdefault">
             <img :src="imgUrl" alt="" v-if="imgUrl" ref="shareImg">
           </div>
           <div class="insist">
             <div class="left">
             <span class="head">
            <img src="../../../assets/image/active/dreamBuild/head2.png" alt="">
          </span>
               <div class="partner">
                 <p>超过60万同学在远智教育一起上进</p>
               </div>
             </div>
             <div id="qrcode" ref="qrcode"></div>
           </div>
         </div>
       </div>
       <div class="shareImg img" v-show="!showC">
         <img src="" alt="" id="share">
         <a href="javascript:;" @click="cancel" >快长按保存图片分享给朋友吧</a>
       </div>

     </div>
    </div>
</template>

<script>
  import html2canvas from "html2canvas"
  import {avatar2} from "../../../filters";
  import QRCode from "qrcodejs2"
  import {imgPosterBaseURL} from "../../../config";
  import shareJson from "./share"
  export default {
      name: "signshare",
      props:['isChecked','sex','isAppOpen','from'],
      data(){
          return{
            imgUrl:'',
            day:'',
            month:'',
            weekday:'',
            insistDay:1,
            shareImgUrl:'',
            showC:true,
            zhimi:5,
            timer:null,
            defaultFlag:false,
            scholarship:32,
            inviteId:'',
            qrcode:'',
            inviteUrl:'',
            headImg:'',
            stdName:'',
            change:false,
            lock:false,
            pic:''
          }
      },
      created(){
          this.inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || '')).replace(/ /g, '+');
      },
      mounted(){
           //this.share()
        this.getPic();
      },
      methods: {
        useqrcode(){
          if(this.from){
            this.inviteUrl = `${window.location.origin}${shareJson[this.from].shareLink}?action=login&inviteId=${encodeURIComponent(this.storage.getItem('authToken'))}`;
          }else{
            this.inviteUrl = `${window.location.origin}/active/newDreamBuildUpdate?action=login&regOrigin=2&inviteId=${encodeURIComponent(this.storage.getItem('authToken'))}`;
          }
          console.log(this.inviteUrl);
          let qrcode = new QRCode('qrcode', {
            width: 90,  // 设置宽度
            height: 90, // 设置高度
            text:this.inviteUrl,
            backgoround:'#fff',
            correctLevel:0
          })
        },
        cancel(){
          this.$emit('cancel')
        },
        getPic(){
          this.$http.post('/us/getSharePic/1.0/').then(res=>{
            if(res.code!='00') return;
            this.$indicator.open();
              if(!res.body){
                this.defaultFlag=true;
              }
            this.pic='http:' + imgPosterBaseURL +res.body.pictureUrl
            var that=this;
              if(res.body.headImg){
                this.lock=true
                this.main(res.body.headImg,"head",(base64)=>{
                  this.headImg=base64;
                  this.stdName=res.body.realName?res.body.realName:'亲爱的';
                  if(res.body.pictureUrl){
                    this.main(this.pic,"pic",(base64)=> {
                      this.imgUrl = base64;
                      this.$nextTick(()=>{
                        that.$refs.shareImg.onload=function(){
                          that.share()
                        }
                      })
                    });
                  }else{
                    this.defaultFlag=true;
                    that.share()
                  }
                })
              }else {
                this.stdName = res.body.realName ? res.body.realName : '亲爱的';
                if (res.body.pictureUrl) {
                  this.main(this.pic, "pic", (base64) => {
                    this.imgUrl = base64;
                    this.$nextTick(() => {
                      that.$refs.shareImg.onload = function () {
                        that.share()
                      }
                    })
                  });
                }
              }

          })
        },
        share() {
          var that=this;
          this.$nextTick (function () {
            that.useqrcode();
          })
          var canvas2 = document.createElement("canvas");
          let _canvas = document.querySelector('.shareImg');
          var w = parseInt(window.getComputedStyle(_canvas).width);
          var h = parseInt(window.getComputedStyle(_canvas).height);
          canvas2.width = w * 3;
          canvas2.height = h * 3;
          canvas2.style.width = w + "px";
          canvas2.style.height = h + "px";
          var context = canvas2.getContext("2d");
          context.scale(2, 2);
          html2canvas(document.querySelector('.shareImg'), {
            canvas: canvas2, useCORS: true,
            width: document.querySelector('.shareImg').offsetWidth,
            height: document.querySelector('.shareImg').outterHeight,
          }).then(function (canvas) {
             that.$indicator.close();
              that.showC=false;
              document.querySelector('#share').src=canvas.toDataURL();
              that.lock=false;
          }).catch(err=>{
            that.$indicator.close();
          });
        },
        getBase64Image(img) {
          var canvas = document.createElement("canvas");
          canvas.width = img.width;
          canvas.height = img.height;
          var ctx = canvas.getContext("2d");
          ctx.drawImage(img, 0, 0, img.width, img.height);
          try {
            var i = 1/0;
            var dataURL = canvas.toDataURL("image/jpeg");  // 可选其他值 image/jpeg
          }catch (e) {
            this.defaultFlag=true;
          }
          return dataURL;
        },
        main(src,type, cb) {
          var image = new Image();
          image.crossOrigin = "Anonymous";  // 支持跨域图片
          image.src = src + '?temp_v=' + Math.random(); // 处理缓存
          var that=this;
          image.onload = function () {
            var base64 = that.getBase64Image(image);
            cb && cb(base64);

          }
          image.onerror=function(){
            if(type=='head'){
              that.headImg=''
              that.main(this.pic,"pic",(base64)=> {
                that.imgUrl = base64;
                that.$nextTick(()=>{
                  that.$refs.shareImg.onload=function(){
                    that.share()
                  }
                })
              });
            }else{
              if(!that.lock){
                that.defaultFlag=true;
                that.$nextTick(()=>{
                  that.$refs.shareImgdefault.onload=function(){
                    that.share()
                  }
                })
              }
            }

          }
        },
      },
    components:{QRCode}
    }
</script>

<style scoped lang="less">
.bg{
  width: 100%;
  max-width: 640px;
  height: 100%;
  background-color: #000;
  opacity: .6;
  overflow: hidden;
  position: fixed;
  top:0;
  z-index: 2003;
}
.close{
  position: fixed;
  right: .45rem;
  top:.3rem;
  width: .24rem;
  height: .24rem;
  z-index:3000;
  img{
    width: 100%;
     pointer-events: none;
  }
}
.sign {
  position: fixed;
  width: 3.05rem;
  z-index: 3000;
  top:.24rem;
  left:50%;
  margin-left: -1.57rem;
  .shareImg {
    width: 3.66rem;
    height: 5.86rem;
    margin: 0 auto;
    /*left: 50%;*/
    /*margin-left: -1.7rem;*/
    color: white;
    position: relative;
    background-color: #f0eeec;
    &.img{
      position: absolute;
      top:0;
      width: 3.05rem;
      max-width: 608px;
      height: 4.89rem;
      margin: 0 auto;
    }
    img {
      width: 100%;
    }
    .head{
      width: 100%;
      height: .88rem;
      padding: .12rem 0 .2rem .14rem;
      .headImg{
        width: .6rem;
        height: .6rem;
        float: left;
        border-radius: 50%;
        overflow: hidden;
        img{width: 100%}
      }
      .headInfo{
        float: left;
        padding: 0.06rem 0;
        h2{
          color: #F26662;
          font-size: .2rem;
          height: .28rem;
          margin-left: .07rem;
        }
        .headTxt{
          margin-left: .06rem;
          clear: both;
          color: #000;
        }
      }

    }
    .img_wrap{
      width: 3.34rem;
      height: 3.84rem;
      margin: 0 .18rem;
      img{
        width: 100%;
      }
    }
    .insist {
      width: 96%;
      height: 0.85rem;
      margin-top: .1rem;
      .left{
        float: left;
        .partner {
          height: .35rem;
          margin-left: .24rem;
          p {
            font-size: .12rem;
            color: black;
            line-height: .35rem;
          }
        }
        .head {
          display: block;
          width: .66rem;
          height: .3rem;
          margin-top: .1rem;
          margin-left: 0.1rem;
          img {
            width: 100%;
          }
        }
      }
      #qrcode {
        float: right;
        width: 0.9rem;
        height: 88%;
        img {
          width: 100%;
        }
      }
    }
  }

  a {
    /*position: fixed;*/
    display: block;
    /*left:50%;*/
    /*bottom:.16rem;*/
    width: 100%;
    /*margin-left: -1.13rem;*/
    margin: .2rem auto 0;
    height: .26rem;
    font-size: .16rem;
    /*z-index: 3000;*/
    text-align: center;
    line-height: .26rem;
    color: white
  }
  .btn{
    position: fixed;
    width: 100%;
    height: .5rem;
    bottom:0.27rem;
    left:0;
    a{
      display: block;
      float: left;
      width: 0.48rem;
      height: 0.48rem;

    }
    a:first-of-type{
      background: url("../../../assets/image/active/dreamBuild/invite_wechat.png");
      background-size: 100%;
      margin: 0 .25rem 0 1.3rem;
     }
    a:nth-of-type(2){
      background: url("../../../assets/image/active/dreamBuild/invite_friendzone.png");
      background-size: 100%;
      margin: 0 ;
    }
  }
}
</style>
