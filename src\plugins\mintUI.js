import {
  TabContainer,
  TabContainerItem,
  DatetimePicker,
  Swipe,
  SwipeItem,
  Spinner,
  Popup,
  Picker,
  Lazyload,
  InfiniteScroll,
  Indicator
} from 'mint-ui';

const mintUI = {
  install(Vue) {
    // vue 组件
    Vue.component(TabContainer.name, TabContainer);
    Vue.component(TabContainerItem.name, TabContainerItem);
    Vue.component(DatetimePicker.name, DatetimePicker);
    Vue.component(Swipe.name, Swipe);
    Vue.component(SwipeItem.name, SwipeItem);
    Vue.component(Spinner.name, Spinner);
    Vue.component(Popup.name, Popup);
    Vue.component(Picker.name, Picker);
    // vue 指令
    Vue.use(Lazyload);
    Vue.use(InfiniteScroll);

    Vue.prototype.$indicator = Indicator;
  }
}

export default mintUI;
