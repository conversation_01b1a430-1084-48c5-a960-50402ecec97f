<template>
  <div class="witness-box">

    <!-- 头部按钮 -->
    <div class="btns">
      <span @click="toCoupons" class="btn">
        <img src="../../../../assets/image/active/gccScholarship/myVolume.png" alt="">
      </span>
      <!-- toCoupons -->
      <span @click="$InviteJump('/active/gzOpenUniversityForward/witnessRecord')" class="btn">
        <img src="../../../../assets/image/active/gccScholarship/witnessBtn.png" alt="">
      </span>
    </div>

    <div class="process">
      <!-- 未领取优惠价 -->
      <div class="unclaimed" v-if="!isReceive">
        <h2>你还未领取奖学金</h2>
        <div class="enrollment-steps">
          <img src="../../../../assets/image/active/gccScholarship/enrollmentSteps.png" />
        </div>
      </div>

      <!-- 已领劵/未见证/已见证 -->
      <div class="taken">
        <commitment v-if="isReceive" :isReceive="isReceive" />
      </div>

    </div>

    <!-- 动态 -->
    <div class="dynamic">
      <witnessRecord :is-jump="true" />
    </div>

  </div>
</template>
<script>
import commitment from '../components/commitment'
import witnessRecord from '../components/witnessRecord'
import bridge from '@/plugins/bridge';
import invite from '../invite'
export default {
  components:{
    commitment,
    witnessRecord
  },
  mixins:[ invite ],
  data(){
    return {
      isReceive: false,
      isAppOpen:false,
    }
  },
  created(){
    bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        this.isAppOpen = true;
      }
    });

    this.getActivityInfo();
    this.getVoucherStatus();
  },
  methods:{
     //获取活动信息
    getActivityInfo(){
      this.$http.post("/mkt/getActivityInfo/1.0/", {scholarship:'126'}).then(res=>{
        const {code, body } = res;
        if(code == '00'){
          this.$emit('setAcInfo',body);
        }
      })
    },
    toCoupons(){
      if(this.isAppOpen){
        window.location.href =`yuanzhiapp://yzwill.cn/Coupons`;
      }else{
        this.$InviteJump('/settings/coupon')
      }
    },
    //是否已领取优惠劵
   async getVoucherStatus() {
      if (!this.storage.getItem("authToken")) {
        return;
      }

      let params = {
        couponType: '06_gk'
      }

     await this.$http.post("/mkt/isGiveCouponByType/1.0/", params).then((res) => {
        if ( res.code == "00" ) {
          res.body == true
          ? this.isReceive = true
          : this.isReceive = false;
        console.log('this.isReceive '+res.body  );
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>
.witness-box{
  background-color: #fff;
  min-height: 100vh;
  // 按钮区
  .btns {
    text-align: center;
    padding-top:0.2rem;
    .btn{
      img{
        width: 1.42rem;
        height: 0.38rem;
      }
    }
    .btn + .btn{
      margin-left: 0.32rem;
    }
  }

  .process{
    margin-top: 0.26rem;
    .unclaimed{
      h2{
        font-size: 0.16rem;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #453838;
        text-align: center;
        margin-top: 0.24rem;
      }
      // 步骤
      .enrollment-steps{
        width: 3.55rem;
        height: 1.12rem;
        margin:0.1rem 0.1rem 0.24rem 0.1rem;
        img {
          width: 100%;
        }
      }
    }
  }

  .dynamic{
    margin-top: 0.24rem;
  }
}
</style>
