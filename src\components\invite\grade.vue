<template>
  <div>
    <!--<div class="select-item" v-for="item in options" :class="{active:value.dictValue===item.dictValue}" @click="selected(item)">{{item.dictName}}</div>-->
    <!--报读年级先写死-->
    <div class="select-item" v-for="item in options" :class="{active:value.dictValue===item.dictValue}" @click="selected(item)">{{item.dictName}}</div>
  </div>
</template>

<script>
  import { domain } from '@/config';
  import qs from 'qs';

  export default {
    props: ['value', 'datas', 'isDingding'],
    data() {
      return {
        options: [],
        option:[{dictName:'2020级',dictValue:'2020',dictId:'grade.2020'},{dictName:'1903级',dictValue:'1903',dictId:'grade.1903'}]
      }
    },
    mounted() {
      const recruitType = this.datas.recruitType.dictValue;
      this.options = dictJson.grade.filter(val => val.ext1 == recruitType);
      this.getList();
    },
    methods: {
      selected: function (val) {
        this.$emit('input', val);
      },
      // /newStudentChange/findTransferByStdId.do
      async getList() {
        if (!this.isDingding) {
          return;
        }
        const res = await this.$http.post(`${domain}/newStudentChange/findStudentGrade.do`);
        if (res.code == '00') {
          const list = res.body || [];
          this.options = [];
          list.forEach((val) => {
            dictJson.grade.forEach((item) => {
              if (val == item.dictValue) {
                this.options.push(item);
              }
            });
          });
        }
      },
    }
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
