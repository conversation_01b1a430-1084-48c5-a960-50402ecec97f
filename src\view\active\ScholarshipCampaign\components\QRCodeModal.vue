<template>
  <!-- 加微信弹框 -->
  <van-popup :close-on-click-overlay="false" round v-model="visible">
    <div class="qrcode-box">
      <p>请同学务必扫码添加老师微信号</p>
      <p>进入奖学金班级群</p>
      <img :src="qrCodeUrl" alt />
      <p class="code-txt">长按二维码识别</p>
    </div>
    <van-icon
      @click="handleClose"
      style="
        position: absolute;
        bottom: -50px;
        left: 50%;
        transform: translateX(-50%);
      "
      name="close"
      size="40"
      color="#ffffff"
    />
  </van-popup>
</template>

<script>
export default {
  name: 'QRCodeModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    qrCodeUrl: {
      type: String,
      default: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.qrcode-box {
  width: 315 * @rem;
  height: 323 * @rem;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10 * @rem;
  padding: 0 15 * @rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #453838;
    line-height: 22 * @rem;
  }

  img {
    margin-top: 30 * @rem;
    width: 99 * @rem;
    height: 99 * @rem;
  }

  .code-txt {
    margin-top: 10 * @rem;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #e53d19;
    line-height: 18px;
  }
}
</style>
