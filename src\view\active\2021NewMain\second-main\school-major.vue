<template>
  <div class="yz-school-major" :class='{invite: inviteId}'>
    <inviteTop @getInviteId='getInviteId' />
    <share
      :title="shareTitle"
      :desc="shareDesc"
      :link="shareLink"
      :isActivity="true"
      :scholarship="scholarship"
      regOrigin='49'
      ref="share"
    />
    <div class="fixed-head" :class='{fixed: fixed || !inviteId}'>
      <p class="t1">“{{pfsnName}}”可报读以下院校：</p>
      <div class="btns">
        <button :class='{active: sort == ""}' @click='srotMethods("")'>推荐排序</button>
        <button :class='{active: sort == "tuition"}'  @click='srotMethods("tuition")'>
          <span>价格</span>
          <img src="../../../../assets/image/active/2021NewMain/black-down.png" class="arrow" :class='{desc: tuitionSeq == "desc"}' alt="">
        </button>
        <button :class='{active: sort == "edu_system"}' @click='srotMethods("edu_system")'>
          <span>学制</span>
          <img src="../../../../assets/image/active/2021NewMain/black-down.png" class="arrow" :class='{desc: eduSystemSeq == "desc"}' alt="">
        </button>
      </div>
    </div>
    <ul class="list-ul" :class='{fixed: fixed}'>
      <li v-for="(item, index) in list" :key="index">
        <div class="school">
          <img v-if='item.unvsBadgeImage' :src="item.unvsBadgeImage | imgOssURL" class="icon" alt="">
          <img v-else src="../../../../assets/image/active/2021NewMain/ic_school.png" class="icon" alt="">
          <span>{{item.unvsNickName}}</span>
        </div>
        <div class="icon-text">
          <img src="../../../../assets/image/active/2021NewMain/m1.png" class="icon-m h2" alt="">
          <span>书杂费：{{item.bookCost}}/学年（按{{item.bookTuition?item.bookTuition:item.eduSystem}}年收取）</span>
        </div>
        <div class="icon-text">
          <img src="../../../../assets/image/active/2021NewMain/m2.png" class="icon-m" alt="">
          <span>学制：{{item.eduSystem}}年</span>
        </div>
        <div class="icon-text">
          <img src="../../../../assets/image/active/2021NewMain/m3.png" class="icon-m" alt="">
          <span>学费：{{item.tuition}}元/学年</span>
        </div>
        <button class="enroll-btn" @click='toEnroll(item)'>前往报名</button>
      </li>
    </ul>
  </div>
</template>

<script>
import { toLogin, isLogin } from '@/common';
import { toAppLogin } from '@/common/jump';
import appShare from '@/mixins/appShare';
import share from "@/components/share";
import inviteTop from "@/view/active/enrollAggregate/components/invite-top";
import statistic from '../statistic.json';

export default {
  mixins: [appShare],
  components: {
    share, inviteTop,
  },
  data() {
    return {
      list: [],
      inviteId: '',
      sort: '',
      tuitionSeq: '',
      eduSystemSeq: '',
      fixed: false,
      shareTitle: '在线报读，名校录取，学信网可查，和我一起提升学历！',
      shareDesc: '远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！',
      link: '/active/newMain/newSecondMain/adMajor',
      isLogin: isLogin(),
    };
  },

  computed: {
    pfsnLevel() {
      return Array.isArray(this.$route.query.pfsnLevel)? this.$route.query.pfsnLevel[0] : this.$route.query.pfsnLevel || '';;
    },
    pfsnId() {
      return this.$route.query.pfsnId || '';
    },
    pfsnName() {
      return this.$route.query.pfsnName || '';
    },
    actName() {
      return this.$route.query.actName || '';
    },
    scholarship() {
      return this.$route.query.scholarship || 164;
    },
    linkWithQuery() {
      return this.link + `?pfsnLevel=${this.pfsnLevel}&pfsnId=${this.pfsnId}&pfsnName=${this.pfsnName}&actName=${this.actName}&scholarship=${this.scholarship}`;
    },
    shareLink() {
      return window.location.origin + this.linkWithQuery;
    },
    regChannel() {
      return this.$route.query.regChannel || '';
    },
    regOrigin() {
      return this.$route.query.regChannel || '';
    },
  },
  mounted() {
    if (this.inviteId) {
      window.addEventListener('scroll', this.scroll);
    }
    this.findUnvsByPfsnId();
    this.$yzStatistic('marketing.base.browse', statistic.pfsnDetail[this.pfsnLevel].main.id, statistic.pfsnDetail[this.pfsnLevel].main.name);
    this.initAppShare(() => {
      this.setShareParams({
        title: this.shareTitle,
        content: this.shareDesc,
        url: this.linkWithQuery,
        actName: this.actName,
        regOrigin: '49',
      });
    });
  },
  methods: {
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    scroll() {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      this.fixed = scrollTop >= 60; // 邀约头的高度
    },
    getInviteId(id) {
      this.inviteId = id;
    },
    srotMethods(sort) {
      if (sort == '') {
        this.tuitionSeq = 'asc';
        this.eduSystemSeq = 'asc';
      } else if (sort == 'tuition') {
        this.eduSystemSeq = 'asc';
        if (this.sort != 'tuition') {
          this.tuitionSeq = 'asc';
        } else {
          this.tuitionSeq = this.tuitionSeq == 'asc' ? 'desc' : 'asc';
        }
      } else if (sort == 'edu_system') {
        this.tuitionSeq = 'asc';
        if (this.sort != 'edu_system') {
          this.eduSystemSeq = 'asc';
        } else {
          this.eduSystemSeq = this.eduSystemSeq == 'asc' ? 'desc' : 'asc';
        }
      }
      this.sort = sort;
      this.findUnvsByPfsnId();
    },
    toEnroll(item){
      this.$yzStatistic('marketing.base.click', statistic.pfsnDetail[this.pfsnLevel].enroll.id, statistic.pfsnDetail[this.pfsnLevel].enroll.name);
      if (!this.login()) {
        return;
      }
      const unvs = { unvsName: item.unvsNickName, unvsId: item.unvsId };
      this.$router.push({
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          pfsnLevel: this.pfsnLevel,
          pfsnId: this.pfsnId,
          pfsnName: this.pfsnName,
          scholarship: this.scholarship,
          actName: this.actName,
          recruitType: 1,
          unvs: JSON.stringify(unvs),
          regChannel: this.regChannel,
        }
      });
    },
    async findUnvsByPfsnId(){
      const query = {
        pfsnName: this.pfsnName,
        pfsnLevel: this.pfsnLevel,
        grade: 2024, // 当前年份
        sort: this.sort, // edu_system学制 tuition学费
        // seq: 'desc', // desc 升序 asc降序
      };
      if (this.sort) {
        query.seq = this.sort == 'tuition' ? this.tuitionSeq : this.eduSystemSeq;
      }
      const { body, code } = await this.$http.post('/mkt/findUnvsByPfsnId/1.0/', query);
      if (code == '00') {
        this.list = body || [];
      }
    },
  },
};
</script>

<style lang="less">
  .yz-school-major{
    padding-top: 0.88rem;
    padding-bottom: 0.1rem;
    min-height: 100vh;
    background: #F8F7F7;
    &.invite{
      padding-top: 0;
    }
    .fixed-head{
      background: #fff;
      padding: 0.15rem 0.1rem 0.08rem;
      width: 100%;
      font-size: 0.14rem;
      z-index: 2;
      &.fixed{
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
      }
      .t1{
        font-weight: 600;
        margin-bottom: 0.15rem;
      }
      .btns{
        display: flex;
        align-items: center;
        justify-content: space-between;
        button{
          background: #F4F4F4;
          height: 0.3rem;
          width: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 100px;
          border: 0;
          color: #000;
          &.active{
            background: rgba(234, 90, 89, 0.1);
            border: 1px solid rgba(234, 90, 89, 0.3);
            color: #EA5A59;
          }
        }
        .arrow{
          width: 0.08rem;
          height: 0.08rem;
          margin-left: 0.04rem;
          transition: all 0.2s;
          &.desc{
            transform: rotate(180deg);
          }
        }
      }
    }
    .list-ul{
      // margin-top: 0.98rem;
      // margin-top:
      &.fixed{
        margin-top: 0.98rem;
      }
      padding: 0 0.1rem;
      li{
        margin-top: 0.1rem;
        background: #fff;
        padding: 0.15rem;
        border-radius: 0.08rem;
        position: relative;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
        .school{
          display: flex;
          align-items: center;
          padding-bottom: 0.1rem;
          border-bottom: 1px solid rgba(23, 6, 6, 0.08);
          font-size: 0.16rem;
          font-weight: 600;
          .icon{
            width: 0.6rem;
            height: 0.6rem;
            border-radius: 50%;
            margin-right: 0.1rem;
          }
        }
        .icon-text{
          margin-top: 0.1rem;
          display: flex;
          align-items: center;
          font-size: 0.13rem;
          color: rgba(0, 0, 0, 0.7);

          .icon-m{
            width: 0.14rem;
            height: 0.14rem;
            margin-right: 0.07rem;
            &.h2{
              height: 0.12rem;
            }
          }
        }
        .enroll-btn{
          position: absolute;
          width: 0.85rem;
          height: 0.3rem;
          text-align: center;
          color: #fff;
          right: 0.15rem;
          bottom: .15rem;
          font-size: 0.14rem;
          font-weight: 600;
          border-radius: 100px;
          background: linear-gradient(135deg, #F09190 0%, #F07877 66%, #F06E6C 100%);
        }
      }

    }
  }
</style>
