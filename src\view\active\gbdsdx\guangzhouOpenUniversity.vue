<template>
  <div class="main" :class="{ active: isIphoneX }">
    <inviteTop :inviteId="inviteId"></inviteTop>
    <div class="banner">
      <img src="../../../assets/image/active/guangzhouOpenUniversity/gzgbdsdx.png" alt="">
    </div>
    <div v-if="!hiddeleLottery" class="content" :class="{ 'bg-g': tabName != 'recruit' }">
      <div class="tab">
        <p :class="{ 'active': tabName == 'recruit' }" @click="tabName = 'recruit'"><span>招生主页</span></p>
        <p :class="{ 'active': tabName == 'introduce' }" @click="tabName = 'introduce'"><span>学校介绍</span></p>
        <p :class="{ 'active': tabName == 'common' }" @click="tabName = 'common'"><span>常见问题</span></p>
      </div>
    </div>
    <div v-if="hiddeleLottery" class="content" :class="{ 'bg-g': tabName != 'recruit' }">
      <div class="tab-3">
        <p :class="{ 'active': tabName == 'recruit' }" @click="tabName = 'recruit'"><span>招生主页</span></p>
        <p :class="{ 'active': tabName == 'introduce' }" @click="tabName = 'introduce'"><span>学校介绍</span></p>
        <p :class="{ 'active': tabName == 'common' }" @click="tabName = 'common'"><span>常见问题</span></p>
      </div>
    </div>
    <transition name="fade2">
      <div v-if="tabName == 'recruit'">
        <div class="schoolAdvantage">
          <div class="content">
            <div class="title">
              299元
            </div>
            <div class="detail">
              <ul>
                <li class="items"><i><img src="../../../assets/image/active/guangzhouOpenUniversity/ic_discount.png"
                      alt=""></i>
                  <p>考前辅导课(礼包价199)</p>
                </li>
                <li class="items"><i><img src="../../../assets/image/active/guangzhouOpenUniversity/ic_discount.png"
                      alt=""></i>
                  <p>三本辅导教材(礼包价100)</p>
                </li>
                <li class="items" v-if="Date.now() < new Date(2019, 6, 1, 9, 30, 0).getTime()"><i><img
                      src="../../../assets/image/active/guangzhouOpenUniversity/ic_discount.png" alt=""></i>
                  <p>成考过{{ score }}分, 奖励第三年全额学费; 成考过295分, 奖励三年全额奖学金</p>
                </li>
                <!-- <li class="items" v-else><i><img src="../../../assets/image/active/guangzhouOpenUniversity/ic_discount.png" alt=""></i><p>成考过295分，奖3年学费等值智米</p></li> -->
              </ul>
              <!-- <p>(上述考分不含政策性加分)</p> -->
            </div>
            <div>
              <button @click="enroll">立即购买</button>
            </div>
          </div>
        </div>
        <div class="charge">
          <div class="chargetop">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
            <p>院校专业及收费</p>
          </div>
        </div>
        <div class="highUniversityBox">
          <div class="highUniversity" v-if='pfsnLevel == 5 || !pfsnLevel'>
            <div class="title">
              <span class="text">- 高起专 -</span>
            </div>
            <div class="content">
              <div style="width:.5rem;text-align:center;padding-top:3%;border-right:1px solid rgba(240, 110, 108, 0.4)">
                外语类
              </div>
              <div class="flLeft">
                <p class="contentText">
                  商务英语
                </p>
              </div>
              <div class="flRight">
                <div class="item">
                  <p class="top">2300</p>
                  <p class="bottom">元/学年</p>
                </div>
              </div>
            </div>
            <div class="content">
              <div style="width:.5rem;text-align:center;padding-top:7%;border-right:1px solid rgba(240, 110, 108, 0.4)">
                文史类
              </div>
              <div class="flLeft">
                <p class="contentText">
                  工商企业管理、人力资源管理、现代物业管理、行政管理、大数据与会计、电子商务、现代物流管理、市场营销
                </p>
              </div>
              <div class="flRight">
                <div class="item">
                  <p class="top">2000</p>
                  <p class="bottom">元/学年</p>
                </div>
              </div>
            </div>
          </div>
          <p style="padding-left:.2rem;margin-top:.05rem;color: rgba(23, 6, 6, 0.6)"><span
              style="color:#F06E6C">•</span> 书杂费:&ensp;400元/学年(按3年收取)</p>
          <p style="padding-left:.2rem;color: rgba(23, 6, 6, 0.6)"><span style="color:#F06E6C">•</span> 学制:&ensp;3年</p>

          <!-- <p  style="color: rgba(23, 6, 6, 0.6);padding-left:.2rem;float:left;">
                    <span style="color:#F06E6C">•</span> 考区:</p>
                    <p style="width:75%;float:left;color:rgba(23,6,6,.6);    margin-left: .07rem;"> 惠州、广州、深圳、东莞、阳江、梅州、肇庆、湛江、汕尾、潮州、韶关、汕头、河源、佛山、茂名、江门</p> -->

        </div>
        <div class="story">
          <div class="top">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">

            <span>学员上进故事</span>
          </div>
          <div class="content">
            <router-link
              :to="{ name: 'scholarshipStoryInfo', query: { id: item.scholarshipId, inviteId: inviteId, resourcesUrl: item.resourcesUrl, createTime: item.createTime } }"
              class="item" v-for="(item, index) in storyList" :key="index">
              <img :src=item.articlePicUrl + storyImgLimit | imgBaseURL>
              <p class="text">{{ item.articleTitle }}</p>
              <p class="date">{{ item.createTime.substring(0, 11) }}</p>
            </router-link>
          </div>
        </div>
        <!-- <div class="userMessage">
          <div class="top">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
            <span class="topTitle">留言区</span>
          </div>
          <div class="textContent">
            <div class="userMessageContentList" :class="{anim:animatePraise==true}">
              <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                <div class="fl">
                  <img :src="item.headImg?item.headImg+headLimit:item.headImg |defaultAvatar2" alt="">
                </div>
                <div class="fr">
                  <p class="userName">{{item.nickname|hideNickname}}</p>
                  <div class="bottom" v-if=item.msgReply>
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                    <div class="content">
                      <div class="line"></div>
                      <p class="answer"><span>回复:&nbsp;</span>{{item.msgReply}}</p>
                    </div>
                  </div>
                  <div class="bottom2" v-if=!item.msgReply style="margin-top:.16rem">
                    <p class="uesrQuestion">{{item.msgContent}}</p>
                  </div>
                </div>
                <div class="line"></div>
              </div>
            </div>
          </div>
          <div class="userMessageContent">
            <div class="fl">
              <img :src="userImg?userImg+headLimit:userImg |defaultAvatar2" alt="">
            </div>
            <div class="fr">
              <p class="userName">{{userName}}</p>
              <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
              <span>{{message.length}}/50</span>
              <button @click="enrollMsg()">提交</button>
            </div>
          </div>
        </div> -->
      </div>
    </transition>
    <transition name="fade2">
      <div v-if="tabName == 'introduce'">
        <div class="schoolDetails" :class="{ showall: true, active: showall }">
          <img src="../../../assets/image/active/guangzhouOpenUniversity/school.png" alt="">
          <p class=schoolDetailsText>
            &emsp;&emsp;
            广州开放大学是由广州市政府主办的一所市属高等院校，创办于1961年9月，是全国建立最早的省级广播电视大学之一。是一所运用广播、电视、文字教材、音像教材、计算机课件和网络等多种媒体，面向广州地区开展远程开放教育的新型高等学校。
            <br>
            &emsp;&emsp;办学五十年来，学校共为广州经济建设输送各类学历教育毕业生达成30多万人，非学历教育结业人数累计四十多万，每年招收各类学生达到2.5万人。目前广州电大的毕业生占广州市同期高校毕业生的四成，占广州市成人高校毕业生的六成，广州电大的在校生人数占广州市同期在校生的三成，占广州市成人高校
            在校生的五成。有人粗略测算，在广州每三个大专毕业生中就有一个电大的毕业生。广州电大已成为引领广州现代远程高等教育的主力军，成为广州大众高等教育的骨干力量。
            <br><br>
          </p>
        </div>
        <div class="lookMore" :class="{ active: showall }" style="text-align:center" v-if="!showall">
          <a :class="{ active: showall }" @click="lookMore">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
            <span>查看更多</span>
          </a>
        </div>
        <div class="lookMore" :class="{ active: showall }" style="text-align:center" v-if="showall">
          <a :class="{ active: showall }" @click="lookMoreCancle">
            <span class="down"></span>
            <span>收起</span>
          </a>
        </div>
        <div class="studentStory">
          <div class="top">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
            <span class="topTitle">学员风采</span>
          </div>
          <div class="swiper">
            <swiper :options="swiperOption">
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/1.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/9.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/2.png">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/3.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/4.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/5.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/6.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/7.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/8.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/10.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/11.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/12.jpg">
              </swiper-slide>
              <swiper-slide>
                <img class="swiperImg" src="../../../assets/image/active/fullTimeSystem/student/13.jpg">
              </swiper-slide>
            </swiper>
          </div>
        </div>
        <!-- <div class="userMessage">
          <div class="top">
            <img src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="">
            <span class="topTitle">留言区</span>
          </div>
          <div class="textContent">
            <div class="userMessageContentList" :class="{anim:animatePraise==true}">
              <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
                <div class="fl">
                  <img :src="item.headImg?item.headImg+headLimit:item.headImg |defaultAvatar2" alt="">
                </div>
                <div class="fr">
                  <p class="userName">{{item.nickname|hideNickname}}</p>
                  <p class="uesrQuestion">{{item.msgContent}}</p>
                  <div class="content" v-if=item.msgReply>
                    <div class="line"></div>
                    <p class="answer"><span>回复:&nbsp;</span>{{item.msgReply}}</p>
                  </div>
                </div>
                <div class="line"></div>
              </div>
            </div>
          </div>
          <div class="userMessageContent">
            <div class="fl">
              <img :src="userImg |defaultAvatar2" alt="">
            </div>
            <div class="fr">
              <p class="userName">{{userName}}</p>
              <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
              <span>{{message.length}}/50</span>
              <button @click="enrollMsg">提交</button>
            </div>
          </div>
        </div> -->
      </div>
    </transition>
    <transition name="fade2">
      <div v-if="tabName == 'common'" style="padding-bottom: .6rem">
        <div class="texta">
          <van-collapse v-model="activeName" accordion>
            <van-collapse-item name="1">
              <div slot="title">
                <span class="indexText">Q1</span>自考会不会比较好
              </div>自考与成考都属于成人教育系列，均是国家承认的学历，证书学信网可查。
            </van-collapse-item>
            <van-collapse-item name="2">
              <div slot="title">
                <span class="indexText">Q2</span>有课上吗？
              </div>有的。购买了上进礼包后，会包含辅导课在里面的。
            </van-collapse-item>
            <van-collapse-item name="3">
              <div slot="title">
                <span class="indexText">Q3</span>怎么查询是否报考成功呀？
              </div>您可以在公众号-学员服务-远智学堂中查看状态，状态为“考前确认”即表示报考成功。
            </van-collapse-item>
            <van-collapse-item name="4">
              <div slot="title">
                <span class="indexText">Q4</span>考不上怎么办？
              </div>考试不难的，只要去考，认真答完试卷基本都能通过。
            </van-collapse-item>
            <van-collapse-item name="5">
              <div slot="title">
                <span class="indexText">Q5</span>应该很难考的吧，没考上可以不读吧？
              </div>考试不难的，在远智辅导的学员，参加成考的通过率高达93.8%。若真的没考上，可以选择不读。
            </van-collapse-item>
            <van-collapse-item name="6">
              <div slot="title">
                <span class="indexText">Q6</span>本科毕业的也能报吗，搞个双学位
              </div>可以的。
            </van-collapse-item>
            <van-collapse-item name="7">
              <div slot="title">
                <span class="indexText">Q7</span>一共考几科？
              </div>成人高考一共考3科，专科考“语文、数学、英语”，本科考“政治、英语、专业科”。
            </van-collapse-item>
            <van-collapse-item name="8">
              <div slot="title">
                <span class="indexText">Q8</span>可以自选居住地的考点吗？
              </div>选择了哪个城市就在哪个城市参考。
            </van-collapse-item>
            <van-collapse-item name="9">
              <div slot="title">
                <span class="indexText">Q9</span>有什么专业可选？
              </div>可以在链接中点击自己所在的城市查看。
            </van-collapse-item>
            <van-collapse-item name="10">
              <div slot="title">
                <span class="indexText">Q10</span>户口不在广东了可以考吗？
              </div>在广东工作就可以，不受户口限制。
            </van-collapse-item>
            <van-collapse-item name="11">
              <div slot="title">
                <span class="indexText">Q11</span>已经报名，然后咧？
              </div>报名后只需要利用碎片时间复习即可，另外会有助学老师跟进你的学习情况和对你入学前的指导。
            </van-collapse-item>
            <van-collapse-item name="12">
              <div slot="title">
                <span class="indexText">Q12</span>报名了，要缴费吗？
              </div>筑梦计划是免费报名的。可以自愿选择是否购买299元的上进礼包。
            </van-collapse-item>
            <van-collapse-item name="13">
              <div slot="title">
                <span class="indexText">Q13</span>免费报名的没辅导课辅导书，自己复习？
              </div>是的。
            </van-collapse-item>
            <van-collapse-item name="14">
              <div slot="title">
                <span class="indexText">Q14</span>要交考试费啊？
              </div>考试费是由教育局收取的，37元/科，3科合计111元，在9月初现场确认时缴纳。
            </van-collapse-item>
            <van-collapse-item name="15">
              <div slot="title">
                <span class="indexText">Q15</span>中专可以升大专吗？
              </div>中专已取得毕业证的同学可以报读成人大专。
            </van-collapse-item>
            <van-collapse-item name="16">
              <div slot="title">
                <span class="indexText">Q16</span>我在读大专，明年拿证，可以报本科吗？
              </div>如果是在明年3月1日前能取得大专毕业证的话，那么可以以应届生的身份参加今年的成人高考。
            </van-collapse-item>
            <van-collapse-item name="17">
              <div slot="title">
                <span class="indexText">Q17</span>报名要交什么资料的吗？
              </div>报名需要填写相关的个人报读信息以及提供身份证正反面、毕业证复印件。
            </van-collapse-item>
            <van-collapse-item name="18">
              <div slot="title">
                <span class="indexText">Q18</span>在职工作是不是真的可以报读？
              </div>可以。
            </van-collapse-item>
            <van-collapse-item name="19">
              <div slot="title">
                <span class="indexText">Q19</span>去哪里上课的？必须要去学校吗？
              </div>课程我们都是安排在周末或者晚上，通过手机直播上课或者是通过我们的远程上课平台学习即可。无需到学校去。
            </van-collapse-item>
            <van-collapse-item name="20">
              <div slot="title">
                <span class="indexText">Q20</span>考试是闭卷的还是开卷考？
              </div>闭卷考。
            </van-collapse-item>
            <van-collapse-item name="21">
              <div slot="title">
                <span class="indexText">Q21</span>初中毕业能报名吗？
              </div>抱歉，初中学历不符合报读资格。需要中专或者高中以上学历才可以。
            </van-collapse-item>
            <van-collapse-item name="22">
              <div slot="title">
                <span class="indexText">Q22</span>你好，教了资料费后怎么领资料？
              </div>会有老师在2个工作日内联系你，跟你确认报读信息以及收件地址。
            </van-collapse-item>
            <van-collapse-item name="23">
              <div slot="title">
                <span class="indexText">Q23</span>如考试未达295，那学费是多少？
              </div>如未达295分，学费按全价收取，相关院校专业的费用请在链接中查看。
            </van-collapse-item>
            <van-collapse-item name="24">
              <div slot="title">
                <span class="indexText">Q24</span>报名成功了，没说什么时候考试的？
              </div>报名成功后，在今年9月初现场确认办理准考证，10月中下旬参加成人高考。
            </van-collapse-item>
            <van-collapse-item name="25">
              <div slot="title">
                <span class="indexText">Q25</span>什么是筑梦计划？
              </div>筑梦计划是远智教育联合各合作高校为广大上进青年提供的一个提升学历的公益项目。
            </van-collapse-item>
          </van-collapse>
        </div>
        <!-- <div class="textContentJoin">
                        <div class="gdBox" :class="{anim:animate==true}">
                            <p v-for="(item,index) in items" :key="index">
                            <img :src="item.headImg|defaultAvatar"><span class="name">恭喜{{item.realName|hideName}}</span><span class="mobile">{{item.mobile|hidePhone(6)}}</span><span>成功报读</span>
                            </p>
                        </div>
        </div>-->
      </div>
    </transition>
    <img v-if="!hiddeleLottery" @click="lotterytips()"
      src="../../../assets/image/active/enrollmentHomepage/<EMAIL>" alt="" class="lottery" ref="inviteRef">
    <van-popup v-model="showPop">
      <div class="popupBox">
        <img src="../../../assets/image/active/openUniversity/<EMAIL>" class="icon" alt="">
        <p class="title">发送成功</p>
        <p class="text1">感谢你的提问, 我们会尽快解决你的疑惑！</p>
      </div>
    </van-popup>
    <share :title="shareObj.title" :desc="shareObj.desc" :regOrigin='regOrigin' :isActivity="true" :link="shareLink"
      :levelId="pfsnLevel" :scholarship="scholarship" :imgUrl="shareObj.img" ref='share' />
    <!-- <o-footer tabName="newintroduce" :Expired="enrollEnd" :inviteId="inviteId" @isIphoneX="setIphoneX" from="introduce" :actName="actName" :unvs="unvs" :scholarship='scholarship'></o-footer> -->
    <second-footer @enroll='enroll' />

  </div>
</template>

<script>
import appShare from '@/mixins/appShare';
import { swiper, swiperSlide } from 'vue-awesome-swiper';
import { toLogin, isEmployee, getIsInTimeByType } from '../../../common';
import share from '../../../components/active/lotteryShare';
import { Collapse, CollapseItem, Popup } from 'vant';
import { imgBaseURL, activityTime } from "../../../config";
import oFooter from "@/components/activePage/footer"
import inviteTop from "../enrollAggregate/components/invite-top"
import SecondFooter from "@/view/active/2021NewMain/components/second-footer";
import statistic from '@/view/active/2021NewMain/statistic.json';

let scholarship = '47'; //优惠类型

export default {
  mixins: [appShare],
  components: {
    Collapse,
    inviteTop,
    CollapseItem,
    swiper,
    swiperSlide,
    share,
    Popup,
    oFooter,
    SecondFooter
  },
  data() {
    return {
      tabName: 'recruit',
      activeName: '',
      animatePraise: false,
      animate: false,
      message: '',
      shareLink: '',
      isLogin: null,
      scholarship: scholarship,
      inviteId: '',
      isPay: true,
      started: true,
      isIphoneX: false,
      learnInfo: '',
      userName: '',
      showall: false,
      userImg: '',
      showInvite: false,
      items: [],
      invite: {},
      changeInfo: false,
      enrollMsgList: [],
      storyList: [],
      imgUrlL: "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/gjkf.png",
      hiddleLotteryTime: 1548680400000, //2019/1/28/21:00 隐藏抽奖入口
      hiddeleLottery: false,
      showPop: false,
      swiperOption: {
        initialSlide: 1,
        autoplay: 2000,
        centeredSlides: true,
        loop: true,
        slidesPerView: "auto",
        loopedSlides: 1,
        autoplayDisableOnInteraction: false
      },
      actName: '',
      enrollEnd: false,
      shareObj: { title: '读学历来广州开放大学，免费报名，学信网可查！', desc: '教育部备案的高等院校，超2万名同学等你一起来上进！', img: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/gdgbds.png' },
      content: '',
      storyImgLimit: '?x-oss-process=image/resize,m_fixed,h_122,w_155',
      loadingFlag: true,
      headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
      score: 185,
      show: true,
      isEmployee: isEmployee(),
      unvs: {
        unvsName: '广州开放大学',
        unvsId: '51'
      },
      isInvite5MonthActive: getIsInTimeByType('decisive'), // 邀约2020-5月活动
      pfsnLevel: '',
      regOrigin: 42
    }
  },

  created() {
    if (Date.now() > activityTime) {
      this.score = 190;
      scholarship = '164';
      this.scholarship = '164';
    }
    this.isLogin = !!this.storage.getItem('authToken');
    this.inviteId = this.$route.query.inviteId || '',
      this.action = this.$route.query.action || '';
    this.pfsnLevel = this.$route.query.pfsnLevel;
    this.shareLink = `${window.location.origin}/active/guangzhouOpenUniversity`;
    this.userName = this.storage.getItem('realName') || this.storage.getItem('zmcName') || this.storage.getItem('mobile');

    setInterval(this.scroll, 3000);
    this.isChangeInfo();
    // 切换前优惠类型改为30
    // let nowTime = new Date().getTime();
    //   let startTime = 1558087200000// 4月1日切换
    //   if(nowTime > startTime) {
    //       scholarship='36';
    //       this.scholarship = '36';
    //   }
    this.getStdLearnInfo();
    this.getActivityInfo();
    if (this.pfsnLevel) {
      this.$yzStatistic('marketing.base.browse', statistic.schoolPage[this.pfsnLevel].main.id, statistic.schoolPage[this.pfsnLevel].main.name);
    }
    this.initAppShare(() => {
      this.setShareParams({
        title: this.shareObj.title,
        content: this.shareObj.desc,
        url: '/active/guangzhouOpenUniversity',
        image: this.shareObj.img,
        regOrigin: this.regOrigin,
      });
    });
  },

  computed: {
    regChannel() {
      return this.$route.query.regChannel || '';
    },
    jumpUrl: function () {
      let url = {
        name: 'adultExamEnrollCheck',
        query: {
          unvs: JSON.stringify({
            unvsName: '广州开放大学',
            unvsId: '51'
          }),
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: scholarship,
          actName: this.actName,
          recruitType: '1',
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
        }
      };
      return url;
    }
  },
  mounted: function () {
    setInterval(() => {
      this.show = !this.show;
    }, 500)
    window.addEventListener('scroll', this.handleScroll, true);
    //获取邀约人信息
    if (!!this.inviteId) {
      this.getInviteInfo();
    } else {
      if (!this.changeInfo) {
        this.$refs.inviteRef.style.top = ".73rem";
        this.$refs.inviteRef.style.visibility = "visible";
      }
    }

  },
  methods: {
    setIphoneX() {
      this.isIphoneX = true
    },
    enroll: function () {
      if (!this.isOpenAppLogin()) {  // 该方法来自appShare.js
        return;
      }
      this.$router.push(this.jumpUrl);
    },
    //活动信息
    getActivityInfo() {
      this.$http.post('/mkt/getActivityInfo/1.0/', { scholarship: scholarship }).then(res => {
        let { code, body } = res;
        if (code !== '00') return;
        this.actName = body.actName;
        if (Date.now() > body.EndTime) {
          this.enrollEnd = true;
        }
      })
    },
    //监听滑动的距离
    handleScroll() {
      let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
      if (this.loadingFlag) {
        if (scrollTop > 330) {
          this.getEnrollMsgList();
          this.getStoryList();
          this.loadingFlag = false;
        }
      }
    },
    getContent() {
      this.$http.post('/bds/getActivityOtherConfig/1.0/', { typeCode: '2_guokai' }).then(res => {
        if (res.code === '00') {
          this.shareObj.title = res.body.title;
          this.shareObj.desc = res.body.content;
          this.shareObj.img = 'http:' + imgBaseURL + res.body.picUrl;
          this.$nextTick(res => {
            this.$refs.share.openNO();
          })
        }
      }).then(res => {
        this.$http.post('/bds/getActivityOtherConfig/1.0/', { typeCode: '1_guokai' }).then(res => {
          if (res.code === '00') {
            if (res.body.content) {
              this.content = res.body.content.split("\n");
            }
          }
        })
      })
    },
    // 根据时间切换内容
    isChangeInfo() {
      let nowTime = new Date().getTime();
      let changTime = 1548691200000; //1月29日00:00切换
      if (nowTime > this.hiddleLotteryTime) {
        this.hiddeleLottery = true;
      }
      if (nowTime > changTime) {
        this.changeInfo = true;
      }
    },
    // 获取学员基础信息
    getStdLearnInfo: function () {
      if (!this.isLogin) {
        return
      }
      let param = { grade: '2020', scholarship: this.scholarship };
      this.$http.post('/mkt/stdLearnInfo/1.0/', param).then(res => {
        let { code, body } = res;
        this.userName = body ? body.std_name : this.storage.getItem('realName');
        this.userImg = body ? body.headImg : this.storage.getItem('headImg');
        if (code !== '00' || body == null) return;
        let learnInfos = body.learnInfos.find(item => (item.scholarship == scholarship && item.grade == '2020')) || {};
        this.learnInfo = learnInfos;
        if (!!learnInfos.scholarship) {
          this.getPayInfo(learnInfos.learnId);
        }
      });
    },
    // 获取邀约人信息
    getInviteInfo() {
      let inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || this.getQueryString(this.redirect, 'inviteId') || '')).replace(/ /g, '+');
      if (inviteId) {
        this.$http.post('/us/getInviteInfo/1.0/', { inviteToken: inviteId }).then(res => {
          let { code, body } = res;
          if (code !== '00') return;
          this.invite = body || {};
          this.showInvite = true;
          this.$refs.inviteRef.style.top = "1.31rem";
          this.$refs.inviteRef.style.visibility = "visible";
        });
      } else {
        this.$refs.inviteRef.style.top = ".73rem";
        this.$refs.inviteRef.style.visibility = "visible";
      }
    },
    // 点击查看更多
    lookMore: function () {
      this.showall = true;
    },
    lookMoreCancle: function () {
      this.showall = false;
      const returnEle = document.querySelector('.schoolDetails');
      if (!!returnEle) {
        returnEle.scrollIntoView(true);
      }
    },

    // 获取学员基础信息
    getPayInfo: function (learnId) {
      let data = {
        learnId: learnId,
        itemCode: 'Y0'
      }
      this.$http.post('/mkt/selectTutionPaidCountByLearnId/1.0/', data).then(res => {
        let { code, body } = res;
        if (code !== '00') return;
        this.isPay = body.ifPay == '1' ? true : false;
      });
    },

    //广州开放大学报名
    gotoEnroll: function () {
      if (!this.isLogin) {
        this.$router.push({
          name: 'adultExamEnrollCheck', query: {
            unvs: JSON.stringify({
              unvsName: '广州开放大学',
              unvsId: '51'
            }), scholarship: scholarship, actName: this.actName, recruitType: '1'
          }
        });
      } else {
        this.$refs.share.isBindMobile('/active/adultExam/check', () => {
          this.$router.push({
            name: 'adultExamEnrollCheck', query: {
              unvs: JSON.stringify({
                unvsName: '广州开放大学',
                unvsId: '51'
              }), scholarship: scholarship, actName: this.actName, recruitType: '1'
            }
          });
        });
      }
    },
    //邀请好友报名
    openShare: function () {
      this.$refs.share.open(null, 'login', `${this.$route.fullPath}${this.$route.fullPath.includes('?') ? '&' : '?'}action=share`);
    },
    tips() {
      this.$modal({ message: '活动暂未开始！', icon: 'warning' });
    },
    lotterytips() {
      let now = new Date().getTime();
      let stop = 1548680400000;// 2019/1/28 21:00 抽奖入口关闭
      if (now > stop) {
        this.$modal({ message: '抽奖活动已经下线！', icon: 'warning' });
      } else {
        this.$router.push({ path: '/active/iphoneXLotteryThr' })
      }

    },

    // 获取留言列表
    getEnrollMsgList: function () {
      this.$http.post('/mkt/getEnrollMsgList/1.0/', { scholarship: scholarship }).then(res => {
        let { code, body } = res;
        if (code === '00') {
          this.enrollMsgList = body;
        }
      });
    },
    phoneNumber() {
      if (this.articleId) {
        window.location.href = 'tel:' + this.cardMobile;
        return;
      }
      window.location.href = 'tel:4008336013';
    },
    // 评论
    enrollMsg: function () {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return
      }
      if (!this.message) {
        this.$modal({ message: '请输入评论内容', icon: 'warning' });
        return
      }
      if (this.isEmployee) {
        this.$modal({ message: '招生老师不能评论！', icon: 'warning' });
        return
      }
      this.$http.post('/mkt/enrollMsg/1.0/', { scholarship: scholarship, msgContent: this.message }).then(res => {
        let { code, body } = res;
        if (code === '00') {
          this.showPop = true;
          setTimeout(() => {
            this.showPop = false;
          }, 5000)
          this.message = '';
          this.getEnrollMsgList();
        }
      });
    },

    // 获取故事列表
    getStoryList() {
      this.$http.post('/mkt/scholarshipStoryList/1.0/', { informationType: 1 }).then(res => {
        const { code, body } = res;
        if (code === '00') {
          this.storyList = body;
        }
      });
    },

    // 轮播用户评论
    scroll() {
      if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
        return
      }
      this.animatePraise = true;
      setTimeout(() => {
        this.enrollMsgList.push(this.enrollMsgList[0]);
        this.enrollMsgList.shift();
        this.animatePraise = false;
      }, 500)
    },
    // 获取最新注册用户列表
    getNewRegList: function () {
      this.$http.post('/us/getNewRegList/1.0/').then(res => {
        let { code, body } = res;
        if (code === '00') {
          this.items = body;
        }
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll, true);
  }
}
</script>


<style lang="less" scoped>
.main /deep/ .van-popup {
  border-radius: 0.1rem;
  top: 45%;
  text-align: center;
}

.texta {
  /deep/.van-collapse {
    .van-collapse-item {
      .van-cell {
        line-height: 0.54rem;
        height: auto;

        .van-cell__right-icon {
          margin-top: 3%;
        }

        .indexText {
          width: 0.15rem;
          height: 0.17rem;
          background: rgba(240, 110, 108, 0.1513);
          border-radius: 8px 8px 8px 0px;
          color: #f06e6c;
          font-size: 0.12rem;
          margin-left: 0.17rem;
          margin-right: 0.09rem;
        }
      }

      .van-collapse-item__wrapper {
        .van-collapse-item__content {
          padding: 0.13rem 0.56rem 0.13rem 0.44rem;
          color: rgba(23, 6, 6, 0.8);
        }
      }
    }
  }
}

.main {
  position: relative;
  background: rgb(246, 246, 246);

  &.active {
    padding-bottom: 34px;
  }

  .invite {
    background-image: url('../../../assets/image/active/enrollmentHomepage/invitBg2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;

    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }

    .rightView {
      float: left;
      margin-top: 2.3%;

      p {
        font-size: 0.16rem;

        span {
          color: #e15443;
          font-size: 0.13rem;
        }

        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }

  .banner {
    img {
      height: 1.11rem;
      width: 3.75rem;
    }
  }

  .content {
    &.bg-g {
      background-image: none;
    }

    .tab {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: rgba(246, 246, 246, 1);

      p {
        float: left;
        height: 0.38rem;
        width: 25%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;

        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }

        &.active {
          padding: 0rem;
          line-height: 0.38rem;

          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;

            &:before {
              content: '';
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }

        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }

    .tab-3 {
      height: 0.5rem;
      margin-top: -0.02rem;
      background: rgba(255, 255, 255, 1);

      p {
        float: left;
        height: 0.38rem;
        width: 33.3%;
        margin-top: 0.06rem;
        text-align: center;
        padding: 0.1rem 0;

        span {
          display: inline-block;
          width: 100%;
          border-right: 0.01rem rgba(23, 6, 6, 0.09);
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }

        &.active {
          padding: 0rem;
          line-height: 0.38rem;

          span {
            position: relative;
            width: auto;
            border-right: none;
            color: #f06e6c;
            border-radius: 0.02rem;

            &:before {
              content: '';
              height: 0.02rem;
              background-color: #f06e6c;
              width: 0.3rem;
              top: 0.42rem;
              transform: translateX(-50%);
              left: 50%;
              position: absolute;
              border-radius: 2px;
            }
          }
        }

        &:last-of-type {
          span {
            border-right: none;
          }
        }
      }
    }
  }

  .lottery {
    position: absolute;
    left: 2.76rem;
    float: left;
    width: 0.94rem;
    height: 0.86rem;
    top: 0.73rem;
    border: 0;
  }

  .schoolAdvantage {
    width: 3.75rem;
    height: 3.55rem;
    background: url('../../../assets/image/active/guangzhouOpenUniversity/bg2.png');
    background-size: 100% 100%;
    overflow: hidden;

    .content {
      width: 3.15rem;
      height: 3.55rem;
      // background-color:rgba(255,255,255,1);
      box-shadow: 0px 4px 20px 0px rgba(23, 6, 6, 0.1);
      border-radius: 10px;
      margin: 0 auto;
      margin-top: 0.2rem;
      overflow: hidden;
      text-align: left;

      // background: url('../../../assets/image/active/guangzhouOpenUniversity/redbg.png') no-repeat center center;
      // background-size: 100% 100%;
      .contentTile {
        width: 1.45rem;
        height: 0.32rem;
        position: absolute;
        background: linear-gradient(318deg, rgba(240, 110, 108, 1) 0%, rgba(240, 145, 144, 1) 100%);
        box-shadow: 0px 2px 4px 0px rgba(240, 110, 108, 0.2), 0px 1px 1px 0px rgba(255, 255, 255, 0.5);
        border-radius: 0px 0px 10px 10px;
        font-size: 0.17rem;
        font-weight: 600;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        margin-left: 1rem;
        margin-top: -0.05rem;
      }

      .title {
        width: 1.43rem;
        height: 0.2rem;
        background: url('../../../assets/image/active/guangzhouOpenUniversity/priceBg.png') no-repeat center;
        margin: 0 auto;
        margin-top: 0.55rem;
        background-size: 100% 100%;
        text-align: center;
        color: rgba(23, 6, 6, 0.8);

        span {
          text-decoration: line-through;
        }
      }

      .detail {
        margin-top: 0.13rem;

        .items {
          width: 2.77rem;
          margin: 0.01rem auto 0.1rem;
          margin-top: 0.34rem;
          height: auto;
          overflow: hidden;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 0.02rem;
          padding: 0.05rem 0.15rem 0.05rem 0.05rem;

          &:last-of-type {
            margin-bottom: 0;
          }

          i {
            font-style: normal;
            float: left;
            width: 0.26rem;
            margin-right: 0.1rem;

            img {
              width: 100%;
            }
          }

          p {
            width: 2.21rem;
            float: left;
            color: rgba(54, 54, 54, 0.8);
            font-size: 0.14rem;
            text-align: left;
            margin-top: 0.03rem;
            margin-left: 0;
          }
        }

        p {
          margin-left: 0.2rem;
          color: white;
          margin-top: 0.08rem;
        }
      }

      button {
        border: none;
        width: 2.57rem;
        height: 0.35rem;
        background: linear-gradient(to top, #ff8943, #ffb80d);
        box-shadow: 0px 3px 0px 0px rgba(251, 106, 20, 1);
        border-radius: 0.17rem;
        font-weight: 500;
        font-size: 0.16rem;
        color: rgba(255, 255, 255, 1);
        margin: 0.25rem auto 0;
        display: block;
        margin-top: 0.5rem;
      }
    }
  }

  .charge {
    background: rgb(255, 255, 255);
    width: 100%;
    height: auto;
    overflow: hidden;

    .chargetop {
      background: #fff;
      width: 3.55rem;
      height: 0.6rem;
      line-height: 0.6rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-left: 0.1rem;
        margin-top: -0.05rem;
      }

      p {
        display: inline-block;
        font-size: 0.17rem;
        height: 0.24rem;
        line-height: 0.24rem;
        color: #170606;
      }
    }

    .title1 {
      background: #fff;
      width: 100%;
      height: 0.28rem;

      .fl {
        width: 50%;
        height: 0.48rem;
        float: left;

        &:first-child {
          padding-left: 0.2rem;
        }

        .flTop {
          height: 0.48rem;
          line-height: 0.21rem;
          position: relative;

          .dot {
            position: absolute;
            display: inline-block;
            width: 0.04rem;
            height: 0.04rem;
            background-color: #f06e6c;
            margin-left: 0.22rem;
            margin-top: 0.09rem;
          }

          .bookPrice {
            display: inline-block;
            font-size: 14px;
            color: rgba(23, 6, 6, 0.4);
            font-weight: 400;
            left: 0.35rem;
            top: -0.02rem;
            width: 1.9rem;
            margin-left: 0.35rem;
            line-height: 0.17rem;
          }

          span {
            display: block;
            font-size: 20px;
            color: rgba(23, 6, 6, 0.6);
            margin-left: 0.35rem;
            line-height: 0.28rem;
          }
        }

        .bookYear {
          //padding-left: .05rem;
          font-size: 0.2rem;
          color: #f06e6c;
        }

        span {
          color: #f06e6c;
          font-size: 0.12rem;
        }
      }
    }
  }

  .highUniversityBox {
    width: 100%;
    padding: 0.1rem;
    padding-top: 0.08rem;
    background: #fff;
    overflow: hidden;

    .highUniversity {
      margin: auto 0.1rem;
      width: 3.33rem;
      background-color: #fff;
      border-radius: 0.1rem;
      // box-shadow:0px 4px 20px 0px rgba(0,0,0,0.2);
      border: 1px solid rgba(240, 110, 108, 0.4);

      .title {
        // width: 3.35rem;
        height: 0.5rem;
        line-height: 0.5rem;
        text-align: center;
        border-radius: 0.1rem 0.1rem 0rem 0rem;
        background: rgba(240, 110, 108, 0.0798);

        .lineLeft {
          width: 0.1rem;
          height: 0.02rem;
          border-radius: 0.01rem;
          background: #f06e6c;
        }

        .text {
          font-size: 0.17rem;
          color: #f06e6c;
          font-weight: 600;
        }
      }

      .content {
        border-top: 1px solid rgba(240, 110, 108, 0.4);
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-items: center;

        .flLeft {
          width: 2.12rem;
          border-right: 1px solid rgba(240, 110, 108, 0.4);

          .contentText {
            display: inline-block;
            width: 1.89rem;
            margin: 0.1rem 0.17rem 0.1rem 0.13rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
          }
        }

        .flRight {
          width: 0.69rem;
          text-align: center;
          line-height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .content {
            width: 0.42rem;
            background-color: aquamarine;
          }

          .item {
            .items {
              width: 100%;
              height: 0.48rem;
              border-bottom: solid 1px rgba(240, 110, 108, 0.4);

              &:last-of-type {
                border-bottom: none;
              }
            }

            &:nth-of-type(3) {
              margin: 0.36rem auto;
            }
          }

          p {
            margin-top: 0.04rem;
          }

          .top {
            color: #f06e6c;
            font-weight: 600;
            font-size: 0.15rem;
            line-height: 0.21rem;
          }

          .bottom {
            font-size: 0.11rem;
            color: #f06e6c;
          }
        }
      }
    }

    .bottomText {
      height: 0.48rem;
      margin-top: 0.1rem;

      .iconBox {
        width: 0.04rem;
        height: 0.04rem;
        display: inline-block;
        background: rgba(240, 110, 108, 1);
        margin-left: 0.3rem;
        margin-top: 0.08rem;
        position: absolute;
      }

      span {
        font-size: 0.14rem;
        color: rgba(23, 6, 6, 0.4);
        margin-left: 0.44rem;
      }

      p {
        display: inline-block;
        width: 2.8rem;
        font-size: 0.14rem;
        color: rgba(23, 6, 6, 0.6);
        margin-left: 0.44rem;
      }
    }
  }

  .clear {
    clear: both;
  }

  .story {
    background: rgb(255, 255, 255);
    margin-top: 0.1rem;
    height: 2.74rem;

    .top {
      width: 3.75rem;
      height: 0.5rem;
      line-height: 0.5rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        vertical-align: middle;
        margin-top: -0.05rem;
        margin-left: 0.1rem;
      }

      span {
        font-size: 0.17rem;
      }
    }

    .content {
      overflow-x: scroll;
      overflow-y: hidden;
      white-space: nowrap;
      position: relative;
      height: 2.26rem;

      .item {
        display: inline-block;
        width: 1.55rem;
        margin-left: 0.1rem;
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.04), 0px 1px 2px 0px rgba(0, 0, 0, 0.04),
          0px 2px 6px 0px rgba(0, 0, 0, 0.04);
        border-radius: 5px;
        height: 2.16rem;
        position: relative;

        img {
          width: 1.55rem;
          height: 1.22rem;
          border-radius: 5px 5px 0px 0px;
          object-fit: cover;
        }

        p {
          margin-left: 0.05rem;
        }

        .text {
          width: 1.39rem;
          font-size: 0.13rem;
          margin-top: 0.08rem;
          color: rgba(23, 6, 6, 0.8);
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
        }

        .date {
          position: absolute;
          font-size: 0.12rem;
          color: rgba(23, 6, 6, 0.4);
          bottom: 0.02rem;
        }
      }
    }
  }

  .userMessage {
    background: rgb(255, 255, 255);
    margin-bottom: 0.6rem;

    .top {
      width: 3.75rem;
      height: 0.7rem;
      line-height: 0.7rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }

      .topTitle {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }

    .textContent {
      overflow: hidden;
      height: 4.1rem;

      .userMessageContentList {
        width: 3.75rem;
        height: auto;
        overflow: hidden;

        &.anim {
          transition: all 1s;
          margin-top: -1.7rem;
        }

        .content {
          /*overflow: hidden;*/
          /*height: 1.33rem;*/
          position: relative;
          height: auto;
          overflow: hidden;
          min-height: 1.33rem;

          .line {
            position: absolute;
            bottom: 0.01rem;
            height: 1px;
            width: 2.76rem;
            left: 0.64rem;
            background-color: rgba(23, 6, 6, 0.08);
          }

          .fl {
            width: 0.64rem;

            img {
              width: 0.38rem;
              height: 0.38rem;
              float: right;
              border-radius: 50%;
              margin-top: 0.11rem;
            }
          }

          .fr {
            width: 3.11rem;

            /*height: 1.14rem;*/
            .userName {
              margin-left: 0.1rem;
              margin-top: 0.12rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.8);
            }

            .uesrQuestion {
              display: inline-block;
              width: 2.76rem;
              margin-top: 0.03rem;
              margin-left: 0.1rem;
              font-size: 0.14rem;
              color: rgba(23, 6, 6, 0.4);
            }

            .content {
              position: relative;
              margin-bottom: 0.09rem;
              height: auto;
              min-height: 0;

              .line {
                position: absolute;
                display: inline-block;
                width: 0.02rem;
                height: 0.14rem;
                background-color: #f06e6c;
                top: 0.17rem;
                left: 0.01rem;
              }

              .answer {
                display: inline-block;
                background: rgba(248, 248, 248, 1);
                border-radius: 3px 3px 3px 0px;
                width: 2.86rem;
                font-size: 0.14rem;
                color: rgba(23, 6, 6, 0.8);
                padding: 0.13rem 0.07rem 0.13rem 0.1rem;
              }
            }
          }
        }
      }
    }

    .userMessageContent {
      width: 3.75rem;
      overflow: hidden;

      .fl {
        width: 0.64rem;

        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }

      .fr {
        width: 3.11rem;
        position: relative;

        .userName {
          margin-left: 0.1rem;
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
          display: inline-block;
          width: 2.48rem;
          height: 0.24rem;
        }

        textArea {
          width: 2.87rem;
          height: 0.86rem;
          border-radius: 5px 5px 5px 5px;
          opacity: 0.9;
          padding: 0.13rem 0.1rem 0.13rem 0.1rem;
          margin-top: 0.03rem;
          color: #000;
        }

        span {
          position: absolute;
          font-size: 0.09rem;
          color: rgba(23, 6, 6, 0.4);
          top: 1.06rem;
          right: 0.305rem;
        }

        button {
          width: 1rem;
          height: 0.35rem;
          background: rgba(240, 110, 108, 1);
          border: 0;
          margin-top: 0.1rem;
          margin-left: 0.89rem;
          margin-bottom: 0.2rem;
          border-radius: 0.2rem;
          color: #ffffff;
          font-size: 0.16rem;
          font-weight: 500;
        }
      }
    }
  }

  .InvitationBg {
    img {
      width: 3.75rem;
      height: 1.5rem;
    }
  }

  .fixBtn {
    position: fixed;
    bottom: 0.01rem;
    right: 0.01rem;
    width: 1.25rem;
    height: 1.27rem;
    line-height: 0.4rem;
    text-align: center;
    color: #fff;
    z-index: 999;
    background-image: url('../../../assets/image/active/openUniversity/<EMAIL>');
    background-size: 100%;
    border-radius: 10px;

    a {
      display: block;
      height: 100%;
      color: #fff;
      font-size: 21px;
      text-decoration: none;
    }
  }

  .bottomBox {
    width: 3.75rem;
    height: 0.9rem;
    line-height: 0.9rem;
    text-align: center;
    margin-bottom: 0.6rem;

    a {
      color: #f06e6c;
      font-size: 0.17rem;
    }

    span {
      color: #f06e6c;
      display: inline-block;
      margin-top: 0.01rem;
    }

    .callUs {
      margin-right: 0.1rem;
    }

    .official {
      margin-left: 0.1rem;
    }
  }

  .popupBox {
    width: 2.83rem;
    height: 1.54rem;
    background: #fff;

    .icon {
      height: 0.32rem;
      width: 0.32rem;
      margin-top: 0.3rem;
      border-radius: 50%;
    }

    .title {
      color: rgba(23, 6, 6, 1);
      font-size: 0.17rem;
      margin-top: 0.14rem;
    }

    .text1 {
      margin-top: 0.05rem;
    }

    .text1 {
      margin-top: 0.04rem;
      color: rgba(23, 6, 6, 0.4);
      font-size: 0.12rem;
    }
  }

  .showall {
    height: 3.97rem;
  }

  .showall.active {
    height: auto;
  }

  .schoolDetails {
    width: 3.75rem;
    background: rgb(255, 255, 255);
    overflow: hidden;

    img {
      width: 3.55rem;
      height: 1.79rem;
      border-radius: 5px;
      margin: 0.1rem 0.1rem 0rem 0.1rem;
    }

    p {
      display: inline-block;
      width: 3.15rem;
      margin: 0.2rem 0.3rem 0.1rem 0.3rem;
      padding: 0rem 0.05rem 0rem 0.05rem;
    }
  }

  .lookMore {
    background: rgb(255, 255, 255);
    padding-top: 0.06rem;
    text-align: center;
    height: 0.54rem;
    line-height: 0.54rem;
    position: relative;

    span {
      color: rgba(23, 6, 6, 0.4);
    }

    img {
      position: absolute;
      top: -0.03rem;
      left: 1.7rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }

  .studentStory {
    background: rgb(255, 255, 255);
    height: 2.41rem;
    width: 3.75rem;
    margin-top: 0.1rem;

    .top {
      height: 0.7rem;
      width: 3.75rem;
      line-height: 0.7rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 0.1rem;
        vertical-align: middle;
        margin-top: -0.05rem;
      }

      span {
        font-size: 0.17rem;
        color: rgba(23, 6, 6, 0.8);
      }
    }
  }
}

.swiper {
  width: 100%;
  height: 1.6rem;
  overflow: hidden;
}

.swiper-slide {
  width: 80%;
  height: 160px;
}

.swiper-slide-active img {
  margin-top: 0;
  width: 100%;
  height: 100%;
}

.swiperImg {
  display: block;
  margin: 0 auto;
  margin-top: 3.5%;
  width: 85%;
  height: 85%;
  border-radius: 5px;
}

.van-cell {
  height: 0.54rem;
  line-height: 0.54rem;
}

.fixBottom {
  position: fixed;
  bottom: 0;
  height: 0.6rem;
  z-index: 9999;
  max-width: 640px;

  .leftBox {
    display: inline-block;
    width: 1.96rem;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;
    border-top: 1px solid #f06e6c;

    img {
      width: 0.39rem;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }

    span {
      display: inline-block;
    }

    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }

    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.13rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }

  .rightBox {
    display: inline-block;
    width: 1.79rem;
    height: 0.6rem;
    background: #f06e6c;
    float: left;
    position: relative;

    .line {
      position: absolute;
      display: inline-block;
      width: 1px;
      height: 0.25rem;
      background: rgba(255, 255, 255, 0.4);
      top: 0.16rem;
      left: 0.88rem;
    }

    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }

    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        opacity: 0.8;
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
  }
}

.fixBottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 0.6rem;
  z-index: 9999;

  .leftBox {
    display: inline-block;
    width: 52%;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;
    border-top: 1px solid #f06e6c;

    img {
      width: 0.39rem;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }

    span {
      display: inline-block;
    }

    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }

    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.14rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }

  .rightBox {
    display: inline-block;
    width: 48%;
    height: 0.6rem;
    background: #f06e6c;
    float: left;
    position: relative;

    .line {
      position: absolute;
      display: inline-block;
      width: 1px;
      height: 0.25rem;
      background: rgba(255, 255, 255, 0.4);
      top: 0.16rem;
      left: 0.88rem;
    }

    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }

    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        opacity: 0.8;
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
  }
}

.lotteryEntrance {
  position: fixed;
  right: 0.01rem;
  bottom: 1rem;
  width: 0.88rem;

  img {
    width: 100%;
  }
}
</style>


