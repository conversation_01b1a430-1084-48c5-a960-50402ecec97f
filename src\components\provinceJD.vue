<template>
  <mt-popup v-model="isShow" position="bottom">
    <div class="commonPop-box">
      <div class="commonPop-box-title">
        <span>所在地区</span>
        <span class="fr" @click="close">
          <img src="../assets/image/closeX.png" alt="">
        </span>
      </div>
      <div class="commonPop-box-content">
        <div class="addressJD-tab">
          <div class="fl" :class="{active:tabName=='province'}" @click="tabName='province'">
            <span v-if="provinceName">{{provinceName}}</span>
            <span class="none" v-else>请选择</span>
          </div>
          <transition name="slide-fade">
            <div class="fl" v-if="showCityTab" :class="{active:tabName=='city'}" @click="tabName='city'">
              <span v-if="cityName">{{cityName}}</span>
              <span class="none" v-else>请选择</span>
            </div>
          </transition>
          <transition name="slide-fade">
            <div class="fl" v-if="showDistrictTab" :class="{active:tabName=='district'}" @click="tabName='district'">
              <span v-if="districtName">{{districtName}}</span>
              <span class="none" v-else>请选择</span>
            </div>
          </transition>
          <transition name="slide-fade">
            <div class="fl" v-if="showStreetTab" :class="{active:tabName=='street'}" @click="tabName='street'">
              <span v-if="streetName">{{streetName}}</span>
              <span class="none" v-else>请选择</span>
            </div>
          </transition>
        </div>
        <div class="addressJD-content">
          <load-bar :isLoading="isLoading"></load-bar>
          <transition name="slide-fade2">
            <div class="addressJD-content-item" v-if="tabName=='province'">
              <ul>
                <li v-for="item in provinceArr" :class="{active:provinceName==item.name}"
                    @click="changeProvince(item.name,item.code)"><span>{{item.name}}</span></li>
              </ul>
            </div>
          </transition>
          <transition name="slide-fade2">
            <div class="addressJD-content-item" v-if="tabName=='city'">
              <ul>
                <li v-for="item in cityArr" :class="{active:cityName==item.name}"
                    @click="changeCity(item.name,item.code)"><span>{{item.name}}</span></li>
              </ul>
            </div>
          </transition>
          <transition name="slide-fade2">
            <div class="addressJD-content-item" v-if="tabName=='district'">
              <ul>
                <li v-for="item in districtArr" :class="{active:districtName==item.name}"
                    @click="changeDistrict(item.name,item.code)"><span>{{item.name}}</span></li>
              </ul>
            </div>
          </transition>
          <transition name="slide-fade2">
            <div class="addressJD-content-item" v-if="tabName=='street'">
              <ul>
                <li v-for="item in streetArr" :class="{active:streetName==item.name}"
                    @click="changeStreet(item.name,item.code)"><span>{{item.name}}</span></li>
              </ul>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </mt-popup>
</template>

<script>
  import {
    provinceName,
    cityName,
    districtName,
    provinceCode,
    cityCode,
    districtCode,
    provinceList,
    cityList,
    districtList
  } from '../common';

  import loadBar from '@/components/loadBar';

  export default {
    props: {
      types: {
        type: Number,
        default: 3
      },
      onlyGD: {
        default: 0
      },
      defaults: {
        type: Object,
        default: () => {
          return {
            provinceCode: '110000',
            cityCode: '110100',
            districtCode: '110101'
          }
        }
      }
    },
    data() {
      return {
        isShow: false,
        isLoading: false,
        provinceName: '',
        provinceCode: '',
        provinceArr: [
          {name: '北京', code: '1'},
          {name: '上海', code: '2'},
          {name: '天津', code: '3'},
          {name: '重庆', code: '4'},
          {name: '河北', code: '5'},
          {name: '山西', code: '6'},
          {name: '河南', code: '7'},
          {name: '辽宁', code: '8'},
          {name: '吉林', code: '9'},
          {name: '黑龙江', code: '10'},
          {name: '内蒙古', code: '11'},
          {name: '江苏', code: '12'},
          {name: '山东', code: '13'},
          {name: '安徽', code: '14'},
          {name: '浙江', code: '15'},
          {name: '福建', code: '16'},
          {name: '广东', code: '17'},],
        cityName: '',
        cityCode: '',
        cityArr: [],
        districtName: '',
        districtCode: '',
        districtArr: [],
        streetName: '',
        streetCode: '',
        streetArr: [],
        tabName: 'province',
        showCityTab: false,
        showDistrictTab: false,
        showStreetTab: false,
        newProvinceObj: {
          provinceName: '',
          cityName: '',
          districtName: '',
          streetName: ''
        }
      }
    },
    created() {

    },
    methods: {
      open: function (ProvinceObjJD) {
        this.isShow = true;
        if (ProvinceObjJD && ProvinceObjJD.provinceName) {
          this.setValues(ProvinceObjJD)
        }
      },
      close() {
        this.isShow = false;
      },
      async getProvince() {
        if(this.onlyGD==1){
          this.provinceArr=[{name: '广东', code: '19'}];
          return ;
        }

        // 获取省列表
        let res = await this.$http.post("/gs/getJDProvince/1.0/").then(res => {
          // console.log(res);
          let datas = res.body || [];

          if (typeof datas == 'string') {
            datas = JSON.parse(datas);
          }
          this.provinceArr = datas;
        })
      },
      async changeProvince(provinceName, provinceCode) {
        let cityArr = [
          {name: '广州市', code: '1'},
          {name: '深圳市', code: '1'},
          {name: '珠海市', code: '1'},
          {name: '汕头市', code: '1'},
          {name: '韶关市', code: '1'},
          {name: '河源市', code: '1'},
          {name: '梅州市', code: '1'},
          {name: '惠州市', code: '1'},
          {name: '汕尾市', code: '1'},
          {name: '东莞市', code: '1'},
          {name: '中山市', code: '1'},
          {name: '江门市', code: '1'},
          {name: '佛山市', code: '1'},
          {name: '阳江市', code: '1'},]
        if (provinceName == this.provinceName) {
          this.tabName = 'city';
          return
        }

        this.provinceName = provinceName;
        this.provinceCode = provinceCode;
        this.tabName = 'city';

        // 改变省时，重新赋值市列表，市默认为空,区和街道列表为空
        this.showCityTab = true;
        this.showDistrictTab = false;
        this.showStreetTab = false;
        this.cityName = '';
        this.cityArr = [];
        this.districtName = '';
        this.districtArr = [];
        this.streetName = '';
        this.streetArr = [];

        this.isLoading = true;

        let data = {id: provinceCode}
        this.$http.post(`/gs/getJDCity/1.0/`,data ).then(res => {
          if (res.code == '00') {
            // console.log(res);
            let datas = res.body || [];

            if (typeof datas == 'string') {
              datas = JSON.parse(datas);
            }

            this.cityArr = datas;

            this.isLoading = false;
          } else {
            this.isLoading = false;
          }
        })

        // 模拟请求
        // let that = this;
        // setTimeout(function () {
        //   that.cityArr = cityArr;
        //   that.isLoading = false;
        // }, 500)
      },
      async changeCity(cityName, cityCode) {
        let districtArr = [
          {name: '罗湖区', code: '1'},
          {name: '福田区', code: '1'},
          {name: '南山区', code: '1'},
          {name: '宝安区', code: '1'},
          {name: '光明新区', code: '1'},
          {name: '龙岗区', code: '1'},
          {name: '坪山新区', code: '1'},
          {name: '盐田区', code: '1'},
          {name: '龙华区', code: '1'},
          {name: '大鹏新区', code: '1'},]
        if (cityName == this.cityName) {
          this.tabName = 'district';
          return
        }

        this.cityName = cityName;
        this.cityCode = cityCode;
        this.tabName = 'district';

        // 改变市时，重新赋值区列表，区默认为空,街道列表为空
        this.showDistrictTab = true;
        this.showStreetTab = false;
        this.districtName = '';
        this.districtArr = [];
        this.streetName = '';
        this.streetArr = [];

        this.isLoading = true;
        let data = {id: cityCode}
        this.$http.post("/gs/getJDCounty/1.0/", data).then(res => {
          if (res.code == '00') {
            // console.log(res);
            let datas = res.body || [];

            if (typeof datas == 'string') {
              datas = JSON.parse(datas);
            }

            this.districtArr = datas;

            this.isLoading = false;
          } else {
            this.isLoading = false;
          }
        });

        // 模拟请求
        // let that = this;
        // setTimeout(function () {
        //   that.districtArr = districtArr;
        //   that.isLoading = false;
        // }, 500)
      },
      async changeDistrict(districtName, districtCode, flag) {
        if (flag == false) {
          let streetArr = [
            {name: '布吉街道', code: '1'},
            {name: '坂田街道', code: '1'},
            {name: '南湾街道', code: '1'},
            {name: '平湖街道', code: '1'},
            {name: '横岗街道', code: '1'},
            {name: '龙岗街道', code: '1'},
            {name: '龙城街道', code: '1'},
            {name: '坪地街道', code: '1'},]

          if (districtName == this.districtName) {
            this.tabName = 'district';
            return
          }

          this.districtName = districtName;
          this.districtCode = districtCode;
          this.tabName = 'street';

          // 改变区时，重新赋值街道列表，街道默认为空
          this.showStreetTab = true;
          this.streetName = '';
          this.streetArr = [];

          this.isLoading = true;
          let data = {id: districtCode}
          let that = this;
          let res = await this.$http.post("/gs/getJDTown/1.0/", data)
          if (res.code == '00') {
            // console.log(res);
            let datas = res.body || [];

            if (typeof datas == 'string') {
              datas = JSON.parse(datas);
            }

            if (datas.length<='0') {
              that.tabName = 'district';
              that.isLoading = false;
              that.showStreetTab = false;
              that.streetName = '';

              that.saveProvinceObj();
              that.close();
            }
            that.streetArr = datas;
            that.isLoading = false;
          } else {
            that.isLoading = false;
          }

          // 模拟请求
          // setTimeout(function () {
          //   let num=Math.random();
          //   if(num>0.5){
          //     that.streetArr = streetArr;
          //     that.tabName = 'street';
          //     that.isLoading = false;
          //     that.showStreetTab = true;
          //     that.streetName = '';
          //     //
          //   }else {
          //     that.streetArr = [];
          //     that.tabName = 'district';
          //     that.isLoading = false;
          //     that.showStreetTab = false;
          //     that.streetName = '';
          //     //
          //     that.saveProvinceObj();
          //     that.close();
          //   }
          //   that.isLoading = false;
          // }, 500)
          // return
        }

        let streetArr = [
          {name: '布吉街道', code: '1'},
          {name: '坂田街道', code: '1'},
          {name: '南湾街道', code: '1'},
          {name: '平湖街道', code: '1'},
          {name: '横岗街道', code: '1'},
          {name: '龙岗街道', code: '1'},
          {name: '龙城街道', code: '1'},
          {name: '坪地街道', code: '1'},]

        this.districtName = districtName;
        this.districtCode = districtCode;
        this.tabName = 'street';

        // 改变区时，重新赋值街道列表，街道默认为空
        this.showStreetTab = true;
        this.streetName = '';
        this.streetArr = [];

        this.isLoading = true;

        let data = {id: districtCode}
        let that = this;
        let res = await this.$http.post("/gs/getJDTown/1.0/", data)
        if (res.code == '00') {
          // console.log(res);
          let datas = res.body || [];

          if (typeof datas == 'string') {
            datas = JSON.parse(datas);
          }

          if (datas.length<='0') {
            that.tabName = 'district';
            that.isLoading = false;
            that.showStreetTab = false;
            that.streetName = '';

            that.saveProvinceObj();
            that.close();
          }
          that.streetArr = datas;
          that.isLoading = false;
        } else {
          that.isLoading = false;
        }

        // 模拟请求
        // setTimeout(function () {
        //   let num=Math.random();
        //   if(num>0.5){
        //     that.streetArr = streetArr;
        //     that.tabName = 'street';
        //     that.isLoading = false;
        //     that.showStreetTab = true;
        //     that.streetName = '';
        //     //
        //   }else {
        //     that.streetArr = [];
        //     that.tabName = 'district';
        //     that.isLoading = false;
        //     that.showStreetTab = false;
        //     that.streetName = '';
        //     //
        //     that.saveProvinceObj();
        //     that.close();
        //   }
        //
        //   that.isLoading = false;
        // }, 500)

      },
      changeStreet: function (streetName, streetCode) {
        this.streetName = streetName;
        this.streetCode = streetCode;
        this.saveProvinceObj();
        this.close();
      },
      setValues: function (ProvinceObjJD) {
        if (ProvinceObjJD.provinceName) {
          this.changeProvince(ProvinceObjJD.provinceName, ProvinceObjJD.provinceCode);
          this.changeCity(ProvinceObjJD.cityName, ProvinceObjJD.cityCode);
          if (!ProvinceObjJD.streetName) {
            this.changeDistrict(ProvinceObjJD.districtName, ProvinceObjJD.districtCode, false);
            this.tabName = 'district'
          } else {
            this.changeDistrict(ProvinceObjJD.districtName, ProvinceObjJD.districtCode, true)
          }
          this.streetName = ProvinceObjJD.streetName;
        }
      },
      saveProvinceObj: function () {
        this.newProvinceObj.provinceName = this.provinceName;
        this.newProvinceObj.provinceCode = this.provinceCode;
        this.newProvinceObj.cityName = this.cityName;
        this.newProvinceObj.cityCode = this.cityCode;
        this.newProvinceObj.districtName = this.districtName;
        this.newProvinceObj.districtCode = this.districtCode;
        this.newProvinceObj.streetName = this.streetName;
        this.newProvinceObj.streetCode = this.streetCode;

        this.$emit('JDProvince', this.newProvinceObj);
      }
    },
    watch: {
      // provinceCode: function () {
      //   // this.setCitySlotValue();
      // }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/commonPopBox";
  @import "../assets/less/variable";

  .mint-popup {
    transition: .3s ease-out;
  }

  .commonPop-box {
    height: 70vh;
    .commonPop-box-title {
      text-align: center;
      font-size: .16rem;
      height: .4rem;
      line-height: .4rem;
      position: relative;
      .fr {
        position: absolute;
        top: 0;
        right: 0;
        width: .4rem;
        height: .4rem;
        padding: .13rem;
        img {
          width: 100%;
        }
      }
    }
    .commonPop-box-content {
      .addressJD-tab {
        height: .3rem;
        line-height: .3rem;
        position: relative;
        &:after {
          .borderBottom;
        }
        .fl {
          padding: 0 .1rem;
          &.active {
            span {
              border-bottom: 1px solid #e85b57;
            }
          }
          span {
            display: inline-block;
            height: .3rem;
            line-height: .3rem;
            font-size: .13rem;
          }
          .none {
            color: #e85b57;
          }
        }
      }
      .addressJD-content {
        position: relative;
        height: 55vh;
        /*overflow: hidden;*/
        .addressJD-content-item {
          /*position: absolute;*/
          /*left: 0;*/
          /*top: 0;*/
          /*width: 100%;*/
          /*height: 3.8rem;*/
          /*overflow: scroll;*/
          height: 55vh;
          overflow: hidden;
          margin-top: .02rem;
          ul {
            height: 55vh;
            overflow: scroll;
            li {
              height: .4rem;
              line-height: .4rem;
              font-size: .13rem;
              padding: 0 .15rem;
              &.active {
                span {
                  color: #e85b57;
                  position: relative;
                  &:after {
                    content: ' ';
                    width: .26rem;
                    height: .26rem;
                    position: absolute;
                    background-image: url("../assets/image/selected.png");
                    background-size: 100%;
                    right: -0.25rem;
                    top: -.05rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 可以设置不同的进入和离开动画 */
  /* 设置持续时间和动画函数 */
  .slide-fade-enter-active {
    transition: all .3s ease;
  }

  .slide-fade-leave-active {
    transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  }

  .slide-fade-enter, .slide-fade-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */ {
    transform: translateX(-10px);
    opacity: 0;
  }

  .slide-fade2-enter-active {
    transition: all .5s ease;
  }

  .slide-fade2-leave-active {
    transition: all .1s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  }

  .slide-fade2-enter, .slide-fade2-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */ {
    transform: translateX(-20px);
    opacity: 0;
  }
</style>
<style lang="less">
  @import "../assets/less/variable";

  .mint-popup-bottom {
    width: 100%;
  }

  .picker-items {
    .picker-slot {
      font-size: .16rem;
    }
  }

  .picker-toolbar {
    height: .5rem;
    line-height: .5rem;
    text-align: center;
    font-size: .16rem;
  }

  .loadmore {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 50%;
  }
</style>
