<template>
    <div class="success_wrap">
        <div class="box">
          <div class="img">
            <img src="../../../assets/image/active/321Activity/top_2.png" alt="">
            <p>已上传成功</p>
          </div>
          <div class="media">
            <div class="audio" v-if="type=='audio'" @click="play" preload="load">{{parseInt(duration)}}
              <img src="../../../assets/image/active/321Activity/''@2x.png" alt="">
              <img src="../../../assets/image/active/321Activity/audio_icon.png" alt="">
              <audio :src="audio" ref="audio"></audio>
            </div>

            <div class="video" v-if="type=='video'" @click="play">
             <template v-if="state!='play'">
               <img :src="video+'?x-oss-process=video/snapshot,t_1000,f_jpg'" alt="">
               <img src="../../../assets/image/active/321Activity/play_black.png" alt="">
             </template>
              <video :src="video" ref="video"></video>
            </div>
            <p v-if="type=='audio'">点击试听</p>
            <p v-if="type=='video'">点击试看</p>
          </div>
          <div class="btns">
            <!--<button @click="upload">重新上传</button>-->
            <button @click="publish">保存发表</button>
          </div>
        </div>
      <img class="beauty" src="../../../assets/image/active/321Activity/beaty.png" alt="">
    </div>
</template>

<script>
  import config from "../../../config"
    export default {
        name: "success",
      props:["type","video","audio","duration","showPic"],
      data(){
          return{
            videoDuration:0,
            state:''
          }
      },
      created(){
        console.log(this.type);
      },
      mounted(){
        if(this.type=='video'){
          this.$refs.video.addEventListener('pause',()=>{
              this.state='paused'
          })
        }
      },
      methods:{
        upload(){
          this.$emit('upload')
        },
        isPlay(){
          if(this.$refs.zom.children[0]){
            //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
            if(this.$refs.zom.children[0].paused){
              this.music=false;
            }
          }
        },
        stop(){
          if(this.$refs.zom.children[0]!==null){
            //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
            if(!this.$refs.zom.children[0].paused){
              this.$refs.zom.children[0].pause();// 这个就是暂停
            }
          }
        },
        play(){
          //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
           if(this.type=='audio'){
             this.$refs.audio.paused?this.$refs.audio.play():this.$refs.audio.pause()
           }else{
             this.$refs.video.paused?this.$refs.video.play():this.$refs.video.pause()
             this.$refs.video.paused?this.state='paused':this.state='play'
           }
        },
          publish(){
            this.$emit('publish')
            // this.$http.post('/mkt/publishUpDeclaration/1.0/',{resourcesUrl:this.url,udType:this.type=='audio'?1:2}).then(res=>{
            //   if(res.code=='00'){
            //
            //   }
            // })
          }
      },
    }
</script>

<style scoped lang="less">
.success_wrap{
  width: 100%;
  height: auto;
  overflow: hidden;
  min-height: 100vh;
  background-color: rgba(181, 19, 14, 1);
  background-image: url("../../../assets/image/active/321Activity/rule_bg.png");
  background-size: 100% 100%;
  z-index: 1000;
  position: fixed;
  left:0;
  top:0;
  .box{
    width: 3.45rem;
    height: 6.12rem;
    overflow: hidden;
    background-color: white;
    border-radius: .18rem;
    box-shadow: 0 0 .2rem 0 rgba(248, 137, 49, 1) inset;
    margin: .34rem auto 0;
    position: relative;
    z-index: 2;
    .img{
      width: 3.11rem;
      height:.5rem;
      margin: .15rem auto;
      position: relative;
      img{
        width: 100%;
      }
      p{
        position: absolute;
        width: 100%;
        line-height: .5rem;
        text-align: center;
        top:0;
        font-size: .2rem;
        color: rgba(248, 137, 49, 1);
        font-weight: bold;
        font-family: initial;
      }
    }
    .media{
      width: 100%;
      padding: 0 .56rem;
      height: 3.67rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: .12rem;
      .audio{
        width: 2.24rem;
        height: .32rem;
        border-radius: .04rem;
        background-color: rgba(132, 201, 102, 1);
        font-size: .16rem;
        line-height: .32rem;
        padding-left: .13rem;
        margin: .18rem auto .23rem;
        img:first-of-type{
          width: .06rem;
          height: .06rem;
          margin-top: .04rem;
        }
        img:last-of-type{
          width: .11rem;
          height: .14rem;
          margin-top: .1rem;
          margin-left: .05rem;
        }
      }
      .video{
        float: left;
        width: 2.32rem;
        height: 3.34rem;
        position: relative;
        margin: 0 auto .16rem;
        border: solid .03rem rgba(248, 137, 49, 1);
        border-radius: .1rem;
        &:before{
          content: "";
          width: 100%;
          height: 100%;
          position: absolute;
          left:0;
          top:0;
          background-color: rgba(0, 0, 0, .1);
          z-index: 2;
        }
        img:first-of-type{
          width: 100%;
          height: 100%;
          position: absolute;
          left:0;
          top:0
        }
        video{
          width: 100%;
          height: 100%;
        }
        img:last-of-type{
          position: absolute;
          width: .32rem;
          height: .32rem;
          left: 0;
          right: 0;
          top:0;
          bottom:0;
          margin: auto;
        }
      }
      p{
        text-align: center;
        color: rgba(153, 153, 153, 1);
        font-size: .12rem;
      }
    }
    .btns{
      margin-top: .34rem;
      button{
        width: 1.7rem;
        height: .4rem;
        display: block;
        text-align: center;
        font-size: .16rem;
        border-radius: .28rem;
        margin: .1rem auto 0;
        /*&:first-of-type{*/
          /*border: solid 1px rgba(252, 190, 95, 1);*/
          /*color: rgba(252, 190, 95, 1);*/
          /*background-color: white;*/
        /*}*/
        &:first-of-type{
          color: white;
          background-image: linear-gradient(to bottom,rgba(252, 190, 95, 1),rgba(248, 137, 49, 1));
          margin-top: .15rem;
          box-shadow: 0 0 .09rem 0 rgba(235, 83, 0, 1);
          border-bottom:solid .03rem rgba(235, 83, 0, 1) ;
          border-right:solid .02rem rgba(235, 83, 0, 1) ;
        }
      }
    }
  }
  .beauty{
    display: block;
    width: 3.3rem;
    height: .83rem;
    margin: -.3rem auto 0;
  }
}
</style>
