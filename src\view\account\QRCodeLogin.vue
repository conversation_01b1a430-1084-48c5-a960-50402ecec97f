<template>
    
</template>


<script>
import { Dialog } from 'vant';
export default {
    data(){
        return{
            id:'',
        }
    },
    created() {
        this.id=this.$route.query.id;
        Dialog.confirm({
            title: '登录',
            message: '请确认是否登录!'
            }).then(() => {
            this.sendQrCodeId();
            }).catch(() => {
            // on cancel
            });

    },
    methods:{

    sendQrCodeId() {
        this.$indicator.open();
        this.$http.post('/us/loginFinanceForQR/1.0/',{id:this.id}).then((res=>{
            this.$indicator.close();
            const {code,body} = res;
            if(typeof body !=='string') return;
            alert(body);
        }))
    }
    }




}
</script>
