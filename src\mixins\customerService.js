/**
 * 
 * 统一七鱼客服与微信客服配置混入文件
 *  
 * */ 
import { isLogin } from '@/common'
import { Toast } from 'vant'

export default {
  data() {
    return {
      defaultAvatar: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/daifu_nan.png',
      serviceUserId: ''
    }
  },
  mounted() {
    console.log('混入文件customerService: ', this.$router.currentRoute)
    this.serviceUserId = this.storage.getItem('userId')
    this.installService()
  },
  methods: {
    // 初始化js文件
    installService() {
      ;(function (w, d, n, a, j) {
        w[n] =
          w[n] ||
          function () {
            return (w[n].a = w[n].a || []).push(arguments)
          }
        j = d.createElement('script')
        j.async = true
        j.src = 'https://qiyukf.com/script/0248ef48611d21c4ca8efd4e7331d4ef.js?hidden=1'
        d.body.appendChild(j)
      })(window, document, 'ysf')
    },
    // 打开客服聊天：配置服务
    openCustomerService(isWx = 0) {
      console.log('打开客服聊天：配置服务', isWx)
      // 微信客服
      if (Number(isWx) === 1) {
        window.location.href = process.env.wechatKFUrl
        return
      }
      // 七鱼客服
      if (!isLogin()) {
        return
      }
      if (!this.serviceUserId) {
        Toast('在线客服正在初始化，请稍后再试。')
        this.getUserInfoById()
        return
      }
      const headImg = this.storage.getItem('headImg')
      const realName = this.storage.getItem('realName') || this.storage.getItem('zmcName')
      const mobile = this.storage.getItem('mobile')
      console.log('配置服务.storage', this.storage)
      ysf('onready', () => {
        ysf('config', {
          uid: this.serviceUserId, // 用户Id
          name: realName || mobile, // 用户名称
          mobile: mobile, // 用户电话
          email: '',
          data: JSON.stringify([{ index: 4, key: 'avatar', label: '头像', value: headImg || this.defaultAvatar }]),
          // robotShuntSwitch: 1 ,   // 机器人优先开关
          success: function () {
            // 成功回调
            console.log('七鱼客服配置成功')
            ysf('open')
            /**
             * home courseDetail selfDetail  newCourseDetail outCashDetails
                ysf('open', {
                  templateId:123
                });
             */
          },
          error: function () {
            console.log('七鱼客服配置失败')
          }
        })
      })
      // 不确定是不是全部都需要
      // window.location.href='https://qiyukf.com/client?k=0248ef48611d21c4ca8efd4e7331d4ef&wp=1';
    },
    // 获取用户信息
    async getUserInfoById() {
      const res = await this.$http.post('/us/getUserInfoById/1.0/')
      if (res.code == '00' && res?.body) {
        const { userId, realName, headImg } = res.body
        this.serviceUserId = userId
        this.storage.setItem('userId', userId)
        this.storage.setItem('realName', realName)
        this.storage.setItem('headImg', headImg)
        // 这个需要测试
        console.log('获取用户信息.', this.$route.query)
        if (this.$route.query?.autoOpen) {
          this.openCustomerService(0)
        }
      }
    }
  }
}
