<template>
  <div class="yz-growth-system">
    <share title='上进青年必须做的事' desc='快来看看你完成了多少？' :link='link' imgUrl='http://yzims.oss-cn-shenzhen.aliyuncs.com/lALPBFuNb4.png' />
     <img
        v-show='showMusicIcon'
        src="../../../assets/image/active/growthSystem/m.png"
        class="music-icon"
        :class='{start: audioPlay, p1: showIndex == 1, p2: showIndex == 3}'
        @touchstart.stop='()=>{}'
        @click.stop='musicIcon'
        alt=""
      >
    <audio src="../../../../static/cztx.mp3" ref='audio' preload loop autoplay controls class="audio" id='pro-audio'></audio>
    <v-loading v-show='loading' @finish='loadingFinish'></v-loading>
    <v-start v-show='showStart' :class='{show: showIndex == 0}' :start='!loading' @finish='finish'></v-start>
    <v-select v-show='preload' :class='{show: showIndex == 1}' :start='showIndex == 1' @finish='finish'></v-select>
    <v-input v-show='preload' :class='{show: showIndex == 2}' @finish='finish'></v-input>
    <v-final
      v-show='preload'
      :class='{show: showIndex == 3}'
      :name='inputName'
      :selects='selectIndex'
      :show='showIndex == 3'
      :headImg='headImg'></v-final>
  </div>
</template>

<script>
import { isWeixin, isIOS } from '@/common';
import config from "@/config";
import share from '@/components/share';
import vLoading from './loading';
import vStart from './start';
import vSelect from './select';
import vInput from './input';
import vFinal from './final';

export default {
  components: {
    vLoading,
    vSelect,
    vStart,
    vInput,
    vFinal,
    share,
  },
  data() {
    return {
      audioPlay: false,
      showIndex: 0,
      preload: false,
      selectIndex: [],
      inputName: '',
      loading: true,
      showMusicIcon: false,
      noPlay: false,
      showStart: false,
      link: '',
      appid: config.appid,
      code: '',
      headImg: '',
    };
  },
  created() {
    if (isWeixin()) {
      this.code = this.$route.query.code || '';
      this.scope = this.$route.query.state || "snsapi_userinfo";
      this.headImg = this.storage.getItem('growheadImg');
      if (!this.headImg) {
        if (!this.code) {
          this.gotoAuth();
        } else {
          this.getUserHeadImg();
        }
      }
    }
  },
  mounted() {
    MtaH5.clickStat("growthsystem");
    window['stopMusic'] = () => {
      this.stopMusic();
    };
    this.link = window.location.href;
    const audio = document.getElementById('pro-audio');
    if (isIOS()) {
      audio.removeAttribute('autoplay');
    }
    document.body.addEventListener("touchstart", () => {
      if (isIOS() && !this.loading && this.audioPlay) {
        audio.play();
      }
    });
  },
  beforeDestroy() {
    document.body.removeEventListener("touchstart",() => {});
    document.getElementById('pro-audio').pause();
  },
  methods: {
    // 微信静默授权
    gotoAuth: function () {
      let redirectUri = window.location.href
        .replace(/(&?code=[^&]*)|(&?state=[^&]*)/g, "")
        .replace(/\?&+/g, "?");
      window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${
        this.appid
      }&redirect_uri=${encodeURIComponent(
        redirectUri
      )}&response_type=code&scope=${this.scope}&state=${
        this.scope
      }#wechat_redirect`;

    },
    async getUserHeadImg() {
      const res = await this.$http.post('/us/getUserHeadImg/1.0/', { code: this.code });
      if (res.code == '00') {
        this.headImg = res.body.headImgUrl;
        this.storage.setItem('growheadImg', this.headImg);
      }
    },
    loadingFinish() {
      this.loading = false;
      this.showStart = true;
      this.$nextTick(() => {
        this.playAudio();
      });
      setTimeout(() => {
        this.preload = true;
      }, 1000);
    },
    finish({ index, selects, name }) {
      if (!this.preload) {
        return;
      }
      this.showIndex = index + 1;
      if (index == 1) {
        this.selectIndex = selects;
      }
      if (index == 2) {
        this.inputName = name;
      }
    },
    playAudio() {
      this.audioPlay = true;
      if (!this.loading) {
        this.showMusicIcon = true;
      }
      if (isWeixin()) {
        document.addEventListener('WeixinJSBridgeReady', () => {
          document.getElementById('pro-audio').play();
        }, false);
        this.audioPlay = true;
      }
    },
    musicIcon() {
      this.audioPlay = !this.audioPlay;
      if (this.audioPlay) {
        this.$refs.audio.play();
      } else {
        this.$refs.audio.pause();
      }
    },
    stopMusic() {
      document.getElementById('pro-audio').pause();
    },
  }
};
</script>

<style lang="less">
  @import '../../../assets/alph/index.css';
  .yz-growth-system{
    color: #fff;
    font-family: alph;
    .audio{
      position: absolute;
      z-index: 0;
      opacity: 0;
      left: 0;
      top: 0;
    }
    .show{
      z-index: 3;
    }
  }
  .music-icon{
    position: absolute;
    top: 0.08rem;
    right: 0.41rem;
    width: 0.32rem;
    height: 0.32rem;
    // animation: music 2s infinite linear;
    transform: rotate(0deg);
    z-index: 100;
    &.p1{
      right: 0.12rem;
      top: 0.8rem;
    }
    &.p2{
      right: 0.04rem;
      top: 0.08rem;
    }
    &.start{
      animation: music 2s infinite linear;
    }
  }
  @keyframes music {
    0%{
      transform: rotate(0deg)
    }
    100%{
      transform: rotate(360deg)
    }
  }
</style>
