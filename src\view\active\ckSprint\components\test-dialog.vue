<template>
  <dialog-container v-model="show" @close='close'>
    <div class="yz-ck-testDialog">
      <div class="t1"></div>
      <div class="line"></div>
      <div class="test-box">
        <button @click='startDraw'>开始<br />抽签</button>
      </div>
      <p class="num">剩余: {{drawCount}}次</p>
      <p v-if='showRecord' class="t3" @click='openList'>我的抽签记录</p>
    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container.vue';

export default {
  components: { DialogContainer },
  data () {
    return {
      show: true,
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    showRecord: {
      type: Boolean,
      default: false,
    },
    drawCount: {
      type: [Number, String],
      default: 0,
    },
  },
  watch: {
    value (val) {
      this.show = val;
    },
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '17', '抽签页');

    this.show = this.value
  },
  methods: {
    close () {
      this.show = false;
      this.$emit('input', false);
    },
    openList () {
      this.$yzStatistic('sprintAct.base.click', '10', '我的抽签记录');
      this.close();
      this.$emit('openList');
    },
    startDraw () {
      this.$yzStatistic('sprintAct.base.click', '9', '开始抽签');
      this.close();
      this.$emit('startDraw');
    },
  },
};
</script>

<style lang="less">
.yz-ck-testDialog {
  text-align: center;
  padding-top: 0.59rem;
  line-height: 1.2;
  .t1 {
    background: url(../../../../assets/image/active/ckSprint/test.png) no-repeat;
    background-size: 100% 100%;
    width: 2.22rem;
    height: 0.31rem;
    margin: 0 auto 0.21rem;
  }
  .line {
    background: url(../../../../assets/image/active/ckSprint/line.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 0.51rem;
    margin: 0 auto 0.17rem;
  }
  .t3 {
    color: #f9d65b;
    font-size: 0.12rem;
    text-align: center;
    text-decoration: underline;
    margin-top: 0.05rem;
  }
  .num {
    margin-top: 0.28rem;
    color: #f9d65b;
    font-size: 0.12rem;
    text-align: center;
  }
}
.test-box {
  text-align: center;
  button {
    width: 0.93rem;
    height: 0.93rem;
    background: linear-gradient(180deg, #fbe472 0%, #f6c43e 100%);
    box-shadow: 0px 0px 0.05rem 0px rgba(255, 255, 255, 0.61);
    border-radius: 50%;
    color: #d0342a;
    font-size: 0.24rem;
    font-weight: 600;
    transform: scale(0.7);
    animation: heart 2s linear infinite;
  }
}
@keyframes heart {
  0% {
    transform: scale(0.7);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(0.7);
  }
}
</style>
