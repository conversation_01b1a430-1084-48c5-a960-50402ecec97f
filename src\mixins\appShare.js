// 关于app分享的一些公用方法封装
// app与h5交互文档 http://doc.yzwill.cn/docs/gx/doc_js_app
import bridge from '@/plugins/bridge';
import { AppShareUrl } from '@/config'
import { isAndroid, isIOS, isLogin } from "@/common";
import { toAppLogin } from '@/common/jump';

export default {
  data() {
    return {
      isAppOpen: false,
      shareParams: {},
    };
  },
  mounted(){},
  methods:{
    isOpenAppLogin() {
      if (this.isAppOpen && !isLogin()) {
        toAppLogin();
        return false;
      }
      return true;
    },
    initAppShare(callback) {
      bridge.callHandler('isAppOpen').then((res) => {
        if (res.appOpen) {
          window['$appShare'] = () =>{
            this.openAppShare();
          };
          this.isAppOpen = true;
          this.setAppShareMenus();
          callback && callback();
        }
      });
    },
    setShareParams(params = {}) {
      this.shareParams = params;
      if (isIOS()) { // ios有差异  需要重新初始化
        this.setAppShareMenus();
      }
    },
    //设置h5在app内右上角的菜单
    setAppShareMenus(){
      let params = [
        {
          btnString: "",
          btnImage:"http://yzims.oss-cn-shenzhen.aliyuncs.com/fakerappshare.png",
          callback: "$appShare",
          needRightMenu: 1,
        }
      ]
      bridge.callHandler('setMenus',params);
    },
    // 关闭右侧按钮
    closeAppRightMenus() {
      bridge.callHandler('setMenu',{ needRightMenu: 0 });
    },
    //调用app分享
    openAppShare(){
      let inviteId = encodeURIComponent(this.storage.getItem('authToken'));
      let regChannel = '';
      const params = this.shareParams;
      if (!params.url) {
        console.error('分享路径不存在');
        return;
      }
      if(isAndroid()){
        regChannel = '5';
      }
      if(isIOS()){
        regChannel = '6';
      }
      // 传进来的url是绝对路径 例：url:'/active/hrActive'
      // 现在可以不需要绝对路径路径 例：url: /active/hrActive?code=1
      let joiner =  params.url.indexOf('?') !== -1 ? '&' : '?';
      const url = AppShareUrl + params.url + `${joiner}inviteId=${inviteId}&regOrigin=${params.regOrigin || 16}&regChannel=${regChannel}&scholarship=${params.scholarship || ''}`;
      let datas = {
        title: params.title || '',
        content: params.content || '',
        image: params.image || 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png',
        url,
        channel: params.channel || 0,
      }
      bridge.callHandler('shareWebPage', datas);
    }
  }
}
