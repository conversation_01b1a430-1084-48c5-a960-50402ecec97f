<template>

  <van-popup v-model="drawShow" class="yz-van-popup" :close-on-click-overlay="true" @closed="close">
    <div class="draw" :style="{backgroundImage: 'url(' +drawObj.src+ ')'}">
      <i class="close-icon" @click="close"></i>
      <div class="draw-sub-name">
        {{drawObj.dec}}。
      </div>

      <template v-if="!isShareDrawType">
        <div class="btn">
          <yellow-btn class='btn-abs' text='再抽一次' @click="$emit('toDraw')" />
          <yellow-btn class='btn-abs' text='分享此签' @click="shareDraw" />
        </div>

      </template>
      <template v-else>
        <div class="btn btn-once">

          <yellow-btn class='btn-abs' text='我也来抽' @click="drawTogether" />
        </div>

      </template>

    </div>
  </van-popup>
</template>

<script>
import YellowBtn from './yellow-btn.vue'
import zyq from '../../../../assets/image/active/ckSprint/zyq.png'
import ssq from '../../../../assets/image/active/ckSprint/ssq.png'
import dsq from '../../../../assets/image/active/ckSprint/dsq.png'
import wnq from '../../../../assets/image/active/ckSprint/wnq.png'
import jlq from '../../../../assets/image/active/ckSprint/jlq.png'
import xyq from '../../../../assets/image/active/ckSprint/xyq.png'
import fdq from '../../../../assets/image/active/ckSprint/fdq.png'
import mxq from '../../../../assets/image/active/ckSprint/mxq.png'
import pbq from '../../../../assets/image/active/ckSprint/pbq.png'
import sjq from '../../../../assets/image/active/ckSprint/sjq.png'

const drawList = [
  {
    drawType: 0,
    name: '状元签',
    src: zyq,
    dec: '金榜题名，光宗耀祖',
  },
  {
    drawType: 1,
    name: '上上签',
    src: ssq,
    dec: '梦想成真，前程似锦',
  },
  {
    drawType: 2,
    name: '上上签',
    src: ssq,
    dec: '水到渠成，马到成功',
  },
  {
    drawType: 3,
    name: '上上签',
    src: ssq,
    dec: '逢考必过，顺风顺水',
  },
  {
    drawType: 4,
    name: '万能签',
    src: wnq,
    dec: '一帆风顺，得心应手',
  },
  {
    drawType: 5,
    name: '锦鲤签',
    src: jlq,
    dec: '锦鲤现身，神运加持',
  },
  {
    drawType: 6,
    name: '幸运签',
    src: xyq,
    dec: '你有多努力，便有多幸运',
  },
  {
    drawType: 7,
    name: '读书签',
    src: dsq,
    dec: '学而时习之，不亦说乎',
  },
  {
    drawType: 8,
    name: '奋斗签',
    src: fdq,
    dec: '星光不问赶路人，时光不负有心人',
  },
  {
    drawType: 9,
    name: '奋斗签',
    src: fdq,
    dec: '宝剑锋从磨砺出，梅花香自苦寒来',
  },
  {
    drawType: 10,
    name: '梦想签',
    src: mxq,
    dec: '以梦为马，不负韶华',
  },
  {
    drawType: 11,
    name: '拼搏签',
    src: pbq,
    dec: '将来的你，一定会感谢现在拼搏的自己',
  },
  {
    drawType: 12,
    name: '拼搏签',
    src: pbq,
    dec: '长风破浪会有时，直挂云帆济沧海',
  },
  {
    drawType: 13,
    name: '上进签',
    src: sjq,
    dec: '常读书，将是你未来成功的伏笔',
  },
  {
    drawType: 14,
    name: '上进签',
    src: sjq,
    dec: '读书破万卷，下笔方能如有神助',
  },
  {
    drawType: 15,
    name: '上进签',
    src: sjq,
    dec: '欲穷千里目，更上一层楼',
  },
  {
    drawType: 16,
    name: '上进签',
    src: sjq,
    dec: '你若盛开，蝴蝶自来',
  },
]

export default {
  components: {
    YellowBtn
  },
  data () {
    return {
      drawShow: false,
      drawObj: {},
      isShareDrawType: false,// 被分享页面
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },

    drawType: {
      type: Number,
      default: 0
    },
  },
  computed: {},
  watch: {
    drawType: {
      handler (newval, oldval) {
        this.drawObj = drawList[newval] || {};
      },
      immediate: true,
    },
    value: {
      handler (newval, oldval) {
        this.drawShow = newval;
      },
      immediate: true,
    },
  },
  methods: {
    close () {
      this.drawShow = false;
      this.$emit('input', false);
      this.$emit('changShare', false, {});
    },
    shareDraw () {
      this.$yzStatistic('sprintAct.base.click', '26', '分享此签');
      const shareObj = {
        link: '/active/ckSprint?drawType=' + this.drawObj.drawType,
        drawType: this.drawObj.drawType,
        title: `我今天抽到了「${this.drawObj.name}」，快来测测你2021的考试运吧？`,
        shareDesc: `签文解读：您将在今年成考过后，${this.drawObj.dec}！`,
      }
      this.$emit('changShare', true, shareObj);
    },
    drawTogether () {
      this.$yzStatistic('sprintAct.base.click', '26', '我也来抽');

    }
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '19', '抽签完成页');
    this.isShareDrawType = this.$route.query.drawType || '';
  },

}
</script>
<style lang='less' scoped>
.yz-van-popup {
  background-color: rgba(0, 0, 0, 0) !important;
}
.draw {
  width: 2.7rem;
  height: 5.69rem;
  background-image: url('../../../../assets/image/active/ckSprint/draw-bg.png');
  background-size: 100%;
  position: relative;
  text-align: center;
  background-repeat: no-repeat;
  .draw-mark {
    position: absolute;
    bottom: 1.3rem;
    left: 50%;
    transform: translateX(-50%);
  }
  .draw-tian {
    position: absolute;
    width: 1.52rem;
    height: 0.52rem;
    left: 50%;
    transform: translateX(-50%);
    top: 2.2rem;
  }
  .draw-name {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-family: jxzk;
    color: #fed555;
    font-weight: 400;
    width: 100%;
    text-align: center;
    top: 1.9rem;
    .draw-name-py {
      font-size: 0.18rem;
      color: #fed555;
      line-height: 0.24rem;
      margin-bottom: 0.08rem;
    }
    .draw-name-cn {
      font-size: 0.36rem;
      line-height: 0.48rem;
      letter-spacing: 0.15rem;
      margin-left: 0.15rem;
    }
  }
  .draw-sub-name {
    position: absolute;
    top: 3.2rem;
    font-size: 0.14rem;
    font-weight: 400;
    color: #ffffff;
    line-height: 0.2rem;
    text-align: center;
    width: 100%;
    padding: 0.2rem 0.4rem;
  }
  .btn {
    position: absolute;
    bottom: 0.5rem;
    width: 100%;
    display: flex;
    justify-content: space-between;
    &.btn-once {
      justify-content: center;
      .btn-abs {
        width: 1.4rem;
      }
    }
    .btn-abs {
      width: 1.09rem;
      height: 0.41rem;
      font-size: 0.18rem;
      font-weight: 600;
    }
  }

  .close-icon {
    width: 0.32rem;
    height: 0.32rem;
    position: absolute;
    top: 0.7rem;
    right: 0.3rem;
    background: url('../../../../assets/image/active/ckSprint/ic_share_close.png') no-repeat 100%;
  }
}
</style>
