<template>
  <!-- 保利威迁移腾讯云逻辑文档: https://www.tapd.cn/44062861/markdown_wikis/show/#1144062861001004524 -->
  <div class="dot-player">
    <tcDotPlayer
      v-if="playerType == 'tc'"
      ref="tcDotPlayer"
      :playerOpts="tcPlayerOpts"
      :opts="opts"
      @inited="inited"
      @switchLine="switchLine"
      @onPlay="onPlay"
      @onPause="onPause"
      @onEnded="onEnded"
    />
    <polyvDotPlayer
      v-else-if="playerType == 'polyv'"
      ref="polyvDotPlayer"
      :playerOpts="polyvPlayerOpts"
      :opts="opts"
      @inited="inited"
      @onPlay="onPlay"
      @onPause="onPause"
      @onEnded="onEnded"
      @onError="onError"
    />
  </div>
</template>

<script>
import tcDotPlayer from "@/components/tcDotPlayer";
import polyvDotPlayer from "@/components/polyvDotPlayer";
export default {
  components: {
    tcDotPlayer,
    polyvDotPlayer,
  },
  props: {
    // 配置项
    opts: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 腾讯云实例化配置项
    tcPlayerOpts: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 保利威实例化配置项
    polyvPlayerOpts: {
      type: Object,
      default: () => {
        return {};
      },
    }
  },
  data() {
    return {
      playerType: "",
      dotPlayer: null,
    };
  },
  watch: {
    opts: {
      handler(nV, oV) {
        const { playerType = "" } = nV;
        this.playerType = playerType;
      },
      immediate: true,
      // deep: true,
    },
  },
  methods: {
    // 初始化腾讯
    initTc() {
      this.$refs.tcDotPlayer.init()
    },
    // 初始化保利威
    initPolyv() {
      this.$refs.polyvDotPlayer.init()
    },
    // 初始化
    init() {
      if (this.playerType == 'tc') {
        this.initTc()
      }else if (this.playerType == 'polyv') {
        this.initPolyv()
      }
    },
    // 切换线路
    switchLine() {
      this.$emit("switchLine");
    },
    // 监听初始化后的方法, 拿到保利威或者腾讯云的实例, 并保存起来
    inited(player) {
      this.handleMerge(player)
      this.dotPlayer = player;
      this.$emit("inited", player);
    },
    // 腾讯云和保利威聚合方法
    handleMerge(player) {
      const playerType = this.playerType
      player.dotMerge = {
        // 获取当前播放时间点
        getCurrentTime() {
          switch (playerType) {
            case 'polyv':
              return player.j2s_getCurrentTime();
            case 'tc':
              return player.currentTime();
            default:
              break;
          }
        },
        // 观看时长
        getPlayTime() {
          switch (playerType) {
            case 'polyv':
              return player.j2s_realPlayVideoTime()
            case 'tc':
              return player.dotExtend.getPlayTime();
            default:
              break;
          }
        },
        // 播放
        play() {
          switch (playerType) {
            case 'polyv':
              return player.j2s_resumeVideo();
            case 'tc':
              return player.play();
            default:
              break;
          }
        },
        // 暂停
        pause() {
          switch (playerType) {
            case 'polyv':
              return player.j2s_pauseVideo();
            case 'tc':
              return player.pause();
            default:
              break;
          }
        },
        // 销毁
        destroyed() {
          switch (playerType) {
            case 'polyv':
              return player.destroy();
            case 'tc':
              return player.dispose();
            default:
              break;
          }
        }
        // 下面添加不同的方法
      }
    }, 
    // 监听播放
    onPlay() {
      this.$emit("onPlay");
    },
    // 监听暂停
    onPause() {
      this.$emit("onPause");
    },
    // 监听播放结束
    onEnded() {
      this.$emit("onEnded");
    },
    // 监听播放失败
    onError() {
      this.$emit("onError");
    },
  },
};
</script>

<style lang="less" scoped>
.dot-player {
  position: relative;
  width: 3.75rem;
  height: 2.11rem;
}
</style>
