<template>
  <div class="yz-my-invite">
    <!-- <div class="yz-my-invite__top">
      <div class="item">
        <div class="title">获得总智米</div> -->
        <!-- <div class="title icon" @click='showTips = true'>获得总智米</div> -->
        <!-- <div class="num">{{zmReward}}</div>
      </div>
      <div class="item">
        <div class="title">
          累计待到账智米
        </div>
        <div class="num">{{waitNum}}</div>
      </div>
      <span class="abs-num">共邀请{{fansCount}}人</span>
    </div> -->
    <div class="no-data2"  v-if='list.length == 0'>
      <img src="../../../assets/image/no-data.png" class="img" alt="">
      <p>暂无邀请学员～<br />快去邀请好友赚智米吧</p>
    </div>

    <div class="list-title" v-if='list.length > 0'>
      <span class="w1">用户</span>
      <span class="w2">累计获得智米</span>
      <span class="w3">待到账奖励</span>
    </div>

    <van-list v-if='list.length > 0' v-model='loading' :finished='finished' :offset="50" class="list-box" @load='getInviteStatisticsUser'>
      <div class="list-item" @touchstart='()=>{}' v-for="(item, index) in list" :key="index" @click='getLearnInfo(item.learnId)'>
        <!-- defaultAvatar -->
        <div class="w1" >
          <img :src="item.headImg | defaultAvatar" alt="" class="head-img">
          <div class="w1-right">
            <p class="name">{{item.realName || item.nickname}}</p>
            <p class="mobile">{{item.mobile | hidePhone}}</p>
          </div>
        </div>
        <div class="w2">{{item.zmRewardTotalStr || 0}}</div>
        <div class="w3">
          <button class="remind" v-if="item.tipStatus == 1" @click.stop='remind(item)'>提醒</button>
          <div class="left">
            <p class="text1">{{item.stageName}}</p>
            <p>
              <!-- {{item.learnId ? '预计':'最高'}} -->
              最高奖励：<span class="num">{{item.zmRewardStr || 0}}</span>
            </p>
          </div>
        </div>

      </div>
    </van-list>

    <van-popup v-model="showTips">
      <div class="dialog-content">
        <img src="../../../assets/image/i-red.png" class="img" alt="">
        <p class="text">实际到账奖励智米数 = 学员实际缴纳金额数</p>
        <div class="bottom" @click='showTips = false'>我知道了</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { List, Toast, Popup } from 'vant';

export default {
  components: {
    List, Popup
  },
  data () {
    return {
      fansCount: 0,
      zmReward: 0,
      loading: false,
      finished: false,
      pageNum: 1,
      waitNum: 0,
      list: [],
      isChecking: false,
      showTips: false,
    };
  },
  mounted () {
    this.getInviteCountAndZmRecord();
    this.getInvite2BZm();
    this.getInviteStatisticsUser();
  },
  methods: {
    tips() {
      Toast({
        message: "100智米 = 1元\n实际到账奖励智米数 = 学员实际缴纳金额数",
        className: 'toast-class'
      });
    },
    remind(item) {
      this.$emit('share', item);
    },
    async getLearnInfo(learnId) {
      if (!learnId) {
        Toast({
          message: `暂无报读信息`,
          className: 'toast-class',
          duration: 5000,
        });
        return;
      }
      if (this.isChecking) {
        return;
      }
      this.isChecking = true;
      const res = await this.$http.post('/mkt/getLearnInfoByLearnId/1.0/', { learnId });
      setTimeout(() => {
        this.isChecking = false;
      }, 3000);
      if (res.code != '00') return;
      let taostPfsnLevel="";
      if(res.body.pfsnLevel=='1'){
         taostPfsnLevel='本科'
      }else if(res.body.pfsnLevel=='5'){
        taostPfsnLevel='专科'
      }else{
         taostPfsnLevel='硕士研究生'
      }
      Toast({
        message: `报读信息：${res.body.unvsName}/${res.body.pfsnName || ""}【${taostPfsnLevel}】`,
        className: 'toast-class',
        duration: 5000,
      });

    },
    // 获取邀约数和智米累计
    async getInviteCountAndZmRecord() {
      const res = await this.$http.post('/us/getInviteCountAndZmRecord/1.0/');
      if (res.code != '00') return;
      const { fansCount, zmReward } = res.body;
      this.fansCount = fansCount;
      this.zmReward = zmReward;
    },
    // 获取邀约待到账智米
    async getInvite2BZm() {
      const res = await this.$http.post('/us/getInvite2BZm/1.0/');
      if (res.code != '00') return;
      this.waitNum = res.body;
    },
    // 获取邀约统计列表
    async getInviteStatisticsUser() {
      this.loading = true;
      const res = await this.$http.post('/us/getInviteStatisticsUser/1.0/', { pageNum: this.pageNum, pageSize: 7 });
      this.loading = false;
      if (res.code != '00') return;
      const body = res.body || [];
      this.list = this.pageNum == 1 ? body : this.list.concat(body);
      if (!res.body || res.body.length == 0) {
        this.finished = true;
      } else {
        this.pageNum += 1;
      }
    },
  },
};
</script>

<style lang="less">
  .yz-my-invite{
    padding-bottom: 0.55rem;
  }
  .list-title{
    display: flex;
    color: #A29B9B;
    padding-top: 0.1rem;
    background: #fff;
    .w1{ width: 37.8%; padding-left: 0.15rem; }
    .w2{ width: 23%; text-align: center; }
    .w3{ width: 39.2%; text-align: right; padding-right: 0.15rem; }
  }
  .no-data2{
    text-align: center;
    color: #A29B9B;
    margin-top: 0.92rem;
    .img{
      width: 1.56rem;
    }
  }
  .list-item{
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(23,6,6,0.1);
    padding: 0.18rem 0.15rem;
    background: #fff;
    &:active{
      background: #f5f6f8;
    }
    .w1{
      width: 37.8%;
    }
    .head-img{
      width: 0.28rem;
      height: 0.28rem;
      border-radius: 50%;
      float: left;
    }
    .w1-right{
      margin-left: 0.28rem;
      padding-left: 0.1rem;
      font-size: 0.12rem;
      .mobile{
        font-size: 0.1rem;
        color: #746A6A;
        opacity: 0.8;
      }
    }
    .w2{
      width: 23%;
      text-align: center;
      color: #F06E6C;
      font-size: 0.16rem;
      font-weight:bold;
    }
    .w3{
      width: 39.2%;
      text-align: right;
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      .left{
        font-size: 0.1rem;
      }
      .num{
        color: #F06E6C;
      }
      .remind{
        width: 0.4rem;
        height: 0.24rem;
        line-height: 0.24rem;
        background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
        color: #fff;
        text-align: center;
        margin-left: 0.1rem;
        font-size: 0.12rem;
        border-radius: 0.03rem;
      }
    }
    &:last-child{
      border-bottom: 0;
    }
  }
  .toast-class{
    background: rgba(0, 0, 0, 0.4);
    text-align: center;
    font-size: 0.12rem;
    border-radius: 0.1rem;
  }
  .yz-my-invite__top{
    height: 1.10rem;
    background:linear-gradient(135deg,rgba(240,145,144,1) 0%,rgba(240,120,119,1) 66%,rgba(240,110,108,1) 100%);
    position: relative;
    display: flex;
    padding-top: 0.2rem;
    color: #fff;
    .item{
      text-align: center;
      width: 50%;
      .title{
        position: relative;
      }
      .icon{
        display: inline-block;
      }
      .icon::after{
        content: '';
        background: url(../../../assets/image/i-white.png) no-repeat;
        background-size: 100% 100%;
        width: 0.12rem;
        height: 0.12rem;
        position: absolute;
        right: -0.15rem;

      }
      .num{
        font-size: 0.28rem;
      }
    }
    .abs-num{
      position: absolute;
      bottom: 0.08rem;
      font-size: 0.12rem;
      font-weight:bold;
      left: 50%;
      transform: translateX(-50%);
    }

  }
  .van-popup{
    border-radius: 0.1rem;
  }
  .dialog-content{
    width: 2.86rem;
    background: #fff;
    border-radius: 0.1rem;
    text-align: center;
    .img{
      margin-top: 0.3rem;
      width: 0.32rem;
      height: 0.32rem;
    }
    .text{
      margin-top: 0.16rem;
      color: #A29B9B;
    }
    .bottom{
      border-top: 1px solid rgba(23,6,6,0.1);
      height: 0.5rem;
      line-height: 0.5rem;
      color: #F06E6C;
      font-size: 0.14rem;
      margin-top: 0.27rem;
    }
  }

</style>
