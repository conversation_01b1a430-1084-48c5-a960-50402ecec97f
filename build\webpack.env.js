// 读取环境变量文件 插入到 process.env

const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');
let config = {};

// 判断文件是否存在
function fileExist(path) {
  try {
    return fs.statSync(path).isFile();
  } catch (err) {
    return false
  }
}

let commonPath = path.resolve(__dirname, '../.env');
let envPath = path.resolve(__dirname, '../.env.'+ process.env.NODE_ENV);

if (fileExist(commonPath)) {
  let commonConfig = dotenv.config({
    path: commonPath
  });
  config = Object.assign(config, commonConfig.parsed)
}

if (fileExist(envPath)) {
  let envConfig = dotenv.config({
    path: envPath
  });
  config = Object.assign(config, envConfig.parsed)
}

// JSON.stringify 格式化一遍值
for (let key in config) {
  config[key] = JSON.stringify(config[key])
}

module.exports = config;
