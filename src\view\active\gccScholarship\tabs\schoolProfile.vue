<template>
  <div class="main">
    <!-- 分享组件 -->
    <share
      title="读本科报广州商学院，奖3000元奖学金，限时领取！"
      desc="为鼓励更多上进青年加入学习队伍，远智教育推出“广州商学院本科奖学金”活动，名额有限，快来领取，提升学历吧！"
      imgUrl='http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png'
      :link="shareLink"
      :isActivity="true"
      scholarship="121"
      ref="share"
    />

    <!-- 学校介绍 -->
      <div class="introdu" :class="{unfold:unfold}">
        <div class="school-img">
          <img src="../../../../assets/image/schoolpage/gzsdetatil.png">
        </div>
        <div class="content">
          <p>广州商学院，创建于1997年，前身是华南师范大学增城学院，位于广东省广州市“中新广州知识城”的核心区域，1999年招收第一批学生，是广东省第一所新机制二级学院、全国第一批独立学院。2011年学校获得学士学位授予权，2014年成为广东省第一所由独立学院转设的普通本科高校，培养经济社会发展需要的高素质应用型人才。</p>
          <p>2016年5月，经广东省人力资源和社会保障厅批准，学校成为广东省第六批博士后创新实践基地单位，是国家教育部批准设立的一所以经管类学科为主，经、管、文、法、工、艺等多学科协调发展的普通本科院校</p>
          <p>2016年5月，经广东省人力资源和社会保障厅批准，学校成为广东省第六批博士后创新实践基地单位，是国家教育部批准设立的一所以经管类学科为主，经、管、文、法、工、艺等多学科协调发展的普通本科院校</p>
        </div>
      </div>
      <div class="shrink" @click="unfold=!unfold">
        <div>
          <van-icon :name="unfold ? 'arrow-up' :'arrow-down'" />
        </div>
        <div class="btn"> {{unfold ? "收起" : "查看更多" }} </div>
      </div>

      <!-- 上进故事 -->
      <div class="Storylist">
        <div class="motivate-title">
          <img src="../../../../assets/image/active/gccScholarship/storyTitle.png" alt="">
        </div>
        <mt-swipe class="list-banner" :auto="3000">
          <mt-swipe-item v-for="(item,index) in bannerList" :key="index">
            <img
              class="img"
              :src="(item.bannerUrl + bannerSize)|imgBaseURL"
              @click="bannerJump(item.redirectUrl)"
            />
          </mt-swipe-item>
        </mt-swipe>
         <router-link
            :to="{name:'scholarshipStoryInfo',query:{id:item.scholarshipId,inviteId:inviteId,resourcesUrl:item.resourcesUrl,createTime:item.createTime,regOrigin:regOrigin}}"
            v-for="(item,index) in storyList"
            :key="item.scholarshipId"
            class="item cl"
          >
            <div class="fl">
              <p class="title">{{item.articleTitle}}</p>
              <div class="date">
                <div class="heart">
                  <span
                    class="heartbeat"
                    :class="{active:item.fabulous}"
                    @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)"
                  >{{item.fabulousNum}}</span>
                  <span class="read">{{item.readNum}}</span>
                </div>
                <span style="float: right">{{item.createTime.substring(0,11)}}</span>
              </div>
            </div>
            <div class="fr">
              <img class="pic" :src="item.articlePicUrl|imgBaseURL" alt />
            </div>
          </router-link>
          <div class="detail-btn">
            <div @click="lookMoreStotyList">查看更多>></div>
          </div>
      </div>

      <!-- 留言区，常见问题 -->
      <div class="service">
        <van-tabs v-model="serviceActive">
          <!-- <van-tab title="留言区">
            <userMessageContent :scholarship="scholarship" ></userMessageContent>
          </van-tab> -->
          <van-tab title="常见问题">
            <quest></quest>
          </van-tab>
        </van-tabs>
      </div>

      <!-- 活动声明 -->
      <div class="acStatement">
        <p>

          承办单位: 广州远智教育科技有限公司<br/>
          邮编: 516600 粤ICP备12034252号-1
        </p>
      </div>

  </div>
</template>
<script>
import { imgBaseURL } from "@/config";
import share from "@/components/share";
import quest from "@/components/activePage/questionList";
import userMessageContent from "@/components/activePage/userMessageContent";
export default {
  components:{
    quest,
    share,
    userMessageContent
  },
  data(){
    return {
      scholarship:'121',
      showall:false,
      unfold:false,
      shareLink: window.location.origin + '/active/gccScholarship/index',
      serviceActive: 0,
      bannerList: [],
      regOrigin: '',
      storyList: [],//故事列表
      inviteId: '',//被邀约人 id
      bannerSize: "?x-oss-process=image/resize,m_fixed,h_250,w_640",
    }
  },
  created(){
    this.getBanner();
    this.getStoryList();
  },
  mounted(){
    this.regOrigin = this.$route.query.regOrigin || '';
    this.inviteId = this.$route.query.inviteId || '';
  },
  methods:{
    // 查看更多上进故事
    lookMoreStotyList() {
      this.$router.push({ name: "scholarshipStory",query:{inviteId:this.inviteId}});
    },
    updateFabulousNum(id, num, i) {
      let data = {
        scholarshipId: id,
        fabulousNum: num ? -1 : 1,
        authToken: this.storage.getItem("authToken"),
      };
      this.$http.post("/mkt/updateFabulousNum/1.0/", data).then((res) => {
        if (res.code == "00") {
          this.storyList[i].fabulous = num ? false : true;
          num ?
          (this.storyList[i].fabulousNum -= 1)
          :
          (this.storyList[i].fabulousNum = 1 + parseInt(this.storyList[i].fabulousNum));
        }
      });
    },
    // banner 跳转
    bannerJump: function (url) {
      if (url) {
        const origin = window.location.origin;
        if (url.startsWith(origin)) {
          if (url === "https://zm.yzou.cn/active/redEnveloped") {
            window.location.replace(url);
          } else {
            this.$router.push(url.replace(origin, ""));
          }
        } else {
          window.location.href = url;
        }
      }
    },
    //获取故事Banner list
    getBanner: function () {
      this.$http
        .post("/bds/bannerList/1.0/", { zmBannerType: "2" })
        .then((res) => {
          if (res.code !== "00") return;
          this.bannerList = res.body;
          this.bannerList = this.bannerList.filter((item) => {
            if (item.bannerBelong == "1") {
              return item;
            }
          });
        });
    },
     // 获取故事列表
    getStoryList() {
      this.$http
        .post("/mkt/scholarshipStoryList/1.0/", { informationType: 1 })
        .then((res) => {
          const { code, body } = res;
          if (code === "00") {
            this.storyList = body.slice(0, 3);
          }
        });
    },
  }
}
</script>
<style lang="less" scoped>
@import "../../../../assets/less/variable.less";
.main {
  background: #ffffff;

  .introdu {
    padding:0.1rem;
    height: 3.6rem;
    overflow: hidden;
    .school-img{
      img{
        width: 3.55rem;
        height: 1.79rem;
        border-radius: .01rem;
      }
    }
    .content{
      margin-top:0.22rem;
      padding: 0 0.32rem;
      p {
        text-indent: 2em;
      }
    }
  }
  .unfold{
    height: auto;
  }

  .shrink {
    height: .54rem;
    text-align: center;
    margin-top: 0.1rem;
    color:rgba(23,6,6,.4);
    font-size: 0.14rem;
  }
  // 常见问题，留言区
  .service {
    margin-top: 0.35rem;
    /deep/ .van-tab{
      color:#B9B4B4;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      font-size: 0.16rem;
      background: #FEFAF0;
    }

    /deep/ .van-tabs__line{
      display: none;
    }
    /deep/ .van-tab--active{
      background-color: #FBC464;
      font-weight: bold;
      color:#170606;
    }
    /deep/ .van-tabs__wrap,
    .van-tabs__wrap scroll-view,
    .van-tabs__nav,
    .van-tab {
      height: 0.5rem!important;
    }
  }

   //承办单位
  .acStatement{
    padding-bottom: 0.2rem;
    p{
      font-size: 0.12rem;
      text-align: center;
    }
  }
}


//上进故事样式
.Storylist {
  margin-top: 0.2rem;
  .motivate-title{
    padding: 0 0.3rem;
    margin-bottom: 0.2rem;
    img{
      width: 100%;
      object-fit: contain;
      height: 0.8rem;
    }
  }
  .list-banner {
    height: 1.48rem;
    z-index: 1;
    background: white;
  }
  .invite {
    background-image: url("../../../../assets/image/active/enrollmentHomepage/invitBg2.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;
    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }
    .rightView {
      float: left;
      margin-top: 2.3%;
      p {
        font-size: 0.16rem;
        span {
          color: #e15443;
          font-size: 0.13rem;
        }
        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }
  .item {
    display: block;
    background-color: #fff;
    position: relative;
    padding: 0.2rem 0.16rem 0.1rem 0.16rem;
    &:before {
      .borderBottom;
    }
    .fl {
      width: 69%;
      p {
        line-height: 1.5;
      }
    }
    .fr {
      width: 30%;
      text-align: right;
    }
    .title {
      font-size: 0.15rem;
      color: #363636;
      min-height: 0.54rem;
      overflow: hidden;
    }
    .date {
      margin-top: 0.1rem;
      .heart {
        width: 1.5rem;
        float: left;
        span {
          display: block;
          float: left;
          margin-right: 0.17rem;
          padding-left: 0.21rem;
          line-height: 0.18rem;
          overflow: hidden;
          &.heartbeat {
            background: url("../../../../assets/image/scholarshipStory/ic_school_like.png") no-repeat;
            background-size: 0.18rem;
            &.active {
              background: url("../../../../assets/image/scholarshipStory/ic_school_like_select.png") no-repeat;
              background-size: 0.18rem;
            }
          }
          &.read {
            background: url("../../../../assets/image/scholarshipStory/ic_school_like copy.png")
              no-repeat 0 0.01rem;
            background-size: 0.16rem;
          }
        }
      }
      font-size: 0.12rem;
      color: rgba(54, 54, 54, 0.6);
    }
    .description {
      font-size: 0.12rem;
      color: #444;
    }
    .pic {
      width: 0.72rem;
      height: 0.72rem;
      object-fit: cover;
    }
  }
  .detail-btn {
    width: 100%;
    height: 0.9rem;
    background: white;
    padding-top: 0.27rem;
    div {
      width: 1rem;
      height: 0.36rem;
      background: #e64b23;
      text-align: center;
      line-height: 0.36rem;
      color: #fff;
      margin: 0 auto;
      font-size: 0.14rem;
      font-weight: bold;
    }
  }
}

</style>
