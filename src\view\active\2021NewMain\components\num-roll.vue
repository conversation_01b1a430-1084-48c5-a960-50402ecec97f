<template>
  <div class="yz-num-roll">
    <span>已报名人数：</span>
    <DigitRoll :rollDigits="count" />
  </div>
</template>

<script>
import DigitRoll from "@huoyu/vue-digitroll";

export default {
  components: { DigitRoll },
  props: {
    count: {
      type: Number,
      default: 0,
    },
  },
};
</script>

<style lang="less">
  .yz-num-roll{
    width: 50%;
    display: flex;
    align-items: center;
    &>span{
      float: left;
      line-height: .18rem;
    }

    .d-roll-list{
      float: left;
      padding-top: 0.03rem;
      margin-top: -0.05rem;
      // color: #C12500;
      font-size: 0.15rem;
      .d-roll-item{
        height: 0.25rem!important;
      }
    }
    .d-roll-wrapper{
      margin:0;
      width: auto;
    }
  }
</style>
