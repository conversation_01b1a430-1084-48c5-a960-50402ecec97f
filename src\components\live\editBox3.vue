<template>
  <form action @submit.prevent="submit">
    <div class="edit-box">
      <div class="cl">
        <i id="btnEmotion" class="fl icons btn-emotion" @click="$refs.emotion.toggleBox()"></i>
        <div @click="sendevalute" class="detail fr">
          <img src="../../assets/image/tutor/classNew/evaluate.png" alt class="evaluate" />
          <div>评价</div>
        </div>
        <div @click="sendmoneryshow" class="detail fr">
          <div class="dise">
            <img src="../../assets/image/tutor/classNew/meney.png" alt />
          </div>
          <div>智米赞赏</div>
        </div>
        <!-- <button class="fr btn" :disabled="!content" @click="submit" :class="{active:change}">{{btnText}}</button> -->
        <div class="ipt-wrap">
          <input
          :disabled="isdisabled"
            type="text"
            :placeholder="inputPlaceholder"
            v-model.trim="content"
            @focus="inputFocus"
            v-if="scroll"
          />
          <input type="text" :placeholder="inputPlaceholder" v-model.trim="content" v-else   :disabled="isdisabled" />
        </div>
        <emotion-list v-model="content" ref="emotion" />
      </div>
    </div>
  </form>
</template>

<script>
import emotionList from "@/components/emotionList1";
export default {
  props: {
    isSocketConnect: {
      type: Boolean,
      default: false
    },
    inputPlaceholder: {
      type: String,
      default: "说点什么吧"
    },
    btnText: {
      type: String,
      default: "提交"
    },
    change: {
      type: Boolean,
      default: false
    },
    scroll: {
      type: Boolean,
      default: true
    },
    isdisabled: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      content: ""
    };
  },
  methods: {
    sendevalute() {
      this.$emit("evalute", true);
    },
    sendmoneryshow() {
      this.$emit("moneryshow", true);
    },
    formatEmotions: function(content) {
      return this.$refs.emotion.formatEmotions(content);
    },
    submit: function() {
      let content = this.content;
      if (!content) return;
      // if (!this.channelId) {
      //   return;
      // }
      // if (!this.isSocketConnect) {
      //   this.$modal({ message: "请稍等，连接中…", icon: "warning" });
      //   return;
      // }
      this.$emit("submit", content);
      this.content = "";
    },
    inputFocus: function() {
      setTimeout(function() {
        window.scrollTo(
          0,
          document.getElementsByTagName("body")[0].offsetHeight
        );
      }, 600);
    }
  },
  components: { emotionList }
};
</script>

<style lang="less" scoped>
@import "../../assets/less/variable";

.edit-box {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  > .cl {
    max-width: @maxWidth;
    margin: 0 auto;
    background-color: #fff;
  }
  .btn {
    line-height: 0.36rem;
    border: none;
    background-color: transparent;
    color: @color;
    font-size: 0.14rem;
    &.active {
      background-color: #e85b57;
      color: white;
      width: 0.5rem;
      height: 0.3rem;
      line-height: 0.3rem;
      text-align: center;
    }
  }
  .ipt-wrap {
    position: relative;
    margin-left: 0.3rem;
    margin-right: 0.4rem;
    width: 2.4rem;
    padding: 0.04rem 0;
    input {
      width: 90%;
      height: 0.4rem;
      padding: 0.08rem 0.1rem 0.08rem 0.11rem;
      line-height: 0.4rem;
      font-size: 0.14rem;
      color: #333;
      border: none;
      background: rgba(237, 235, 235, 1);
      border-radius: 0.2rem;
    }
  }
}
.btn-emotion {
  width: 0.32rem;
  height: 0.32rem;
  background-image: url(../../assets/image/student/ic_course_face.png);
  margin-top: 0.04rem;
}
.i-quiz {
  position: absolute;
  top: 0.08rem;
  left: 0.1rem;
  width: 0.16rem;
  height: 0.17rem;
  vertical-align: middle;
  background-image: url(../../assets/image/live_ico_quiz.png);
}
.detail {
  width: 0.5rem;
  height: auto;
  font-size: 11px;
  font-size: 11px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(23, 6, 6, 0.8);
  margin-right: 0.1rem;
  text-align: center;
  .dise {
    width: 0.32rem;
    height: 0.32rem;
    background: linear-gradient(
      315deg,
      rgba(244, 25, 14, 1) 0%,
      rgba(234, 138, 84, 1) 100%
    );
    border-radius: 19px;
    position: relative;
    left: 0.09rem;
    img {
      width: 0.18rem;
      height: 0.19rem;
      position: relative;
      top: 0.07rem;
    }
  }
  .evaluate {
    width: 0.32rem;
    height: 0.32rem;
  }
}
</style>
