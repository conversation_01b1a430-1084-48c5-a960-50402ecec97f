
export default {
  created () {
    this.$getActivityInfo();
  },
  data () {
    return {
      AC_INFO: {},//活动信息
      AC_STATUS: 0, // 活动状态 0：活动未开始，1：活动开始中，2：活动已结束 3：活动结束7天后
      AFTER7DAYS: 0,//7天后的时间
    }
  },
  methods: {
    //获取活动信息
    $getActivityInfo () {
      this.$http.post("/mkt/getActivityInfo/1.0/", { scholarship: '128' }).then(res => {
        const { code, body } = res;
        if (code == '00') {
          this.AC_INFO = body;
          const now = new Date().getTime();
          this.AFTER7DAYS = body.EndTime + (7 * 24 * 60 * 60 * 1000); //7天后时间
          //活动前
          if (now < body.StartTime) {
            this.AC_STATUS = 0;
          }
          // 活动中
          if ((now >= body.StartTime && body.EndTime >= now)) {
            this.AC_STATUS = 1;
          }
          // 活动结束
          if (now > body.EndTime) {
            this.AC_STATUS = 2;
          };
          //活动结束7天后
          if (now > this.AFTER7DAYS) {
            this.AC_STATUS = 3;
          }
        }
      })
    }
  }
}