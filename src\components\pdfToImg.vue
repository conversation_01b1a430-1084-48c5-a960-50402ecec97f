<template>
  <div>
    <div class="contract-modal" >
      <div class="contract-detail"
           id="contractDetail">
        <div id="mycanvas" ref="mycanvas" ></div>
      </div>
    </div>
    <!--<div class="img_wrap" v-if="isShow">-->
      <!--<div class="bg" @click="isShow=false"></div>-->
      <!--<img :src="img" alt="" v-if="img">-->
      <!--<a href="javascript:;" @click="isShow=false" >长按保存图片并下载打印</a>-->
    <!--</div>-->
  </div>
</template>

<script>
  // import pdf from '../../static/pdf/build/pdf'
  const pdf = require('../../static/pdf/build/pdf')
  import imgPopup from '@/components/imgPopupChange';
  export default {
    name: "pdfToImg",
    props:['url'],
    data(){
      return{
        img:'',
        isShow:false
      }
    },
    created(){

    },
    mounted(){

    },
    methods:{
      open(){
        this.isShow=true
        this.getPdf()
      },
      getPage (pdf,pageNumber,container,numPages) { //获取pdf
        let _this = this
        pdf.getPage(pageNumber).then((page) => {
          let scale = (container.offsetWidth/page.view[2])
          let viewport = page.getViewport(scale)
          let canvas = document.createElement("canvas")
          canvas.width= viewport.width
          canvas.height= viewport.height
          container.appendChild(canvas)
          let ctx = canvas.getContext('2d');
          var renderContext = {
            canvasContext: ctx,
            transform: [1, 0, 0, 1, 0, 0],
            viewport: viewport,
            intent: 'print'
          };
          page.render(renderContext).then(() => {
           // _this.img=canvas.toDataURL();
            // _this.isShow=true
            _this.$emit('showImg', canvas.toDataURL());
            console.log(_this.img);
            pageNumber +=1
            if(pageNumber<=numPages) {
              _this.getPage(pdf,pageNumber,container,numPages)
            }
          })
        })
      },
      getPdf () {
        // 此中方式接受流形式返回
        this.$refs.mycanvas.scrollTop =0
//                let accessToken = cache.get('TOKEN').Authorization
//                let url = `${config.baseUrls}/api/fund/v1/contractReports/previewContractContent?access_token=${accessToken}&id=${contractData.id}&contractUrl=${contractData.contractUrl}&.pdf`
        let url = this.url
        let pdfjsLib = pdf

        pdfjsLib.PDFJS.workerSrc = '/static/pdf/build/pdf.worker.js'
        pdfjsLib.PDFJS.cMapUrl = '../../static/pdf/web/cmaps/';
        pdfjsLib.PDFJS.cMapPacked = true;
        let loadingTask = pdfjsLib.getDocument(url)
        loadingTask.promise.then((pdf) =>{
          let numPages = pdf.numPages
          let container = document.getElementById('mycanvas')
          let pageNumber = 1
          this.getPage(pdf,pageNumber,container,numPages)
        }, function (reason) {

        });
      }
    },
    components:{imgPopup}
  }
</script>

<style scoped lang="less">
  .contract-modal {
    position: absolute;
    right: 15%;
    width: 100%;
    height: 500px;
    /*overflow: scroll;*/
    /*background: rgba(139, 148, 171, 0.7);*/
    padding: 20px 0 0;
    z-index: 900;
  }
  .contract-modal .contract-detail {
    margin: 0 auto;
    max-width: 96%;
    height: auto;
  }
  .contract-btns{
    height: 50px;
    background-color:  #fff;
    text-align: center;
    padding-bottom:44px;
    padding-top:10px;
  }
  #mycanvas {
    width: 2000px;
    min-height: 50vh;
    background: #fff;
    opacity: 0
  }
  canvas{
    margin: 0 auto;
    display: block;
    border-bottom:2px solid #aaa;
  }
  .close-btn{
    position: absolute;
    right: 15%;
    width: 26px;
    height: 26px;
    z-index: 999;
    /*background-color: #666;*/
    border-radius: 50%;
    cursor: pointer;
  }
  .img_wrap{
    position: fixed;
    width: 100%;
    height:100%;
    overflow: hidden;
    left:0;
    right: 0;
    top:0;
    bottom:0;
    margin: auto;
    z-index:999;
    .bg{
      position: fixed;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.4);
      z-index: 999;
    }
    img{
      width: 2.5rem;
      position: absolute;
      left:0;
      right: 0;
      top:0;
      bottom:0;
      margin: auto;
      z-index:99999;
    }
    a{
      display: block;
      width: 100%;
      height: .3rem;
      line-height: .3rem;
      position: absolute;
      bottom:1rem;
      color: white;
      text-align: center;
      z-index: 99999;
    }
  }
</style>
