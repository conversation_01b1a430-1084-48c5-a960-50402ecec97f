import wx from "weixin-js-sdk";
import { downloadApp } from '@/common';
import { OpenAppId } from '@/config';

export default {
  data() {
    return {
      wxCallAppData: {},
      OpenAppId
    };
  },
  methods: {
    // 用户点击跳转按钮后出现错误
    callAppError(e) {
      if (e == 1) this.$yzStatistic('applent.upward.act.event', '2', '小程序活动下载页-点击前往APP')
      console.log('用户点击跳转按钮后出现错误：' + JSON.stringify(e))
      if (e == 99) this.$yzStatistic('applent.upward.act.event', '2', '小程序活动下载页-点击前往APP')
      else this.$yzStatistic('applet.customerServiceDownloadApp.event', '2', '小程序客服-下载app');
      downloadApp();
    },
    // 微信标签唤起app
    wxCallAppInit() {
      let url = window.location.href
      const params = { url }
      console.log('=>wxCallAppInit-params', params)
      this.$http
        .post("/bds/jsapiSign/1.0/", params)
        .then((res) => {
          if (res.code !== "00") return;
          this.wxCallAppData = res.body;
          console.log('=>wxCallAppInit-body', res.body)
          if (this.wxCallAppData && this.wxCallAppData.appid) {
            wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: this.wxCallAppData.appid, // 必填，企业号的唯一标识，此处填写企业号corpid
              timestamp: this.wxCallAppData.timestamp, // 必填，生成签名的时间戳
              nonceStr: this.wxCallAppData.noncestr, // 必填，生成签名的随机串
              signature: this.wxCallAppData.signature, // 必填，签名，见附录1
              jsApiList: [
                "hideMenuItems",
                "updateAppMessageShareData",
                "hideAllNonBaseMenuItem",
              ],
              openTagList: ["wx-open-launch-app", "wx-open-launch-weapp"],
            });
            wx.error(function (err) {
              console.log("=>wxCallAppInit-错误：", err);
            });
            wx.ready(function (res) {
              console.log("获取微信签名成功", res)
            });
          } else {
            console.log("获取微信签名失败");
          }
        });
    },
  }
}
