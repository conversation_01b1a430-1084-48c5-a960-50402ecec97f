/**
 * 读书计划列表页跳转处理 抽出公共函数
 */

//未购买跳转处理
export let notPurchased = (self, book)=> {
  // let params = {
  //   readPlanId: book.readPlanId
  // };
  // self.$inviteJump('/aspirantUniversity/bookDetails', params);
  self.$inviteJump(`/aspirantUniversity/goods/${book.goodsShelfId}`)

};

// 处理已购买跳转页面处理
export let handleBought = async (self, book) => {
  let params = {
    readPlanId: book.readPlanId
  };

  let res = await self.$http.post('/pu/getReadPlanProgresInfo/1.0/', params);
  const { code, body } = res;

  if (code === '00') {
    let toPageProcessor = {
      // 已开课
      classStarted() {
        self.$router.push({
          path: '/aspirantUniversity/bookCatalogue',
          query: params,
        });
      },
      // 未开课
      noClass() {
        self.$inviteJump('/aspirantUniversity/bookNotice', params);
      },
    };

    //未开课
    if (body.startClassStatus === 0) {
      toPageProcessor.noClass();
      return;
    }

    // 已开课 未结束
    if(body.startClassStatus === 1 && body.endClassStatus === 0) {
      toPageProcessor.classStarted();
      return;
    }

    // 已开课，已结束。
    if(body.startClassStatus === 1 && body.endClassStatus === 1) {
      self.$router.push({
        path: '/aspirantUniversity/graduationCompletion',
        query: {
          enroId: body.enroId,
          readPlanId: book.readPlanId
        }
      });
    }
  }
};

