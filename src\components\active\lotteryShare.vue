<template>
  <transition name="fade">
    <div class="share" v-if="isShow" @click="close">
      <div class="tips">
        <img src="../../assets/image/arrow.png" alt="">
        <p>点击右上角分享链接</p>
      </div>
      <div class="tipsTxt" v-if="show" style="text-align: left;text-indent: 2em;">
        在2019年7月15日9点-2019年7月31日23点59分59秒期间，每邀请一名好友报读缴费，不仅可以获得5倍智米奖励，还可以赚100元学费啦！推荐越多赚的越多，快来邀请好友报读缴费吧！
      </div>
      <template v-else>
        <div v-if="title == '来国家开放大学读学历，权威有保证！' || link.includes('scholarshipStoryInfo')" class="tipsTxt">
          每邀请一名好友报读缴费，可获得<br>被邀请人学费同等金额数智米奖励！
        </div>
        <div v-else class="tipsTxt">
          活动期间报读缴费后，每邀请一名<br>好友报读缴费，可获得被邀请人学<br>费的同等金额数智米奖励！
        </div>
      </template>
    </div>
  </transition>
</template>

<script>
import config from '../../config';
import { isWeixin, toLogin } from '../../common';

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    link: {
      type: String,
      default: ''
    },
    imgUrl: {
      type: String,
      // default: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png'
      default: 'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/gzcj_icon.jpg'
    },
    type: {
      type: String,
      default: 'link'
    },
    dataUrl: {
      type: String,
      default: ''
    },
    scholarship: {
      type: String,
      default: ''
    },
    // 层次ID
    levelId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      isShow: false,
      action: '',
      isReady: false,
      wxConfig: null,
      isLogin: null,
      show: true
    }
  },
  created() {
    this.isLogin = !!(this.storage.getItem('authToken'));
    this.action = this.$route.query.action || '';
    this.getWxScript();
    if (Date.now() >= new Date(2019, 7, 1, 0, 0, 0).getTime()) {
      this.show = false
    }
  },
  methods: {
    open: function (msg, action, redirect) {
      // 如果是未登录
      if (!this.isLogin) {
        toLogin.call(this, msg, action, redirect);
      } else {
        this.isBindMobile(redirect, () => {
          if (!isWeixin()) {
            this.$modal({ message: '请在微信客户端打开链接进行分享', icon: 'warning' });
            return;
          }
          if (this.isReady) {
            this.isShow = true
          } else {
            this.getWxScript();
          }
        });
      }
    },
    openNO: function (msg, action, redirect, fn) {
      if (!isWeixin()) {
        return;
      }
      if (this.isReady) {
        fn();
      } else {
        this.getWxScript();
      }
    },
    close: function () {
      this.isShow = false;
    },
    getWxScript: function () {
      if (!isWeixin()) return;

      if (!window.wx) {
        let $script = document.createElement('script');
        $script.src = config.jweixin;
        $script.onload = () => {
          this.wxJsapiSign();
        };
        global.document.body.appendChild($script);
      } else {
        this.wxJsapiSign();
      }
    },
    wxJsapiSign: function () {
      if (!this.isLogin) return;
      if (this.wxConfig && this.wxConfig.appid) {
        this.wxAuth(this.wxConfig);
        return;
      }

      const url = window.location.origin + this.$route.fullPath.split('#')[0];
      this.$http.post('/bds/jsapiSign/1.0/', { url: url }).then(res => {
        if (res.code !== '00') return;
        this.wxConfig = res.body;
        if (this.wxConfig && this.wxConfig.appid) {
          this.wxAuth(this.wxConfig);
        } else {
          this.$modal({ message: '微信签名失败，请重试', icon: 'warning' });
        }
      });
    },
    // 通过config接口注入权限验证配置
    wxAuth: function (data) {
      wx.config({
        debug: false,
        appId: data.appid,
        timestamp: data.timestamp,
        nonceStr: data.noncestr,
        signature: data.signature,
        jsApiList: ['onMenuShareTimeline', 'onMenuShareAppMessage', 'onMenuShareQQ', 'onMenuShareQZone']
      });

      wx.ready(() => {
        this.init();
        this.isReady = true;
        if (this.action === 'share') this.open();
      });
    },
    init: function () {
      let shareLink = this.link || `${window.location.origin}/invite`;
      shareLink = shareLink.replace(/(&?action=[^&]*)|(&?scholarship=[^&]*)|(&?inviteId=[^&]*)/g, '').replace(/\?&+/g, '?');
      shareLink += (shareLink.includes('?') ? '&' : '?') + `action=login&scholarship=${this.scholarship}&inviteId=${encodeURIComponent(this.storage.getItem('authToken') || '')}&empId=${this.storage.getItem('empId') || ''}&pfsnLevel=${this.levelId}`;

      // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
      wx.onMenuShareTimeline({
        title: this.title,
        link: shareLink,
        imgUrl: this.imgUrl,
        success: function () {
        },
        cancel: function () {
        }
      });

      // 获取“分享给朋友”按钮点击状态及自定义分享内容接口
      wx.onMenuShareAppMessage({
        title: this.title,
        desc: this.desc,
        link: shareLink,
        imgUrl: this.imgUrl,
        type: this.type,
        dataUrl: this.dataUrl,
        success: function () {
        },
        cancel: function () {
        }
      });

      // 获取“分享到QQ”按钮点击状态及自定义分享内容接口
      wx.onMenuShareQQ({
        title: this.title,
        desc: this.desc,
        link: shareLink,
        imgUrl: this.imgUrl,
        success: function () {
        },
        cancel: function () {
        }
      });

      // 获取“分享到QQ空间”按钮点击状态及自定义分享内容接口
      wx.onMenuShareQZone({
        title: this.title,
        desc: this.desc,
        link: shareLink,
        imgUrl: this.imgUrl,
        success: function () {
        },
        cancel: function () {
        }
      });
    },
    // 判断是否绑定手机
    isBindMobile: function (redirect, callback) {
      if (this.storage.getItem('isBindMobile') === '1') {
        callback();
        return;
      }

      this.$http.post('/us/isBindMobile/1.0/').then(res => {
        if (res.code !== '00') return;

        if (res.body === '1') {
          this.storage.setItem('isBindMobile', '1');
          callback();
        } else {
          toLogin.call(this, '尚未绑定手机，请先绑定', 'bindMobile', redirect);
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
.share {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  background-color: rgba(0, 0, 0, .8);

  .tips {
    position: absolute;
    top: .1rem;
    right: .1rem;
    width: .8rem;
    height: 1rem;
    color: #fff;
    text-align: center;

    img {
      display: inline-block;
      width: .3rem;
    }
  }

  .tipsTxt {
    position: absolute;
    width: 100%;
    text-align: center;
    color: #fff;
    top: 2.3rem;
    font-size: .2rem;
    padding: 0 .3rem;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity .4s
}

.fade-enter,
.fade-leave-to {
  opacity: 0
}
</style>
