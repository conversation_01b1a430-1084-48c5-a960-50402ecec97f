<template>
  <van-overlay :show="show" class='dialogContainer' @click='close'>
    <div class="yz-ck-dialogContainer" @click.stop>
      <div class="dialog">
        <img src="../../../../assets/image/close-grey.png" class="close-btn" @click='close' alt="">
        <slot></slot>
      </div>
      <slot name='other'></slot>
    </div>
  </van-overlay >
</template>

<script>
import { Overlay } from 'vant';

export default {
  components: {
    Overlay,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      show: false,
    };
  },
  watch: {
    value(val) {
      this.show = val;
    },
  },
  mounted() {
    this.show = this.value
  },
  methods: {
    close() {
      this.show = false;
      this.$emit('input', false);
      this.$emit('close', false);
    },
  },

};
</script>

<style lang="less">
  .van-overlay.dialogContainer{
    z-index: 10;
  }
  .yz-ck-dialogContainer{
    position: absolute;
    top: 1.4rem;
    left: 50%;
    transform: translateX(-50%);
    .dialog{
      width: 2.7rem;
      height: 3.54rem;
      margin: 0 auto;
      border-radius: 0.1rem;
      border: 0.03rem solid #FAC561;
      background: linear-gradient(180deg, #E8854C 0%, #CC2825 100%);
      box-shadow: 0px 0px 0.1rem 0px rgba(255,255,255,0.65);
    }

    .close-btn{
      width: 0.32rem;
      height: 0.32rem;
      position: absolute;
      top: 0.1rem;
      right: 0.1rem;
    }
  }
</style>
