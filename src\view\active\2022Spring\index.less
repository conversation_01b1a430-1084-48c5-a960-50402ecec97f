* {
  margin: 0;
  padding: 0;
}
.invite {
  background-image: url("../../../assets/image/active/2022Spring/Share-head-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 0.55rem;
  position: relative;
  /* margin-bottom: -.1rem; */
  z-index: 99;
  img {
    width: 0.4rem;
    height: 0.4rem;
    float: left;
    border-radius: 50%;
    margin-top: 1.8%;
    margin-left: 0.12rem;
    margin-right: 0.07rem;
  }
  .rightView {
    float: left;
    margin-top: 2.3%;
    p {
      font-size: 0.12rem;
      span {
        color: #ffffff;
        font-size: 0.12rem;
      }
      span:first-of-type {
        margin-right: .05rem;
        font-size: 0.12rem;
        color: #fff;
        opacity: 0.5;
      }
    }
    p:nth-child(2) {
      font-size: 0.15rem;
      font-weight: 600;
      color: #ffffff;
    }
  }
  .look-more {
    position: relative;
    float: right;
    margin: 0.13rem 0.1rem 0 0;
    width: 0.72rem;
    height: 0.3rem;
    line-height: 0.3rem;
    text-align: center;
    background: #d92a2a;
    border-radius: 0.2rem;
    font-size: 0.14rem;
    font-weight: 600;
    color: #ffffff;
    .launch-follow {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 100%;
      z-index: 4;
      overflow: hidden;
    }
  }
}
.contain {
  width: 100%;
  height: 6.77rem;
  background: url("../../../assets/image/aspirantUniversity/red-bg.png")
    no-repeat;
  background-size: 100% 100%;
  padding-top: 0.7rem;
  .footer {
    padding-top: 0.15rem;
    .open-redgift {
      position: relative;
      margin: 0 auto;
      padding-top: 0.13rem;
      width: 2.1rem;
      height: 0.67rem;
      border-radius: 0.32rem;
      background: url("../../../assets/image/aspirantUniversity/btn-bg.png")
        no-repeat;
      background-size: 100% 100%;
      text-align: center;
      h3 {
        font-size: .16rem;
        font-weight: 500;
        color: #fce7ba;
        line-height: .22rem;
        text-shadow: 0rem .02rem .04rem rgba(201, 0, 0, 0.5);
      }
      p {
        font-size: .12rem;
        font-weight: 400;
        color: #fce7ba;
        line-height: .17rem;
        text-shadow: 0rem .02rem .04rem rgba(201, 0, 0, 0.5);
      }
      .launch-follow {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 4;
        overflow: hidden;
      }
    }
  }

  .reward {
    width: 3rem;
    height: 0.82rem;
    margin: 0 auto;
    img {
      width: 3rem;
      height: 0.82rem;
    }
  }
  .zm-value {
    font-size: .22rem;
    font-weight: 400;
    color: #fff3d9;
    line-height: .3rem;
    text-align: center;
    margin-bottom: 0.2rem;
  }
  .tips {
    font-size: .14rem;
    font-weight: 400;
    text-align: center;
    color: #ffe3b5;
    line-height: .2rem;
    margin-bottom: 0.27rem;
  }
  .gift {
    width: 3.14rem;
    height: 2.06rem;
    margin: 0 auto;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .active-time {
    font-size: .14rem;
    font-weight: 400;
    text-align: center;
    color: #ffe3b5;
    line-height: .2rem;
  }
  .help-rules {
    width: 3.55rem;
    margin: 0.2rem auto 0;
    font-size: .12rem;
    font-weight: 400;
    color: #fff3d9;
    line-height: .17rem;
  }
}
/deep/ .van-popup {
  background-color: transparent;
  overflow-y: visible;
}
.pop-contain {
  width: 3rem;
  height: 4rem;
  background: linear-gradient(180deg, #fb7842 0%, #f5431f 100%);
  border-radius: 0.14rem;
  padding-top: 0.04rem;
  .pop-contain-in {
    position: relative;
    margin: 0 auto;
    width: 2.92rem;
    height: 3.91rem;
    text-align: center;
    background: #fff5e5;
    border-radius: 0.11rem;
    padding-top: 0.35rem;
    .pop-head {
      position: absolute;
      left: 50%;
      top: -10%;
      width: 0.62rem;
      height: 0.62rem;
      box-shadow: 0rem .03rem .04rem .01rem rgba(164, 50, 11, 0.5);
      border: .02rem solid #ffe3b5;
      border-radius: 50%;
      transform: translate(-50%, 0);
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }
    .head-bot {
      position: absolute;
      left: 51%;
      top: 2%;
      transform: translate(-50%, 0);
      width: 1.15rem;
      height: 0.22rem;
    }
    .help-txt {
      margin: 0 auto 0.12rem;
      width: 1.9rem;
      height: 0.36rem;
      line-height: 0.43rem;
      background: url("../../../assets/image/aspirantUniversity/pop.png")
        no-repeat;
      background-size: 100% 100%;
      text-align: center;
      font-size: .16rem;
      font-weight: 500;
      color: #fce7ba;
    }
    h3 {
      margin-bottom: 0.17rem;
      font-size: 0.16rem;
      font-weight: 600;
      color: #cc2725;
      line-height: 0.22rem;
      text-align: center;
    }
    .pop-gift {
      margin: 0 auto;
      width: 2.3rem;
      height: 1.5rem;
      margin-bottom: 0.25rem;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .help-btn {
      position: relative;
      margin: 0 auto;
      width: 2rem;
      height: 0.6rem;
      line-height: 0.6rem;
      text-align: center;
      background: url("../../../assets/image/aspirantUniversity/btn-bg.png")
        no-repeat;
      background-size: 100% 100%;
      font-size: 0.16rem;
      font-weight: 600;
      color: #fce7ba;
      text-shadow: 0rem 0.02rem 0.04rem rgba(201, 0, 0, 0.5);
    }
  }
}
