<template>
  <div>
    <div class="select-item" v-for="item in options" :class="{active:value.dictValue===item.dictValue}" @click="selected(item)">{{item.dictName}}</div>
  </div>
</template>

<script>
  export default {
    props: ['value'],
    data() {
      return {
        options: dictJson.recruitType
      }
    },
    methods: {
      selected: function (val) {
        this.$emit('input', val);
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
