.yz-graduate-main{
  background: #F2F2F2;
  .banner_swiper{
    z-index: 1;
  }
  .graduate-top-tab {
    height: 0.5rem;
    overflow: hidden;
    position: relative;
    z-index: 3;
    &.fixed{
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }
    .item {
      float: left;
      height: 0.5rem;
      width: 25%;
      background: #3E2173;
      background-image: none;
      text-align: center;
      span {
        display: inline-block;
        width: 100%;
        line-height: 0.5rem;
        font-size: 0.16rem;
        color: #fff;
        font-weight: bold;
      }
      &:first-of-type {
        position: relative;
        &:after {
          content: none;
        }
      }
      &.active {
        background: #E9346F;
        span {
          position: relative;
          width: auto;
          border-right: none;
          color: #fff;
        }
      }
    }
    .line {
      position: absolute;
      width: 0.4rem;
      left: 0.45rem;
      height: 0.02rem;
      background-color: #fff;
      bottom: 0.09rem;
      transform: translateX(-50%);
      border-radius: 2px;
      transition: all 0.5s;
      &.active {
        left: 1.4rem;
      }
      &.active2 {
        left: 2.35rem;
      }
      &.active3 {
        left: 3.3rem;
      }
    }
  }
  .banner-box{
    position: relative;
    height: 3.72rem;
    &.fixed{
      margin-top: 0.5rem;
    }
    .banner{
      width: 100%;
      height: 100%;
    }
  }
  .banner-top{
    position: absolute;
    width: 100%;
    top: 0.2rem;
    padding-left: .1rem;
  }
  .tab-content-2 {
    background:#fff;
    img {
      width:100%
    }
    .img2 {
      margin-top:.35rem;
    }
  }
  .tab-content-3 {
    background: #fff;
    ul {
      width: 3.4rem;
      margin: 0 auto;
      overflow: hidden;
      padding-bottom:.3rem;
      li {
        width: 1.7rem;
        float: left;
        margin-top: .15rem;
          img {
            width: .26rem;
            height: .26rem;
            margin-top: .22rem;
          }
          .text-1 {
            font-size: .16rem;
            font-weight: 600;
            color: #363636;
            margin-top: .11rem;
          }
          .text-2 {
            font-size: .12rem;
            color: #363636;

          }
      }
    }
  }
  .messageHead {
    padding: 0.14rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width:.2rem;
      margin-right: .05rem;
    }
    span {
      font-size:.16rem;
      font-weight:600;
    }
  }

}

