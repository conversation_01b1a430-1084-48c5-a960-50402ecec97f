<template>
  <div class="home">
    <!-- 邀约关系组件 -->
    <inviteTop :inviteId="inviteId"></inviteTop>
    <!-- 分享组件 -->
    <share
      title="800元奖学金报读活动，限时领取！"
      desc="活动旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！"
      imgUrl='http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png'
      :link="shareLink"
      :isActivity="true"
      scholarship="128"
      ref="share"
    />
    <!-- tab -->
    <van-tabs v-model="tabIndex" background="#970303">
      <van-tab title="报考主页">
        <apply-home v-if="tabIndex == 0"
          :pfsnLevel='pfsnLevel'
          :regChannel='regChannel'
          :unvs='unvs'
          @setAcInfo="setActiveInfo"
          @setActiveStatus="setActiveStatus"
          @isReceive="setIsReceive"
          @setTabIndex='setTabIndex'
        />
      </van-tab>
      <van-tab title="我的见证">
        <witness v-if="tabIndex == 1" :is-receive="isReceive" @setAcInfo="setActiveInfo" />
      </van-tab>
      <van-tab title="学校介绍">
        <schoolProfile v-if="tabIndex == 2" />
      </van-tab>
    </van-tabs>
    <!-- <page-footer
      :scholarship="footer.scholarship"
      :Expired="footer.enrollEnd"
      :inviteId="footer.inviteId"
      :actName="footer.actName"
      :actStartTime="footer.actStartTime"
      :actEndTime="footer.actEndTime"
      :unvs="unvs"
    /> -->
    <second-footer @enroll='enroll' />
  </div>
</template>
<script>
// import oFooter from "@/components/activePage/footer";
import pageFooter from './components/footer'
import applyHome from './tabs/applyHome';
import witness from './tabs/witness';
import schoolProfile from './tabs/schoolProfile'
import inviteTop from "../enrollAggregate/components/invite-top";
import share from "@/components/share";
import AppShare from './AppShare.js';
import SecondFooter from "@/view/active/2021NewMain/components/second-footer";
import statistic from '@/view/active/2021NewMain/statistic.json';

export default {
  components: {
    pageFooter,
    applyHome,
    witness,
    schoolProfile,
    share,
    inviteTop,
    SecondFooter
  },
  mixins:[ AppShare ],
  created(){
    this.inviteId = this.$route.query.inviteId || ""
    this.tabIndex = this.$route.params.tabIndex || 0;
    const schoolId = this.$route.query.schoolId;
    if (schoolId) {
      this.unvs.unvsId = schoolId;
    }
    if(this.$route.query.type && this.$route.query.type == 'advance.witness.act.202010705.jr'){
      this.tabIndex = 1;
    }
    this.footer.inviteId =  this.inviteId;
    if (this.pfsnLevel) {
      this.$yzStatistic('marketing.base.browse', statistic.schoolPage[this.pfsnLevel].main.id, statistic.schoolPage[this.pfsnLevel].main.name);
    }
  },
  data(){
    return {
      tabIndex: 0,
      isReceive: false,//是否领取优惠卷
      inviteId:'',
      acInfo:{},
      acStatus: 0,
      unvs: {
        unvsName:'广东金融学院',
        unvsId:'5'
      },
      shareLink: window.location.origin + '/active/gdjrUniversityForward/index',
      footer: {
        actName: '',
        inviteId: '',
        enrollEnd: false, //活动是否结束
        scholarship: '128',
        actStarted: false,//活动是否开始
        actEndTime:0, //活动结束时间
        actStartTime:0,//活动开始时间
      },
      regOrigin: 42,
    }
  },
  computed: {
    pfsnLevel() {
      return this.$route.query.pfsnLevel || '1';
    },
    regChannel() {
      return this.$route.query.regChannel || '';
    },
  },
  methods:{
    enroll() {
      this.$router.push({
        name: 'adultExamEnrollCheck',
        query: {
          activityName: 'scholarship',
          inviteId: this.inviteId,
          actName:this.footer.actName,
          action: 'login',
          scholarship: this.footer.scholarship,
          recruitType:'1',
          pfsnLevel: this.pfsnLevel,
          regChannel: this.regChannel,
          unvs: JSON.stringify(this.unvs),
        }
      });
    },
    setActiveInfo(acInfo){
      this.acInfo = acInfo
      this.footer.actName = acInfo.actName;
      this.footer.actStartTime =acInfo.StartTime
      this.footer.actEndTime = acInfo.EndTime;
    },
    setTabIndex(index){
      this.tabIndex = index;

      if(document.documentElement.scrollTop){
        document.documentElement.scrollTop = 0;
      }else if(document.body.scrollTop){
        document.body.scrollTop=0;
      }

    },
    setActiveStatus(status){
      this.acStatus =status;

      if(status == 1){
        this.footer.actStarted = true;
      };

      //活动结束
      if( status == 2){
        this.footer.enrollEnd = true;

      }
    },
    setIsReceive(status){
      this.isReceive = status;
    },
  }
}
</script>
<style lang="less" scoped>
.home{
  margin-bottom: .6rem;
  /deep/ .van-tab{
    color:#fff;
    font-family: SourceHanSansCN-Bold, SourceHanSansCN;
    font-size: 0.14rem;
    /deep/ .van-tab__text--ellipsis{
      overflow: inherit;
    }
  }

  /deep/ .van-tabs__line{
    display: none;
  }
  /deep/ .van-tab--active{
    background-color: #E14E49;
    font-weight: bold;
  }
  /deep/ .van-tabs__wrap,
  .van-tabs__wrap scroll-view,
  .van-tabs__nav,
  .van-tab {
    height: 0.5rem!important;
  }
}
</style>
