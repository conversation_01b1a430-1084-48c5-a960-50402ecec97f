<template>
  <!-- 客服联系-手动下载APP页面 -->
  <div class="landingPage-wrapper">
    <wx-open-launch-app
      id="launch-btn"
      @error="handleCallAppError"
      @launch="callAppLaunchClickPoint"
      :appid="OpenAppId"
      :extinfo="extinfo"
    >
      <script type="text/wxtag-template">
              <style>
          .down{
              width: 1000px;
              height: 1000px;
            z-index: 999;
          }
        </style>
        <div class='down' id="down"></div>
      </script>
    </wx-open-launch-app>
  </div>
</template>

<script>
import openApp from "@/mixins/openApp";
import { isIOS } from "@/common";

export default {
  mixins: [openApp],
  data() {
    return {
      inforObj: {},
      extinfo: "yuanzhiapp://yzwill.cn",
    };
  },
  beforeRouteEnter(to, from, next) {
    // 修复iOS版微信HTML5 History兼容性问题
    if (isIOS() && to.path !== location.pathname) {
      location.assign(to.fullPath);
    } else {
      next();
    }
  },
  created() {
    let obs = {}; // 获取参数
    let search = window?.location?.search;
    search = decodeURIComponent(search);
    search = search?.split("?")[1]?.split("&");
    if (search && search.length) {
      for (let i = 0; i < search.length; i++) {
        const key = search[i]?.split("=");
        if ([key[0]]) obs[key[0]] = key[1];
      }
      // encodeURIComponent 对URL路径字符手动转码
      // decodeURIComponent 对URL路径解码
      this.extinfo = `yuanzhiapp://yzwill.cn${
        obs.pageName || ""
      }?params=${JSON.stringify(obs)}`;
    }
    console.log("手动下载APP页面-h5-解码：", this.extinfo, obs);
    this.inforObj = obs;
    this.callAppLaunch();
  },
  mounted() {
    this.wxCallAppInit();
  },
  methods: {
    /**
     * 埋点参数说明
     * targetType: 埋点类型
     * targetId: 描述Id
     * entryRemark: 进入页面埋点的描述
     * clickRemark: 点击前往按钮埋点的描述
     */
    // 唤醒App失败
    handleCallAppError() {
      this.callAppLaunchClickPoint();
      downloadApp();
    },
    // 前往App按钮点击埋点
    callAppLaunchClickPoint() {
      const { targetType, targetId = "2", clickRemark } = this.inforObj;
      const remark = clickRemark;
      if (targetType && remark) {
        this.$yzStatistic(targetType, targetId, remark);
      }
    },
    // 执行页面进入埋点
    callAppLaunch() {
      const { targetType, targetId = "2", entryRemark } = this.inforObj;
      if (targetType && entryRemark) {
        this.$yzStatistic(targetType, targetId, entryRemark);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.landingPage-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background: url("https://static.yzou.cn/h5/guide-download.png") no-repeat
    center;
  background-size: 100%;
}
</style>
