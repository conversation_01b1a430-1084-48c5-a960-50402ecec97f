<template>
  <mt-popup v-model="isShow" position="bottom">
    <div class="commonPop-box">
      <div class="commonPop-content">
        <mt-picker :slots="slots" @change="onValuesChange" ref="picker"></mt-picker>
      </div>
      <div class="commonPop-title">
        <span @click="close">取消</span>
        <span @click="confirm">确定</span>
      </div>
    </div>
  </mt-popup>
</template>

<script>
  import {
    provinceName,
    cityName,
    districtName,
    provinceCode,
    cityCode,
    districtCode
  } from '../../common';
  import picker from '@/components/picker';
  
  export default {
    props: {
      types: {
        type: Number,
        default: 3
      },
      defaults: {
        type: Object,
        default: () => {
          return {
            provinceCode: '110000',
            cityCode: '110100',
            districtCode: '110101'
          }
        }
      },
      eyId: null
    },
    data() {
      return {
        isShow: false,
        provinceDatas: {},
        slots: [
          {flex: 1, values: []},
          {divider: true, content: '-'},
          {flex: 1, values: []},
          {divider: true, content: '-'},
          {flex: 1, values: []}
        ],
        provinceName: '',
        provinceCode: '',
        cityName: '',
        cityCode: '',
        districtName: '',
        districtCode: ''
      }
    },
    created() {
      this.init();
      this.getDatas();
    },
    methods: {
      // 获取省市区
      getDatas: function () {
        this.$http.post('/mkt/getProvince/1.0/', {learnId: this.$route.query.learnId, eyId: this.eyId}).then(res => {
          const {code, body} = res;
          if (code === '00') {
            this.provinceDatas = body;
            this.init();
          }
        });
      },
      init: function () {
        const province = this.provinceDatas.province || [];
        let provinceList = [];
        for (let item of province) {
          provinceList.push(item.provinceName);
        }
        this.slots[0].values = provinceList;
  
        this.$nextTick(() => {
          this.$refs.picker.setSlotValue(0, provinceList[0])
        });
      },
      open: function () {
        this.isShow = true;
      },
      close(){
        this.isShow = false;
      },
      // 设置市的备选值数组
      setCitySlotValue: function () {
        if (!this.provinceCode) return;
  
        const filterVal = this.provinceCode.substr(0, 2);
        let citys = [];
        let cityList = this.provinceDatas.city || [];
        cityList = cityList.filter(item => item.cityCode.startsWith(filterVal));
        for (let item of cityList) {
          citys.push(item.cityName)
        }
        this.$refs.picker.setSlotValues(1, citys);
      },
      // 设置区的备选值数组
      setDistrictValue: function () {
        if (!this.cityCode) return;
  
        const filterVal = this.cityCode.substr(0, 4);
        let districts = [];
        let districtList = this.provinceDatas.district || [];
        districtList = districtList.filter(item => item.districtCode.startsWith(filterVal));
        for (let item of districtList) {
          districts.push(item.districtName)
        }
        this.$refs.picker.setSlotValues(2, districts);
      },
      onValuesChange: function (picker, values) {
        if (values[0] && this.provinceName !== values[0]) {
          this.provinceName = values[0];
          this.provinceCode = provinceCode(this.provinceName);
        }
        if (values[1] && this.cityName !== values[1]) {
          this.cityName = values[1];
          this.cityCode = cityCode(this.cityName, this.provinceCode);
        }
        if (values[2]) {
          this.districtName = values[2];
          this.districtCode = districtCode(this.districtName, this.provinceCode, this.cityCode);
        }
      },
      confirm() {
        const val = {
          provinceCode: this.provinceCode,
          provinceName: this.provinceName,
          cityCode: this.cityCode,
          cityName: this.cityName,
          districtCode: this.districtCode,
          districtName: this.districtName
        };
        this.$emit('input', val);
        this.isShow = false;
      },
      setValues: function (provinceCode, cityCode, districtCode) {
        this.$refs.picker.setValues([provinceName(provinceCode), cityName(cityCode, provinceCode), districtName(districtCode, provinceCode, cityCode)]);
        this.$nextTick(() => {
          this.confirm();
        });
      }
    },
    watch: {
      provinceCode: function () {
        this.setCitySlotValue();
      },
      cityCode: function () {
        this.setDistrictValue();
      }
    },
    components: {picker}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/commonPopBox";
</style>
<style lang="less">
  @import "../../assets/less/variable";
  
  .mint-popup-bottom{ width:100%; }
  .picker-items{
    .picker-slot{ font-size:.16rem; }
  }
  .picker-toolbar{ height:.5rem; line-height:.5rem; text-align:center; font-size:.16rem; }
</style>
