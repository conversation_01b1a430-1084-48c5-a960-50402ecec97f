<template>
    <div class="cert_wrap" >
      <div class="my_cert">
      <div style="overflow: scroll;width: 100%;height: 100%">
        <template v-if="hasCert">
          <div class="info">
            <div class="info_detail">
              <img :src="list[0].headImg" alt="">
              <div class="txt">
                <p>{{list[0].nikeName||''|b64DecodeUnicode}}</p>
                <h2>我是第{{list[0].udNum}}位上进者</h2>
              </div>
            </div>
            <div class="save" @click="shareCert">保存证书</div>
          </div>
          <div class="title_cert">
            <img src="../../../assets/image/active/321Activity/txt_cert.png" alt="">
          </div>
          <!--<div class="cert">-->
          <!--<img src="../../../assets/image/active/321Activity/certification.png" alt="">-->
          <!--<span>{{list[0].udNum}}</span>-->
          <!--<span>{{list[0].nickName||''|b64DecodeUnicode}}</span>-->
          <!--</div>-->
          <div class="cert_img" id="cert_img">

            <img src="../../../assets/image/active/321Activity/my-cert.png" alt="">
            <!--<span>{{info.udNum}}</span>-->
            <!--<span>{{nickName}}</span>-->

            <p class="txt4"><span>证书编号：</span>{{code}}</p>
            <p class="txt1">第 <span>{{list[0].udNum}}</span> 位上进者 <span>{{list[0].nikeName||''|b64DecodeUnicode}} </span>，在「321上进日」活动中发表《上进宣言》，并郑重承诺今后努力实现上进目标，传递时代正能量,展现上进新风貌，现已通过【上进协会】评估认证。</p>
            <p class="txt2">特此发证，以资鼓励。</p>
            <p class="txt3">{{Date.now() | formatDate('yyyy.MM.dd')}}</p>
            <div id="qrcode" ref="qrcode"></div>
          </div>
          <div class="title_audio title_cert">
            <img src="../../../assets/image/active/321Activity/txt_audio.png" alt="">
          </div>
          <div class="all_publish">
            <div class="item" v-for="item,i in list" >
              <div class="audio" v-if="item.udType=='1'" @click="play(item.udType,i,item.udId,item)">{{item.resourcesTime}}
                <img  src="../../../assets/image/active/321Activity/''@2x.png" alt="">
                <img v-if="item.state!='play'" src="../../../assets/image/active/321Activity/audio_icon.png" alt="">
                  <img src="../../../assets/image/active/321Activity/audio.gif" alt="" v-else>
                <audio :src="item.resourcesUrl |imgBaseURL" ref="media"></audio>
              </div>

              <div class="video" v-if="item.udType=='2'" @click="play(item.udType,i,item.udId,item)">
                <template v-if="item.state!='play'">
                  <div class="bg"></div>
                  <img :src="item.resourcesUrl+'?x-oss-process=video/snapshot,t_1000,f_jpg' | imgBaseURL" alt="">
                  <img src="../../../assets/image/active/321Activity/play_black.png" alt="">
                </template>
                <video :src="item.resourcesUrl |imgBaseURL" ref="media"></video>
              </div>
              <div class="share_heart" v-if="item.isShow!=2">
                <div class="heart" @click="heart(item.udId,item.isPraise,i)">
                  <img src="../../../assets/image/active/321Activity/mycert_heart.png" alt="" v-if="!item.isPraise">
                  <img src="../../../assets/image/active/321Activity/mycert_heart_sel.png" alt="" v-else>
                  <p>{{item.praiseNum||0}}人祝福</p>
                </div>
                <div class="share" @click="share(item.udId)">
                  <img src="../../../assets/image/active/321Activity/mycert_share.png" alt="">
                  <p>分享</p>
                </div>
              </div>
              <p v-else style="color: rgba(255, 232, 226, 1);text-align: center;font-size: .12rem">内容审核不通过</p>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="nothing">
            <img src="../../../assets/image/active/321Activity/nothing.png" alt="">
            <p>您暂无发表任何上进宣言目标哦！
              快去发表，获得上进荣誉证书吧～</p>
            <!--<button @click="toHome">发表上进宣言目标</button>-->
          </div>
        </template>
      </div>
      </div>
      <div class="img_wrap" v-if="isShow">
        <div class="bg" @click="isShow=false"></div>
        <img :src="imgBase" alt="">
        <p>长按保存图片</p>
      </div>
      <div class="footer" v-if="inviteId&&hasCert" @click="toHome">去查看活动👉</div>
      <share title="我在参加321上进日活动，快来围观我的上进宣言，为上进发声！" :imgUrl="imgUrl" desc="3月21日，发布上进宣言，干一件上进的事，影响更多的人一起上进。" :link="shareLink" />
    </div>
</template>

<script>
  import share from "@/components/share"
  import QRCode from "qrcodejs2";
  import * as html2canvas from "html2canvas";
  import {Toast} from 'vant'
    export default {
        name: "mycertification",
      components:{share},
      data(){
          return{
            hasCert:false,
            list:[],
            endTime:new Date(2020,2,28,0,0,0).getTime(),
            pageSize:10,
            pageNum:1,
            isShow:false,
            imgBase:'',
            title:'',
            imgUrl:'http://yzims.oss-cn-shenzhen.aliyuncs.com/logo/321act/321logo.png',
            shareLink:'',
            showInvite:false,
            invite:{},
            inviteId:'',
            ShortUrlId:'',
            flag:true
          }
      },
      created(){

        this.inviteId=this.$route.query.ykAuthtoken;
        if (!!this.inviteId) {
          this.getOther()
        }else{
          this.getList()
        }
        this.shareLink=`${window.location.origin}/active/321Activity/mycert?ykAuthtoken=${this.storage.getItem('ykAuthtoken')}`;
      },
      computed:{
        code(){
          let date=new Date()
          let str='SJYZ-'+date.getFullYear()+((date.getMonth()+1)>9?date.getMonth()+1:'0'+(date.getMonth()+1));
          str+=date.getDate()>9?date.getDate():'0'+date.getDate();
          let num=(this.list[0].udNum.toString().split("").reverse().join("")+'0000').substr(0,5).split("").reverse().join("")
          return str+num
        }
      },
      methods:{
        play(type,i,udId,item){
          //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
          if(this.$refs.media[i].paused){
            let audio=document.getElementsByTagName('audio');
            for (let i=0;i<audio.length;i++){
              if(!audio[i].paused){
                audio[i].pause();
              }
            }
            let video=document.getElementsByTagName('video');
            for (let i=0;i<video.length;i++){
              if(!video[i].paused){
                video[i].pause();
              }
            }
            this.$refs.media[i].play()
            item.state='play'
            this.$set(this.list,i,item)
            this.$refs.media[i].addEventListener('pause',()=>{
              item.state='stop'
              this.$set(this.list,i,item)
            })
          }else{
            this.$refs.media[i].pause()
          }
        },
          getOther(){
            this.$http.post('/mkt/getUpDeclarationListByOthers/1.0/',{othersToken:this.inviteId.replace(/ /g, "+")}).then(res=>{
                if(res.code=='00'){
                  this.list=res.body;
                  if(this.list.length){
                    this.hasCert=true;
                    this.$nextTick(()=>{
                      this.transferShortUrl()
                    })

                  }
                }
            })
          },
        toHome(){
          this.$router.push({name:"321Activity"})
          MtaH5.clickStat('1')
        },
          getList(){
            this.$http.post('/mkt/getSelfUpDeclarationList/1.0/').then(res=>{
              if(res.code=='00'){
                this.list=res.body;
                if(this.list.length){
                  this.hasCert=true;
                  this.$nextTick(()=>{
                    this.transferShortUrl()
                  })

                }
              }
            })
          },
        b64DecodeUnicode(str)  {
          return decodeURIComponent(atob(str).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
        },
        modal(){
          this.$modal({message:'今年321上进日活动已经结束了哦~欢迎您明年赶早来参与上进啦！',icon:'warning'})
        },
        heart(praiseId,isPraise,i){
          if(this.endTime<Date.now()){
            this.modal();
            return;
          }
          if(this.flag) {
            this.flag = false;
            let fabulousNum=isPraise?-1:1;
            this.$http.post('/mkt/praiseUpDeclaration/1.0/',{praiseType:4,praiseId:praiseId,fabulousNum:fabulousNum}).then(res=>{
              if(res.code=='00'){
                this.list[i].isPraise=!isPraise;
                isPraise?this.list[i].praiseNum--:this.list[i].praiseNum++;
                this.flag = true;
              }
            })
          }else {
            Toast('手速慢一点!')
          }

        },
        share(udId){
            this.$router.push({name:'321ActivityShare',query:{udId:udId}})
        },
        shareCert(){
          this.$indicator.open()
          this.$nextTick(() => {
            html2canvas(document.getElementById('cert_img'), {width: document.getElementById('cert_img').offsetWidth, height: document.getElementById('cert_img').offsetHeight, useCORS: true}).then(canvas => {
              this.$indicator.close();
              this.imgBase=canvas.toDataURL();
              this.isShow=true
            });
          });
          MtaH5.clickStat('3')
        },
        useqrcode(){
          // this.inviteUrl = `${window.location.origin}/active/321Activity?inviteId=${this.storage.getItem('authToken')}&code=1`;
          this.inviteUrl = `${window.location.origin}/active/321Activity/urlTransfer?u=${this.ShortUrlId}`
          let qrcode = new QRCode('qrcode', {
            width: 72,  // 设置宽度
            height: 72, // 设置高度
            text: this.inviteUrl,
            correctLevel:QRCode.CorrectLevel.L,
            backgoround: '#fff'
          })
          this.$nextTick(()=>{
            let qrcode=document.getElementById('qrcode');
            qrcode.getElementsByTagName("img")[0].style.width="100%"
          })
        },

      transferShortUrl(){
        let url = `${window.location.origin}/active/321Activity?inviteId=${this.storage.getItem('authToken')}&code=1`;
        this.$http.post('/proxy/commitShortUrl/1.0/',{url:url}).then((res=>{
            const {code,body} = res;
            if(code !=='00') return;
            this.ShortUrlId = body;
            this.$nextTick(()=>{
                this.useqrcode();
            })
        }))
      },        
      }
    }
</script>

<style scoped lang="less">
.cert_wrap{
  width: 100%;
  overflow: hidden;
  max-width: 640px;
  margin: 0 auto;
  background-color: rgba(194, 22, 16, 1);
  height: 100vh;
  padding-top: .2rem;
  padding-bottom: .2rem;
  &.active{
    padding-top: 0;
    display: flex;
    flex-direction: column;
    .my_cert{
      margin-top: .2rem;
    }
  }
  .my_cert{
    width: 3.28rem;
    margin: 0 auto;
    height: 100%;
    /*overflow: scroll;*/
    background-image: url("../../../assets/image/active/321Activity/shareBg.png");
    background-size: 100% 100%;
    /*padding-bottom: .4rem;*/
    padding: .1rem 0 .15rem;
    background-color: rgba(194, 22, 16, 1);
      .info{
        margin-top: .21rem;
        height: auto;
        overflow: hidden;
        .info_detail{
          float: left;
          img{
            float: left;
            margin-left: .35rem;
            width: .48rem;
            height: .48rem;
            border-radius: 50%;
          }
          .txt{
            float: left;
            margin-left: .08rem;
            p{
              font-size: .12rem;
              margin-top: .07rem;
              color: white;
              width: 1.3rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            h2{
              font-weight: bold;
              color: rgba(253, 188, 0, 1);
              width: 1.3rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .save{
          float: right;
          width: .78rem;
          height: .28rem;
          line-height: .28rem;
          text-align: center;
          color: white;
          background-image: linear-gradient(to right,RGBA(249, 172, 127, 1),RGBA(253, 125, 84, 1));
          border-radius: .14rem;
          margin-right: .22rem;
          margin-top: .15rem;
        }
      }
    .title_cert{
      width: 2.1rem;
      height: .17rem;
      margin: .18rem auto;
      img{
        width: 100%;
      }
    }
    .cert_img{
      width: 3.02rem;
      height: 2.58rem;
      position: relative;
      margin-left: .15rem;
      .txt1{
        position: absolute;
        color: rgba(51, 51, 51, 1);
        font-size: .1rem;
        text-indent: 2em;
        width: 2.5rem;
        top:.9rem;
        left:.25rem;
        text-align: left;
        word-break: break-all;
        span{
          color: RGBA(183, 155, 89, 1);
        }
      }
      .txt2{
        position: absolute;
        color: rgba(51, 51, 51, 1);
        font-size: .1rem;
        text-indent: 2em;
        width: 2.8rem;
        top:1.45rem;
        left:.2rem;
        text-align: left;
      }
      .txt3{
        width: 1rem;
        position: absolute;
        color: rgba(51, 51, 51, 1);
        font-size: .1rem;
        text-indent: 2em;
        top:2.25rem;
        right:.13rem;
        text-align: left;
      }
      .txt4{
        width:100%;
        text-align: center;
        position: absolute;
        color: rgba(51, 51, 51, 1);
        font-size: .1rem;
        text-indent: 2em;
        top:.7rem;
        right:0rem;
        span{
          color: RGBA(174, 147, 102, 1);
        }
      }
      #qrcode{
        position: absolute;
        width: .72rem;
        height: .72rem;
        top:1.7rem;
        left:.28rem;
        img{
            width:100%; ;
       }
      }
      img{
        width: 3.02rem;
        height: 100%;
        position: absolute;

      }
      /*span{*/
        /*position: absolute;*/
        /*font-size: .1rem;*/
        /*right: 1.15rem;*/
        /*top:.92rem;*/
        /*width: .4rem;*/
        /*text-align: center;*/
        /*overflow: hidden;*/
        /*word-break: break-all;*/
        /*white-space: nowrap;*/
        /*color:rgba(102, 102, 102, 1) ;*/
        /*&:last-of-type{*/
          /*width: .5rem;*/
          /*right:.27rem*/
        /*}*/
      /*}*/
    }
    .cert{
      width: 2.88rem;
      height: 2.46rem;
      margin: 0 auto .04rem;
      position: relative;
      img{
        width: 100%;
      }
      span{
        position: absolute;
        font-size: .08rem;
        right: 1.05rem;
        top:.8rem;
        width: .3rem;
        text-align: center;
        overflow: hidden;
        word-break: break-all;
        white-space: nowrap;
        color:rgba(102, 102, 102, 1) ;
        &:last-of-type{
          width: .4rem;
          right:.27rem
        }
      }
    }
    .all_publish{
     .item{
       .audio{
         width: 2.24rem;
         height: .32rem;
         border-radius: .04rem;
         background-color: rgba(132, 201, 102, 1);
         font-size: .16rem;
         line-height: .32rem;
         padding-left: .13rem;
         margin: .18rem auto .15rem;
         img:first-of-type{
           width: .06rem;
           height: .06rem;
           margin-top: .04rem;
         }
         img:last-of-type{
           width: .11rem;
           height: .14rem;
           margin-top: .1rem;
           margin-left: .05rem;
         }
       }
       .video{
         width: 2.32rem;
         height: 3.34rem;
         position: relative;
         margin: .16rem auto;
         border: solid 2px rgba(255, 199, 152, 1) ;
         border-radius: .15rem;
         video{
           width: 100%;
           height: 100%;
         }
         .bg{
           width: 100%;
           height: 100%;
           position: absolute;
           left:0;
           top:0;
           background-color: rgba(0, 0, 0, .1);
           z-index: 2;
         }
         img:first-of-type{
           width: 100%;
           height: 100%;
           position: absolute;
           border-radius: .15rem;
         }
         img:last-of-type{
           position: absolute;
           width: .32rem;
           height: .32rem;
           left: 0;
           right: 0;
           top:0;
           bottom:0;
           margin: auto;
         }
       }
       .share_heart{
         height: auto;
         overflow: hidden;
         margin-bottom: .08rem;
         display: flex;
         justify-content: center;
        .heart{
          /*float: left;*/
          /*margin-left: .88rem;*/
          display: flex;
          align-items: center;
          flex-direction: column;
          img{
            width: .44rem;
            height: .42rem;
            margin-bottom: .05rem;
          }
        }
         p{
           color: white;
           font-size: .12rem;
           line-height: .17rem;
         }
         .share{
           margin-left: .81rem;
           float: left;
           display: flex;
           align-items: center;
           flex-direction: column;
           img{
             width: .32rem;
             height: .32rem;
             margin-bottom: .05rem;
           }

         }
       }
       .err{
         font-size: .12rem;
         text-align: center;
         color: rgba(255, 232, 226, 1);
         margin-bottom: .15rem;
       }
     }
    }
    .nothing{
      position: absolute;
      top:0;
      left:0;
      right:0;
      bottom:0;
      margin: auto;
      img{
        display: block;
        width: 1.25rem;
        height: 1.03rem;
        margin: 2.25rem auto 0;
      }
      p{
        margin: .29rem auto 0;
        width: 2rem;
        height: .34rem;
        color: rgba(255, 232, 226, 1);
        font-size: .12rem;
      }
      button{
        width: 1.7rem;
        height: .4rem;
        display: block;
        color: white;
        text-align: center;
        font-size: .16rem;
        background-image: linear-gradient(to bottom,rgba(252, 190, 95, 1),rgba(248, 137, 49, 1));
        border-radius: .28rem;
        margin: .41rem auto 0;
        box-shadow: 0 0 .09rem 0 rgba(235, 83, 0, 1);
        border-bottom:solid .03rem rgba(235, 83, 0, 1) ;
        border-right:solid .02rem rgba(235, 83, 0, 1) ;
      }
    }
  }
}
.img_wrap{
  position: fixed;
  width: 100%;
  height:100%;
  overflow: hidden;
  left:0;
  right: 0;
  top:0;
  bottom:0;
  margin: auto;
  z-index:999;
  .bg{
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.4);
    z-index: 999;
  }
  img{
    width: 2.5rem;
    position: absolute;
    left:0;
    right: 0;
    top:0;
    bottom:0;
    margin: auto;
    z-index:99999;
  }
  p{
    color: white;
    width: 100%;
    text-align: center;
    position: absolute;
    left:0;
    bottom:1.5rem;
    margin: auto;
    z-index:99999;
  }
}
.invite {
  padding: 0.16rem 0;
  background-image: url("../../../assets/image/active/dreamBuild/content-bg.png");
  background-size: 100%;
  img {
    width: 0.48rem;
    height: 0.48rem;
    border-radius: 50%;
    float: left;
    margin-left: 0.24rem;
  }
  .headTxt {
    float: left;
    margin-left: 0.12rem;
    p {
      font-size: 0.14rem;
      span {
        color: #e15443;
        font-size: 0.14rem;
      }
      &:first-of-type {
        font-size: 0.14rem;
      }
    }
  }
}
.invite-top {
  background-color: #f3b422;
  height: 0.72rem;
  position: relative;
  z-index: 10;
}
.footer{
  position: fixed;
  width: 100%;
  background-color: rgba(237, 152, 66, 1);
  height: .6rem;
  line-height: .6rem;
  color: white;
  font-size: .16rem;
  bottom:0;
  left:0;
  text-align: center;
}
</style>
