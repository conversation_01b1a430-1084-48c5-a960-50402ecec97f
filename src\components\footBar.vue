<template>
  <div class="footer">
    <div class="wrap bc-w">
      <div class="inner" @click="submit" :class="{ grad: grad, gray: gray }">
        {{ title }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    grad: {
      type: Boolean,
      default: false,
    },
    // 按钮置灰
    gray: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    submit: function () {
      this.$emit("submit");
    },
  },
};
</script>

<style lang="less" scoped>
@import "../assets/less/variable";
.footer {
  height: 0.6rem;
  .wrap {
    background: transparent;
    position: inherit;
    margin-top: 0.1rem;
    padding: 0.1rem;
  }
  &[disabled] {
    .inner {
      opacity: 0.6;
      pointer-events: none;
    }
  }
  .inner {
    border-radius: 0.25rem;
    line-height: 0.5rem;
    text-align: center;
    color: #fff;
    font-size: 0.15rem;
    background-color: #f26662;
    background-image: @bgColor;
    &.grad {
      background-image: linear-gradient(
        to right,
        rgba(240, 145, 144, 1),
        rgba(240, 120, 119, 1),
        rgba(240, 110, 108, 1)
      );
    }
  }
  &.active {
    .inner {
      background-color: #efe142;
      color: #333;
      background-image: linear-gradient(to bottom right, #efe142, #ffd100);
    }
  }
  .inner {
    &.gray {
      background: #b9b4b4;
      color: #fff;
    }
  }
}
</style>
