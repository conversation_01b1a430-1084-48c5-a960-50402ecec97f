<template>
  <div class="yz-self-form">
    <div class="yz-self-form__content">
      <label>
        <span>姓名</span>
        <van-field class="right" v-model="name" placeholder="请输入姓名" />
      </label>
      <label>
        <span>身份证</span>
        <van-field class="right" v-model="idCard" @blur="idCardBlur" placeholder="请输入身份证" />
      </label>
      <label @click='openPopup("unvsId")'>
        <span>主考院校</span>
        <div class="right with-arrow">
          <van-icon name='arrow'></van-icon>
          <span class="val">{{unvs.unvsName}}</span>
        </div>
      </label>

      <label @click='openPopup("pfsnId")'>
        <span>专业</span>
        <div class="right with-arrow">
          <van-icon name='arrow'></van-icon>
          <span class="val">{{pfsn.pfsnName}}</span>
        </div>
      </label>
        <label @click='openPopup("scholarship")'>
        <span>活动名称</span>
        <div class="right with-arrow">
          <van-icon name='arrow'></van-icon>
          <span class="val">{{scholarshipObj.aliasName}}</span>
        </div>
      </label>
      <label>
        <span>年级</span>
        <!-- --changeEnroll-- -->
        <div class="right"> <span class="val2">2026研</span> </div>
      </label>

    </div>
    <select-popup ref="selectPopup">
      <unvs v-model="unvs" :datas="$data" v-if="currentSelect==='unvsId'"  @onChange="onChange"/>
      <pfsn v-model="pfsn" :datas="$data" v-if="currentSelect==='pfsnId'" @onChange="onChange" />
      <scholarship v-model="scholarshipObj" :datas="$data" v-if="currentSelect==='scholarship'" />
    </select-popup>
    <button class="yz-self-form__next" @click='next'>下一步</button>
  </div>
</template>

<script>
import { Field, Icon, Picker, Popup } from 'vant';
import { isIDCard, isStudent, isEmployee } from '@/common';
import selectPopup from '@/components/selectPopup';
import unvs from '@/components/invite/graduate-unvs';
import pfsn from '@/components/invite/graduate-pfsn';
import scholarship from '@/components/invite/graduate-scholarship';

export default {
  components: {
    Field,
    Icon,
    Popup,
    selectPopup,
    unvs,
    pfsn,
    scholarship
  },
  data () {
    return {
      name: '',
      idCard: '',
      unvs: {},
      pfsn: {},
      currentSelect: '', // 显示选择器的类型 对应params的字段名
      activityArr:[],
      scholarshipObj:{}
    };
  },
  created () {
    this.$yzStatistic('marketing.base.browse', '17', '研究生报读缴费页');
  },
  beforeDestroy () {
  },
  mounted () {
    if (isEmployee()) {
      this.$modal({ message: '助学老师不能报读', icon: 'warning' });
      setTimeout(() => {
        this.$router.go(-1);
      }, 500);
      return;
    }
    const url = window.location.pathname
    if (this.storage.getItem('bindStudent') == '1') {
      this.$router.push({ name: 'roleAuth', query: { redirect: url } })
      return
    } else if (this.storage.getItem('bindStudent') == '0') {
      if (isStudent()) {
        this.$router.push({ name: 'roleAuth', query: { redirect: url } })
        return
      }
    }
    this.getInfo();
    this.unvs = this.$route.query.unvs ? JSON.parse(this.$route.query.unvs) || {} : '';
    this.pfsn = {
      pfsnName:this.$route.query.pfsnName||'',
      pfsnId:this.$route.query.pfsnId||''
    }
    console.log(this.pfsn);
  },
  watch: {
    // unvs () {
    //   this.pfsn = {};
    // },
    pfsnLevel () {
      this.pfsn = {};
    },
    "pfsn.pfsnId": {
      handler: function (val, olVal) {
        if (this.unvs.unvsId && val) {
          this.$http.post('/bds/getResearchScholarship/1.0/', { pfsnId: val }).then(res => {
            let { code, body } = res;
            console.log(res);
            this.activityArr = body
            this.scholarshipObj=body[0]
          })
        }
      },
      immediate: true,
    }


  },
  methods: {
    openPopup (key) {
      if ((key === 'unvsId' || key === 'pfsnId')&& this.$route.query.unvs) {
        return;
      }
      if (key === 'pfsnId' && !this.unvs.unvsId) {
        this.$modal({ message: '请选择院校', icon: 'warning' });
        return;
      }
      if(key=== 'scholarship'&&!this.pfsn.pfsnId){
        this.$modal({ message: '请选择专业', icon: 'warning' });

      }
      this.currentSelect = key;
      this.$refs.selectPopup.open();
    },
    async getInfo () {
      const res = await this.$http.post('/mkt/getSelfEnroBaseInfo/1.0/');
      if (res.code == '00') {
        this.name = res.body.stdName || res.body.realName || this.storage.getItem('realName');
        this.idCard = res.body.idCard;
      }
    },
    next () {
      this.$yzStatistic('marketing.base.click', '18', '研究生报读缴费下一步');
      if (isEmployee()) {
        this.$modal({ message: '助学老师不能报读', icon: 'warning' });
        setTimeout(() => {
          this.$router.go(-1);
        }, 500);
        return;
      }
      if (!this.name) {
        this.$modal({ message: '请填写姓名', icon: 'warning' });
        return;
      }
      if (!this.idCard) {
        this.$modal({ message: '请填写身份证', icon: 'warning' });
        return;
      }
      if (!this.unvs.unvsId) {
        this.$modal({ message: '请选择院校', icon: 'warning' });
        return;
      }
      if (!this.pfsn.pfsnId) {
        this.$modal({ message: '请选择专业', icon: 'warning' });
        return;
      }
      if (!this.scholarshipObj.aliasName) {
        this.$modal({ message: '请选择活动名称', icon: 'warning' });
        return;
      }
      this.$router.push({
        name: 'classStyle',
        query: {
          ...this.unvs,
          ...this.pfsn,
          name: this.name,
          idCard: this.idCard,

        },
      });
    },
    idCardBlur () {
      this.idCard = this.idCard.trim();
      if (!this.idCard) {
        return;
      }
      if (!isIDCard(this.idCard)) {
        this.$modal({ message: '身份证号不合法，请重新输入', icon: 'warning' });
        this.idCard = '';
      }
    },
    onChange(type){
      if(type==='pfsn'){
        this.scholarshipObj={}
      }else if(type==='unvs'){
        this.pfsn={}
      }
    }
  },
};
</script>

<style lang="less">
.yz-self-form {
  min-height: 100vh;
  background: #f5f6f8;
  padding-bottom: 0.5rem;
}
.yz-self-form__content {
  padding-left: 0.16rem;
  background: #fff;
  & > label {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    height: 0.54rem;
    font-size: 0.14rem;
    &:not(:last-child) {
      border-bottom: 1px solid #eeeeee;
    }
  }
  .right {
    width: 60%;
    text-align: right;
    color: #ababab;
    height: 100%;
    // line-height: 0.54rem;
    .van-field__body {
      height: 100%;
    }
    .val2 {
      line-height: 0.54rem;
      // color: #000;
      margin-right: 0.16rem;
    }
    input {
      height: 100%;
      text-align: right;
      color: #ababab;
    }
    &.with-arrow {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      padding-right: 0.1rem;
      .van-icon-arrow {
        // font-size: 0.18rem;
      }
      .val {
        font-size: 0.14rem;
        padding-right: 0.03rem;
        padding-top: 0.02rem;
      }
    }
  }
}
.yz-self-form__next {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0.5rem;
  font-size: 0.15rem;
  color: #fff;
  text-align: center;
  line-height: 0.5rem;
  background: linear-gradient(
    135deg,
    rgba(240, 145, 144, 1) 0%,
    rgba(240, 120, 119, 1) 66%,
    rgba(240, 110, 108, 1) 100%
  );
}
</style>
