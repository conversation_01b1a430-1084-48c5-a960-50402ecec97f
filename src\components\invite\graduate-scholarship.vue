<template>
  <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
    <div class="select-item" v-for="item in options" :class="{active:value.scholarship===item.scholarship}" @click="selected(item)">{{item.aliasName}}</div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';

  export default {
    props: ['value', 'datas'],
    data() {
      return {
        options: [],
        pageNum: 0,
        pageSize: 20,
        isLoading: false,
        allLoaded: false,
        pfsnId:''
      }
    },
    created() {
      this.pfsnId = this.datas.pfsn.pfsnId || '';
    },
    mounted() {
      this.getUnvsList()
    },
    methods: {
      getUnvsList: function () {
        this.isLoading = true;
      
        this.$http.post('/bds/getResearchScholarship/1.0/', { pfsnId:  this.pfsnId }).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || [];
          this.options.push(...datas);
          this.$nextTick(() => {
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },

      // loadMore: function () {
      //   this.pageNum++;
      //   this.getUnvsList();
      // },
      loadMore: function () {},
      selected: function (val) {
        this.$emit('input', val);
      },
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
</style>
