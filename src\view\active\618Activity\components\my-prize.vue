<template>
  <dialog-container
    v-model="show"
    title='我的奖品'
    @close='close'
    close-on-click-overlay
  >
    <div class="yz-my-prize" :class='{nodata: list.length == 0}'>
      <no-data v-if='list.length == 0' text='你尚未获得任何奖品～' />
      <template v-else>
        <div class="table-head">
          <span>奖品名称</span>
          <span>领取状态</span>
        </div>
        <ul class="yellow-table table-scroll">
          <li v-for="(item, index) in list" :key="index">
            <span class="w1 span">{{item.prizeVO.prizeName}}</span>
            <span class="w1 span" @click='submit(item)'>
              <red-btn :disabled='item.receiveStatus == 1' @click='submit(item)'>
                {{item.receiveStatus == 1 ? "已领取" : '立即领取'}}
              </red-btn>
            </span>
          </li>
        </ul>
        <!-- <div class="table-scroll">
          <table class="yellow-table" border='1'>
            <thead>
              <tr>
                <td class="w-half">奖品名称</td>
                <td class="w-half">领取状态</td>
              </tr>
            </thead>
            <tbody class="small">
              <tr v-for="(item, index) in list" :key="index">
                <td>{{item.prizeVO.prizeName}}</td>
                <td>
                  <red-btn :disabled='item.receiveStatus == 1' @click='submit(item)'>
                    {{item.receiveStatus == 1 ? "已领取" : '立即领取'}}
                  </red-btn>
                </td>
              </tr>
            </tbody>
          </table> -->
        <!-- </div> -->
      </template>
      <p class="notice" v-if='list.length > 0'>注意：所有奖品在{{endTime | formatTimeByMoment('YYYY-MM-DD HH:mm')}} 前截止领取</p>
    </div>
  </dialog-container>
</template>

<script>
import { Toast } from 'vant';
import DialogContainer from './dialog-container';
import RedBtn from './red-btn';
import NoData from './no-data';

export default {
  components: { DialogContainer, RedBtn, NoData },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    endTime: {
      type: [String, Number],
      default: '2021-06-30 23:59',
    },
  },
  data() {
    return {
      show: false,
    }
  },
  watch: {
    value() {
      this.show = this.value;
    },
  },
  methods: {
    close() {
      this.$emit('input', false);
    },
    submit(item) {
      switch (Number(item.prizeId)) {
        case 1:
        case 2:
        case 3:
        case 7:
          this.$emit('openAddress', item.lotteryId);
          break;
        case 4:
        case 8:
          Toast('已成功领取');
          break;
        case 5:
          this.$router.push('/aspirantUniversity/classSelect'); // 月卡 -> 课程
          break;
        case 6:
          this.$router.push('/aspirantUniversity/learningCenter?tabIndex=1'); // 周卡 -> 读书
          break;
        default:
          break;
      }
      this.close();
    },
  },
};
</script>

<style lang="less">
  .yz-my-prize{
    padding: 0 0.15rem 0.15rem;
    position: relative;
    &.nodata{
      padding: 0.33rem 0 0.68rem 0;
    }
    .table-head{
      background: rgb(254, 247, 223);
      color: #453838;
      font-size: 0.14rem;
      font-weight: 500;
      display: flex;
      span{
        width: 50%;
        height: 0.4rem;
        line-height: 0.4rem;
        display: inline-block;
        text-align: center;
        &:not(:last-child){
          border-right: 1px solid #fff;
        }
      }

    }
    .table-scroll{
      max-height: 3rem;
      overflow: auto;
    }
    .notice{
      color: rgba(69, 56, 56, 0.7);
      margin-top: 0.2rem;
      text-align: center;
      font-size: 0.13rem;
    }
  }
</style>
