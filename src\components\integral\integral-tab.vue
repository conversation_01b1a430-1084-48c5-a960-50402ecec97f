<template>
  <div>
    <div class="tab-nav bc-w mt clearfix">
      <a class="item" href="javascript:;" :class="{active:active==='earnings'}" @click="active='earnings'">收入</a>
      <a class="item" href="javascript:;" :class="{active:active==='outlay'}" @click="active='outlay'">支出</a>
    </div>
    <mt-tab-container :swipeable="false" v-model="active">
      <mt-tab-container-item id="earnings" v-infinite-scroll="loadMore"
                             infinite-scroll-disabled="isLoading" :infinite-scroll-distance="20">
        <router-link to="" class="item" v-for="(item,index) in accountSerial" :key="index">
          <div class="fr" v-text="`+${Number(item.amount).toFixed(0)}`"></div>
          <div class="fl">
            <p v-text="item.excDesc"></p>
            <p>获得时间：{{item.updateTime}}</p>
          </div>
        </router-link>
        <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="accountSerial.length">
          <!--<div class="other">(╯︵╰) 没有更多啦~</div>-->
        </load-bar>
      </mt-tab-container-item>
      <mt-tab-container-item id="outlay" v-infinite-scroll="loadMore2"
                             infinite-scroll-disabled="isLoading2" :infinite-scroll-distance2="20">
        <router-link to="" class="item" v-for="(item,index) in accountSerial2" :key="index">
          <div class="fr" v-text="`-${Number(item.amount).toFixed(0)}`"></div>
          <div class="fl">
            <p v-text="item.excDesc"></p>
            <p v-text="item.updateTime"></p>
          </div>
        </router-link>
        <load-bar :isLoading="isLoading2" :allLoaded="allLoaded2" :total="accountSerial2.length">
          <!--<div class="other">(╯︵╰) 没有更多啦~</div>-->
        </load-bar>
      </mt-tab-container-item>
    </mt-tab-container>
  </div>
</template>
<script>
  import loadBar from '@/components/loadBar';
  export default {
    data() {
      return {
        active: 'earnings',
        bottomStatus: '',
        index: 0,
        index2: 0
      }
    },
    methods: {
      loadMore() {
        if (this.active !== 'earnings') return;
        this.index++;
        this.$emit('LoadMore', this.index)
      },
      loadMore2() {
        if (this.active !== 'outlay') return;
        this.index2++;
        this.$emit('LoadMore2', this.index2)
      }
    },
    watch:{
      active: function () {
        if (this.index2 === 0) {
          this.$parent.$data.isLoading2 = false;
        }
      }
    },
    props: ['accountSerial', 'isLoading', 'allLoaded', 'accountSerial2', 'isLoading2', 'allLoaded2'],
    components: {loadBar}
  }
</script>
<style lang="less" scoped>
  @import "../../assets/less/variable";
  .other{ padding:.15rem 0; clear:both; text-align:center; color:#868686; font-size:.12rem; }
  .tab-nav {
    line-height: .44rem;
    position: relative;
    &:after {
      .borderBottom;
    }
    .item {
      display: block;
      position: relative;
      float: left;
      width: 50%;
      text-align: center;
      color: #444;
      &.active {
        color: @color;
        &:after {
          position: absolute;
          right: .5rem;
          bottom: 0;
          left: .5rem;
          height: 2px;
          background-color: @color;
          content: '';
        }
      }
    }
  }
  .mint-tab-container-item {
    li {
      display: block;
    }
    a.item {
      display: block;
      min-height: .86rem;
      padding: .12rem;
      background-color: #fff;
      position: relative;
      &:after {
        .borderBottom;
      }
      .fl{
        float: none;
        overflow: hidden;
        text-overflow: ellipsis;
        display: table-cell;
        height: .62rem;
        vertical-align: middle;
        p {
          margin: 0;
          &:nth-of-type(1) {
            color: #444;
            font-size: .14rem;
            line-height: 1.3;
          }
          &:nth-of-type(2) {
            color: #666;
            font-size: .12rem
          }
          &:last-child {
            color: #666;
            font-size: .12rem;
            padding-top: .05rem;
          }
        }
      }
      .fr {
        margin-left:.1rem;
        line-height: 0.5rem;
        color: @color;
        font-size: 0.16rem;
      }
    }
  }
</style>
<style lang="less">

</style>

