<template>
    <div class="wrap">
    <img class="sucess" src="../../../assets/image/active/success/ic_get_success.png" alt="">
    <p class="firstText">领取成功！</p>

    <!--<p class="sceondText">请前往&thinsp;<a class="goTo" @click="goTo()" >个人中心-优惠劵</a>&thinsp;查看</p>-->
      <div class="coupon_wrap">
        <div class="prince" >
          <p><span>{{price}}</span>元</p>
          <p>可抵第三年等值学费</p>
        </div>
        <img src="../../../assets/image/active/success/receiveSuccess2.png" alt="" class="coupon">
      </div>
      <p class="thirdText" v-if="status==1">可在<span  class="goTo">我的见证-优惠券中心</span>查看, 快去报<br />读进行学历提升吧!</p>
      <p class="thirdText" v-else>可在<span  class="goTo">个人中心-优惠券</span>查看,<br />快去报读进行学历提升吧！</p>

    <button @click="toEnroll" v-if="status==1 &&!isEnroll">马上报读</button>
    <button @click="toSprint()" v-else>马上激活</button>
    </div>
</template>


<script>
export default {
    data() {
        return {
            inviteId:'',
          unvs:{},
          scholarship:"",
          activityName:'',
          actName:'',
          price:3000,
          status:0,
          isEnroll:false
        }
    },
  beforeRouteEnter(to ,from ,next){
    console.log(from);
    next(vm => {
      if (from.path) {
        vm.oldUrl = from.name;
      }
    });
  },
    created() {
          this.isEnroll = this.storage.getItem('relation')=='4'?true:false
         this.inviteId = this.$route.query.inviteId || "";
         this.scholarship = this.$route.query.scholarship || "";
         this.activityName = this.$route.query.activityName || "";
         this.actName = this.$route.query.actName || "";
         this.unvs=JSON.parse(this.$route.query.unvs||'{}');
         this.status=this.$route.query.status||0
         if(this.$route.query.type=='hn'|| this.$route.query.type=='advance.witness.act.202001.hn' || this.$route.query.type=='jr' ||this.$route.query.type=='12_gj'){
           this.price=1000
         }
    },
    methods:{
        goTo() {
            this.$router.push({path:'/settings/coupon'});
        },
        back() {
            this.$router.go(-1);
        },
        toEnroll() {
            this.$router.push({name:'adultExamEnrollCheck',query:{action:'login',activityName:this.activityName,inviteId:this.inviteId,scholarship:this.scholarship,unvs:JSON.stringify(this.unvs),actName:this.actName}});
        },
        toSprint(){
          if(this.$route.query.type=='hn'){
            this.$router.push({name:'southChinaAgriculturalUniversity',query:{tab:'sprint'}})
          }else if(this.$route.query.type=='jr' ) {
            this.$router.push({name:'gdJinRong',query:{tab:'sprint'}})
          }else if(this.$route.query.type=='ln' ) {
            this.$router.push({name:'gdLingNan',query:{tab:'sprint'}})
          }else if(this.$route.query.type=='12_lz' || this.$route.query.type=='12_gj') {
            this.$router.push({name:'stuInfo'})
          }
          else{
            this.$router.push({name:'enrollmentHomepage',query:{tab:'sprint'}})
          }
          //this.$router.push({name:'enrollmentHomepage',query:{tab:'sprint'}})
        },
    },
  components:{}

}
</script>


<style lang="less" scoped>
.wrap {
    text-align: center;
    background-color: #fff;
    height: 100vh;
    background-image: url("../../../assets/image/active/success/bg.jpg");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    .firstText {
        color: #363636;
        font-size: .17rem;
        text-align: center;
        margin-top: .21rem;
        font-weight: bold;
    }
  .thirdText {
    color: #363636;
    font-size: .14rem;
    margin-top: .14rem;
    padding:0 .5rem ;
    margin-bottom: .21rem;
    color: rgba(54, 54, 54, 1);
    text-align: center;
    .goTo {
      color: rgba(237, 82, 6, 1);
      /*font-size: .17rem;*/
      /*text-decoration: underline;*/
    }
  }
    .sceondText {
        color: #363636;
        font-size: .17rem;
        text-align: center;
        margin-top: .1rem;

    }
    button {
        width: 2.38rem;
        height: .4rem;
        background-image: linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
        border-radius: .2rem;
        margin-top: .15rem;
        font-size: .15rem;
        color:rgba(255,255,255,1);
        border: none;
    }
.sucess {
    width: .3rem;
    height: .3rem;
    margin: .55rem auto 0;
}

.topBar {
    height: .5rem;
    line-height: .5rem;
    background-color: #fff;
    border-bottom: 1px solid rgba(54,54,54,.08);
    padding-left: .15rem;
    font-size: .15rem;
    color:rgba(54,54,54,1);
    text-align: left;
    img {
        width: .24rem;
        height: .24rem;
        margin-top: .13rem;
        margin-right: .09rem;
    }
}
}
.coupon_wrap{
  position: relative;
  width: 2.9rem;
  height: .92rem;
  margin: 0 auto;
  .prince{
    width: 1.3rem;
    text-align: left;
    position: absolute;
    top:0.2rem;
    right: .5rem;
    color: rgba(237, 82, 6, 1);
    p{
      font-size: .14rem;
      line-height: .14rem;
      &:first-of-type{
        font-size: .15rem;
        line-height: .34rem;
      }
      span{
        font-size: .34rem;
      }
    }
  }
  img{
    width: 2.9rem;
    height: .92rem;
  }
}
</style>
