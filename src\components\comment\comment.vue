  <!--公用组件：
      /**
      * 组件名称 comment 弹窗二级评论组件
      * @module @/components/comment/comment
      * @desc 弹窗评论组件 编辑器带图片上传及缓存12小时草稿
      * <AUTHOR>
      * @date 2021/08
      * @param {Object} [info] - 一级评论 回复的对象
      * @param {String｜Number} [mappingId] - 一级评论的mappingId
      * @param {Function} [update] - 更新回复视图
      * @param {Function} [updateComment] - 更新评论列表
      * @example 调用示例
      *  <comment ref='allReply' :mappingId='comMappingId' :info='commentInfo' @update='updateReplay' @updateComment='updateCommentList'></comment>
      */
    -->

<template>
  <van-popup class='comment-popup' v-model="isShow" position="bottom">
    <div class="header-title">
      <span>{{replyList.length || 0}}条回复</span>
      <img class="close" src="../../assets/image/close-grey.png" @click='isShow = false' alt="">
    </div>
    <div class="scroll-box">
      <div class="textContent">
        <div class="userMessageContentList">
          <div class="content clearfix">
            <div class="fl">
              <img :src="commentInfo.headUrl?commentInfo.headUrl+headLimit:commentInfo.headUrl|defaultAvatar2" alt>
            </div>
            <div class="fr">
              <p class="userName">{{commentInfo.userName}}<img class='hot'
                     :src="commentInfo.headUrl||'../../assets/image/scholarshipStory/ic_activity_critical.png'"
                     v-if="commentInfo.hot==1" alt=""></p>
              <p class="time">{{commentInfo.createTime | transfromTimeStr}}</p>
              <span class='fr-zan-icon' @click.stop="like(commentInfo)">
                <img v-if="commentInfo.ifFabulous" class="zan-icon" src="../../assets/image/app/circleAd/new_zan.png"
                     alt />
                <img v-if="!commentInfo.ifFabulous" class="zan-icon"
                     src="../../assets/image/app/circleAd/new-zan-not.png" alt />
                <span v-if="commentInfo.fabulousNum">{{commentInfo.fabulousNum}}</span>
              </span>
              <div class="bottom">
                <p class="uesrQuestion" v-html="commentInfo.content"> </p>
                <template v-if="commentInfo.picUrl" v-for="(img,ix) in commentInfo.picUrl">
                  <img class="imgs" :src="img | imgBaseURL" alt @click="showImgClick(ix,commentInfo.picUrl)" />
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="userMessageContentList" v-for="(item, index) in replyList" :key="index">
        <div class="content clearfix">
          <div class="fl">
            <img :src="item.headUrl?item.headUrl+headLimit:item.headUrl|defaultAvatar2" alt>
          </div>
          <div class="fr" :class='{bb: index != (replyList.length - 1)}'>
            <p class="userName">{{item.userName||item.nickName}}<img class='hot'
                   src="../../assets/image/scholarshipStory/ic_activity_critical.png" v-if="item.hot==1" alt=""></p>
            <p class="time">{{item.createTime | transfromTimeStr}}</p>
            <span class='fr-zan-icon' @click.stop="like(item)">
              <img v-if="item.ifFabulous" class="zan-icon" src="../../assets/image/app/circleAd/new_zan.png" alt />
              <img v-if="!item.ifFabulous" class="zan-icon" src="../../assets/image/app/circleAd/new-zan-not.png" alt />
              <span v-if="item.fabulousNum">{{item.fabulousNum}}</span>
            </span>
            <div class="bottom">
              <p class="uesrQuestion">
                <span>回复 <span class="uesrQuestion-name"> {{item.targetNickName}}</span>：</span>
                <span v-html="item.content"></span>
              </p>
              <template v-if="item.picUrl" v-for="(img,ix) in item.picUrl">
                <img class="imgs" :src="img | imgBaseURL" alt @click="showImgClick(ix,item.picUrl)" />
              </template>
              <!--  不能自己回复自己 -->
              <div class="reply" v-if="item.userId!==storage.getItem('userId')"
                   @click="replyMsg(item.userId,item.userName||item.nickName,commentInfo.commentId,$event)">
                <span>回复</span>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="bg" @click="cancel" v-if="reply"></div>
    <div v-if="reply" class="edit">
      <edit-box v-on:submit="toComment" ref="questionEditBox" key="questionEditBox" :change="true" btnText="提交"
                :inputPlaceholder="holder" :isSocketConnect="true" locatStorageTime="12" />
    </div>

    <edit-box class="hide" v-else ref="questionEditBox" key="questionEditBox" :change="true" btnText="提交"
              :inputPlaceholder="holder" :isSocketConnect="true" locatStorageTime="12" />
    <van-image-preview v-model="showImgInfo" :images="imageArr" :start-position="imgIndex">
    </van-image-preview>
  </van-popup>
</template>

<script>
import { Popup, Dialog } from 'vant'
import editBox from '@/components/live/appEditBox';
import { imgBaseURL, articleURL } from "../../config";

export default {
  name: "comment",
  components: { editBox, Popup, Dialog },
  data () {
    return {
      headLimit: "?x-oss-process=image/resize,m_fixed,h_38,w_38",
      commentId: '',
      comment: [],
      headImg: '',
      nickName: '',
      content: '',
      replyList: [],
      commentInfo: {
        replyList: [],
      },
      commentMsg: '',
      reply: false,
      targetUserId: '',
      parentId: '',
      targetNickName: '',
      holder: '我要评论...',
      isShow: false,
      showImgInfo: false,
      imageArr: [],
      imgIndex: 0,
    }
  },
  props: {
    info: {
      type: Object,
      default: () => { },
    },
    mappingId: {
      type: String | Number,
      default: '',
    },

  },
  created () {
    this.nickName = this.storage.getItem('realName') || this.storage.getItem('zmcName');
  },
  methods: {
    showImgClick (index, arr) {
      this.imgIndex = index;
      arr = arr.map(item => (imgBaseURL + item))
      this.imageArr = arr
      this.showImgInfo = true;
    },
    show () {
      this.isShow = true;
      this.$nextTick(() => {
        this.commentInfo = this.info;
        this.commentId = this.info.commentId;
        this.replyList = this.commentInfo.replyList || [];
        this.getCommentAll();
      });

    },
    init () {
      this.$nextTick(() => {
        document.getElementsByTagName("input")[0].style.width = '2.5rem';
        document.getElementsByTagName("input")[0].focus()
      })
    },
    cancel () {
      this.reply = false
      this.targetUserId = '';
      this.parentId = '';
      this.targetNickName = '';
      this.holder = '我要评论...'
      // document.getElementsByTagName("input")[0].style.width='1.79rem';
    },
    getCommentAll (commentId = this.commentId) {
      this.$http.post('/mkt/getCommentReplyInfo/1.0/', { commentId }).then(res => {
        if (res.code == '00') {
          for (let i = 0; i < res.body.list.length; i++) {
            const item = res.body.list[i];
            item.showAll = true;
            if (item.content) {
              item.showAll = item.content.length <= 25;
              item.contentD = !item.showAll ? item.content.substr(0, 25) : '';
            }
            // 名字
            if (item.mobile) {
              item.userName =
                item.realName ||
                item.stdName ||
                item.nickName ||
                `同学${item.mobile.slice(-4)}`;
              ("同学");
            } else {
              item.userName =
                item.realName ||
                item.stdName ||
                item.nickName ||
                "同学";
            }
            item.fabulousNum =
              item.fabulousNum > 99
                ? "99+"
                : item.fabulousNum;
            item.content = this.$refs.questionEditBox.formatEmotions(item.content);
            if (item.picUrl) {
              item.picUrl = item.picUrl.split(",");
            }
          }

          this.replyList = res.body.list;
          this.$emit('update', { commentId, replyList: this.replyList });
        }
      })
    },
    replyMsg (userId, nickName, commentId, event) {
      if (!this.storage.getItem("authToken")) {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
        return;
      }
      this.reply = true
      this.targetUserId = userId;
      this.parentId = commentId;
      this.holder = `回复@${nickName}`
      this.targetNickName = nickName;
      this.$nextTick(() => {
        this.$refs.questionEditBox.editFocus();
      })
    },
    toComment (content, picUrlList) {
      if (!picUrlList) {
        if (!content) {
          this.$modal({ message: "请输入评论内容!", icon: "warning" });
          return;
        }
      }
      if (content.length > 100) {
        this.$modal({ message: '请输入100字以内的评论内容!', icon: 'warning' })
        return;
      }
      let data = {
        ifLimit: 0,
        content: content,
        headUrl: this.storage.getItem('headImg'),
        picUrl: picUrlList,
        mappingId: this.mappingId,
        commentType: 4,
        nickName: this.nickName,
        authToken: this.storage.getItem('authToken')
      }
      if (this.parentId) {
        data["parentId"] = this.parentId;
        data["targetNickName"] = this.targetNickName;
        data["targetUserId"] = this.targetUserId;
      }
      this.$http.post('/mkt/addNewComment/1.0/', data).then(res => {
        if (res.code == '00') {
          this.parentId = '';
          this.targetNickName = '';
          this.targetUserId = '';
          this.commentMsg = '';
          this.holder = '我要评论...'
          this.reply = false;
          this.getCommentAll(this.commentId);
          this.$nextTick(() => {
            let domEle = document.getElementsByTagName('html')[0] || document.getElementsByTagName('body')[0];
            domEle.scrollTop = this.oldScrollTop
          })
        }
      })
    },
    // 点赞
    async like (item) {
      if (!!this.storage.getItem("authToken")) {
        const res = await this.$http.post('/us/usPraise/1.0/', {
          fabulousNum: item.ifFabulous == 1 ? -1 : 1, // （用户第一次触发传1 第二触发取消点赞传 -1）
          praiseType: 5,
          praiseId: item.commentId,
          userName: this.nickName,
          circleUserId: '',
        });
        if (res.code == '00') {
          item.ifFabulous = item.ifFabulous == 1 ? 0 : 1;
          if (item.ifFabulous) {
            if (!item.fabulousNum) {
              item.fabulousNum = 1;
            } else {
              item.fabulousNum += 1;
            }
          } else {
            item.fabulousNum -= 1;
          }
        }
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }

    },

  },
}
</script>

<style lang='less'>
@import '../../assets/less/variable';
.emotionimg {
  width: 0.24rem;
  height: 0.24rem;
  vertical-align: bottom;
}
.comment-popup {
  border-radius: 0.08rem 0.08rem 0 0;
  .header-title {
    height: 0.6rem;
    line-height: 0.6rem;
    color: #453838;
    font-size: 0.18rem;
    position: relative;
    text-align: center;
    &::after {
      .borderBottom;
    }
    .close {
      position: absolute;
      top: 50%;
      right: 0.1rem;
      transform: translateY(-50%);
      width: 0.38rem;
      height: 0.38rem;
      z-index: 2;
    }
  }
  .scroll-box {
    height: 5.3rem;
    overflow: auto;
  }
}
</style>


<style scoped lang="less">
@import '../../assets/less/variable';
.textContent {
  border-bottom: 0.1rem solid #f8f7f7;
}
.hide {
  left: -999px;
  bottom: -999px;
  opacity: 0;
  position: absolute;
}

.userMessageContentList {
  padding-top: 0.23rem;
  .content {
    padding-bottom: 0.1rem;
    .item {
      position: relative;
      .line {
        position: absolute;
        bottom: 0.01rem;
        height: 1px;
        width: 2.76rem;
        left: 0.64rem;
        background: rgba(23, 6, 6, 0.08);
      }
    }
    .fl {
      width: 0.52rem;
      img {
        width: 0.37rem;
        height: 0.37rem;
        float: right;
        border-radius: 50%;
      }
    }
    .fr {
      width: 3.11rem;
      padding-right: 0.15rem;
      height: auto;
      overflow: hidden;
      // border-bottom: solid 1px rgba(102,102,102,.1);
      position: relative;
      &.bb::after {
        .borderBottom;
      }

      .time {
        font-size: 0.1rem;
        color: #a29b9b;
      }
      .userName {
        font-size: 0.15rem;
        // line-height: .37rem;
        // font-weight: bold;
        color: #746a6a;
        .hot {
          width: 0.47rem;
          margin-top: 0.03rem;
          margin-left: 0.02rem;
        }
      }
      .uesrQuestion {
        display: inline-block;
        width: 2.96rem;
        margin-top: 0.03rem;
        margin-bottom: 0.08rem;
        /*margin-left: 0.1rem;*/
        font-size: 0.14rem;
        color: #453838;
        word-break: break-all;
        /*margin-bottom: .09rem;*/
        &-name {
          color: #004e97;
        }
      }
      .fr-zan-icon {
        position: absolute;
        right: 0.15rem;
        top: 0;
        .zan-icon {
          width: 0.24rem;
          height: 0.24rem;
        }
        span {
          font-size: 0.12rem;
          font-weight: 400;
          color: #000000;
          line-height: 0.3rem;
          margin-left: -0.04rem;
        }
      }
      .like-box {
        position: absolute;
        line-height: 0.2rem;
        font-size: 0.1rem;
        color: #999999;
        top: 0;
        right: 0.15rem;
        display: flex;
        align-items: center;
        .like {
          background: url(../../assets/image/like-default.png) no-repeat;
          background-size: 100% 100%;
          width: 0.2rem;
          height: 0.2rem;
          margin-right: 0.03rem;
          display: inline-block;
        }
        &.active {
          color: #f06e6c;
          .like {
            background: url(../../assets/image/like.png) no-repeat;
            background-size: 100% 100%;
          }
        }
      }
      .reply {
        display: block;
        color: #004e97;
        font-size: 0.12rem;
        position: relative;
        padding-bottom: 0.1rem;
        &.nomb {
          margin-bottom: 0;
        }
      }
      .delete {
        position: absolute;
        right: 0;
        top: -0.06rem;
        width: 0.28rem;
        height: 0.28rem;
      }
      .content {
        position: relative;
        margin-bottom: 0.09rem;
        height: auto;
        background-color: #f9f8f8;
        .line {
          position: absolute;
          display: inline-block;
          width: 0.02rem;
          height: 0.14rem;
          background-color: #f06e6c;
          top: 0.17rem;
          left: 0.01rem;
        }
        .answer {
          display: inline-block;
          /*background-color: rgba(23, 6, 6, 0.03);*/
          border-radius: 0.05rem;
          width: 2.92rem;
          font-size: 0.14rem;
          color: #453838;
          padding: 0.1rem;
        }
      }
      .reply-ofter {
        word-break: break-all;
        color: #746a6a;
        font-size: 0.14rem;
      }
    }
    .imgs {
      width: 0.65rem;
      height: 0.65rem;
      border-radius: 0.03rem;
      margin-top: 0.03rem;
      margin-left: 0.1rem;
      margin-bottom: 0.1rem;
    }
    .imgs:first-of-type {
      margin-left: 0;
    }
  }
}
.bg {
  position: fixed;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  top: 0;
  opacity: 0.3;
  z-index: 99;
  background-color: rgb(0, 0, 0);
}
.edit {
  position: fixed;
  bottom: 0;
  height: 0.5rem;
  z-index: 999;
  width: 100%;
  max-width: 640px;
}
</style>
