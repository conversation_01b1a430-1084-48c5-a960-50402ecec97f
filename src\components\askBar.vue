<template>
  <div class="footer comment-bar">
    <div class="wrap">
      <div class="inner" @click="show">
        <input class="input1" type="text" disabled placeholder="我要提问"/>
      </div>
  
      <div class="comment-box" v-if="isShow" @click="hide">
        <div class="wrap bc-w" @click.stop>
          <div class="tit">{{title}}</div>
          <slot></slot>
          <div>
            <textarea class="textarea1" rows="5" v-bind:placeholder=textareaContent v-model.trim="content" v-focus></textarea>
          </div>
          <div class="mt txt-r">
            <button class="btn1" v-if="isSubmiting">提交中…</button>
            <button class="btn1" :disabled="!content" @click="submit" v-else>发表提问</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    directives: {
      focus: {
        inserted: function (el) {
          el.focus();
        }
      }
    },
    props: {
      title: {
        type: String,
        default: '提问'
      },
      textareaContent:{
        type: String,
        default: '请输入提问内容'
      },
      isSubmiting: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        isShow: false,
        content: ''
      }
    },
    methods: {
      show: function () {
        this.content = '';
        this.isShow = true;
      },
      hide: function () {
        this.isShow = false;
      },
      submit: function () {
        this.$emit('submit', this.content);
      },
      // 评论成功弹窗
      successModal: function () {
        const h = this.$createElement;
        this.$modal({
          message: h(
            'div', {style: 'text-align:center'}, [
              h('div', {style: 'font-size:.18rem'}, '提问成功！'),
              h('div', {style: 'color:#999;font-size:.14rem'}, '（审核通过后将公开显示）')
            ]
          )
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .comment-bar{
    .input1{
      width:100%; height:.36rem; margin-top:.07rem; padding:0 .12rem; color:#888; font-size:.12rem; border:1px solid #e7e7e7; background-color:#f7f7f7; border-radius:.04rem;
      &:disabled{ background-color:#f7f7f7; }
    }
  }
  .comment-box{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; background-color:rgba(0, 0, 0, .50);
    .wrap{ position:absolute; right:0; bottom:0; left:0; padding:.12rem; }
    .tit{ padding-bottom:.12rem; text-align:center; color:@color; }
    .textarea1{ width:100%; padding:.05rem .08rem; border:none; font-size:.12rem; border:1px solid #e7e7e7; background-color:#f7f7f7; border-radius:.04rem; }
  }
</style>
