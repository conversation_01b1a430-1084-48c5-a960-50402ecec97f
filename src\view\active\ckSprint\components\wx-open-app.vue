<template>
  <div class="wx-open">
    <div class="launch-post" v-if="!isLogin" @touchstart="goLogin"></div>
    <wx-open-launch-app class="launch-post" @error="callAppError" :appid="OpenAppId" extinfo="extinfo" v-else>
      <script type="text/wxtag-template">
        <style>
               .follow {
                    width: 1000px;
                    height: 1000px;
                
                }
            </style>
                    <div  class="follow"></div>

          </script>
    </wx-open-launch-app>
  </div>
</template>

<script>
import openApp from "@/mixins/openApp";
import { formatEmotions, formatWrap, downloadApp, isIOS, isLogin, toLogin } from '@/common';

export default {
  components: {},
  mixins: [openApp],

  data () {
    return {
      isLogin: isLogin()
    };
  },
  computed: {},
  watch: {},
  methods: {
    goLogin () {
      toLogin.call(this, null)
    }
  },
  created () { },
  mounted () {
    this.wxCallAppInit(); // 微信标签唤起app

  },

}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.wx-open {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100vh;
  height: 100vh;
  z-index: 9999;
}
.launch-post {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  overflow: hidden;
}
</style>