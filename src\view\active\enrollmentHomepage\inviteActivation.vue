<template>
  <div class="sprint">
    <top-bar title="我的见证"></top-bar>
    <div class="headTab">
      <div class="jiang" @click="topopn"></div>
    </div>
    <div class="top-title">
      <div class="title" v-if="titleshow">2022级“上进华夏奖学金”券</div>
      <div class="title" v-else>
        202{{scholarship==72?1:2}}级“一年学费读大学”
        上进奖学金
      </div>
    </div>
    <div class="sprint_wrap">
      <div class="sprint_invite">
        <div class="headTxt">上进承诺书</div>
        <p
          class="sprintTxt"
        >本人(姓名: {{stdName>6?stdName.substr(0,6)+'...':stdName}})正在申请参加远智教育202{{scholarship==72?1:2}}级成人学历报读上进奖学金，本人承诺将秉承上进精神，爱国、爱家、爱企业，在工作中不断拼搏上进，努力成为上进青年，为企业树立上进榜样</p>
        <p class="remark">*奖学金激活需企业领导在线见证上进点评，完成后才能激活使用奖学金。</p>
        <button class="receive" @click="openShareSprint(scholarship)">见证激活</button>
        <u class="toMethod" @click="toMethod()">如何见证激活？</u>
      </div>
    </div>
    <share
      :title="title"
      :desc="desc"
      :link="shareLink"
      :scholarship="scholarship"
      :imgUrl="imgUrlL"
      ref="share"
    />
  </div>
</template>

<script>
import share from "../../../components/share";
import topBar from "@/components/topBar";
export default {
  name: "inviteActivation",
  data() {
    return {
      stdName: "",
      title: "",
      desc: "",
      shareLink: "",
      imgUrlL: "",
      scholarship: "",
      currentUnvsName: "",
      titleshow: true
    };
  },
  components: { share, topBar },
  created() {
    document.title=`202${this.scholarship==72?1:2}级一年学费读大学`
    this.stdName =
      this.storage.getItem("realName") || this.storage.getItem("zmcName");
    this.currentUnvsName = this.$route.query.unvsName || "";
    this.scholarship = this.$route.query.scholarship || "";
    if (this.scholarship == 70) {
      this.titleshow = true;
    } else if (this.scholarship == 103) {
      this.titleshow = false;
    } else {
      this.titleshow = false;
    }
  },
  methods: {
    toMethod() {
      this.$router.push({ name: "sprintProcess" });
    },
    topopn() {
      this.$router.replace({ name: "coupon" });
    },
    //分享见证
    openShareSprint(qes) {
      if (qes == 70) {
        this.title = `求助！我是上进青年${this.storage.getItem("realName") ||
          this.storage.getItem("zmcName")}，邀请您为我上进承诺见证助威！`;
        this.imgUrlL =
          "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/hxzy.png";
        this.desc =
          "我正在申请参加远智教育2022级成人学历上进奖学金活动，需要您的见证才可以激活9000元奖学金，快来帮帮我！";
        this.shareLink = `${
          window.location.origin
        }/active/gzHuaXia/HXmysprint?sprint_token=${encodeURIComponent(
          this.storage.getItem("authToken")
        )}&stdName=${encodeURIComponent(
          this.storage.getItem("realName") || this.storage.getItem("zmcName")
        )}&type=jr&regOrigin=14`;
        this.$nextTick(() => {
          this.$refs.share.openNO(true);
        });
      } else if (qes == '103') {
        this.title = `求助！我是上进青年${this.storage.getItem("realName") ||
          this.storage.getItem("zmcName")}，邀请您为我上进承诺见证助威！`;
        this.imgUrlL =
          "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png";
        this.desc =
          "我正在申请参加远智教育2022级成人学历上进奖学金活动，需要您的见证才可以激活9000元奖学金，快来帮帮我！";
        this.shareLink = `${
          window.location.origin
        }/active/oneYearSchool/HXmysprint?sprint_token=${encodeURIComponent(
          this.storage.getItem("authToken")
        )}&stdName=${encodeURIComponent(
          this.storage.getItem("realName") || this.storage.getItem("zmcName")
        )}&type=jr&regOrigin=14&scholarship=${qes}`;
        this.$nextTick(() => {
          this.$refs.share.openNO(true);
        });
      } else if (qes == 72) {
        this.title = `求助！我是上进青年${this.storage.getItem("realName") ||
          this.storage.getItem("zmcName")}，邀请您为我上进承诺见证助威！`;
        this.imgUrlL =
          "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png";
        this.desc =
          "我正在申请参加远智教育2021级成人学历上进奖学金活动，需要您的见证才可以激活9000元奖学金，快来帮帮我！";
        this.shareLink = `${
          window.location.origin
        }/active/oneYearSchool/HXmysprint?sprint_token=${encodeURIComponent(
          this.storage.getItem("authToken")
        )}&stdName=${encodeURIComponent(
          this.storage.getItem("realName") || this.storage.getItem("zmcName")
        )}&type=jr&regOrigin=14&scholarship=${qes}`;
        this.$nextTick(() => {
          this.$refs.share.openNO(true);
        });
      }
    }
  }
};
</script>

<style lang="less"  scoped>
.sprint {
  width: 100%;
  max-width: 640px;
  margin: 0 auto 0.6rem;
  position: relative;
  background: rgba(255, 255, 255, 1);
  .headTab {
    .jiang {
      margin-top: 0.1rem;
      width: 0.95rem;
      height: 0.34rem;
      background: url("../../../assets/image/active/old/jiang.png");
      background-size: 100% 100%;
    }
    .lfBag,
    .rightRecord {
      position: relative;
      width: 1.5rem;
      height: 0.35rem;
      line-height: 0.35rem;
      background: #fde9c0;
      text-align: center;
    }
    .lfBag {
      background: url("../../../assets/image/active/enrollmentHomepage/btn_coupon.png")
        no-repeat;
      background-size: 100% 100%;
      span {
        position: absolute;
        right: 0.24rem;
        color: #fc421b;
        font-weight: bold;
        line-height: 0.35rem;
      }
    }
    .rightRecord {
      background: url("../../../assets/image/active/enrollmentHomepage/btn_sprint.png")
        no-repeat;
      background-size: 100% 100%;
      span {
        position: absolute;
        right: 0.35rem;
        color: #ff8700;
        font-weight: bold;
        line-height: 0.35rem;
      }
    }
  }
  .top-title {
    width: 100%;
    min-height: 0.5rem;
    margin-top: 0.5rem;
  }
  .title {
    width: 2.5rem;
    height: 0.25rem;
    font-size: 0.18rem;
    font-family: PingFang-SC-Heavy, PingFang-SC;
    font-weight: 800;
    color:rgba(193,57,53,1);
    line-height: 0.25rem;
    margin: 0 auto;
    text-align: center;
  }
  .sprint_wrap {
    width: 3.35rem;
    height: auto;
    overflow: hidden;
    margin: auto;
    margin-top: 0.15rem;
    margin-bottom: 0.15rem;
    box-shadow: 0px 3px 22px 0px rgba(8, 1, 3, 0.15);
    .sprint_invite {
      width: 3.35rem;
      height: 3.55rem;
      &.active {
        height: auto;
        overflow: hidden;
      }
      // margin: 0.2rem;
      background-color: white;
      position: relative;
      overflow: hidden;
      margin: 0 auto;
      .headTxt {
        width: 100%;
        color: rgba(54, 54, 54, 1);
        line-height: 0.3rem;
        font-size: 0.17rem;
        text-align: center;
        font-weight: bold;
        margin: 0.25rem auto 0.15rem;
      }
      .sprintTxt {
        font-size: 0.14rem;
        color: rgba(54, 54, 54, 1);
        line-height: 0.23rem;
        width: 2.91rem;
        margin: 0 auto;
        text-indent: 2em;
      }
      .remark {
        width: 3.06rem;
        margin: 0.15rem auto 0;
        color: rgba(193,57,53,1);
        font-size: 0.12rem;
        padding: 0.1rem 0.12rem 0 0.05rem;
        border-top: dashed 1px rgba(252, 66, 27, 0.35);
        font-family:PingFang-SC-Bold,PingFang-SC;
        font-weight:bold;
      }
      .toMethod {
        color: rgba(54, 54, 54, 0.6);
        line-height: 0.4rem;
        text-align: center;
        margin: 0.06rem 0 0.1rem;
        position: relative;
        left: 35%;
      }
      .receive {
        display: block;
        line-height: 0.4rem;
        text-align: center;
        background-color: rgba(252, 66, 27, 1);
        font-size: 0.17rem;
        font-family: Alibaba-PuHuiTi-B, Alibaba-PuHuiTi;
        font-weight: 600;
        color: rgba(193, 57, 53, 1);
        line-height: 0.22rem;
        border: none;
        margin: 0.2rem auto 0;
        width: 2.52rem;
        height: 0.44rem;
        background: linear-gradient(
          180deg,
          rgba(255, 217, 126, 1) 0%,
          rgba(248, 185, 86, 1) 100%
        );
        border-radius: 0.22rem;
      }
      .info_edit {
        position: absolute;
        top: 3.6rem;
        .headBox {
          width: 0.92rem;
          height: 0.3rem;
          text-align: center;
          margin: 0.4rem auto 0rem;
          color: rgba(54, 54, 54, 1);
          font-size: 0.15rem;
          position: relative;
          &:before {
            content: "";
            position: absolute;
            left: -0.4rem;
            top: 0rem;
            width: 0.39rem;
            height: 0.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_left.png");
            background-size: 0.39rem;
          }
          &:after {
            content: "";
            position: absolute;
            right: -0.4rem;
            top: 0rem;
            width: 0.39rem;
            height: 0.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
            background-size: 0.39rem;
          }
        }
        p {
          width: 3.22rem;
          height: 0.5rem;
          margin-left: 0.26rem;
          padding-left: 0.2rem;
          &.red {
            color: rgba(252, 66, 27, 1);
            font-size: 0.12rem;
            line-height: 0.2rem;
            margin-top: 0.05rem;
          }
          span {
            display: block;
            float: left;
            height: 0.5rem;
            width: 0.63rem;
            text-align: justify;
            font-size: 0.14rem;
            line-height: 0.5rem;
            text-align-last: justify;
          }
          input {
            width: 2.21rem;
            height: 0.45rem;
            border: none;
            border-bottom: solid 1px rgba(54, 54, 54, 0.6);
          }
        }
      }
      .sprint_info {
        width: 3.06rem;
        padding-top: 0.1rem;
        position: relative;
        margin: 0.27rem auto 0;
        border-top: dashed 1px rgba(252, 66, 27, 0.35);
        img {
          width: 1.35rem;
          height: 0.56rem;
          position: absolute;
          right: 0.19rem;
          top: 0.28rem;
        }
        .info {
          p {
            font-size: 0.14rem;
            line-height: 0.14rem;
            color: rgba(54, 54, 54, 1);
            margin-bottom: 0.09rem;
            &:last-of-type {
              margin-bottom: 0;
            }
          }
        }
        .comment {
          margin-right: 0.15rem;
          margin-bottom: 0.19rem;
          span {
            color: rgba(252, 66, 27, 1);
          }
          margin-top: 0.3rem;
          line-height: 0.22rem;
          font-size: 0.14rem;
          color: rgba(54, 54, 54, 1);
        }
      }
    }
  }
  .sprint_list {
    background-color: white;
    height: auto;
    overflow: hidden;
    margin-top: 0.5rem;
    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;
      text-align: center;
      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        display: inline-block;
      }
      .headTitleIcon {
        width: 0.24rem;
        height: 0.24rem;
        margin-top: 0.03rem;
      }
    }
    .list {
      width: 100%;
      height: auto;
      overflow: hidden;
      .empty {
        margin: 0.5rem auto;
        width: 1rem;
        height: 1rem;
        padding-top: 0.83rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/no_sprint.png");
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        color: rgba(54, 54, 54, 1);
      }
      ul {
        li {
          margin: 0.1rem auto;
          width: 3.52rem;
          height: auto;
          overflow: hidden;
          padding: 0.1rem;
          background-color: white;
          border-bottom: 1px solid rgba(54, 54, 54, 0.08);
          .head_info {
            .head {
              width: 0.38rem;
              height: 0.38rem;
              overflow: hidden;
              float: left;
              border-radius: 50%;
              img {
                width: 100%;
              }
            }
            .info {
              float: left;
              width: 2.8rem;
              margin-left: 0.05rem;
              margin-top: 0.03rem;
              p {
                clear: both;
                margin-bottom: 0.1rem;
                font-size: 0.14rem;
                .name {
                  float: left;
                }
                .time {
                  float: right;
                  color: rgba(54, 54, 54, 0.4);
                  font-size: 0.12rem;
                  margin-top: 0;
                }
              }
            }
          }
          .detail {
            clear: both;
            width: 3.04rem;
            padding: 0.1rem;
            border: dashed 0.01rem rgba(239, 186, 6, 1);
            margin-left: 0.3rem;
            font-size: 0.14rem;
            color: rgba(54, 54, 54, 0.8);
            line-height: 0.25rem;
            span {
              color: rgba(239, 186, 6, 0.8);
            }
          }
        }
      }
    }
  }
  .sprint_process {
    background-color: white;
    padding: 0.27rem 0 0 0;
    p {
      width: 100%;
      color: #363636;
      font-size: 0.15rem;
      text-align: center;
      margin-bottom: 0.05rem;
      font-weight: bold;
    }
    img {
      display: block;
      margin: 0 auto;
      width: 3.75rem;
      height: 1rem;
    }
  }
}
</style>
