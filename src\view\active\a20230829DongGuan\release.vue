<template>
  <div class="submit-box">
    <div class="top-box">
      <span @click="onClickLeft">返回</span>
      <button @click="submitAll" class="submit">发布</button>
    </div>

    <div class="content-box">
      <el-input v-model.trim="scText" :rows="6" type="textarea" placeholder="点击说说学习心得吧！（不低于10个字）" />
    </div>
  </div>
</template>

<script>
import config from '@/config'
import { Toast } from 'vant'
export default {
  data() {
    return {
      fileList: [],
      scPicUrl: [],
      scText: '',
      semesterId: '',
      punchOrder: null,
      isPatchCard: 0
    }
  },
  mounted() {
    this.semesterId = this.$route.query.semesterId || ''
    this.punchOrder = this.$route.query.punchOrder || ''
    this.isPatchCard = this.$route.query.isPatchCard
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1)
    },
    submitAll() {
      if (this.scText.length < 9) {
        Toast('请至少输入10个字的心得哦～')
        return
      }
      this.$indicator.open()
      const params = {
        scText: this.scText,
        scPicUrl: this.scPicUrl.join(','),
        scSource: '7',
        scType: 1
      }
      let data = {
        semesterId: this.semesterId,
        punchCardType: '1',
        contents: this.scText,
        punchCardImg: this.scPicUrl.join(','),
        isPatchCard: this.isPatchCard,
        punchOrder: this.punchOrder
      }
      this.$http.post('/mkt/sjScholarshipPunchCard/1.0/', data).then(res => {
        this.$indicator.close()
        if (res.code === '00') {
          //清楚缓存观看时长
          localStorage.removeItem(`${this.vids}-surplusDuration`)
          Toast('发帖成功')
          setTimeout(() => {
            this.$router.push({
              name: 'a20230829DongGuan'
            })
          }, 500)
        }
      })

      this.$http.post('/us/usNewPosting/1.0/', params).then()
    }
  }
}
</script>

<style lang="less" scoped>
.head-box {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // margin-top: -16%;
  img {
    width: 32px;
    height: 32px;
  }
  button {
    background: #ea401c;
    padding: 5px 11px;
    border-radius: 5px;
    font-size: 14px;
    color: #fff;
    font-weight: bold;
  }
}
.upload-box {
  background: #fff;
  padding: 0.1rem;
}
.tips {
  margin: 2px 0;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 17px;
}

.top-box {
  height: 0.4rem;
  width: 3.75rem;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 0.1rem;
  justify-content: space-between;
  span {
    font-size: 14px;
    font-weight: bold;
  }
  .submit {
    width: 0.5rem;
    height: 0.3rem;
    background: #f06e6c;
    border-radius: 5px;

    font-size: 14px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
  }
}
</style>
