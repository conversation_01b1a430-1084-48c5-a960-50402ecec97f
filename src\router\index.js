import Vue from "vue";
import Router from "vue-router";
import { isStudent, setDocumentTitle, creatDeviceId } from "@/common";
import config from "@/config";
import storage from "@/plugins/storage";
import zhimiRouter from "./zhimi";
import studentRouter from "./student";
import recruitAppRouter from "./recruitApp";
import netRegistRouter from "./netRegist";
import activityRouter from "./activity";
import teacherRouter from "./teacher";
import appRouter from "./app";
import aspirantRouter from "./aspirant";
import user from "./user";
import newMain from "./newMain2021";
import actRouter from "./act";
import whiteList from "./whiteList";

Vue.use(Router);

const router = new Router({
  mode: "history",
  routes: [
    ...zhimiRouter,
    ...studentRouter,
    ...recruitAppRouter,
    ...netRegistRouter,
    ...activityRouter,
    ...teacherRouter,
    ...appRouter,
    ...aspirantRouter,
    ...user,
    ...newMain,
    ...actRouter,
    {
      path: "/pay/test",
      name: "PayTest",
      component: () => import("../view/test-pay.vue"),
      meta: {
        title: "支付测试",
        requiresAuth: false,
      },
    },

    {
      path: "*",
      component: () => import("../view/404"),
    },
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      if (from.meta.keepAlive) {
        from.meta.savedPosition = parseInt(
          window.sessionStorage.getItem("pageY") || 0
        );
      }
      return { x: 0, y: to.meta.savedPosition || 0 };
    }
  },
});

// 全局钩子
router.beforeEach((to, from, next) => {
  // 百度统计
  var _hmt = _hmt || [];
  (function () {
    var hm = document.createElement("script");
    hm.src = "https://hm.baidu.com/hm.js?a7d4883dcb026727f44fe6bc65fac428";
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(hm, s);
  })();
  const { fullPath, path, query } = to;
  if (path.includes("/newPages") || whiteList.includes(path)) {
    location.href = `${location.origin}/new-h5${fullPath}`;
    next(false);
    return;
  }
  
  // 兼容旧页面跳转重构后的智米商城
  if (path.startsWith("/newHome")) {
    location.href = `${location.origin}/new-h5/newPages/zhiMiMall${fullPath.replace('/newHome','')}`;
    next(false);
    return;
  }

  var _mtac = { senseHash: 0, autoReport: 0 };
  (function () {
    var mta = document.createElement("script");
    mta.src = "//pingjs.qq.com/h5/stats.js?v2.0.4";
    mta.setAttribute("name", "MTAH5");
    mta.setAttribute("sid", "500709415");
    mta.setAttribute("cid", "500709416");
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(mta, s);
  })();

  var _hmt = _hmt || []
  ;(function () {
    var hm = document.createElement('script')
    hm.src = 'https://hm.baidu.com/hm.js?300c9e1bcc08aac2de7da249472c2196'
    var s = document.getElementsByTagName('script')[0]
    s.parentNode.insertBefore(hm, s)
  })()

  // 所有正式环境的http链接自动跳转至https
  if (window.location.origin === "http://zm.yzou.cn") {
    window.location.href = window.location.href.replace("http://", "https://");
  }

  // 设置title
  setDocumentTitle(to.query.title || to.meta.title);

  // 记录当前滚动条位置
  if (from.meta.keepAlive) {
    window.sessionStorage.pageY =
      document.getElementsByTagName("body")[0].scrollTop;
  }
  //记录进入app时的url
  if (typeof window.entryUrl === "undefined" || window.entryUrl === "") {
    window.entryUrl = location.href.split("#")[0];
  }
  if (!storage.getItem("phoneID")) {
    storage.setItem("phoneID", creatDeviceId());
  }
  // learnId
  // if (to.query.learnId) {
  //   storage.setItem('learnId', to.query.learnId);
  // }
  if (!global.dictJson && to.meta.loadDict) {
    const $script = document.createElement("script");
    $script.src = config.dict;
    global.document.body.appendChild($script);
    $script.onload = () => {
      handle(to, from, next);
    };
  } else {
    handle(to, from, next);
  }
});

// router.afterEach(route => {
//   if (route.path.indexOf('aspirantUniversity') > -1) {
//     try {
//       let params = {
//         titleStyle: '2', //标题位置 1：居左 2：居中 3：居右
//         leftMenuBackShow: to.meta.menus.includes('back') ? "1" : "0",
//         leftMenuHomeShow: to.meta.menus.includes('home') ? "1" : "0",
//         rightMenuMoreShow: '0',
//         // rightMenuMoreShow: to.meta.menus.includes('more') ? '1': '0',
//         // 左边按钮区域是否展示
//         needLeftMenu: to.meta.menus.indexOf('back') > -1 || to.meta.menus.indexOf('home') > -1 ? '1': '0',
//         needRightMenu: '1', // 右边按钮区域是否展示
//         rightMenuCloseShow: '1', //右边关闭按钮必须要写死
//       }
//
//       if (to.meta.menus.includes('home')) {
//         params.leftHomeCallback = '$backWebHome';
//         bridge.registerHandler('$backWebHome', () => {
//           router.push('/aspirantUniversity');
//         });
//       }
//
//       bridge.callHandler('setAppMenus', params)
//         .then(() => {})
//     } catch (e) {}
//   }
// })

function handle(to, from, next) {
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (storage.getItem("authToken")) { 
      window.localStorage.wmUserInfo = JSON.stringify({ userId: storage.getItem("mobile") || "", userTag: storage.getItem("realName") || "", projectVersion: "1.0",});
      // 检查是否绑定身份证
      if (to.fullPath.startsWith("/student")) {
        // 如果不是学员
        if (!isStudent()) {
          if ( to.name === "stuInfo" || to.name == "myTask" || to.name == "myCurriculum" || to.name == "more" ) {
            // 如果访问的是远智学堂首页
            next({ name: "roleAuth", query: { redirect: to.fullPath } });
          } else {
            next({ name: "idcardBind", query: { redirect: to.fullPath } });
          }
        } else if (storage.getItem("bindStudent") !== "2") {
          next({ name: "roleAuth", query: { redirect: to.fullPath } });
        } else {
          // 如果缓存没有learnId，重定向到学员信息页面
          if ( to.name !== "stuInfo" && to.name !== "settings" && !storage.getItem("learnId") && !to.query.learnId) {
            const [path, queryString] = to.fullPath.split('?');
            let queryParams = ''
            // 解析查询参数
            const searchParams = new URLSearchParams(queryString);
            for (const [key, value] of searchParams.entries()) {
              if (key == 'recruitType') {
                queryParams = value
              }
            }
            next({ name: "stuInfo", query: { recruitType: queryParams }});
          } else {
            next();
          }
        }
      } else {
        next();
      }
    } else {
      const query = {
        redirect: to.path,
        inviteId: from.query.inviteId || to.query.inviteId || "",
        scholarship: from.query.scholarship || to.query.scholarship || "",
        action: from.query.action || to.query.action || "",
        registerPath: from.path,
        plan: from.query.plan,
        regOrigin: from.query.regOrigin,
        regChannel: from.query.regChannel || "",
      };
      next({
        name: "login",
        query: Object.assign(query, to.query),
      });
    }
  } else {
    next();
  }
}

export default router;
