import { inviteId, loadPcdJson } from "../common";

export default [
  {
    path: "/active/adultExam/enrollInfo",
    name: "adultExamEnrollInfo",
    component: () => import("../view/active/adultExam/enrollInfo"),
    meta: {
      title: "报读详情",
    },
  },
  {
    path: "/active/adultExam/pay/success",
    name: "adultExamPaySuccess",
    component: () => import("../view/active/adultExam/paySuccess"),
    meta: {
      title: "缴费成功",
    },
  },
  {
    path: "/active/adultExam/check",
    name: "adultExamEnrollCheck",
    component: () => import("../view/active/adultExam/enrollCheck"),
    meta: {
      title: "加载中…",
      requiresAuth: true,
    },
  },
  {
    path: "/active/enrollmentHomepage",
    name: "enrollmentHomepage",
    component: () =>
      import("../view/active/enrollmentHomepage/enrollmentHomepage"),
    meta: {
      title: "广州城建职业学院",
    },
  },
  {
    path: "/active/southChinaAgriculturalUniversity",
    name: "southChinaAgriculturalUniversity",
    component: () =>
      import("../view/active/SouthChinaAgriculturalUniversity/index"),
    meta: {
      title: "华南农业大学",
    },
  },
  {
    path: "/active/openUniversity",
    name: "openUniversity",
    component: () => import("../view/active/openUniversity/openUniversity"),
    meta: {
      title: "国家开放大学",
    },
  },
  {
    path: "/active/gdProfessionUniversity",
    name: "gdProfessionUniversity",
    component: () =>
      import("../view/active/gdProfessionUniversity/gdProfessionUniversity"),
    meta: {
      title: "广东职业技术学院",
    },
  },
  {
    path: "/active/DongguanUniversity",
    name: "DongguanUniversity",
    component: () => import("../view/active/DongguanUniversity/index"),
    meta: {
      title: "东莞理工学院",
    },
  },
  {
    path: "/active/guangZhouUniversity",
    name: "guangZhouUniversity",
    component: () => import("../view/active/guangZhouUniversity/index"),
    meta: {
      title: "广州大学",
    },
  },
  {
    path: "/active/shanweiInstituteTechnology",
    name: "shanweiInstituteTechnology",
    component: () => import("../view/active/shanweiInstituteTechnology/index"),
    meta: {
      title: "汕尾职业技术学院",
    },
  },
  {
    path: "/active/Guangzhoucommercial",
    name: "Guangzhoucommercial",
    component: () => import("../view/active/Guangzhoucommercial/index"),
    meta: {
      title: "广州商学院",
    },
  },
  {
    path: "/active/guangzhouOpenUniversity",
    name: "guangzhouOpenUniversity",
    component: () => import("../view/active/gbdsdx/guangzhouOpenUniversity"),
    meta: {
      title: "广州开放大学",
    },
  },
  {
    path: "/active/gdJinRong",
    name: "gdJinRong",
    component: () => import("../view/active/gdjr/index"),
    meta: {
      title: "广东金融学院",
    },
  },
  {
    path: "/active/gdLinNanHomePage",
    name: "gdLinNan",
    component: () => import("../view/active/gdLingNan/index"),
    meta: {
      title: "广东岭南职业技术学院",
    },
  },
  {
    path: "/active/stUnviersity",
    name: "stUnviersity",
    component: () => import("../view/active/stUnviersity/index"),
    meta: {
      title: "汕头大学",
    },
  },
  {
    path: "/active/jnUniversity",
    name: "jnUniversity",
    component: () => import("../view/active/jnUniversity/index"),
    meta: {
      title: "暨南大学",
    },
  },
  // 自考相关
  {
    path: "/active/selfTought",
    name: "selfTought",
    component: () => import("../view/active/selfTought/index.vue"),
    meta: {
      title: "自考学历提升计划",
    },
  },
  {
    path: "/active/selfTought/major",
    name: "selfTought.major",
    component: () => import("../view/active/selfTought/major.vue"),
    meta: {
      title: "",
    },
  },
  {
    path: "/active/selfTought/form",
    name: "selfTought.form",
    component: () => import("../view/active/selfTought/form"),
    meta: {
      title: "报读信息填写",
      keepAlive: true,
      requiresAuth: true,
    },
  },
  {
    path: "/active/selfTought/select",
    name: "selfTought.select",
    component: () => import("../view/active/selfTought/select-type"),
    meta: {
      title: "选择自考计划",
      requiresAuth: true,
    },
  },
  {
    path: "/active/selfTought/confirm",
    name: "selfTought.confirm",
    component: () => import("../view/active/selfTought/confirm"),
    meta: {
      title: "确认订单",
      requiresAuth: true,
    },
  },
  {
    path: "/active/selfTought/success",
    name: "selfTought.success",
    component: () => import("../view/active/selfTought/success"),
    meta: {
      title: "报读成功",
      requiresAuth: true,
    },
  },
  {
    path: "/active/selfTought/update",
    name: "selfTought.update",
    component: () => import("../view/active/selfTought/update"),
    meta: {
      title: "自考升级计划",
      requiresAuth: true,
    },
  },
  {
    path: "/active/selfTought/updateForm",
    name: "selfTought.updateForm",
    component: () => import("../view/active/selfTought/update/form"),
    meta: {
      title: "选择赠送成教的城市与考区",
      requiresAuth: true,
    },
  },
  {
    path: "/active/newDreamBuildUpdate",
    name: "newDreamBuild",
    component: () => import("../view/active/enrollAggregate/index"),
    meta: {
      title: "远智教育",
    },
  },
  {
    path: "/active/classStyle",
    name: "classStyle",
    component: () => import("../view/active/enrollAggregate/classStyle"),
    meta: {
      title: "购买班型",
    },
  },
  {
    path: "/active/321Activity",
    name: "321Activity",
    component: () => import("../view/active/321Activity/index"),
    meta: {
      title: "3月21日,干一件上进的事，影响更多人上进",
    },
  },
  {
    path: "/active/321Activity/audio",
    name: "321ActivityAudio",
    component: () => import("../view/active/321Activity/audio"),
    meta: {
      title: "321上进日",
    },
  },
  {
    path: "/active/321Activity/rule",
    name: "321ActivityRule",
    component: () => import("../view/active/321Activity/rule"),
    meta: {
      title: "321上进日",
    },
  },
  {
    path: "/active/321Activity/mycert",
    name: "My321ActivityCert",
    component: () => import("../view/active/321Activity/mycertification"),
    meta: {
      title: "321上进日",
    },
  },
  {
    path: "/active/321Activity/share",
    name: "321ActivityShare",
    component: () => import("../view/active/321Activity/share"),
    meta: {
      title: "321上进日",
    },
  },
  {
    path: "/active/321Activity/showMwdia",
    name: "321ActivityShowMedia",
    component: () => import("../view/active/321Activity/showMedia"),
    meta: {
      title: "321上进日",
    },
  },
  {
    path: "/active/321Activity/urlTransfer",
    name: "urlTransfer",
    component: () => import("../view/active/321Activity/urlTransfer"),
    meta: {
      title: "321上进日",
    },
  }, //奶盖声明的路由
  {
    path: "/active/dreamBuild/newDreamBuildInfo/:name",
    name: "newDreamBuildInfo",
    component: () => import("../view/active/dreamBuild/newDreamBuildInfo"),
    meta: {
      title: "筑梦计划",
    },
  },
  {
    path: "/active/activityGround",
    name: "activityGround",
    component: () =>
      import("../view/active/enrollAggregate/activityGround.vue"),
    meta: {
      title: "活动广场",
    },
  },
  {
    path: "/active/enrollmentHomepage/CJmysprintDes",
    name: "CJmysprintBeforeExamDes",
    component: () =>
      import("../view/active/enrollmentHomepage/CJmysprintDes.vue"),
    meta: {
      title: "见证记录",
    },
  },
  {
    path: "/active/enrollmentHomepage/CJsprintNote",
    name: "CJsprintNote",
    component: () =>
      import("../view/active/enrollmentHomepage/CJsprintNotes.vue"),
    meta: {
      title: "见证记录",
      requiresAuth: true,
    },
  },

  {
    path: "/active/dreamBuild/address",
    name: "dreamBuildAddress",
    component: () => import("../view/active/dreamBuild/address"),
    meta: {
      requiresAuth: true,
      title: "教材收货地址",
    },
  },
  {
    path: "/active/dreamBuild/invite",
    name: "dreamBuildInvite",
    beforeEnter: inviteId,
    component: () => import("../view/active/dreamBuild/invite2"),
    meta: {
      title: "我的邀约",
      requiresAuth: true,
    },
  },
  {
    path: "/active/sprint",
    name: "sprint",
    component: () => import("../view/active/enrollmentHomepage/mySprint"),
    meta: {
      title: "我的邀约见证",
      loadDict: true,
    },
  },
  {
    path: "/active/sprintProcess",
    name: "sprintProcess",
    component: () => import("../view/active/enrollmentHomepage/sprintProcess"),
    meta: {
      title: "如何见证激活",
    },
  },
  {
    path: "/inviteActivation",
    name: "inviteActivation",
    component: () =>
      import("../view/active/enrollmentHomepage/inviteActivation.vue"),
    meta: {
      title: "邀请见证",
      requiresAuth: true,
    },
  },

  //一年学费读大学
  {
    path: "/active/invincibleOneYearSchool",
    name: "newOneYearSchool",
    component: () => import("../view/active/oneYearSchool/oneYear.vue"),
    meta: {
      title: "2022级一年学费读大学",
    },
  },
  {
    path: "/active/oneYearSchool/sucess",
    name: "oneYearSuccess",
    component: () => import("../view/active/oneYearSchool/sucess.vue"),
    meta: {
      title: "2022级一年学费读大学",
      requiresAuth: true,
    },
  },
  {
    path: "/active/oneYearSchool/HXmysprint",
    name: "oneYearmysprint",
    component: () => import("../view/active/oneYearSchool/HXmysprint.vue"),
    meta: {
      title: "2022级一年学费读大学",
    },
  },
  {
    path: "/active/oneYearSchool/mojar",
    name: "oneYearmojar",
    component: () => import("../view/active/oneYearSchool/mojar.vue"),
    meta: {
      title: "活动规则",
    },
  },
  {
    path: "/active/oneYearSchool/Scholarship",
    name: "Scholarship",
    component: () => import("../view/active/oneYearSchool/Scholarship.vue"),
    meta: {
      title: "领取券",
    },
  },
  {
    path: "/active/oneYearSchool/oneYearerrollSuccess",
    name: "oneYearerrollSuccess",
    component: () =>
      import("../view/active/oneYearSchool/oneYearerrollSuccess.vue"),
    meta: {
      title: "上课指引",
    },
  },
  {
    path: "/active/graduate/form",
    name: "graduateForm",
    component: () => import("../view/active/graduate/index.vue"),
    meta: {
      title: "报读信息填写",
      requiresAuth: true,
    },
  },
  {
    path: "/growthSystem",
    name: "growth.system",
    component: () => import("../view/active/growthSystem"),
    meta: {
      title: "上进青年必做的事情",
    },
  },
  {
    path: "/active/fullScholarshipInviation/invitation",
    name: "fullScholarshipInviation",
    component: () =>
      import("../view/active/fullScholarshipInviation/invitation"),
    meta: {
      title: "“全额奖学金颁奖典礼”邀请函",
      requiresAuth: true,
    },
  },
  {
    path: "/active/fullScholarshipInviation/shareInvitation",
    name: "fullScholarshipShareInviation",
    component: () =>
      import("../view/active/fullScholarshipInviation/shareInvitation"),
    meta: {
      title: "“全额奖学金颁奖典礼”邀请函",
    },
  },
  {
    path: "/active/gccScholarship",
    name: "gccScholarship",
    redirect: "/active/gccScholarship/index",
    component: () => import("../view/active/gccScholarship/jump"),
    meta: {
      title: "广州商学院本科奖学金",
    },
    children: [
      {
        path: "index",
        name: "gccScholarship.home",
        component: () => import("../view/active/gccScholarship/index"),
        meta: {
          title: "广州商学院本科奖学金",
        },
      },
      {
        path: "activityRule",
        name: "gccScholarship.activityRule",
        component: () => import("../view/active/gccScholarship/activityRule"),
        meta: {
          title: "广州商学院本科奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "gccScholarship.witnessRecord",
        component: () => import("../view/active/gccScholarship/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "gccScholarship.witness",
        component: () => import("../view/active/gccScholarship/witness"),
        meta: {
          title: "广州商学院本科奖学金",
        },
      },
      {
        path: "success",
        name: "gccScholarship.success",
        component: () => import("../view/active/gccScholarship/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "gccScholarship.witnessDetails",
        component: () => import("../view/active/gccScholarship/witnessDetails"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "gccScholarship.activation",
        component: () => import("../view/active/gccScholarship/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/active/forward",
    name: "forward",
    redirect: "/active/forward/index",
    component: () => import("../view/active/forward/jump"),
    meta: {
      title: "上进专项奖学金",
    },
    children: [
      {
        path: "index",
        name: "forward.home",
        component: () => import("../view/active/forward/index"),
        meta: {
          title: "上进专项奖学金",
        },
      },
      {
        path: "activityRule",
        name: "forward.activityRule",
        component: () => import("../view/active/forward/activityRule"),
        meta: {
          title: "上进专项奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "forward.witnessRecord",
        component: () => import("../view/active/forward/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "forward.witness",
        component: () => import("../view/active/forward/witness"),
        meta: {
          title: "上进专项奖学金",
        },
      },
      {
        path: "success",
        name: "forward.success",
        component: () => import("../view/active/forward/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "forward.witnessDetails",
        component: () => import("../view/active/forward/witnessDetails"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "forward.activation",
        component: () => import("../view/active/forward/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/active/ssProfressionUniversityForward",
    name: "ssProfressionUniversityForward",
    redirect: "/active/ssProfressionUniversityForward/index",
    component: () =>
      import("../view/active/ssProfressionUniversityForward/jump"),
    meta: {
      title: "广东松山职业技术学院奖学金",
    },
    children: [
      {
        path: "index",
        name: "ssProfressionUniversityForward.home",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/index"),
        meta: {
          title: "广东松山职业技术学院奖学金",
        },
      },
      {
        path: "activityRule",
        name: "ssProfressionUniversityForward.activityRule",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/activityRule"),
        meta: {
          title: "广东松山职业技术学院奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "ssProfressionUniversityForward.witnessRecord",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "ssProfressionUniversityForward.witness",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/witness"),
        meta: {
          title: "广东松山职业技术学院奖学金",
        },
      },
      {
        path: "success",
        name: "ssProfressionUniversityForward.success",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "ssProfressionUniversityForward.witnessDetails",
        component: () =>
          import(
            "../view/active/ssProfressionUniversityForward/witnessDetails"
          ),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "forward.activation",
        component: () =>
          import("../view/active/ssProfressionUniversityForward/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/active/gdjrUniversityForward",
    name: "gdjrUniversityForward",
    redirect: "/active/gdjrUniversityForward/index",
    component: () => import("../view/active/gdjrUniversityForward/jump"),
    meta: {
      title: "广东金融学院上进奖学金",
    },
    children: [
      {
        path: "index",
        name: "gdjrUniversityForward.home",
        component: () => import("../view/active/gdjrUniversityForward/index"),
        meta: {
          title: "广东金融学院上进奖学金",
        },
      },
      {
        path: "activityRule",
        name: "gdjrUniversityForward.activityRule",
        component: () =>
          import("../view/active/gdjrUniversityForward/activityRule"),
        meta: {
          title: "广东金融学院上进奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "gdjrUniversityForward.witnessRecord",
        component: () =>
          import("../view/active/gdjrUniversityForward/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "gdjrUniversityForward.witness",
        component: () => import("../view/active/gdjrUniversityForward/witness"),
        meta: {
          title: "广东金融学院上进奖学金",
        },
      },
      {
        path: "success",
        name: "gdjrUniversityForward.success",
        component: () => import("../view/active/gdjrUniversityForward/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "gdjrUniversityForward.witnessDetails",
        component: () =>
          import("../view/active/gdjrUniversityForward/witnessDetails"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "forward.activation",
        component: () =>
          import("../view/active/gdjrUniversityForward/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/active/jdProfressionUniversityForward",
    name: "jdProfressionUniversityForward",
    redirect: "/active/jdProfressionUniversityForward/index",
    component: () =>
      import("../view/active/jdProfressionUniversityForward/jump"),
    meta: {
      title: "广东机电职业技术学院奖学金",
    },
    children: [
      {
        path: "index",
        name: "jdProfressionUniversityForward.home",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/index"),
        meta: {
          title: "广东机电职业技术学院奖学金",
        },
      },
      {
        path: "activityRule",
        name: "jdProfressionUniversityForward.activityRule",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/activityRule"),
        meta: {
          title: "广东机电职业技术学院奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "jdProfressionUniversityForward.witnessRecord",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "jdProfressionUniversityForward.witness",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/witness"),
        meta: {
          title: "广东机电职业技术学院奖学金",
        },
      },
      {
        path: "success",
        name: "jdProfressionUniversityForward.success",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "jdProfressionUniversityForward.witnessDetails",
        component: () =>
          import(
            "../view/active/jdProfressionUniversityForward/witnessDetails"
          ),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "forward.activation",
        component: () =>
          import("../view/active/jdProfressionUniversityForward/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/active/gzOpenUniversityForward",
    name: "gzOpenUniversityForward",
    redirect: "/active/gzOpenUniversityForward/index",
    component: () => import("../view/active/gzOpenUniversityForward/jump"),
    meta: {
      title: "广州开放大学奖学金",
    },
    children: [
      {
        path: "index",
        name: "gzOpenUniversityForward.home",
        component: () => import("../view/active/gzOpenUniversityForward/index"),
        meta: {
          title: "广州开放大学奖学金",
        },
      },
      {
        path: "activityRule",
        name: "gzOpenUniversityForward.activityRule",
        component: () =>
          import("../view/active/gzOpenUniversityForward/activityRule"),
        meta: {
          title: "广州开放大学奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "gzOpenUniversityForward.witnessRecord",
        component: () =>
          import("../view/active/gzOpenUniversityForward/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "witness",
        name: "gzOpenUniversityForward.witness",
        component: () =>
          import("../view/active/gzOpenUniversityForward/witness"),
        meta: {
          title: "广州开放大学奖学金",
        },
      },
      {
        path: "success",
        name: "gzOpenUniversityForward.success",
        component: () =>
          import("../view/active/gzOpenUniversityForward/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "gzOpenUniversityForward.witnessDetails",
        component: () =>
          import("../view/active/gzOpenUniversityForward/witnessDetails"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "activation",
        name: "gzOpenUniversityForward.activation",
        component: () =>
          import("../view/active/gzOpenUniversityForward/activation"),
        meta: {
          title: "我的见证",
        },
      },
    ],
  },
  {
    path: "/extension/home",
    name: "extension.home",
    component: () => import("../view/active/extension/home"),
    meta: {
      title: "上进学社",
    },
  },
  {
    path: "/extension/pic",
    name: "extension.pic",
    component: () => import("../view/active/extension/pic"),
    meta: {
      title: "上进学社",
    },
  },
  {
    path: "/active/hrActive",
    name: "hrActive",
    component: () => import("../view/active/hrActive"),
    meta: {
      title: "上进HR·成长计划",
    },
  },
  {
    path: "/active/makeProgress321",
    name: "makeProgress321",
    component: () => import("../view/active/makeProgress321/index"),
    meta: {
      title: "321上进日·发现上进的力量",
    },
  },

  {
    path: "/active/schoolHome/:name",
    name: "schoolHome",
    component: () => import("../view/active/schoolHome/index.vue"),
    meta: {
      title: "",
    },
  },
  {
    path: "/active/618Activity",
    name: "618Activity",
    component: () => import("../view/active/618Activity/index.vue"),
    meta: {
      title: "618活动",
    },
  },
  {
    path: "/active/registerReading",
    name: "registerReading",
    redirect: "/active/registerReading/index",
    component: () => import("../view/active/registerReading/jump"),
    meta: {
      title: "上进奖学金",
    },
    children: [
      {
        path: "index",
        name: "registerReading.home",
        component: () => import("../view/active/registerReading/index"),
        meta: {
          title: "上进奖学金",
        },
      },
      {
        path: "activityRule",
        name: "registerReading.activityRule",
        component: () => import("../view/active/registerReading/activityRule"),
        meta: {
          title: "上进奖学金",
        },
      },
      {
        path: "witnessRecord",
        name: "registerReading.witnessRecord",
        component: () => import("../view/active/registerReading/witnessRecord"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "success",
        name: "registerReading.success",
        component: () => import("../view/active/registerReading/success"),
        meta: {
          title: "领取成功",
        },
      },
      {
        path: "witnessDetails",
        name: "registerReading.witnessDetails",
        component: () =>
          import("../view/active/registerReading/witnessDetails"),
        meta: {
          title: "见证记录",
        },
      },
      {
        path: "inviteActivation",
        name: "registerReading.inviteActivation",
        component: () =>
          import("../view/active/registerReading/inviteActivation"),
        meta: {
          title: "见证邀请",
        },
      },
      {
        path: "mysprint",
        name: "registerReading.mysprint",
        component: () => import("../view/active/registerReading/mysprint.vue"),
        meta: {
          title: "邀请认证",
        },
      },
    ],
  },
  {
    path: "/active/ckSprint",
    name: "ckSprint",
    component: () => import("../view/active/ckSprint/index.vue"),
    meta: {
      title: "成考冲刺月",
    },
  },
  {
    path: "/active/schoolWelfareMonth",
    component: () => import("../view/active/schoolWelfareMonth/index.vue"),
    meta: {
      title: "上进学社福利月",
    },
  },
  {
    path: "/active/school11",
    component: () => import("../view/active/school11/index.vue"),
    meta: {
      title: "副业赚钱职场技能好课",
    },
  },
  {
    path: "/active/school104",
    component: () => import("../view/active/school104/index.vue"),
    meta: {
      title: "补贴证书！补贴现金1000～2500元",
    },
  },
  {
    path: "/active/school202112",
    component: () => import("../view/active/school202112/index.vue"),
    meta: {
      title: "双十二有豪事发生，资格证课程限时有礼",
    },
  },
  {
    path: "/active/school105",
    component: () => import("../view/active/school105/index.vue"),
    meta: {
      title: "来上进学社领取考会计证秘籍课程",
    },
  },
  {
    path: "/active/2022Spring",
    name: "2022Spring",
    component: () => import("../view/active/2022Spring/index.vue"),
    meta: {
      title: "虎啸风生·新年红包活动",
    },
  },
  {
    path: "/active/transitionPage",
    name: "2022Spring.transitionPage",
    component: () => import("../view/active/2022Spring/transitionPage.vue"),
    meta: {
      title: "虎啸风生·新年红包活动",
    },
  },
  {
    path: "/active/school107",
    component: () => import("../view/active/school107/index.vue"),
    meta: {
      title: "百万学霸挑战金等你来拿",
    },
  },
  {
    path: "/active/signAct",
    name: "signAct",
    component: () => import("../view/active/signAct/index.vue"),
    meta: {
      title: "城建上进21奖学金",
    },
  },
  {
    path: "/active/signAct2023",
    name: "signAct2023",
    component: () => import("../view/active/signAct2023/index.vue"),
    meta: {
      title: "城建上进21奖学金",
    },
  },
  {
    path: "/active/signAct/release",
    name: "release",
    component: () => import("../view/active/signAct/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signAct/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signAct/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signAct/readBook",
    name: "readBook",
    component: () => import("../view/active/signAct/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  // 广商
  {
    path: "/active/signActCommerce",
    name: "signActCommerce",
    component: () => import("../view/active/signActCommerce/index.vue"),
    meta: {
      title: "广商上进21奖学金",
    },
  },
  {
    path: "/active/signActCommerce/release",
    name: "release",
    component: () => import("../view/active/signActCommerce/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActCommerce/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActCommerce/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActCommerce/readBook",
    name: "readBook",
    component: () => import("../view/active/signActCommerce/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  // 广生态
  {
    path: "/active/signActEcology",
    name: "signActEcology",
    component: () => import("../view/active/signActEcology/index.vue"),
    meta: {
      title: "广生态上进21奖学金",
    },
  },
  {
    path: "/active/signActEcology/release",
    name: "release",
    component: () => import("../view/active/signActEcology/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActEcology/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActEcology/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActEcology/readBook",
    name: "readBook",
    component: () => import("../view/active/signActEcology/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 华商
  {
    path: "/active/signActHuaShang",
    name: "signActHuaShang",
    component: () => import("../view/active/signActHuaShang/index.vue"),
    meta: {
      title: "华商上进21奖学金",
    },
  },
  {
    path: "/active/signActHuaShang/release",
    name: "release",
    component: () => import("../view/active/signActHuaShang/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActHuaShang/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActHuaShang/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActHuaShang/readBook",
    name: "readBook",
    component: () => import("../view/active/signActHuaShang/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  // 广商2
  {
    path: "/active/signActGuangShang",
    name: "signActGuangShang",
    component: () => import("../view/active/signActGuangShang/index.vue"),
    meta: {
      title: "广商上进21奖学金（2）",
    },
  },
  {
    path: "/active/signActGuangShang/release",
    name: "release",
    component: () => import("../view/active/signActGuangShang/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActGuangShang/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActGuangShang/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActGuangShang/readBook",
    name: "readBook",
    component: () => import("../view/active/signActGuangShang/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广东松山职业技术学院
  {
    path: "/active/signActSongShan",
    name: "signActSongShan",
    component: () => import("../view/active/signActSongShan/index.vue"),
    meta: {
      title: "松职上进21奖学金",
    },
  },
  {
    path: "/active/signActSongShan/release",
    name: "release",
    component: () => import("../view/active/signActSongShan/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActSongShan/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActSongShan/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActSongShan/readBook",
    name: "readBook",
    component: () => import("../view/active/signActSongShan/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 新华商
  {
    path: "/active/signActHuaShangNew",
    name: "signActHuaShangNew",
    component: () => import("../view/active/signActHuaShangNew/index.vue"),
    meta: {
      title: "华商上进21奖学金（2）",
    },
  },
  {
    path: "/active/signActHuaShangNew/release",
    name: "release",
    component: () => import("../view/active/signActHuaShangNew/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActHuaShangNew/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActHuaShangNew/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActHuaShangNew/readBook",
    name: "readBook",
    component: () => import("../view/active/signActHuaShangNew/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州铁路职业技术学院上进21奖学金
  {
    path: "/active/signActTieLu",
    name: "signActTieLu",
    component: () => import("../view/active/signActTieLu/index.vue"),
    meta: {
      title: "广州铁路职业技术学院上进21奖学金",
    },
  },
  {
    path: "/active/signActTieLu2023",
    name: "signActTieLu2023",
    component: () => import("../view/active/signActTieLu2023/index.vue"),
    meta: {
      title: "广州铁路职业技术学院上进21奖学金",
    },
  },
  {
    path: "/active/signActTieLu/release",
    name: "release",
    component: () => import("../view/active/signActTieLu/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActTieLu/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActTieLu/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActTieLu/readBook",
    name: "readBook",
    component: () => import("../view/active/signActTieLu/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州华夏职业学院上进21奖学金
  {
    path: "/active/signActHuaXia",
    name: "signActHuaXia",
    component: () => import("../view/active/signActHuaXia/index.vue"),
    meta: {
      title: "广州华夏职业学院上进21奖学金",
    },
  },
  {
    path: "/active/signActHuaXia/release",
    name: "release",
    component: () => import("../view/active/signActHuaXia/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActHuaXia/runRelease",
    name: "runRelease",
    component: () => import("../view/active/signActHuaXia/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActHuaXia/readBook",
    name: "readBook",
    component: () => import("../view/active/signActHuaXia/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  {
    path: "/active/2024signActHuaXia",
    name: "2024signActHuaXia",
    component: () => import("../view/active/2024signActHuaXia/index.vue"),
    meta: {
      title: "广州华夏职业学院上进21奖学金",
    },
  },
  {
    path: "/active/2024signActHuaXia/release",
    name: "2024release",
    component: () => import("../view/active/2024signActHuaXia/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/2024signActHuaXia/runRelease",
    name: "2024runRelease",
    component: () => import("../view/active/2024signActHuaXia/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/2024signActHuaXia/readBook",
    name: "2024readBook",
    component: () => import("../view/active/2024signActHuaXia/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },  {
    path: "/active/2024GuangJiu",
    name: "2024GuangJiu",
    component: () => import("../view/active/2024GuangJiu/index.vue"),
    meta: {
      title: "广东酒店同桌共读计划",
    },
  },
  {
    path: "/active/2024GuangJiu/release",
    name: "2024GuangJiurelease",
    component: () => import("../view/active/2024GuangJiu/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/2024GuangJiu/runRelease",
    name: "2024GuangJiurunRelease",
    component: () => import("../view/active/2024GuangJiu/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/2024GuangJiu/readBook",
    name: "2024GuangJiureadBook",
    component: () => import("../view/active/2024GuangJiu/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  // {
  //   path: "/active/newYearActive/transitPage",
  //   name: "transitPage",
  //   component: () =>
  //     import("../view/active/newYearActive2023-01-05/transitPage.vue"),
  //   meta: {
  //     title: "瑞兔送福抽大奖",
  //   },
  // },
  // 新年红包活动2025
  {
    path: "/active/newYear2025/transitPage",
    name: "newYear2025",
    component: () => import("../view/active/newYearActive2025/transitPage.vue"),
    meta: { title: "2025 金蛇送福" },
  },
  {
    path: "/active/newYearActive/landingPage",
    name: "landingPage",
    component: () =>
      import("../view/active/newYearActive2023-01-05/landingPage.vue"),
    meta: {
      title: "下载远智教育APP，一起成长",
    },
  },
  {
    path: "/active/collegeExamination/transitPage",
    name: "transitPage",
    component: () =>
      import("../view/active/collegeExamination/transitPage.vue"),
    meta: {
      title: "四月圈子逛逛节",
    },
  },
  {
    path: "/active/newRedEnvelope20240223/transitPage",
    name: "transitPage",
    component: () =>
      import("../view/active/newRedEnvelope20240223/transitPage.vue"),
    meta: {
      title: "金榜题名",
    },
  },
  {
    path: "/active/Automatic20240308/index",
    name: "automatic2024",
    component: () =>import("../view/active/Automatic20240308/index.vue"),
    meta: {  title: "打开小程序",  },
  },
  {
    path: "/active/Automatic20240308/education",
    name: "automatic2024",
    component: () =>import("../view/active/Automatic20240308/education.vue"),
    meta: {  title: "打开小程序",  },
  },
  {
    path: "/active/OpenMiniProgram",
    name: "openMiniProgram",
    component: () =>import("../view/active/OpenMiniProgram.vue"),
    meta: {  title: "打开小程序",  },
  },
  {
    path: "/active/active20230619/transitPage",
    name: "transitPage",
    component: () => import("../view/active/active20230619/transitPage.vue"),
    meta: {
      title: "上进活动",
    },
  },
  {
    path: "/active/active20230619/landingPage",
    name: "landingPage",
    component: () => import("../view/active/active20230619/landingPage.vue"),
    meta: {
      title: "前往远智教育APP，一起成长",
    },
  },

  // 广东松山职业技术学院-2023年奖学金活动
  {
    path: "/active/signActSongShan2023",
    name: "signActSongShan2023",
    component: () => import("../view/active/signActSongShan2023/index.vue"),
    meta: {
      title: "松职上进21奖学金",
    },
  },
  {
    path: "/active/signActSongShan2023/release",
    name: "signActSongShan2023Release",
    component: () => import("../view/active/signActSongShan2023/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActSongShan2023/runRelease",
    name: "signActSongShan2023RunRelease",
    component: () =>
      import("../view/active/signActSongShan2023/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActSongShan2023/readBook",
    name: "signActSongShan2023ReadBook",
    component: () => import("../view/active/signActSongShan2023/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州软件学院-2023年奖学金活动
  {
    path: "/active/signActGuangRuan",
    name: "signActGuangRuan",
    component: () => import("../view/active/signActGuangRuan/index.vue"),
    meta: {
      title: "广软上进21奖学金",
    },
  },
  {
    path: "/active/signActGuangRuan/release",
    name: "signActGuangRuanRelease",
    component: () => import("../view/active/signActGuangRuan/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/signActGuangRuan/runRelease",
    name: "signActGuangRuanRunRelease",
    component: () => import("../view/active/signActGuangRuan/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/signActGuangRuan/readBook",
    name: "signActGuangRuanReadBook",
    component: () => import("../view/active/signActGuangRuan/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  {
    path: "/active/trainingCamp",
    name: "trainingCamp",
    component: () => import("../view/active/trainingCamp/index"),
    meta: { title: "下载远智教育APP，一起成长" },
  },
  // h5支付测试环境中转站
  {
    path: "/pay/h5pay",
    name: "h5pay",
    component: () => import("../view/active/h5Pay/index"),
    meta: { title: "" },
  },

  // 上进学社投放落地页-绘画课程和影视后期剪辑
  {
    path: "/active/placeCourse",
    name: "placeCourse",
    component: () => import("../view/active/placeCourse/index.vue"),
  },
  {
    path: "/pay/payPlaceCourse",
    name: "payPlaceCourse",
    component: () => import("../view/active/placeCourse/payPlaceCourse.vue"),
    meta: {
      title: "支付",
    },
  },
  {
    path: "/active/placeCourse/success",
    name: "payPlaceCourseSuccess",
    component: () => import("../view/active/placeCourse/success.vue"),
    meta: {
      title: "报名成功",
    },
  },

  // 广州华商职业学院-20230829奖学金活动
  {
    path: "/active/a20230829HuaShang",
    name: "a20230829HuaShang",
    component: () => import("../view/active/a20230829HuaShang/index.vue"),
    meta: {
      title: "广州华商职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20230829HuaShang/release",
    name: "a20230829HuaShangRelease",
    component: () => import("../view/active/a20230829HuaShang/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20230829HuaShang/runRelease",
    name: "a20230829HuaShangRunRelease",
    component: () => import("../view/active/a20230829HuaShang/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20230829HuaShang/readBook",
    name: "a20230829HuaShangReadBook",
    component: () => import("../view/active/a20230829HuaShang/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 东莞城市学院-20230829奖学金活动
  {
    path: "/active/a20230829DongGuan",
    name: "a20230829DongGuan",
    component: () => import("../view/active/a20230829DongGuan/index.vue"),
    meta: {
      title: "东莞城市学院上进21奖学金",
    },
  },
  {
    path: "/active/a20230829DongGuan/release",
    name: "a20230829DongGuanRelease",
    component: () => import("../view/active/a20230829DongGuan/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20230829DongGuan/runRelease",
    name: "a20230829DongGuanRunRelease",
    component: () => import("../view/active/a20230829DongGuan/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20230829DongGuan/readBook",
    name: "a20230829DongGuanReadBook",
    component: () => import("../view/active/a20230829DongGuan/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },
  // 通用引导App下载或者唤醒App
  {
    path: "/active/aCommon/landingPage",
    name: "transitPage",
    component: () => import("../view/active/aCommon/landingPage.vue"),
    meta: {
      title: "前往远智教育APP，一起成长",
    },
  },

  // 广东松山职业技术学院-20240417奖学金活动
  {
    path: "/active/a20240417SongShan",
    name: "a20240417SongShan",
    component: () => import("../view/active/a20240417SongShan/index.vue"),
    meta: {
      title: "广东松山职业技术学院上进21奖学金",
    },
  },
  {
    path: "/active/a20240417SongShan/release",
    name: "a20240417SongShanRelease",
    component: () => import("../view/active/a20240417SongShan/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20240417SongShan/runRelease",
    name: "a20240417SongShanRunRelease",
    component: () => import("../view/active/a20240417SongShan/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20240417SongShan/readBook",
    name: "a20240417SongShanReadBook",
    component: () => import("../view/active/a20240417SongShan/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州商学院-20240417奖学金活动
  {
    path: "/active/a20240417GuangShang",
    name: "a20240417GuangShang",
    component: () => import("../view/active/a20240417GuangShang/index.vue"),
    meta: {
      title: "广州商学院上进21奖学金",
    },
  },
  {
    path: "/active/a20240417GuangShang/release",
    name: "a20240417GuangShangRelease",
    component: () => import("../view/active/a20240417GuangShang/release.vue"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20240417GuangShang/runRelease",
    name: "a20240417GuangShangRunRelease",
    component: () => import("../view/active/a20240417GuangShang/runRelease.vue"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20240417GuangShang/readBook",
    name: "a20240417GuangShangReadBook",
    component: () => import("../view/active/a20240417GuangShang/readBook.vue"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州城建职业学院-20240704-奖学金活动
  {
    path: "/active/a20240704ChengJian",
    name: "a20240704ChengJian",
    component: () => import("../view/active/a20240704ChengJian/index"),
    meta: {
      title: "广州城建职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20240704ChengJian/release",
    name: "a20240704ChengJianRelease",
    component: () => import("../view/active/a20240704ChengJian/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20240704ChengJian/runRelease",
    name: "a20240704ChengJianRunRelease",
    component: () => import("../view/active/a20240704ChengJian/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20240704ChengJian/readBook",
    name: "a20240704ChengJianReadBook",
    component: () => import("../view/active/a20240704ChengJian/readBook"),
    meta: {
      title: "学习视频",
    },
  },
  {
    path: "/active/2024ChengJian",
    name: "2024ChengJian",
    component: () => import("../view/active/2024ChengJian/index"),
    meta: {
      title: "广州城建职业学院上进21奖学金",
    },
  },
  {
    path: "/active/2024ChengJian/release",
    name: "2024ChengJianRelease",
    component: () => import("../view/active/2024ChengJian/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/2024ChengJian/runRelease",
    name: "2024ChengJianRunRelease",
    component: () => import("../view/active/2024ChengJian/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/2024ChengJian/readBook",
    name: "2024ChengJianReadBook",
    component: () => import("../view/active/2024ChengJian/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州软件学院-20240819-奖学金活动
  {
    path: "/active/a20240819GuangRuan",
    name: "a20240819GuangRuan",
    component: () => import("../view/active/a20240819GuangRuan/index"),
    meta: {
      title: "广州软件学院上进21奖学金",
    },
  },
  {
    path: "/active/a20240819GuangRuan/release",
    name: "a20240819GuangRuanRelease",
    component: () => import("../view/active/a20240819GuangRuan/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20240819GuangRuan/runRelease",
    name: "a20240819GuangRuanRunRelease",
    component: () => import("../view/active/a20240819GuangRuan/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20240819GuangRuan/readBook",
    name: "a20240819GuangRuanReadBook",
    component: () => import("../view/active/a20240819GuangRuan/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州华商职业学院-20240819-奖学金活动
  {
    path: "/active/a20240819HuaShang",
    name: "a20240819HuaShang",
    component: () => import("../view/active/a20240819HuaShang/index"),
    meta: {
      title: "广州华商职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20240819HuaShang/release",
    name: "a20240819HuaShangRelease",
    component: () => import("../view/active/a20240819HuaShang/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20240819HuaShang/runRelease",
    name: "a20240819HuaShangRunRelease",
    component: () => import("../view/active/a20240819HuaShang/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20240819HuaShang/readBook",
    name: "a20240819HuaShangReadBook",
    component: () => import("../view/active/a20240819HuaShang/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 东莞城市学院-20241023-奖学金活动
  {
    path: "/active/a20241023DongGuan",
    name: "a20241023DongGuan",
    component: () => import("../view/active/a20241023DongGuan/index"),
    meta: {
      title: "东莞城市学院上进21奖学金",
    },
  },
  {
    path: "/active/a20241023DongGuan/release",
    name: "a20241023DongGuanRelease",
    component: () => import("../view/active/a20241023DongGuan/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20241023DongGuan/runRelease",
    name: "a20241023DongGuanRunRelease",
    component: () => import("../view/active/a20241023DongGuan/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20241023DongGuan/readBook",
    name: "a20241023DongGuanReadBook",
    component: () => import("../view/active/a20241023DongGuan/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州铁路职业技术学院-20241023-奖学金活动
  {
    path: "/active/a20241023GuangTie",
    name: "a20241023GuangTie",
    component: () => import("../view/active/a20241023GuangTie/index"),
    meta: {
      title: "广州铁路职业技术学院上进21奖学金",
    },
  },
  {
    path: "/active/a20241023GuangTie/release",
    name: "a20241023GuangTieRelease",
    component: () => import("../view/active/a20241023GuangTie/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20241023GuangTie/runRelease",
    name: "a20241023GuangTieRunRelease",
    component: () => import("../view/active/a20241023GuangTie/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20241023GuangTie/readBook",
    name: "a20241023GuangTieReadBook",
    component: () => import("../view/active/a20241023GuangTie/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广东生态工程职业学院-20241023-奖学金活动
  {
    path: "/active/a20241023GuangSheng",
    name: "a20241023GuangSheng",
    component: () => import("../view/active/a20241023GuangSheng/index"),
    meta: {
      title: "广东生态工程职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20241023GuangSheng/release",
    name: "a20241023GuangShengRelease",
    component: () => import("../view/active/a20241023GuangSheng/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20241023GuangSheng/runRelease",
    name: "a20241023GuangShengRunRelease",
    component: () => import("../view/active/a20241023GuangSheng/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20241023GuangSheng/readBook",
    name: "a20241023GuangShengReadBook",
    component: () => import("../view/active/a20241023GuangSheng/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州城建职业学院-20250609-奖学金活动
  {
    path: "/active/a20250609ChengJian",
    name: "a20250609ChengJian",
    component: () => import("../view/active/a20250609ChengJian/index"),
    meta: {
      title: "广州城建职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20250609ChengJian/release",
    name: "a20250609ChengJianRelease",
    component: () => import("../view/active/a20250609ChengJian/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20250609ChengJian/runRelease",
    name: "a20250609ChengJianRunRelease",
    component: () => import("../view/active/a20250609ChengJian/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20250609ChengJian/readBook",
    name: "a20250609ChengJianReadBook",
    component: () => import("../view/active/a20250609ChengJian/readBook"),
    meta: {
      title: "学习视频",
    },
  },

  // 广州南方学院-20250609-奖学金活动
  {
    path: "/active/a20250609NanFang",
    name: "a20250609NanFang",
    component: () => import("../view/active/a20250609NanFang/index"),
    meta: {
      title: "广州南方学院上进21奖学金",
    },
  },
  {
    path: "/active/a20250609NanFang/release",
    name: "a20250609NanFangRelease",
    component: () => import("../view/active/a20250609NanFang/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20250609NanFang/runRelease",
    name: "a20250609NanFangRunRelease",
    component: () => import("../view/active/a20250609NanFang/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20250609NanFang/readBook",
    name: "a20250609NanFangnReadBook",
    component: () => import("../view/active/a20250609NanFang/readBook"),
    meta: {
      title: "学习视频",
    },
  },


  // 广州华夏职业学院-20250609-奖学金活动
  {
    path: "/active/a20250609HuaXia",
    name: "a20250609HuaXia",
    component: () => import("../view/active/a20250609HuaXia/index"),
    meta: {
      title: "广州华夏职业学院上进21奖学金",
    },
  },
  {
    path: "/active/a20250609HuaXia/release",
    name: "a20250609HuaXiaRelease",
    component: () => import("../view/active/a20250609HuaXia/release"),
    meta: {
      title: "打卡",
    },
  },
  {
    path: "/active/a20250609HuaXia/runRelease",
    name: "a20250609HuaXiaRunRelease",
    component: () => import("../view/active/a20250609HuaXia/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
  {
    path: "/active/a20250609HuaXia/readBook",
    name: "a20250609HuaXiaReadBook",
    component: () => import("../view/active/a20250609HuaXia/readBook"),
    meta: {
      title: "学习视频",
    },
  },
  
  // =================================================================
  // == 通用奖学金活动模板路由 (用于未来新活动)
  // =================================================================
  {
    path: "/active/scholarshipCampaign/:campaignKey",
    name: "ScholarshipCampaign",
    component: () => import("../view/active/ScholarshipCampaign/index"),
    meta: {
      title: "上进21奖学金", // 通用标题，组件内部可覆盖
    },
  },
  {
    path: "/active/scholarshipCampaign/:campaignKey/release",
    name: "ScholarshipCampaignRelease",
    component: () => import("../view/active/ScholarshipCampaign/release"),
    meta: {
      title: "发布学习心得",
    },
  },
  {
    path: "/active/scholarshipCampaign/:campaignKey/readBook",
    name: "ScholarshipCampaignReadBook",
    component: () => import("../view/active/ScholarshipCampaign/readBook"),
    meta: {
      title: "学习视频",
    },
  },
  {
    path: "/active/scholarshipCampaign/:campaignKey/runRelease",
    name: "ScholarshipCampaignRunRelease",
    component: () => import("../view/active/ScholarshipCampaign/runRelease"),
    meta: {
      title: "跑步打卡",
    },
  },
];
