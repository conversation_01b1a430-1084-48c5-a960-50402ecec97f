<template>
  <!-- Root element of PhotoSwipe. Must have class pswp. -->
  <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true" ref="pswp">
    <!-- Background of PhotoSwipe.
         It's a separate element, as animating opacity is faster than rgba(). -->
    <div class="pswp__bg"></div>
    
    <!-- Slides wrapper with overflow:hidden. -->
    <div class="pswp__scroll-wrap">
      <!-- Container that holds slides. PhotoSwipe keeps only 3 slides in DOM to save memory. -->
      <div class="pswp__container">
        <!-- don't modify these 3 pswp__item elements, data is added later on -->
        <div class="pswp__item"></div>
        <div class="pswp__item"></div>
        <div class="pswp__item"></div>
      </div>
      
      <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
      <!--<div class="pswp__ui pswp__ui&#45;&#45;hidden">-->
      <div class="pswp__ui ">
        <div class="pswp__top-bar">
          <!--  Controls are self-explanatory. Order can be changed. -->
          <div class="pswp__counter"></div>
          <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
          <!--<button class="pswp__button pswp__button&#45;&#45;share" title="Share"></button>
          <button class="pswp__button pswp__button&#45;&#45;fs" title="Toggle fullscreen"></button>
          <button class="pswp__button pswp__button&#45;&#45;zoom" title="Zoom in/out"></button>-->
          
          <!-- Preloader demo https://codepen.io/dimsemenov/pen/yyBWoR -->
          <!-- element will get class pswp__preloader--active when preloader is running -->
          <div class="pswp__preloader">
            <div class="pswp__preloader__icn">
              <div class="pswp__preloader__cut">
                <div class="pswp__preloader__donut"></div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
          <div class="pswp__share-tooltip"></div>
        </div>
        <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
        <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>
        <div class="pswp__caption">
          <div class="pswp__caption__center"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import PhotoSwipe from 'photoswipe'
  import PhotoSwipeUI_Default from 'photoswipe/dist/photoswipe-ui-default'
  import 'photoswipe/dist/photoswipe.css'
  import 'photoswipe/dist/default-skin/default-skin.css'

  export default {
    data() {
      return {
        gallery: null,
        items: [],
        options: {
          history: false,
          focus: false,
          showAnimationDuration: 0,
          hideAnimationDuration: 0,
          fullscreenEl: false,
          zoomEl: false,
          shareEl: false,
          tapToToggleControls: false,
          //tapToClose: true,
          bgOpacity: .8,
          index: 0,
          isClickableElement: function (el) {
            return el.tagName.toLowerCase() === 'img';
          }
        }
      }
    },
    methods: {
      init: function () {
        this.$indicator.close();
        this.gallery = new PhotoSwipe(this.$refs.pswp, PhotoSwipeUI_Default, this.items, this.options);
        this.gallery.init();
      },
      open: function (items, index) {
        if (items.length === 0) return;
        this.$indicator.open();
        this.items = [];
        this.options.index = index || 0;

        for (let i in items) {
          this.setItems(items[i], i, items.length);
        }
      },
      setItems: function (item, index, count) {
        let img = new Image();
        img.src = item.type === 'base64' ? item.url : `${item.url}?=${new Date().getTime()}`;
        img.onerror = (err) => {
          this.items[index] = {src: item.url, title: '', w: 0, h: 0};
        };
        this.startTimer(img, item, index, count);
      },
      // 获取图片宽高
      startTimer: function (img, item, index, count) {
        let timer;
        timer = setInterval(() => {
          // 如果当前图片无法加载
          if (this.items[index]) {
            clearInterval(timer);
            if (count === this.items.length) {
              let isLoad = true;
              for (let val of this.items) {
                if (!val) isLoad = false;
              }
              isLoad && this.init();
            }
          } else if (img.width > 0) {
            clearInterval(timer);
  
            this.items[index] = {src: item.url, title: item.title, w: img.width, h: img.height};
            if (count === this.items.length) {
              let isLoad = true;
              for (let val of this.items) {
                if (!val) isLoad = false;
              }
              isLoad && this.init();
            }
          }
        }, 60);
      }
    }
  }
</script>

<style lang="less" scoped>
  .pswp{ z-index:9999; }
  /*.pswp__top-bar{ display:none; }*/
  .pswp__caption{ min-height:0; }
  .pswp__caption__center{ text-align:center; font-size:.14rem; }
</style>
