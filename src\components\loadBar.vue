<template>
  <div>
    <div class="loadmore" v-show="isLoading&&!allLoaded">
      <mt-spinner class="mint-loadmore-spinner" :size="20" type="fading-circle"></mt-spinner>
      <span class="mint-loadmore-text">加载中...</span>
    </div>
    <template v-if="allLoaded"><slot></slot></template>
    <slot name="noData" v-if="isNoData"><div class="no-data">暂无记录</div></slot>
  </div>
</template>

<script>
  export default {
    props: {
      isLoading: {
        type: Boolean,
        default: false
      },
      allLoaded: {
        type: Boolean,
        default: false
      },
      total: null
    },
    computed: {
      isNoData: function () {
        console.log(this.total,this.allLoaded, 'this.total');
        return 'number' === typeof this.total && this.total === 0 && this.allLoaded;
      }
    },
    updated(){
      console.log(this.isLoading,this.allLoaded)
    }
  }
</script>

<style lang="less" scoped>
  .loadmore{ padding:.13rem 0; clear:both; text-align:center; color:#868686; font-size:.12rem; }
</style>
