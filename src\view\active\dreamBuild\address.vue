<template>
  <div>
    <div class="addressEdit mt">
      <div class="addressEdit-item disabled">
        <span>收货人</span><input type="text" v-model="saName">
      </div>
      <div class="addressEdit-item disabled">
        <span>联系电话</span><input type="number" maxlength="20" v-model="mobile">
      </div>
      <div class="addressEdit-item" v-show="saType=='3'">
        <span>邮箱</span><input type="text" maxlength="20" v-model="addressObj.email">
      </div>
      <div class="addressEdit-item">
        <span>所在地区</span>
        <div class="fr row1" @click="$refs.provinceJD.open(provinceObjJD);$refs.provinceJD.getProvince()">
          {{provinceObjJD.provinceName}} {{provinceObjJD.cityName}} {{provinceObjJD.districtName}}
          {{provinceObjJD.streetName}}<i class="icons pic-right"></i>
        </div>
      </div>
      <div class="addressEdit-item">
        <textarea v-model="addressObj.address" placeholder="请输入详细地址.." maxlength="100"></textarea>
      </div>
      <p class="note">注意：详细地址不能出现特殊符号、繁体字</p>
    </div>
    <province-j-d :onlyGD="onlyGD" ref="provinceJD" v-model="province" @JDProvince="getProvinceObj" key="province"/>
    <foot-bar class="active" title="提交" v-on:submit="saveEditAddress"></foot-bar>
  </div>
</template>

<script>
  import topBar from '@/components/topBar';
  import footBar from '@/components/footBar';
  import province from '@/components/province';
  import provinceJD from '@/components/provinceJD';

  export default {
    name: 'dreamBuildAddress',
    directives: {
      focus: {
        inserted: function (el) {
          el.focus();
        }
      }
    },
    data() {
      return {
        saId: 0,
        learnId: '',
        saType: '1',//地址类型，默认3京东，1为教材
        saName: '',
        onlyGD: '1',
        saNameDis: false,
        mobile: '',
        addressObj: {
          excType: '',
          provinceName: '',
          provinceCode: '',
          cityName: '',
          cityCode: '',
          districtName: '',
          districtCode: '',
          streetName: '',
          streetCode: '',
          address: '',
          saName: '',
          isDefault: '',
        },
        province: {},
        unvsName:'',
        provinceObjJD: {
          provinceName: '',
          provinceCode: '',
          cityName: '',
          cityCode: '',
          districtName: '',
          districtCode: '',
          streetName: '',
          streetCode: '',
          scholarship:''
        },
        from: 0, // 来源 0是默认 1是推送消息
      }
    },
    created() {
      this.onlyGD = this.$route.query.onlyGD || '1';
      this.learnId = this.$route.query.learnId?.trim() || '';
      this.scholarship = this.$route.query.scholarship ||'',
      this.from = this.$route.query.from || 0,
      this.saName = this.storage.getItem('realName');

      this.getUserInfo();
      if (this.from == 1) {
        this.isHasAddress();
      }
    },
    mounted() {
      this.saId = this.$route.query.saId;
      this.backType = this.$route.query.backType || '';
      this.addressObj.excType = '1';    // 新增收货地址
      this.addressObj.saName = this.saName;
      this.saNameDis = true;

      this.addressObj.isDefault = this.storage.getItem('zmcAddressJD') ? '0' : '1';   // 如果是第一次增加收货地址，则设成默认收货地址
      if(this.$route.query.empId){
        this.storage.setItem('empId', this.$route.query.empId);  // 公众号通知打开  自己缴费是没有跟进人的  后续手动分配  缓存中empId还是空的 （在登录的时候缓存的）
      }
    },
    methods: {
      // 获取收货地址信息
      async getEditInfo() {
        let addressArray = []
        if (this.saType == '3') {
          addressArray = this.storage.getItem('zmcAddressJD') || '[]';
        } else {
          addressArray = this.storage.getItem('zmcAddress') || '[]';
        }

        addressArray = JSON.parse(addressArray);
        addressArray = addressArray.find(item => item.saId === this.saId);

        if (!addressArray) {
          let res = await this.$http.post('/us/myAddress/1.0/', {saType: this.saType});
          if (res.code !== '00') return;
          addressArray = (res.body || []).find(item => item.saId === this.saId);
        }

        Object.assign(this.addressObj, addressArray);

        for (let k in this.addressObj) {
          if (k in this.provinceObjJD) {
            this.provinceObjJD[k] = this.addressObj[k]
          }
        }

        this.$nextTick(() => {
          this.$refs.provinceJD.setValues(this.provinceObjJD);
        });
      },
      async isHasAddress() {
        const res = await this.$http.post('/us/getStudentTextBookRecord/1.0/', {learnId: this.learnId});
        if (res.code == '00') {
          if(res.body){
            this.navToNewbieTask()
            // this.$router.replace({
            //   name: "studyGuide",
            //   // path: "/tutorialClass/optimzeHome",
            //   // query: { scholarship: this.scholarship },
            // });
          }
        }
      },

      // 修改收货地址
      async saveEditAddress() {
        this.addressObj.address=this.addressObj.address.replace(/\s/g,"")
        if (!(!/[^\w\u4E00-\u9FA5]/g.test(this.addressObj.address))) {
          this.$modal({message: '地址不能包括特殊符号，请重新输入!', icon: 'warning'});
          return
        }

        this.$indicator.open();
        if (this.province.provinceCode) {
          Object.assign(this.addressObj, this.province);
        }
        for (var k in this.addressObj) {
          if (this.addressObj[k] == ''  && !['streetCode', 'streetName'].includes(k)) {
            this.$nextTick(() => {
              this.$indicator.close();
            });
            this.$modal({message: '信息未完善!', icon: 'warning'});
            return;
          }
        }
        // 保存
        let data = Object.assign(this.addressObj, {learnId: this.learnId});
        let res = await this.$http.post("/us/editStudentCoachAddress/1.0/", data);
        if(this.scholarship =='103') {
          this.$http.post("/mkt/isGiveCouponByType/1.0/",{couponType:'09_hx_ln'}).then(res=>{
            this.quan=res.body
          })
          this.$http.post('/mkt/getAdvanceWitnessByCoverToken/1.0/',{
            scholarship:103,coverToken:this.storage.getItem("authToken")
          }).then(res=>{
            if(res.body==null){
              this.zheng=false
            }else{
              this.zheng=true
            }
          });
        }

        this.$indicator.close();
        if (res.code == '00') {
          this.$modal({
            message: '提交成功！',
            beforeClose:async (action, instance, done) => {
              await this.navToNewbieTask()
              done()
              // 取消上课指引页面展示(新手任务需求调整)
              // if((this.quan==false||this.zheng==false)&& this.scholarship=='103'){
              //   this.$router.replace({name:'oneYearerrollSuccess',query:{zheng:this.zheng,quan:this.quan}})
              // }else{
              //   this.$router.replace({
              //     // name: "optimzeHome",
              //     // name: "stuInfo",
              //     name: "studyGuide",
              //   });
              // }
            }
          });
        }
      },
      getProvinceObj: function (provinceObj) {
        this.provinceObjJD = provinceObj;
        this.addressObj.provinceName = this.provinceObjJD.provinceName;
        this.addressObj.provinceCode = this.provinceObjJD.provinceCode;
        this.addressObj.cityName = this.provinceObjJD.cityName;
        this.addressObj.cityCode = this.provinceObjJD.cityCode;
        this.addressObj.districtName = this.provinceObjJD.districtName;
        this.addressObj.districtCode = this.provinceObjJD.districtCode;
        this.addressObj.streetName = this.provinceObjJD.streetName;
        this.addressObj.streetCode = this.provinceObjJD.streetCode;
      },
      // 获取用户信息
      getUserInfo: function () {
        this.$http.post('/us/userInfo/1.0/').then(res => {
          if (res.code === '00') {
            const mobile = res.body.mobile || '';
            this.mobile = mobile;
            this.storage.setItem('mobile', mobile);
          }
        });
      },
      back() {
        this.$router.replace({name: 'dreamBuild'})
      },
      // 缴费后填写教材收货地址，完成后判断是否进入新手任务活动页(取消上课指引页面展示)
      async navToNewbieTask() {
        const subOrderNo = this.$route.query?.orderNo
        if (subOrderNo) {
          const res = await this.$http.post(
            "/marketing-server/newbie/task/init",
            {
              data: {
                learnId: this.learnId,
                subOrderNo,
              },
              client: { platform: "H5" },
            },
            { proxyName: "yzapp" }
          )
          if (res.state.code === 0 && res?.data) {
            // 满足新人任务条件，进入新手任务活动页
            this.$router.replace('/newPages/newbie-task/index')
            return
          }
        }
        // 默认进入学堂页
        this.$router.replace({ name: "stuInfo" })
      },
    },
    components: {topBar, footBar, province, provinceJD}
  }
</script>

<style lang="less" scoped>
  @import "../../../assets/less/variable";

  .note {
    color: @color;
    margin-top: .05rem;
    font-size: .12rem;
    text-indent: .12rem;
  }

  .addressEdit-item {
    padding: 0 .16rem;
    background-color: #fff;
    position: relative;
    width: 100%;
    overflow: hidden;
    &.disabled {
      pointer-events: none;
    }

    &:after {
      .borderBottom;
    }
    &:nth-of-type(4) {
      position: relative;
      padding-right: 0;
      line-height: .56rem;
      span {
        margin-right: 0;
      }
    }
    input {
      border: none;
      vertical-align: baseline;
      color: #444;
      height: .53rem;
      width: 100%;
      overflow: hidden;
      margin-right: -2rem;
    }
    span {
      color: #666;
      margin-right: .16rem;
    }
    textarea {
      border: none;
      display: inline-block;
      line-height: 1.5;
      width: 100%;
      color: #666;
      padding: .12rem 0;
      height: .75rem;
    }
    .pic-right {
      position: absolute;
      top: .1rem;
      right: 0;
      width: 0.36rem;
      height: 0.36rem;
      background-image: url("../../../assets/image/public_ico_open_right.png");
    }
    .fr {
      width: 2.5rem;
      min-height: .56rem;
      padding-right: .36rem;
      text-align: right;
      color: #999;
    }
  }
</style>
