<template>
    <div class="question_wrap" v-if="isShow">
        <div class="bg"></div>
        <div class="question">
          <div>
            <div class="title">
              <span>课堂问答</span>
              <div class="close"><img src="../../assets/image/student/ic_close.png" alt="" @click="isShow=false" v-if="data.skip=='true'"></div>
            </div>
            <div class="question_body" v-if="showType=='showanswer'">
              <p><span v-if="data.type=='R'">单选</span><span v-else>多选</span>{{data.question}}</p>
            </div>
            <div class="question_body" v-else>
              <p><span v-if="data.type=='R'">单选</span><span v-else>多选</span>{{data.question}}</p>
            </div>
            <div class="question_detail" v-if="showType=='showanswer'">
              <ul>
                <li v-for="item,o in options"  :class="addClass(item)">
                  <img src="../../assets/image/student/ic_true.png" alt="" v-if="answer.indexOf(item.option)!=-1&&item.right_answer==1">
                  <img src="../../assets/image/student/ic_wrong.png" alt="" v-else-if="answer.indexOf(item.option)!=-1&&item.right_answer!=1">
                  <span v-else>{{item.option}}. </span>
                  {{item.answer}}</li>
                <!--<li   :class="addClass(i,it)">-->
                  <!--<img src="../../assets/image/student/ic_true.png" alt="" v-if="data.content.answer=='B'">-->
                  <!--<img src="../../assets/image/student/ic_wrong.png" alt="" v-else-if="data.content.answer!='B'&&storage.getItem('question')=='B'">-->
                  <!--<span v-else>B. </span>{{data.content.option2}}</li>-->
              </ul>
            </div>
            <div class="question_detail" v-else>
              <ul v-if="data.type=='R'">
                <li v-for="item,o in options"   @click="select(item)" :class="{active:singleselect==item.option}" ><span>{{item.option}}</span>{{item.answer}}</li>
                <!--<li @click="select('B')" :class="{active:singleselect=='B'}"><span>B. </span>{{data.option2}}</li>-->
              </ul>
              <ul v-else>
                <li v-for="item,o in options"   @click="select(item)" :class="{active:manyselect.indexOf(item.option)!=-1}" ><span>{{item.option}}</span>{{item.answer}}</li>
              </ul>
            </div>
            <button v-on:click="close" v-if="showType=='showanswer'">知道了</button>
            <button @click="submit" :disabled="singleselect==''&&manyselect.length==0"  v-else>提交</button>
          </div>
        </div>
    </div>
</template>

<script>
    export default {
      name: "question",
      props:["data"],
      data(){
          return{
            isShow:false,
            options:{},
            singleselect:'',
            manyselect:[],
            showType:'',
            answer:''
          }
      },
      created(){
      },
      mounted(){

      },
      methods:{
        open(type){
            this.isShow=true
            this.showType=type
        },
        close(){
          this.isShow=false
          this.$emit('close')
        },
        submit(){
            let answer;
            if(this.data.type=='R'){
              answer=this.singleselect;
            }else{
              answer=this.manyselect.join("");
            }
            //this.isShow=false
          this.answer=answer
          this.showType='showanswer'
        },
        select(data){
          //R表示单选，C表示多选
          if(this.data.type=='R'){
            this.singleselect=data.option
            console.log(this.singleselect);
          }else{
            if(this.manyselect&&this.manyselect.indexOf(data.option)==-1){
              let an=[...this.manyselect,data.option]
              this.manyselect=an;
            }else if (this.manyselect&&this.manyselect.indexOf(data.option)!=-1) {
              let an=[]
              for(let i=0;i<this.manyselect.length;i++){
                if(data.option!=this.manyselect[i]){
                  an.push(this.manyselect[i])
                }
              }
              this.manyselect=an;
            }else{
              this.manyselect.push(data.option)
            }
          }
        },
        addClass(item){
          let num;
          if(this.answer.indexOf(item.option)!=-1||item.right_answer==1){
            num=item.right_answer==1?1:2;
            return 'class'+num;
          }else{
            return '';
          }
        }
      },
      watch:{
        data:function(newValue){
          this.manyselect=[]
          this.singleselect=''
            for(let o=0;o<newValue.options.length;o++){
              if(newValue.options[o]){
                  let obj=newValue.options[o];
                  let detail;
                  switch (o) {
                    case 0:detail='A';break;
                    case 1:detail='B';break;
                    case 2:detail='C';break;
                    case 3:detail='D';break;
                    case 4:detail='E';break;
                    default:break;
                  }
                  obj["option"]=detail
                  console.log(obj);
                  this.$set(this.options,o,obj)
                }
            }
            console.log(this.options);
          }
      }
    }
</script>

<style scoped lang="less">
.question_wrap{
  position: fixed;
  width: 100%;
  max-width: 640px;
  height: 100%;
  min-height: 100vh;
  overflow: auto;
  z-index:9999;
  top:0rem;
  left:0;
  bottom:0;
  right: 0;
  margin: auto;
  .bg{
    width: 100%;
    max-width: 640px;
    min-height: 100vh;
    height: 100%;
    position: fixed;
    top:0;
    left:0;
    bottom:0;
    right: 0;
    margin: auto;
    z-index:210;
    background-color: rgba(0,0,0,.3);
  }
  .question{
    position:absolute;
    bottom:0;
    z-index:400;
    width: 100%;
    max-height: 55vh;
    overflow-y: scroll;
    overflow-x: hidden;
    background-color: white;
    .title{
      height: auto;
      overflow: hidden;
      margin-left: .2rem;
      margin-right: .15rem;
      padding-bottom: .05rem;
      border-bottom: solid 1px rgba(23,6,6,.1);
      span{
        display: block;
        float: left;
        background-size: .24rem .24rem;
        font-size: .15rem;
        margin-top: .17rem;
      }
      .close{
        float: right;
        width: .32rem;
        height: .32rem;
        margin-top: .11rem;
        img{
          width: 100%;
        }
      }
    }
  }
  .question_body{
    font-size: .15rem;
    line-height: .2rem;
    font-weight: 600;
    width: 3.38rem;
    background-color: white;
    border-radius: .03rem;
    margin: 0.23rem .25rem .1rem .2rem;
    p{
      word-break: break-word;
      width: 100%;
    }
    span{
      border: solid 1px #3F9DF2;
      font-size: .12rem;
      color: #3F9DF2;
      border-radius: .04rem;
      float: left;
      padding: 0.01rem 0.05rem;
      height: .18rem;
      line-height: .16rem;
      margin-top: .01rem;
      margin-right: .05rem;
    }
  }
  .question_detail{
    width: 100%;
    margin: 0 auto;
    ul{
      li{
        margin: 0 .2rem;
        font-size: .15rem;
        line-height: .2rem;
        min-height: .5rem;
        padding: 0.23rem 0 0 0.2rem;
        &.class1{
          color: #40C65D;
        }
        &.class2{
          color: #E85B57;
        }
        img{
          width: .25rem;
          height:.25rem;
        }
        span{
          display: inline-block;
          width: .25rem;
          height:.25rem;
          text-align: center;
          line-height: .25rem;
        }
        &.active{
          color: #59B7FF;
        }
      }
    }
  }
  button{
    display: block;
    margin: .2rem auto 0;
    line-height: .4rem;
    border: none;
    font-size: .14rem;
    color: #fff;
    background: #E85B57;
    padding: 0 0.1rem;
    width: 100%;
    cursor: pointer;
  }
}
</style>
