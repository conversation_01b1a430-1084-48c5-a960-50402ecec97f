<template>
  <mt-datetime-picker class="datetime-ui" type="date" ref="picker"
                      :startDate="new Date(startDate)"
                      :endDate="new Date(endDate)"
                      year-format="{value} 年"
                      month-format="{value} 月"
                      date-format="{value} 日"
                      @confirm="setDate">
  </mt-datetime-picker>
</template>

<script>
  import {fmtDate} from '../common';
  
  export default {
    props: {
      startDate: {
        default: '1960/01/01'
      },
      endDate: {
        default: new Date().getTime()
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      open: function () {
        if(this.disabled) return;
        this.$refs.picker.open();
      },
      setDate: function (value) {
        this.$emit('input', fmtDate(value.valueOf(), 'yyyy-MM-dd'));
      }
    }
  }
</script>

<style lang="less">
  @import "../assets/less/variable.less";
  .datetime-ui{
    .mint-datetime-picker{ position:relative; padding-bottom:.5rem; }
    .picker-toolbar{ position:absolute; right:0; bottom:0; left:0; height:.5rem; border-bottom:none; border-top:1px solid #eaeaea; }
    .mint-datetime-action{
      line-height:.5rem; color:#666; font-size:.16rem;
      &:last-child{ color:#fff; background-color:#f26662; background-image:@bgColor; }
    }
    .mint-datetime-cancel{ color:#666; border-right:1px solid #eaeaea; }
  }
</style>
