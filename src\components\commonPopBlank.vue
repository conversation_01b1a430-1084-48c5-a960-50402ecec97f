<template>
  <transition name="slide">
    <div @click="hidePop" class="commonPop" v-if="show">
      <div @click.stop class="commonPop-box">
        <div :class="isColor?'commonPop-box-in':'commonPop-box-on'">
          <slot></slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  export default {
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      outClose: {
        type: Boolean,
        default: false
      },
      isColor: {
        type: Boolean,
        default:true
      }
    },
    data() {
      return {
        show: this.isShow
      }
    },
    methods: {
      open: function () {
        this.show = true;
      },
      close: function () {
        this.show = false;
      },
      confirm: function () {
//        this.show = false;
        this.$emit('submit')
      },
      hidePop(){
        if(this.outClose){
          this.show = false;
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .commonPop {
    z-index: 2023;
    /*z-index: 2030;*/
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color:rgba(0, 0, 0, .5);
    transition:opacity .2s ease;
    .commonPop-box {
      z-index: 2031;
      width: 100%;
      min-height: 2.05rem;
      position: fixed;
      padding:0 .12rem;
      background: transparent;
      transition:all .3s ease;
      top: 30%;
      right: auto;
      bottom: 0;
      left: 0;
      .commonPop-box-in{
        background-color: #fff;
        padding: .2rem .19rem;
        border-radius: .04rem;
        width: 3.42rem;
        margin: 0 auto;
        position: relative;
      }
      .commonPop-box-on{
        // background-color: red;
        // padding: .2rem .19rem;
        border-radius: .04rem;
        // width: 3.42rem;
        // margin: 0 auto;
        position: relative;
      }
    }
  }

  .slide-enter, .slide-leave-active{
    opacity:0;
  }
  .slide-enter .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
    /*transform: translate3d(-50%, 0, 0);*/
  }
  .slide-enter-active .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
  }
  .slide-leave .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
  }
  .slide-enter .commonPop-box,.slide-leave-active .commonPop-box{
    transform: scale(.8);
    opacity:0;
  }
</style>
