<template>
  <!-- 微信右上角分享提示 -->
  <div class="mainsBox-cover" ref="cover" @click="onCloseBtn" v-if="shareFlag">
    <div class="bg"></div>
    <div class="J-weixin-tip weixin-tip" ref="wexin_tip">
      <div class="weixin-tip-content">
        {{ shareObs.one }}<br />
        {{ shareObs.two }}<br />
        {{ shareObs.three }}<br />
      </div>
      <div class="airport"></div>
    </div>
    <div class="J-weixin-tip-img weixin-tip-img" ref="wexin_tip_img"></div>
  </div>
</template>

<script>
export default {
  props: {
    shareFlag: { type: Boolean, default: false },
    shareObs: { type: Object, default: () => {} }
  },
  methods: {
    onCloseBtn() {
      this.$emit('onClose')
    }
  }
}
</script>

<style lang="less" scoped>
.mainsBox-cover {
  width: 100%;
  max-width: 640px;
  height: 100%;
  position: fixed;
  top: 0;
  z-index: 2002;

  .bg {
    width: 100%;
    max-width: 640px;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    overflow: hidden;
    z-index: 2003;
    position: fixed;
    top: 0;
  }

  .weixin-tip {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: fixed;
    top: 15px;
    right: 20px;
    width: 285px;
    padding: 55px 0 0;
    text-align: left;
    background: url(@/assets/image/icon_arrow.png) no-repeat right top;
    background-size: 0.74rem auto;
    z-index: 3000;
    color: #fff;
  }

  .weixin-tip-img {
    display: none;
    padding: 110px 0 0;
  }

  .weixin-tip-content {
    padding-left: 20px;
    text-align: center;
    margin-top: 20px;
    line-height: 0.22rem;
    font-size: 0.16rem;
  }
}
</style>
