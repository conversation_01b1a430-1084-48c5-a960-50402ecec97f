<template>
  <div class="main">
    <div class="textContent">
      <div v-if="enrollMsgList.length===0" class='textContent-empty'>
        <img src="../../assets/image/comment.png" alt="" class="textContent-empty-img">
        <p>暂无留言，点击下方发表想法吧</p>
      </div>
      <div class="userMessageContentList" :class="{anim:animatePraise==true}">
        <div class="content" v-for="(item,index) in enrollMsgList" :key="index">
          <div class="fl">
            <img :src="item.headImg|defaultAvatar2" alt>
          </div>
          <div class="fr">
            <p class="userName">{{item.realName|hideNickname}}</p>
            <div class="bottom" v-if="item.msgReply">
              <p class="uesrQuestion">{{item.msgContent}}</p>
              <div class="content">
                <div class="line"></div>
                <p class="answer">
                  <span>回复:&nbsp;</span>
                  {{item.msgReply}}
                </p>
              </div>
            </div>
            <div class="bottom2" v-if="!item.msgReply" style="margin-top:.16rem">
              <p class="uesrQuestion">{{item.msgContent}}</p>
            </div>
          </div>
          <div class="line"></div>
        </div>
      </div>
    </div>
    <div class="userMessageContent">
      <div class="fl">
        <img :src="userImg|defaultAvatar2" alt>
      </div>
      <div class="fr">
        <p class="userName">{{userName}}</p>
        <textarea maxlength="50" v-model.trim="message" placeholder="请留言..."></textarea>
        <span>{{message.length}}/50</span>
        <button v-if='btnColor' @click="enrollMsg()" :class="{'noBackImg':btnColor}"
                :style="{'backgroundColor':btnColor}">提交留言</button>
        <button class='after-btn' v-else @click="enrollMsg()"></button>
      </div>
    </div>
    <van-popup v-model="showPop">
      <div class="popupBox">
        <img src="../../assets/image/active/openUniversity/<EMAIL>" class="icon" alt>
        <p class="title">发送成功</p>
        <p class="text1">感谢你的提问, 我们会尽快解决你的疑惑！</p>
      </div>
    </van-popup>
  </div>
</template>

<script>

import { toLogin, filterEmoji } from "../../common";
import { Popup } from "vant";
export default {
  name: 'userMessageContent',
  props: [
    'scholarship', 'btnColor', 'topShow'
  ],

  data () {
    return {
      enrollMsgList: [],
      message: '',
      userImg: '',
      userName: '',
      animatePraise: false,
      isLogin: null,
      showPop: false,
      loadingFlag: true

    }
  },
  created () {
    console.log(this.btnColor);
    this.isLogin = !!this.storage.getItem("authToken");
    this.userName = this.storage.getItem("realName") || "";
    this.userImg = this.storage.getItem("headImg") || "";
    setInterval(this.scroll, 3000);
  },
  mounted: function () {
    if (this.topShow === '1') {
      // topshow 不需要滚动到某的地方才加载
      this.getEnrollMsgList();
    } else {
      window.addEventListener("scroll", this.handleScroll, true);
    }
  },
  methods: {
    // 获取留言列表
    getEnrollMsgList: function () {
      this.$http
        .post("/mkt/getEnrollMsgList/1.0/", { scholarship: this.scholarship })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.enrollMsgList = body;
          }
        });
    },
    // 评论
    enrollMsg: function () {
      if (!this.isLogin) {
        toLogin.call(this, null);
        return;
      }
      if (this.storage.getItem('relation') == '2' || this.storage.getItem('relation') == '6') {
        this.$modal({ message: "招生老师不可以评论哦！", icon: "warning" });
        return;
      }
      if (!this.message) {
        this.$modal({ message: "请输入评论内容", icon: "warning" });
        return;
      }

      this.$http
        .post("/mkt/enrollMsg/1.0/", {
          scholarship: this.scholarship,
          msgContent: filterEmoji(this.message)
        })
        .then(res => {
          let { code, body } = res;
          if (code === "00") {
            this.showPop = true;
            setTimeout(() => {
              this.showPop = false;
            }, 5000);
            this.message = "";
            this.getEnrollMsgList();
          }
        });
    },
    // 轮播用户评论
    scroll () {
      if (!this.enrollMsgList.length || this.enrollMsgList.length <= 3) {
        return;
      }
      this.animatePraise = true;
      setTimeout(() => {
        this.enrollMsgList.push(this.enrollMsgList[0]);
        this.enrollMsgList.shift();
        this.animatePraise = false;
      }, 500);
    },
    //监听滑动的距离
    handleScroll () {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (this.loadingFlag) {
        if (scrollTop > 330) {
          this.getEnrollMsgList();
          this.loadingFlag = false;
        }
      }
    },
  },
  beforeDestroy () {
    if (this.topShow !== '1') {

      window.removeEventListener("scroll", this.handleScroll, true);
    }
  }
}
</script>

<style lang="less" scoped>
.main /deep/ .van-popup {
  top: 45%;
  text-align: center;
  background-color: rgba(246, 246, 246, 0);
}
.textContent {
  overflow: hidden;
  height: 4.1rem;
  &-empty {
    padding-top: 1rem;
    text-align: center;
    &-img {
      width: 1.5rem;
      height: 1.5rem;
      margin-bottom: 0.2rem;
    }
  }
  .userMessageContentList {
    width: 3.75rem;
    &.anim {
      transition: all 1s;
      margin-top: -1.7rem;
    }
    .content {
      overflow: hidden;
      height: 1.33rem;
      position: relative;
      .line {
        position: absolute;
        bottom: 0.01rem;
        height: 1px;
        width: 2.76rem;
        left: 0.64rem;
        background-color: rgba(23, 6, 6, 0.08);
      }
      .fl {
        width: 0.64rem;
        img {
          width: 0.38rem;
          height: 0.38rem;
          float: right;
          border-radius: 50%;
          margin-top: 0.11rem;
        }
      }
      .fr {
        width: 3.11rem;
        height: 1.14rem;
        .userName {
          margin-left: 0.1rem;
          margin-top: 0.12rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.8);
        }
        .uesrQuestion {
          display: inline-block;
          width: 2.76rem;
          margin-top: 0.03rem;
          margin-left: 0.1rem;
          font-size: 0.14rem;
          color: rgba(23, 6, 6, 0.4);
        }
        .content {
          position: relative;
          margin-bottom: 0.09rem;
          height: auto;

          .line {
            position: absolute;
            display: inline-block;
            width: 0.02rem;
            height: 0.14rem;
            background: rgba(239, 186, 6, 1);
            top: 0.17rem;
            left: 0.01rem;
          }
          .answer {
            display: inline-block;
            background: rgba(239, 186, 6, 0.05);
            border-radius: 3px 3px 3px 0px;
            width: 2.86rem;
            font-size: 0.14rem;
            color: rgba(23, 6, 6, 0.8);
            padding: 0.13rem 0.07rem 0.13rem 0.1rem;
          }
        }
      }
    }
  }
}
.userMessageContent {
  width: 3.75rem;
  overflow: hidden;
  .fl {
    width: 0.64rem;
    img {
      width: 0.38rem;
      height: 0.38rem;
      float: right;
      border-radius: 50%;
      margin-top: 0.11rem;
    }
  }
  .fr {
    width: 3.11rem;
    position: relative;
    .userName {
      margin-left: 0.1rem;
      margin-top: 0.12rem;
      font-size: 0.14rem;
      color: rgba(23, 6, 6, 0.8);
      display: inline-block;
      width: 2.48rem;
      height: 0.24rem;
    }
    textarea {
      width: 2.87rem;
      height: 0.86rem;
      border-radius: 5px;
      padding: 0.13rem 0.1rem 0.13rem 0.1rem;
      margin-top: 0.03rem;
      border-color: rgba(239, 186, 6, 1);
    }
    span {
      position: absolute;
      font-size: 0.09rem;
      color: rgba(23, 6, 6, 0.4);
      top: 1.07rem;
      right: 0.305rem;
    }
    button {
      width: 1rem;
      height: 0.35rem;
      border: 0;
      margin-top: 0.1rem;
      margin-left: 0.89rem;
      margin-bottom: 0.2rem;
      /*background: linear-gradient(*/
      /*0deg,*/
      /*rgba(13, 139, 58, 1),*/
      /*rgba(46, 183, 95, 1)*/
      /*);*/
      background-color: #d7d7d8;
      /*background-image: linear-gradient(to top,#8E1613,#C91D1A);*/
      background: url('../../assets/image/active/gdLingNan/btn_msg.png') no-repeat;
      background-size: 100%;
      border-radius: 3px;
      color: #ffffff;
      font-size: 0.14rem;
      &.after-btn {
        background: url('../../assets/image/active/gdLingNan/btn_msg2.png') no-repeat;
        background-size: 100%;
        width: 1.35rem;
        height: 0.54rem;
        margin-left: 0.6rem;
      }
      &.noBackImg {
        background-image: none;
      }
    }
  }
}
.popupBox {
  width: 2.83rem;
  height: 1.54rem;
  background: #fff;
  .icon {
    height: 0.32rem;
    width: 0.32rem;
    margin-top: 0.3rem;
    border-radius: 50%;
  }
  .title {
    color: rgba(23, 6, 6, 1);
    font-size: 0.17rem;
    margin-top: 0.14rem;
  }
  .text1 {
    margin-top: 0.05rem;
  }
  .text1 {
    margin-top: 0.04rem;
    color: rgba(23, 6, 6, 0.4);
    font-size: 0.12rem;
  }
}
</style>
