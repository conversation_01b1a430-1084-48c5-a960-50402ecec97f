<template>

  <van-popup v-model="drawingShow" class="yz-van-popup" :close-on-click-overlay="false">
    <img src="../../../../assets/image/active/ckSprint/drawing.gif" alt="" class="drawing-icon">
    <img src="../../../../assets/image/active/ckSprint/drawing.png" alt="" class="drawing-title">
  </van-popup>
</template>

<script>


export default {
  components: {
  },
  data () {
    return {
      drawingShow: false
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {},
  watch: {
    value (val) {
      this.drawingShow = val;
    },
  },
  methods: {
    close () {
      this.drawingShow = false;
      this.$emit('input', false);
    },
  },
  created () {

  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '18', '抽签中');
    this.drawingShow = this.value
  },

}
</script>
<style lang='less' scoped>
.yz-van-popup {
  background-color: rgba(0, 0, 0, 0) !important;
  text-align: center;
  .drawing-title {
    margin-top: 0.1rem;
    width: 1.53rem;
    height: 0.6rem;
  }
  .drawing-icon {
    width: 3.5rem;
    height: 3.5rem;
  }
}
</style>