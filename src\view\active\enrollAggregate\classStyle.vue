<template>
  <div class="content">
    <div class="school" :style="{background:`url(http://yzims.oss-cn-shenzhen.aliyuncs.com/university/backgroundForH5/${unvsCode}.png)`,backgroundSize:'100%'}" >
      <div class="school-title">
        <div class="red-block"></div>报读院校
      </div>
      <div class="school-detail">
        <img :src="'http://yzims.oss-cn-shenzhen.aliyuncs.com/'+ 'university/logo/'+ unvsCode +'.png'" alt class="school-pic" />
        <div class="school-rg">
          <div class="school-name">{{schoolName}}</div>
          <div class="school-major">{{schoolMajor}}</div>
        </div>
      </div>
    </div>
    <div class="class-detail">
      <div class="title">
        <div class="red-block"></div>班型
      </div>
      <div class="class-style">
        <template v-for="item in scholarshipArr">
          <div class="everyClass" @click="checkClass(item.aliasName,item.feeAmount,item.scholarship)" :class="{'active':style==item.aliasName}">
            <img src="../../../assets/image/active/enrollAggregate/ok.png" alt v-if="style==item.aliasName" />{{item.aliasName}}
          </div>
        </template>
      </div>
      <div class="class-check" v-if="style.includes('无忧全程班')">
        <div class="title">
          <div class="red-block"></div>包含服务
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>院校提前面试或正常批面试的理论导入、材料辅导、面试辅导，以及笔试科目全程辅导</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>所有科目在线直播，回放课程循环听课学习</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>划分模块、详细解读全科知识点、提炼考试重难点</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>线上题库针对性训练、查缺补漏、提高正确率</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>助教老师全程答疑解惑，贴心服务贯穿全课程</div>
        </div>
      </div>
      <div class="class-check" v-if="style=='精品笔试班'">
        <div class="title">
          <div class="red-block"></div>包含服务
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>笔试科目英语二及管理类综合全程辅导</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>所有科目在线直播，回放课程循环听课学习</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>划分模块、详细解读全科知识点、提炼考试重难点</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>线上题库针对性训练、查缺补漏、提高正确率</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>助教老师全程答疑解惑，贴心服务贯穿全课程</div>
        </div>
      </div>
      <div class="class-check" v-if="style=='精品面试班'">
        <div class="title">
          <div class="red-block"></div>包含服务
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>院校提前面试或正常批面试的理论导入、材料辅导、面试辅导</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>所有科目在线直播，回放课程循环听课学习</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>划分模块、详细解读全科知识点、提炼考试重难点</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>线上题库针对性训练、查缺补漏、提高正确率</div>
        </div>
        <div class="cluase">
          <img src="../../../assets/image/active/enrollAggregate/prg-gift.png" alt />
          <div>助教老师全程答疑解惑，贴心服务贯穿全课程</div>
        </div>
      </div>
    </div>
    <div class="flow">
      <div class="title">
        <div class="red-block"></div>课程安排
      </div>
      <div class="step-box">
        <img v-if="style.includes('无忧全程班')" src="../../../assets/image/active/enrollAggregate/graduate/classIcon/bimian.png" alt="">
        <img v-if="style=='精品笔试班'" src="../../../assets/image/active/enrollAggregate/graduate/classIcon/bishi.png" alt="">
        <img v-if="style=='精品面试班'" src="../../../assets/image/active/enrollAggregate/graduate/classIcon/mianshi.png" alt="">
      </div>

    </div>
    <div class="bottomToast">
      <div class="toastTite">温馨提醒</div>
      <div class="tosat-detail">
        · 7天内可申请退费
        <br>
        · 面试辅导分提前批面试和正常批复试
         <br>
        · 所有课程在线直播，不限时无限次回放
      </div>
    </div>
    <div class="btn">
      <div class="text" @click="getCommitToken()">{{price}}元报读</div>
    </div>
  </div>
</template>
<script>
import { getIsAppOpen } from '@/common';
import { toPayWithApp } from '@/common/jump';

export default {
  data() {
    return {
      style: "",
      scholarshipArr:[],
      price:0,
      scholarship:'',
      schoolName:'',
      schoolMajor:'',
      unvsCode:'',
      isAppOpen: false,
    };
  },
  created() {
    this.schoolName = this.$route.query.unvsName || '';
    this.schoolMajor = this.$route.query.pfsnName || '';
    this.unvsCode = this.$route.query.unvsCode || '';
    this.getResearchScholarship();
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
  },
  methods: {
        // 获取zmtoken，防止重复提交
      getCommitToken: function () {
        this.$indicator.open();
        this.$http.post('/proxy/getCommitToken/1.0/', {itName: 'enroll'}).then(res => {
          if (res.code === '00') {
            this.zmtoken = res.body;
            this.submit();
          } else {
            this.$indicator.close();
          }
        }).catch(err => {
          this.$indicator.close();
        });
      },
      submit: function () {
        let datas = {
          scholarship: this.scholarship,
          name: this.$route.query.name || '',
          idCard: this.$route.query.idCard || '',
          recruitType: '5',
          grade: '2026研', // --changeEnroll--
          pfsnLevel: '6',
          unvsId: this.$route.query.unvsId,
          unvsName: this.$route.query.unvsName,
          pfsnId: this.$route.query.pfsnId,
          pfsnName: this.$route.query.pfsnName,
          pfsnCode:this.$route.query.pfsnCode,
          taId:'',
          taName:'',
          zmtoken: this.zmtoken
        };

        this.$http.post('/mkt/enroll/1.0/', datas).then(res => {
          this.$indicator.close();
          if (res.code !== '00') return;
          const learnId = res.body.learnId;
          this.storage.setItem('bindStudent', res.body.bindStudent);
          this.storage.setItem('relation', res.body.relation);
          this.learnId = learnId;
          if (learnId) {
            this.storage.setItem('learnId', learnId);
          }
          if (history.replaceState) {
            history.replaceState(null, null, `${window.location.origin}/student`);
          }
           this.$modal({message: '恭喜您，报读成功'});
           setTimeout(() => {
             toPayWithApp(this.isAppOpen, this.learnId);
           }, 1500);

        }).catch(err => {
          this.$indicator.close();
        });
      },


    getResearchScholarship() {
      this.$http.post('/bds/getResearchScholarship/1.0/',{pfsnId:this.$route.query.pfsnId}).then(res=>{
        let {code,body} = res;
        if(code !== '00') return;
        this.scholarshipArr = body || [];
        this.style  = this.scholarshipArr.length > 0 ? this.scholarshipArr[0].aliasName : ''
        this.scholarship  = this.scholarshipArr.length > 0 ? this.scholarshipArr[0].scholarship : ''
        this.price  = this.scholarshipArr.length > 0 ? this.scholarshipArr[0].feeAmount : ''
      })
    },
    checkClass(check,price,scholarship) {
      this.style = check;
      this.scholarship = scholarship;
      this.price = price;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  padding: 0.15rem;
  background: white;
  padding-bottom: 0.5rem;
   &::-webkit-scrollbar {
          opacity: 0;
        }
        &::-webkit-scrollbar-thumb {
          opacity: 0;
        }
  .red-block {
    width: 0.04rem;
    height: 0.14rem;
    background: #f06e6c;
    display: inline-block;
    margin-right: 0.1rem;
  }
  .school {
    width: 3.45rem;
    background-size: 100% 100%;
    height: 1.14rem;
    margin-bottom: 0.15rem;
    border-radius: 10px;
    box-shadow: 0px 0px 0.1rem 0px rgba(23, 6, 6, 0.2);
    .school-title {
      padding-top: 0.1rem;
      font-size: 0.17rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #170606;
    }
    .school-detail {
      margin-top: 0.15rem;
      padding-left: 0.1rem;
      display: flex;
      .school-pic {
        width: 0.5rem;
        height: 0.5rem;
        display: block;
        margin-right: 0.05rem;
      }
      .school-name {
        font-size: 0.17rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #170606;
      }
      .school-major {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #170606;
      }
    }
  }
  .title {
    font-size: 0.17rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #170606;
  }
  .class-detail {
    box-shadow: 0px 0px 0.1rem 0px rgba(23, 6, 6, 0.2);
    border-radius: 0.1rem;
    width: 100%;
    padding-top: 0.1rem;
    margin-bottom: 0.15rem;
    .class-style {
      display: flex;
      width: 3.25rem;
      margin: 0.15rem auto;
      height: 0.5rem;
      border-bottom: 0.01rem solid #edebeb;
    }
    .everyClass {
      min-width: 0.71rem;
      background: #edebeb;
      border-radius: 0.18rem;
      padding: 0 0.1rem;
      height: 0.35rem;
      line-height: 0.35rem;
      font-size: 0.14rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #170606;
      text-align: center;
      img {
        width: 0.16rem;
        height: 0.16rem;
        margin-top: 0.1rem;
        margin-right: 0.02rem;
      }
      margin-right: 0.1rem;
    }
    .class-check {
      width: 100%;
      padding-bottom: 0.15rem;
      .cluase {
        display: flex;
        padding-left: 0.13rem;
        margin-top: .15rem;
        img {
          width: 0.12rem;
          height: 0.14rem;
          margin-right: 0.08rem;
          display: block;
          margin-top: 0.03rem;
        }
        div {
          font-size: 0.14rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #170606;
          padding-right: 0.2rem;
        }
      }
    }
  }
  .flow {
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 0px 0.1rem 0px rgba(23, 6, 6, 0.2);
    border-radius: 0.1rem;
    padding: 0.1rem 0;
    .step-box {
      padding: 0 .1rem;
      margin-top: .1rem;
      img {
        width: 100%;
      }
    }

  }
  .bottomToast {
    width: 100%;
    margin-top: 0.3rem;
    margin-bottom: .3rem;
    .toastTite {
      font-size: 0.15rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #170606;
      margin-bottom: 0.1rem;
    }
    .tosat-detail {
      font-size: 0.14rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #170606;
    }
  }
  .btn {
    width: 100%;
    height: 0.5rem;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0rem;
    background: white;
    z-index: 999;
    .text {
      width: 3.55rem;
      height: 0.4rem;
      margin: 0 auto;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      border-radius: 0.2rem;
      text-align: center;
      line-height: 0.4rem;
      font-size: 0.15rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
    }
  }
  .active {
    background: #fef4f4 !important;
    color: #f06e6c !important;
    font-weight: 600 !important;
    border: 0.01rem solid #f06e6c;
  }
}
</style>
