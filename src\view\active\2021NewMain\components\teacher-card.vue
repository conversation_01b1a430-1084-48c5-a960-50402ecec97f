<template>
  <div v-if='show'>
    <div class="yz-teacher-card animated fade" v-show='showInfo' @click='close'>
      <!-- 老师信息 -->
      <div class="card-box">
        <p class="name">{{info.cardNickname}}</p>
        <p class="t1">{{info.company}}</p>
        <p>{{info.position}}</p>
        <div class="mobile" @click.stop>
          <span>{{info.cardMobile}}</span>
          <a class="call" :href="'tel: '+info.cardMobile">拨打电话</a>
        </div>
        <p class="t2">{{info.motto}}</p>
        <div class="code-box">
          <img class="code" :src="info.cardWechatQrcode | imgBaseURL" alt="">
          <p>长按识别二维码</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    value: {
      type: <PERSON><PERSON><PERSON>,
      defaule: false,
    },
    bottom: {
      type: String,
      default: '0.6rem',
    },
    inviteId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      show: false,
      showInfo: false,
      info: {},
    }
  },
  watch: {
    value(val) {
      this.show = val;
      if (val) {
        this.getSpreadCard();
      }
    },
  },
  mounted() {
    this.show = this.value;
    this.getSpreadCard();
  },
  beforeDestroy() {
    this.showInfo = false;
  },
  methods: {
    close() {
      this.$emit('close');
      this.$emit('input', false);
    },
    // 老师卡片
    async getSpreadCard() {
      if (!this.inviteId) {
        return;
      }
      const { code, body } = await this.$http.post('/mkt/getSpreadCardByInviteId/1.0/', { qType: 'findTeacher', inviteId: this.inviteId });
      if (code == '00') {
        if (body) {
          this.showInfo = true;
          this.info = body;
        } else {
          this.$emit('noTeacher');
        }
      }
    },

  },
};
</script>

<style lang="less">
  .yz-teacher-card{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0.6rem;
    z-index: 11;
    background: rgba(0, 0, 0, 0.7);
    .card-box{
      position: absolute;
      bottom: 0.05rem;
      left: 0.15rem;
      right: 0.15rem;
      border-radius: 0.05rem;
      background: #fff;
      box-shadow: 0px 0px 10px 0px rgba(23, 6, 6, 0.15);
      padding: 0.15rem;
      font-size: 0.12rem;
      color: rgba(23, 6, 7, 0.8);
      .name{
        color: #170606;
        font-weight: 600;
        font-size: 0.18rem;
        margin-bottom: 0.1rem;
      }
      .t1{
        margin-bottom: 0.05rem;
      }
      .t2{
        margin-top: 0.01rem;
        width: 1.86rem;
      }
      .mobile{
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 1.86rem;
        margin-top: 0.02rem;
      }
      .call{
        width: 0.64rem;
        background: linear-gradient(180deg, #DB3135 0%, #A50609 100%);
        color: #fff;
        border-radius: 20px;
        padding: 0.02rem 0;
        line-height: normal;
        text-align: center;
      }
    }
    .code-box{
      position: absolute;
      bottom: 0.15rem;
      right: 0;
      width: 1.23rem;
      text-align: center;
      .code{
        width: 0.8rem;
        height: 0.8rem;
        margin-bottom: 0.1rem;
      }
      &::before{
        content: '';
        position:absolute;
        top: 0.09rem;
        bottom: 0.17rem;
        left: 0;
        width: 1px;
        background-color: #EDEBEB;
        opacity: 0.8;

      }
    }
  }
</style>
