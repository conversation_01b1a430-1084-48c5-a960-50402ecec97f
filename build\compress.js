const path = require("path");
const fs = require("fs");
const compressing = require("compressing");
const ora = require("ora");
const chalk = require("chalk");

function resolve(dir) {
  return path.resolve(__dirname, "..", dir);
}

const currentDate = new Date();
const month = String(currentDate.getMonth() + 1).padStart(2, "0"); // 当前月份，补零
const day = String(currentDate.getDate()).padStart(2, "0"); // 当前日期，补零

const projectName = "zmc";
const zipFileName = `${projectName}-${month}.${day}.zip`; // 压缩包文件名
const sourcePath = resolve("dist");
const renamedSourcePath = resolve(projectName); // 重命名后的源文件夹路径
const targetPath = resolve(zipFileName); // 压缩文件路径

// 检查 dist 文件 是否存在
if (!fs.existsSync(sourcePath)) {
  console.log(`${chalk.bgRed(" ERROR ")} 找不到 dist 文件，请先执行打包命令\n`);
  process.exit(0);
}

// 检查是否存在同名的压缩包文件,存在同名压缩包文件，删除旧的压缩包
if (fs.existsSync(targetPath)) {
  console.log(
    `${chalk.bgYellow.black(
      " WARN "
    )}  检查到 ${zipFileName} 文件已存在，已为你删除并重新压缩，请耐心等待... \n`
  );
  fs.unlinkSync(targetPath);
}

// 重命名源文件夹
fs.renameSync(sourcePath, renamedSourcePath);

const loading = ora(`Compress dist file...`).start();

compressing.zip
  .compressDir(renamedSourcePath, targetPath)
  .then(() => {
    // 恢复源文件夹名称
    fs.renameSync(renamedSourcePath, sourcePath);
    // 停止
    loading.stop();

    console.log(
      `${chalk.bgGreen.black(
        " DONE "
      )} Compress complete at ${chalk.cyan.underline(targetPath)}\n`
    );
  })
  .catch(() => {
    // 停止
    loading.stop();
    // 恢复源文件夹名称
    fs.renameSync(renamedSourcePath, sourcePath);
  });
