<template>
  <div class="yz-618-swiper">
    <div v-if='showBlack' class="black animated fadeIn" :class='{fadeOut: fadeOut}'></div>
    <div v-if='itemShow' class="swiper-item cl" :style='{left: itemLeft + "px"}'>
      <div class="img-box">
        <img src="../../../../assets/image/active/618Activity/l2.png" class="r-light" alt="">
        <img v-if='item.prizeId == 1' src="../../../../assets/image/active/618Activity/g1.png" class="img1 g1" alt="">
        <img v-if='item.prizeId == 2' src="../../../../assets/image/active/618Activity/g2.png" class="img1" alt="">
        <img v-if='item.prizeId == 3' src="../../../../assets/image/active/618Activity/g4.png" class="img1 g4" alt="">
        <img v-if='item.prizeId == 4' src="../../../../assets/image/active/618Activity/g7.png" class="img1 g7" alt="">
      </div>
      <div class="yellow-bg">
        <span>恭喜</span>
        <b>{{ item.userName | hideNickname }}</b>
        <span>抽中了</span>
        <b class="red">{{ item.prizeVO.prizeName }}</b>
      </div>

      <img src="../../../../assets/image/active/618Activity/x1.png" class="img2" alt="">
    </div>
  </div>
</template>

<script>
import { Swipe, SwipeItem } from 'vant';
import 'animate.css';

export default {
  components: { Swipe, SwipeItem },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      item: {
        prizeVO: {},
      },
      showIndex: 0,
      fadeOut: false,
      showBlack: false,
      itemLeft: 999,
      itemTimer: null,
      itemShow: true,
    };
  },
  computed: {
    windowWidth() {
      return window.innerWidth;
    },
  },
  watch: {
    list() {
      if (this.list.length > 0) {
        this.item = this.list[this.showIndex];
      }
    },
  },
  mounted() {
    this.itemLeft = this.windowWidth;
    if (this.list.length > 0) {
      this.item = this.list[this.showIndex];
    }
    setTimeout(() => {
      this.start();
    }, 3000);
  },
  methods: {
    // 奖品移入移出
    moveItem(type = 'in') { // in 移入， out：移出
      this.itemLeft = type == 'in' ? 0 : -this.windowWidth;
    },
    // 背景淡入淡出
    blackFadeOut() {
      this.fadeOut = true;
      setTimeout(() => {
        this.showBlack = false;
        setTimeout(() => {
          this.reset();
        }, 100);
      }, 300);
    },
    start() {
      if (this.list.length == 0) {
        return;
      }
      this.showBlack = true;
      this.moveItem();
      setTimeout(() => {
        this.end();
      }, 4000);
    },
    end() {
      this.moveItem('out');
      setTimeout(() => {
        this.itemShow = false;
        this.blackFadeOut();
      }, 200);
    },
    reset() {
      setTimeout(() => {
        this.itemShow = true;
      }, 200);
      this.itemLeft = this.windowWidth;
      this.fadeOut = false;
      this.showIndex = (this.showIndex + 1) >= this.list.length ? 0 : (this.showIndex + 1);
      this.item = this.list[this.showIndex];
      setTimeout(() => {
        this.start();
      }, 3000);
    },
  },
};
</script>

<style lang="less">
  .yz-618-swiper{
    position: absolute;
    left: 0;
    right: 0;
    top: 0.74rem;
    height: 0.92rem;
    z-index: 2;
    .animated{
      animation-duration: 0.5s;
    }
    .black{
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
    }
    .swiper-item{
      position: absolute;
      left: 999px;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
      transition: left 0.5s;
      .img-box{
        width: 0.92rem;
        height: 0.92rem;
        text-align: center;
        position: relative;
        margin-left: 0.1rem;
        z-index: 2;
        float: left;
      }
      .r-light{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        animation: rotate 6s linear infinite;
      }
      .img1{
        margin-top: 0.1rem;
        position: relative;
        z-index: 2;
        height: 0.72rem;
        vertical-align: middle;
        &.g1{
          height: 0.82rem;
          margin-top: 0.05rem;
        }
        &.g4{
          height: 0.52rem;
          margin-top: 0.2rem;
        }
        &.g7{
          height: 0.62rem;
          margin-top: 0.15rem;
        }
      }
      .yellow-bg{
        background: linear-gradient(90deg, #FBDF3D 0%, #FFE992 100%);
        height: 0.32rem;
        line-height: 0.32rem;
        font-size: 0.14rem;
        border-radius: 0 100px 100px 0;
        margin-top: 0.3rem;
        padding-left: 0.27rem;
        float: left;
        width: 2.66rem;
        margin-left: -0.35rem;
        span,b{
          float: left;
          line-height: 0.32rem;
        }
        b{
          color: #F54212;
          padding: 0 0.05rem;
        }
        .red{
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          width: 1.2rem;
          padding-right: 0;
        }
      }
      .img2{
        margin-left: 0.05rem;
        margin-top: 0.36rem;
        float: left;
        width: 0.24rem;
        height: 0.19rem;
      }
    }
  }
  @keyframes rotate{
    from{
      transform: rotate(0deg);
    }
    to{
      transform: rotate(360deg);
    }
  }
</style>
