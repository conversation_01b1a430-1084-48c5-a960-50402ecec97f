// 数据访问统计混入
// import { isAndroid } from '@/common';

export default {
    methods: {
        /**
         * 
         * @param {*} targetType 事件类型
         * @param {*} targetId  描述id
         * @param {*} targetReamrk  参数描述
         * @param {*} udata 预留参数
         */
        statistic (targetType,targetId,targetReamrk,udata) {
             let phone={}
             phone.deviceId=this.storage.getItem('phoneID');
             phone.targetType=targetType;
             phone.version='1.0';
             phone.platform='WECHAT'; 
             phone.url=window.location.href;
             phone.targetId=targetId;
             phone.targetReamrk=targetReamrk;
             phone.udata=udata;
             
             return new Promise((resolve, reject) => {
                this.$http.post('/us/operationStatistic/1.0/',{
                    platform: phone.platform,
                    deviceId:phone.deviceId,
                    version:phone.version,
                    url:phone.url,
                    targetType:phone.targetType,
                    targetId:phone.targetId,
                    targetReamrk:phone.targetReamrk
                }).then(res=>{
                    let { code, body } = res;
                    if (code == "00") {
                        resolve(body);
                        return;
                    } 
                    reject(false);
                }).catch(() => {
                    reject(false);
                })
             });
               //打印系统版本和手机型号
               // console
           
           }
    },
};