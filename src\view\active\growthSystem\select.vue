<template>
  <div class="yz-growth-select">
    <div class="yz-select-head">
      <div class="left">
        <img src="../../../assets/image/active/growthSystem/man.png" alt="">
        <div class="content">
          <p class="p1">上进青年</p>
          <p class="p2">坚持上进，某天你会发现，原来<br />上进已在你的心中生根发芽。</p>
        </div>
      </div>
      <div class="right">
        <span class="num">{{num}}</span>
        <span class="t1">件</span>
      </div>
      <div class="text3">已完成</div>
    </div>
    <div class="select-tips" :class='[tipColor]'>{{tipsText}}</div>

    <div class="select-bottom">
      <div class="star-box" v-show='num == 0'>
        <img src="../../../assets/image/active/growthSystem/star.gif" alt="" class="star">
      </div>
      <!-- <p class="p1">试试左右滑动</p> -->
      <p v-show='num == 0'><span class="p1" >试试左右滑动，</span>点击选择你做过的上进事情</p>
      <div class="btn-box" v-show='num > 0'>
        <img src="../../../assets/image/active/growthSystem/select-btn.png" class="btn" alt="" @click='finish'>
      </div>
    </div>

    <div class="wapper" id='wapper' @scroll="scroll">
      <div class="wapper2" id='wapper2'>
        <div class="select-container" id="container" :style='{height: start ? wapperHeight : "100%"}' @touchstart='touchStart' @touchend='touchEnd'>
          <div class="line-1">
            <label class="check-label" :for='"label-" + item' v-for="(item, index) in line1Index" :key="index">
              <input
                type="checkbox"
                name="check"
                class="checkbox"
                :id='"label-" + item' :data-index='item'
                @change.prevent='checkChange'>
              <div class="img-box">
                <img src="../../../assets/image/active/growthSystem/check.png" class="check" alt="">
                <img :src="item | getImgByIndex" class="img" alt="">
              </div>
              <p>{{item}}、{{textList[item - 1]}}</p>
            </label>
          </div>
          <div class="line-2">
            <label class="check-label" :for='"label-" + item' v-for="(item, index) in line2Index" :key="index">
              <input
                type="checkbox"
                name="check"
                class="checkbox"
                :id='"label-" + item' :data-index='item'
                @change.prevent='checkChange'>
              <div class="img-box">
                <img src="../../../assets/image/active/growthSystem/check.png" class="check" alt="">
                <img :src="item | getImgByIndex" class="img" alt="">
              </div>
              <p>{{item}}、{{textList[item - 1]}}</p>
            </label>
          </div>
          <div class="line-3">
            <label class="check-label" :for='"label-" + index' v-for="(item, index) in line3Index" :key="index">
              <input
                type="checkbox"
                name="check"
                class="checkbox"
                :id='"label-" + item' :data-index='item'
                @change.prevent='checkChange'>
              <div class="img-box">
                <img src="../../../assets/image/active/growthSystem/check.png" class="check" alt="">
                <img :src="item | getImgByIndex" class="img" alt="">
              </div>
              <p>{{item}}、{{textList[item - 1]}}</p>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

// 获取下标
const fun = (start) => {
  const arr = [];
  for (let i = start; i <= 47; i+=3) {
    arr.push(i);
  }
  return arr;
}

export default {
  props: {
    start: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      num: 0,
      tipsText: '学习力爆棚',
      line1Index: fun(1),
      line2Index: fun(2),
      line3Index: fun(3),
      conStyle: {},
      interval: null,
      startX: 0,
      listIndex: [],
      isBack: false,
      wapperHeight: 'calc(100vh - 1.6rem)',
      oldScrollLeft: 0,
      isTouchStart: false,
      tipColor: '',
      textList: [
        '坚持每个月至少读1本书',
        '坚持每天学10个英语单词',
        '考取过至少一项证书',
        '参加过外语等级考试',
        '坚持每个月记录读书笔记',
        '除英语，自学了一门外语',
        '学习一种新技能如ps等',
        '学会了一种乐器',
        '和孩子共读至少50本书',
        '坚持每个月记账',
        '坚持每周至少5天跑步',
        '定期阅读爱国主义书籍',
        '学习一项新的运动',
        '公共场合发表过一次演讲',
        '拿过至少1次奖学金',
        '参加过单位或更高规模的比赛并获奖',
        '在工作中荣获过单位荣誉',
        '主动投入个人时间为团队赢得荣誉',
        '升职加薪后第一时间为家人购买礼物',
        '主动报名岗位职责之外的项目并得到认可',
        '支教山区',
        '至少无偿献血1次',
        '在公众场合，勇敢地抵制不良行为',
        '成功挑战过自己害怕的极限运动',
        '因为业余爱好结交了新朋友',
        '参加了青年志愿者组织',
        '参与过社区爱心活动',
        '每天8点前起床，晚上12点前入睡',
        '定期存钱，从不月光',
        '不抽烟，不喝酒',
        '坚持每周至少下厨2天',
        '购买的课程都按时上完',
        '定期整理自己的房间、工作区域等',
        '每年至少带家人外出旅游1次',
        '每年对父母和家人说一次我爱你',
        '每年带父母家人体检1次',
        '节假为父母做一顿拿手好菜',
        '每年拍一张全家福',
        '一年回老家至少一趟，看望长辈',
        '和家人一起周末定期体验户外运动',
        '制定并执行健身计划，成功减重',
        '会为自己做1年、3年、5年规划',
        '观看过《八佰》或其他爱国主义题材电影',
        '聚餐践行光盘行动，不浪费食物',
        '领养过动物',
        '捐赠过衣物、金钱给需要帮助的人',
        '和家人参观过爱国主义教育基地',
      ],
    };
  },
  filters: {
    getImgByIndex(val) {
      return require(`../../../assets/image/active/growthSystem/c-icon/${val}.png`);
    },
  },
  watch: {
    start(bool) {
      if (bool) {
        this.animate2();
      } else {
        clearInterval(this.interval);
        const dom = document.getElementById('wapper');
        dom.scrollLeft = 0;
      }
    },
  },
  mounted() {
  },
  methods: {
    animate2() {
      const dom = document.getElementById('wapper');
      const wWidth = window.innerWidth;
      const domWidth = document.getElementById('container').offsetWidth;
      clearInterval(this.interval);
      this.interval = null;
      this.interval = setInterval(() => {
        dom.scrollLeft += 1;
        if ((dom.scrollLeft + wWidth) >= domWidth) {
          this.isBack = true;
          clearInterval(this.interval);
          this.animateBack();
        }
      }, 26);
    },
    animateBack() {
      const dom = document.getElementById('wapper');
      clearInterval(this.interval);
      this.interval = null;
      this.interval = setInterval(() => {
        dom.scrollLeft -= 1;
        if (dom.scrollLeft == 0) {
          this.isBack = false;
          clearInterval(this.interval);
          this.animate2();
        }
      }, 26);
    },

    scroll(e) {
      const scrollLeft = e ? e.target.scrollLeft : document.getElementById('wapper').scrollLeft;
      this.oldScrollLeft = scrollLeft;
      if (scrollLeft < 1000) {
        this.tipsText = '学习力爆棚';
        this.tipColor = 'red';
      } else if (scrollLeft >= 900 && scrollLeft < 2100) {
        this.tipsText = '影响力满分';
        this.tipColor = 'yellow';
      } else if (scrollLeft >= 2100) {
        this.tipsText = '上进习惯养成';
        this.tipColor = 'blue';
      }
      setTimeout(() => {
        if (scrollLeft == this.oldScrollLeft && !this.isTouchStart) {
          this.continue();
        }
      }, 260);

    },

    checkChange() {
      const allCheckbox = document.getElementById('wapper2').querySelectorAll('.checkbox');
      const arr = [];
      for (let i = 0; i < allCheckbox.length; i++) {
        const item = allCheckbox[i];
        if (item.checked) {
          arr.push(Number(item.getAttribute('data-index')));
        }
      }
      this.num = arr.length;
      this.listIndex = arr.sort();
    },
    finish() {
      this.$emit('finish', { index: 1, selects: this.listIndex });
    },
    touchStart(e) {
      this.isTouchStart = true;
      clearInterval(this.interval);
    },
    touchEnd(e) {
      this.isTouchStart = false;
      this.scroll();
    },
    continue() {
      if (!this.isBack) {
        this.animate2();
      } else {
        this.animateBack();
      }
    },
  },
};
</script>

<style lang="less">
  .yz-growth-select{
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    height: 100vh;
    overflow: hidden;
    width: 100%;
    background: #fff;
    .red{
      background: #FF6A5F;
    }
    .yellow{
      background: #FFAE18;
    }
    .blue{
      background: #72A8F2;
    }
    .wapper{
      width: 100%;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      height: 100%;
      padding-top: 0.2rem;
      transition: all 0.1s;
      transform: translate3d(0,0,0);
    }
    .wapper2{
      width: 42rem;
      display: flex;
      height: 100%;
    }
    .select-container{
      // height: calc(100vh - 0.7rem);
      // padding-top: 0.2rem;
      // height: calc(100vh - 0.7rem);
      height: 100%;
      transform: translateX(0);
      // transition: transform 2s;
      .line-1{
        padding-left: 0.98rem;
        .check-label{
          margin-top: 0.4rem;
        }
      }
      .line-2{
        padding-left: 1.66rem;
      }
      .line-3{
        padding-left: 2.24rem;
      }
      .check-label{
        display: inline-block;
        position: relative;
        text-align: center;
        font-size: 0.14rem;
        color: #000;
        margin-right: 0.5rem;
        margin-top: 0.25rem;
        & > p{
          font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif;
        }
      }
      .checkbox{
        position: absolute;
        opacity: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
      }
      .img-box{
        position: relative;
        width: 0.8rem;
        height: 0.8rem;
        margin: 0 auto 0.05rem;
        .img{
          width: 100%;
          height: 100%;
        }
        .check{
          position: absolute;
          display: none;
          width: 0.32rem;
          right: -0.09rem;
          top: -0.07rem;
        }
      }
      .checkbox:checked + .img-box > .check{
        display: block;
      }
    }
  }
  .yz-select-head{
    background: #FF6A5F;
    border: 1px solid #000000;
    height: 0.7rem;
    box-sizing: content-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    .left{
      min-width: 70%;
      img{
        float: left;
        width: 0.41rem;
        height: 0.55rem;
        margin-left: 0.17rem;
      }
      .content{
        margin-left: 0.58rem;
        padding-left: 0.13rem;
        font-size: 0.14rem;
        .p1{
          padding-top: 0.01rem;
          margin-bottom: 0.05rem;
        }
        .p2{
          font-size: 0.1rem;
          line-height: 0.12rem;
          font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif;
        }
      }
    }
    .right{
      background: #fff;
      width: 0.61rem;
      min-height: 0.53rem;
      border: 0.01rem solid #000000;
      color: #000000;
      font-size: 0.12rem;
      text-align: center;
      margin-right: 0.28rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .num{
        font-size: 0.28rem;
      }
      .t1{
        padding-top: 0.08rem;
        padding-left: 0.02rem;
      }
    }
    .text3{
      position: absolute;
      width: 20px;
      height: 100%;
      background: #000;
      color: #fff;
      font-size: 14px;
      text-align: center;
      padding-top: 1px;
      padding-left: 1px;
      right: 0;
      top: 0;
    }
  }
  .select-tips{
    background: #FF6A5F;
    font-size: 0.18rem;
    padding: 0.05rem 0.11rem;
    line-height: 1;
    position: absolute;
    left: 0.23rem;
    top: 0.9rem;
    text-align: center;
    transition: all 0.2s;
  }
  .select-bottom{
    position: absolute;
    bottom: 0.15rem;
    left: 0;
    // transform: translateX(-50%);
    width: 100%;
    color: #666;
    font-size: 0.14rem;
    text-align: center;
    font-family: TrebuchetMS, Rotobo, PingFangSC-Regular, PingFang SC, "Microsoft YaHei", sans-serif;
    line-height: 1;
    z-index: 3;
    .star-box{
      img{
        width: 35px;
        height: 35px;
      }
    }
    .btn-box{
      margin-top: 0.07rem;
      margin-bottom: 0.05rem;
      img{
        width: 1rem;
        height: 0.42rem;
      }
    }
  }
</style>
