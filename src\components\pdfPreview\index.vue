<template>
  <div>
    <div v-if="pdfType === 'canvas' || (pdfType === 'img' && !imgUrl.length)" :style="{ opacity:  pdfType === 'img' ? 0 :  1}" class="yz-schooldata-pdf" id='student-book' ref='student-book'>

    </div>
    <div v-if="pdfType === 'img' && imgUrl.length">
      <img v-for="(item,index) in imgUrl" :key="index" :src="item" alt="" crossorigin="anonymous" style="width:100%;user-select: none;"/>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
// import Pdfjs from '../../../../static/pdf/build/pdf';
const Pdfjs = require('../../../static/pdf/build/pdf')

export default {
  props: {
    fileUrl: {
      type: String,
      default: ''
    },
    pdfType: {
      type: String,
      default: 'canvas'
    },
    scale: {
      type: String,
      default: '3'
    }
  },
  data() {
    return {
      imgUrl: []
    }
  },
  watch: {
    fileUrl(val) {
      if (val) {
        this.getPdf();
      }
    },
  },
  mounted() {
    if (this.fileUrl) {
      this.getPdf();
    }
  },
  methods: {
    getPdf() {
      Toast.loading({
        message: '加载中...',
        zIndex: 9999,
        duration: 0,
      });
      Pdfjs.PDFJS.workerSrc = '/static/pdf/build/pdf.worker.js';
      Pdfjs.PDFJS.cMapUrl = '/static/pdf/web/cmaps/';
      Pdfjs.PDFJS.cMapPacked = true;
      const container = this.$refs['student-book'];
      const loadingTask = Pdfjs.getDocument(this.fileUrl);
      loadingTask.promise.then(pdf => {
        const numPages = pdf.numPages;
        this.$emit('startInterval');
        Toast.clear();
        for (let i = 1; i <= numPages; i++) {
          pdf.getPage(i).then(async (page) => {
            const scale = (container.offsetWidth/page.view[2]);
            const viewport = page.getViewport(scale * this.scale);
            const canvas = document.createElement("canvas");
            const radio = window.devicePixelRatio || 1;

            canvas.width= viewport.width;
            canvas.height= viewport.height;
            canvas.style.width = `${viewport.width / this.scale}px`;
            canvas.style.height = `${viewport.height / this.scale}px`;
            const ctx = canvas.getContext('2d');
            container.appendChild(canvas);
            const renderContext = {
              canvasContext: ctx,
              // transform: [1, 0, 0, 1, 0, 0],
              viewport: viewport,
              // intent: 'print'
            };
            await page.render(renderContext).promise;

            if (this.pdfType === 'img') {
              // 将 canvas 转换为图片 URL
              const imageUrl = canvas.toDataURL('image/png');
              this.imgUrl.push(imageUrl);
            }
          });
        }
        if (this.pdfType === 'img') {
          this.$emit('imgList', this.imgUrl);
        }
      });
    },
  },
};
</script>

<style lang="less">
  .yz-schooldata-pdf{

  }
</style>
