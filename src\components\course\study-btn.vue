<template>
  <wx-open-launch-app
    class="launch-btn"
    id="launch-btn"
    @error="callAppError"
    :appid="OpenAppId"
    extinfo="extinfo"
  >
    <script type="text/wxtag-template">
      <style>
        .btn{
          width: 100px;
          height: 30px;
          margin-bottom: 0;
          line-height: 30px;
          text-align: center;
          border-radius: 15px;
          background: linear-gradient(
            135deg,
            rgba(240, 145, 144, 1) 0%,
            rgba(240, 120, 119, 1) 66%,
            rgba(240, 110, 108, 1) 100%
          );
          font-size: 14px;
          border: 0;
          font-weight: 600;
          color: #fff;
        }
      </style>
      <button class='btn'>去APP上课</button>
    </script>
  </wx-open-launch-app>
</template>

<script>
import openApp from '@/mixins/openApp';
export default {
  mixins: [openApp],
  mounted() {
    this.wxCallAppInit(); // 微信标签唤起app
  },
}
</script>

<style lang="less" scoped>
  .launch-btn {
    float: right;
    margin-top: 10px;
    width: 100px;
    height: 30px;
    margin-right: 10px;
    margin-bottom: 0;
  }
</style>
