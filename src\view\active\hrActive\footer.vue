<template>
<div>
  <div class="footerbar nav-bar" :class="{active:isIphoneX}">
    <div class="wrap" :class="{active:isIphoneX}">
      <div class="inner plr">
        <a class="item phone" @click="tel"  >
          <i class="icons i-integral"></i>
          <span class="label">咨询老师</span>
        </a>
        <button class="btn" @click='btnEvent'>
          {{ btnText }}
        </button>
      </div>
    </div>

  </div>
  <div class="iphonex_bottom" v-if="isIphoneX"></div>
</div>
</template>
<script>
  import { toLogin, isIphoneX } from "@/common";

  export default {
    props: {
      btnEvent: {
        type: Function,
        default: () => {},
      },
      btnText: {
        type: String,
        default: '¥299立即报名',
      },
    },
    data() {
      return {
        isIphoneX: isIphoneX(),
      }
    },
    created(){
    },
    watch:{},
    methods:{
      tel() {
        if (this.storage.getItem('cardMobile')) {
          window.location.href = "tel:" + this.storage.getItem('cardMobile');
          return;
        }
        window.location.href = "tel:4008336013";
      },

    }
  }
</script>
<style lang="less" scoped>
  .footerbar{
    height: .6rem;
    position: fixed;
    bottom:0;
    background-color: #fff;
    width: 100%;
    max-width: 640px;
    z-index: 3;
    &.active{
      bottom:34px;
    }
    .wrap{
      width: 100%;
      height: 100%;
      .inner{
        display: flex;
        height: 100%;
      }
      .phone{
        width: 0.94rem;
        padding-top: .1rem;
        text-align:center;
        border-top: 1px solid #DB3135;
        height:100%;
      }
      .btn{
        background: linear-gradient(180deg, #DB3135, #A50609);
        box-shadow: 0px 2px 2px 0px rgba(166, 19, 8, 0.4);
        flex: 1;
        color: #fff;
        font-size: 0.18rem;
        text-align: center;
        font-weight: 600;
        height:100%;
      }

      .label{ display:block; padding-top:.03rem; line-height:1; font-size:.12rem;color: #A6080B;font-weight: 600 }

      .icons{ width:.25rem; height:.25rem; border-radius: 50%;overflow: hidden}
      .icons>img{width: 100%;height: 100%}
      .i-home{ background-image:url(../../../assets/image/active/enrollAggregate/ic-free-enroll.png); }
      .i-integral{ background-image:url(../../../assets/image/active/enrollAggregate/ic-concact.png); }
    }
  }
  .iphonex_bottom{
    position: fixed;
    width: 100%;
    height: 34px;
    background-color: white;
    bottom:0;
    left:0;
    z-index: 3;
  }
</style>
