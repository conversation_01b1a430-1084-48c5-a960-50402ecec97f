<template>
  <div class="main">
    <div class="myHour" @click="$router.push({name:'coupon'})"></div>
    <div class="title">广州开放大学奖学金</div>
    <div class="taken">
      <commitment :isReceive="isReceive" />
    </div>
  </div>
</template>
<script>
import commitment from './components/commitment'
export default {
  components:{
    commitment
  },
  data(){
    return {
      isReceive:false
    }
  },
  mounted(){
    this.getVoucherStatus()
  },
  methods:{
     //获取是否领取优惠劵状态
    getVoucherStatus() {
      if (!this.storage.getItem("authToken")) {
        return;
      }

      let params = {
        couponType: '06_gk'
      }

      this.$http.post("/mkt/isGiveCouponByType/1.0/", params).then((res) => {
        if ( res.code == "00" ) {
          res.body == true
          ? this.isReceive = true
          : this.isReceive = false;
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>
.main {
  padding-top: 0.65rem;
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;
  .title{
    height: 0.25rem;
    font-size: 0.18rem;
    font-family: Alibaba-PuHuiTi-B, Alibaba-PuHuiTi;
    font-weight: bold;
    color: #C13935;
    text-align: center;

  }
  .taken{
    margin-top: 0.3rem;
  }
  .myHour{
    position: absolute;
    left: 0;
    top: 0.1rem;
    background:url('../../../assets/image/active/old/jiang.png') ;
    background-size:100% 100%;
    width:.87rem ;
    height:.34rem ;
    z-index:9999;
  }
}
</style>
