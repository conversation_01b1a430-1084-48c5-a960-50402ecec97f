<template>
  <div class="suspension" @click="toPage" v-if="show" v-drag>
    <img :src="'https://static.yzou.cn/pu/goods/fl.gif?v='+ new Date().getTime()"  alt="">
  </div>
</template>

<script>
export default {
  props: {
    targetId: {
      type: String,
      required: true
    },
    targetReamrk: {
      type: String,
      required: true
    }
  },
  directives: {
    drag: {
      inserted (el) {
        let positionParams = {
          x: 20,
          y: 105,
          startX: 0,
          startY: 0,
          endX: 0,
          endY: 0
        }
        el.addEventListener('touchstart', function (e) {
          positionParams.startX = e.touches[0].pageX
          positionParams.startY = e.touches[0].pageY
        })
        el.addEventListener('touchend', function (e) {
          positionParams.x = positionParams.endX
          positionParams.y = positionParams.endY
          positionParams.startX = 0
          positionParams.startY = 0
        })
        el.addEventListener('touchmove', function (e) {
          if (e.touches.length > 0) {
            let offsetX = e.touches[0].pageX - positionParams.startX
            let offsetY = e.touches[0].pageY - positionParams.startY
            let x = positionParams.x - offsetX
            let y = positionParams.y - offsetY
            if (x + el.offsetWidth > document.documentElement.offsetWidth) {
              x = document.documentElement.offsetWidth - el.offsetWidth
            }
            if (y + el.offsetHeight > document.documentElement.offsetHeight) {
              y = document.documentElement.offsetHeight - el.offsetHeight
            }
            if (x < 0) { x = 0 }
            if (y < 0) { y = 0 }
            el.style.right = x + 'px'
            el.style.bottom = y + 'px'
            positionParams.endX = x
            positionParams.endY = y
            e.preventDefault()
          }
        })
      }
    }
  },
  data() {
    return {
      show: true,
    };
  },
  created() {},
  mounted() {
    let now = new Date().getTime();
    // 1635695999000 时间为 2021-11-13 10:00:00 的秒数
    if (now > 1636768800000) {
      this.show = false;
    }
  },
  methods: {
    toPage() {
      this.$yzStatistic('pu.base.browse', this.targetId, this.targetReamrk);
      this.$router.push('/active/school11')
    }
  },
  beforeDestroy() {}
};
</script>

<style lang='scss' scoped>
.suspension {
  width: 1.1rem;
  height: 1.1rem;
  position: fixed;
  right: 0;
  bottom: .6rem;
  z-index: 1000;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
