<template>
  <div class="top-bar" v-if="isShowTopbar">
    <div class="wrap">
      <div class="inner">
        <div class="lf">
          <button
            class="icons btn-back"
            :class="{ active: active }"
            @click="goBack"
            v-if="showBackButton"
          ></button>
        </div>
        <div class="mid row1" :class="{ 'font-m': title.length >= '13' }">
          {{ title }}
        </div>
        <div class="rt"><slot name="rt"></slot></div>
      </div>
    </div>
  </div>
</template>

<script>
import { setDocumentTitle } from "../common";

export default {
  props: {
    title: {
      type: String,
      default: "",
    },
    showBackButton: {
      type: Boolean,
      default: true,
    },
    fnGoBack: {
      type: Function,
    },
    active: {
      type: Boolean,
    },
    backOrderList: {
      type: String,
      default: "",
    },
    jobPage: {
      type: Boolean,
    },
  },
  data() {
    return {
      redirect: "",
      isShowTopbar: true,
    };
  },
  created() {},
  mounted() {
    this.redirect = this.$route.query.redirect;

    // app访问时隐藏topbar
    if (
      this.$route.query.topbar === "no" ||
      this.$route.query.browser === "app" ||
      window.sessionStorage.getItem("browser") === "app"
    ) {
      this.isShowTopbar = false;
      window.sessionStorage.setItem("browser", "app");

      // app设置title
      setDocumentTitle(this.title);
    }
  },
  methods: {
    goBack: function () {
      if (this.jobPage) {
        this.$emit("closeJobPage", false);
        return;
      }
      if (this.backOrderList == "backToList") {
        if (this.search != "") {
          console.log("1");
        }
        this.$router.push({ name: "order" });
        return;
      }
      // app返回
      if ("undefined" !== typeof yz) {
        yz.goBack();
        return;
      }

      // web返回
      if ("function" === typeof this.fnGoBack) {
        this.fnGoBack();
      } else {
        if (!this.redirect) {
          if (window.history.length === 1) {
            const routerName = this.$route.fullPath.startsWith("/student")
              ? "stuInfo"
              : "home";
            this.$router.push({ name: routerName });
          } else {
            let tabActives = this.$route.query.tabActive;
            if (this.$route.query.tabActive) {
              this.$router.push({
                name: "pending",
                query: { tabActive: tabActives },
              });
            } else {
              this.$router.go(-1);
            }
          }
        } else {
          this.$router.push(decodeURIComponent(this.redirect));
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "../assets/less/variable";

.top-bar {
  height: 0.44rem;
  .wrap {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 2000;
    height: 0.44rem;
  }
  .inner {
    display: flex;
    align-items: center;
    position: relative;
    height: 100%;
    max-width: @maxWidth;
    margin: 0 auto;
    overflow: hidden;
    background-color: #fff;
    &:after {
      .borderBottom;
    }
  }
  .mid {
    flex: 1;
    padding: 0 0.1rem;
    line-height: 0.44rem;
    text-align: center;
    font-size: 0.17rem;
    &.font-m {
      font-size: 0.14rem;
    }
  }
  .lf,
  .rt {
    min-width: 0.5rem;
  }
  .rt {
    text-align: right;
  }
}
.btn-back {
  width: 0.44rem;
  height: 0.44rem;
  border: none;
  background-color: transparent;
  background-image: url(../assets/image/back.png);
  &.active {
    background: url(../assets/image/teacherKit/top.png) no-repeat;
    background-size: 90%;
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
  }
}
.top-bar.red {
  .inner {
    color: #fff;
    background-color: #f14a5f;
    &:after {
      display: none;
    }
  }
  .btn-back {
    background-image: url(../assets/image/back-white.png);
  }
}
</style>
