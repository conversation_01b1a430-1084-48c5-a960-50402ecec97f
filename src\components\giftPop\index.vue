<template>
  <transition name="slide">
    <div class="yz-gift-pop" v-if='value' @click='close'>
      <div class="content">
        <div class="yz-gift-pop__content" @click.stop='()=>{}'>
          <p class="big-title">上进辅导礼包</p>
          <p class="project">
            备考科目：
            (<span v-for="(item,index) in project" :key='index'>{{item.subjectName}}<em v-if="index!=project.length-1">、</em></span>)
          </p>
          <div class="white-item-box">
            <div class="item" v-for="(item, index) in content" :key="index">{{item}}</div>
          </div>
          <div class="btn-box">
            <button class="close-btn" @click='close'>自信裸考</button>
            <button class="buy" @click='toPay'>¥299 购买上进礼包</button>
          </div>
          <p class="small-text">{{smallText}}</p>
        </div>
        <div class="close-box">
          <img src="../../assets/image/close-w.png" alt="">
        </div>
      </div>

    </div>
  </transition>
</template>

<script>
import scholarshipText from './text';

export default {
  props: {
    value: {
      type: Boolean,
      defalut: false,
      required: true,
    },
    learnId: String,
    isJump: { // 是否跳转到远智学堂
      type: Boolean,
      default: false,
    },
    changeData: {
      type: Object,
      default: null,
    }, // 自主异动
  },
  data () {
    return {
      project: [],
      content: [],
      scholarship: '',
      pfsnId: '',
      pfsnLevel: '',
      unvsId:''
    };
  },
  computed: {
    scholarshipText() {
      return scholarshipText(this.$data);
    },
    smallText() {
      const defaultText = '自愿购买，购买后不予退还';
      return this.scholarship == '123' ? '若审核未通过，缴纳的费用可全额退回' : defaultText;
    },
  },
  mounted () {
    if (this.pfsnId) {
      this.getProject();
    }

    if (this.scholarship && this.scholarshipText) {
      this.content = this.scholarshipText;
    } else {
      this.getContent();
    }
  },
  watch: {
    value(val) {
      if (val) {
        if (this.scholarship && this.scholarshipText) {
          this.content = this.scholarshipText;
        } else {
          this.getContent();
        }
        if (this.learnId && !this.changeData) {
          this.getLearnInfo();
        }
      }
    },
    changeData(obj) {
      if (obj) {
        this.scholarship = obj.scholarship;
        this.pfsnLevel = obj.pfsnLevel;
        this.pfsnId = obj.pfsnId;
        this.unvsId = obj.unvsId;
      }
    },
    pfsnId(val) {
      if (val) {
        this.getProject();
      }
    },
    scholarship(val) {
      if (val && this.scholarshipText) {
        this.content = [].concat(this.scholarshipText);
      } else {
        this.getContent();
      }
    },

  },
  methods: {
    close() {
      this.$emit('input', false);
      if (this.isJump) {
        this.$router.push({name:'stuInfo'});
      }
    },
    toPay() {
      this.$http.post('/mkt/getActivityInfo/1.0/',{scholarship:this.scholarship}).then(res=>{
        if (res.code=='00') {
          if (Date.now() > res.body.EndTime) {
               if(this.storage.getItem('grade')==2021){
                this.$modal({message:'您之前报考的活动已结束，请联系助学老师！',duration:3000,icon:'warning'});
                  return
                }
            this.$modal({
              message:'您之前参与的活动已结束，是否转报其他活动？',
              icon:'warning',
              showConfirmButton:true,
              showCancelButton:true,
              closeOnClickModal:false,
              duration:0,
              cancel:()=>{
              },
              yes:()=>{

                this.$router.push({
                  name: "inviteEnroll",
                  query: {
                    activityName: "scholarship",
                    inviteId: this.$route.query.inviteId,
                    action: "login",
                    scholarship: "164",
                    actName: '2024级筑梦计划',
                    recruitType: "1",
                    changeActivity:'changeActivity',
                    learnId:this.learnId,
                  }
                });
              }
            })
          }else{
            this.$router.push({
              name: 'stuPayment',
              query: {learnId: this.learnId, activityName: 'dreamBuildScholarship'}
            });
          }
      }})
      // this.$router.push({
      //   name: 'stuPayment',
      //   query: {learnId: this.learnId, activityName: 'dreamBuildScholarship'}
      // });
    },
    getProject(){
      this.$http.post('/bds/getExamSubject/1.0/',{ pfsnId: this.pfsnId}).then(res=>{
        if(res.code==='00'){
          this.project=res.body;
        }
      })
    },
    getContent(){
      this.$http.post('/bds/getActivityOtherConfig/1.0/',{ typeCode:'1_jiaofei' }).then(res=>{
        if(res.code==='00'){
          if(res.body.content){
            if (!this.scholarship || !this.scholarshipText) {
              this.content = res.body.content.split("\n");
            }
          }
        }
      })
    },
    getLearnInfo() {
      this.$http.post('/mkt/getLearnInfoByLearnId/1.0/',{ learnId: this.learnId }).then(res=>{
        if (res.code==='00') {
          const keys = ['pfsnId', 'pfsnLevel', 'scholarship','unvsId'];
          for (const key in res.body) {
            if (res.body[key]) {
              this[key] = res.body[key];
            }
          }
        }
      })
    },
  }
}
</script>

<style lang="less" scoped>
  .yz-gift-pop{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2001;
    background: rgba(0, 0, 0, 0.7);
    // padding-top: 0.8rem;
    .content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .yz-gift-pop__content{
    width: 3.55rem;
    min-height: 3.62rem;
    margin: 0 auto;
    background: url(../../assets/image/gift-bg.png) no-repeat;
    background-size: 100% 100%;
    padding-bottom: 0.15rem;
    .big-title{
      color: #E81733;
      font-size: 0.27rem;
      font-weight:500;
      padding-top: 0.16rem;
      text-align: center;
    }
    .project{
      color: #E81733;
      font-weight:500;
      text-align: center;
    }
    .small-text{
      color: #fff;
      font-size: 0.1rem;
      text-align: center;
      margin-top: 0.05rem;
    }
    .white-item-box{
      margin-top: 0.37rem;
      padding: 0 0.43rem;
      .item{
        background: #fff;
        border-radius: 5px;
        padding: 0.05rem 0 0.05rem 0.2rem;
        position: relative;
        margin-top: 0.05rem;
        word-break: break-all;
        &::before{
          content: '';
          position: absolute;
          left:0.1rem;
          top: 0.09rem;
          width: 0.05rem;
          height: 0.05rem;
          border-radius: 50%;
          background: #F9503C;
        }
      }
    }
    .btn-box{
      padding: 0 0.43rem;
      margin-top: 0.16rem;
      button{
        width: 100%;
        display: block;
        height: 0.46rem;
        background: none;
        border-radius: 0.05rem;
        font-size: 0.16rem;
        margin-bottom: 0.1rem;
      }
      .close-btn{
        border: 0.02rem solid #FDF2B9;
        color: #FDF2B9;
        font-weight:500;
      }
      .buy{
        color: #6B420B;
        font-weight:500;
        background:linear-gradient(270deg,rgba(240,198,100,1) 0%,rgba(254,243,190,1) 100%);
        box-shadow:0px 4px 4px 0px rgba(234,21,39,1);
      }
    }
  }
  .close-box{
    width: 0.3rem;
    height: 0.3rem;
    border-radius: 50%;
    margin: 0.1rem auto 0;
    border: 1px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      width: 0.17rem;
    }
  }
</style>
