<template>
  <van-swipe class="yz-newMain-swiper" :autoplay="3000" indicator-color="white">
    <van-swipe-item v-for="(item) in bannerList" :key="item.bannerUrl">
      <img class="banner-img" :src="item.bannerUrl | imgOssURL" @click='itemClick(item)' alt="">
    </van-swipe-item>
  </van-swipe>
</template>

<script>
import { getQueryString } from '@/common';
import { Swipe, SwipeItem } from 'vant';

export default {
  components: { Swipe, SwipeItem },
  props: {
    inviteId: String,
  },
  data() {
    return {
      bannerList: [],
      scholarship: '',
    };
  },
  mounted() {
    this.getBannerList();
  },
  methods: {
    toEnroll(unvs){
      this.$router.push({
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName:this.actName,
          unvs:JSON.stringify(unvs),
          recruitType:'1'
        }
      });
    },
    itemClick(item) {
      if (!item.redirectUrl) {
        return;
      }
      const query = this.$route.query;
      let searchUrl = '';
      const activeShip = getQueryString(item.redirectUrl, 'activeShip');
      if(item.redirectUrl.includes('/active/elevenCoupon')) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=119`;
      } else if (activeShip) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=${activeShip}`;
      } else {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}`;
      }

      if (item.redirectUrl.includes('scholarship')) {
        this.scholarship = getQueryString(item.redirectUrl, 'scholarship');
        this.toEnroll();
      } else {
        const url = item.redirectUrl.includes('?') ? `${item.redirectUrl}&${searchUrl}` : `${item.redirectUrl}?${searchUrl}`;
        window.location.href = url;
      }
    },
    async getBannerList() {
      const res = await this.$http.post('/bds/bannerList/1.0/', { zmBannerType: 4 });
      if (res.code == '00') {
        this.bannerList = res.body || [];
        if (this.bannerList.length > 0) {
          this.bannerList.forEach((item) => {
            if (item.bannerUrl) {
              item.bannerUrl += `?timestamp=${Date.now()}`;
            }
          });
        }
      }
    },
  },
}
</script>

<style lang="less">
  .yz-newMain-swiper{
    height: 100%;
    .banner-img{
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }
    .van-swipe__indicators{
      bottom: 0.78rem;
      .van-swipe__indicator{
        width: 0.04rem;
        height: 0.04rem;
        border-radius: 0.04rem;
        transition: all 0.2s;
        opacity: 1;
      }
      .van-swipe__indicator--active{
        width: 0.09rem;
      }
    }
  }
</style>
