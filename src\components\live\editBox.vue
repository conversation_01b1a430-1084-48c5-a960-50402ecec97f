<template>
  <form action="" @submit.prevent="submit">
    <div class="edit-box">
      <div class="cl">
        <i id="btnEmotion" class="fl icons btn-emotion" @click="$refs.emotion.toggleBox()"></i>
        <button class="fr btn" :disabled="!content" @click="submit">提交</button>
        <div class="ipt-wrap">
          <input type="text" :placeholder="inputPlaceholder" v-model.trim="content" @focus="inputFocus"/>
          <i class="icons i-quiz"></i>
        </div>
      </div>
      <emotion-list v-model="content" ref="emotion"/>
    </div>
  </form>
</template>

<script>
  import emotionList from '@/components/emotionList';

  export default {
    props: {
      isSocketConnect: {
        type: Boolean,
        default: false
      },
      inputPlaceholder: {
        type: String,
        default: '我要留言'
      }
    },
    data() {
      return {
        content: ''
      }
    },
    methods: {
      formatEmotions: function (content) {
        return this.$refs.emotion.formatEmotions(content);
      },
      submit: function () {
        let content = this.content;
        if (!content) return;
        if (!this.isSocketConnect) {
          this.$modal({message: '请稍等，连接中…', icon: 'warning'});
          return;
        }
        this.$emit('submit', content);
        this.content = '';
      },
      inputFocus: function () {
        setTimeout(function () {
          window.scrollTo(0, document.getElementsByTagName('body')[0].offsetHeight);
        }, 600);
      }
    },
    components: {emotionList}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/variable";
  
  .edit-box{
    position:absolute;right:0;bottom:0;left:0;z-index:1;
    > .cl{max-width:@maxWidth;margin:0 auto;padding:.07rem .15rem;background-color:#fff;}
    .btn{line-height:.36rem;border:none;background-color:transparent;color:@color;font-size:.14rem;}
    .ipt-wrap{
      position:relative;margin-left:.3rem;margin-right:.4rem;
      input{width:100%;height:.36rem;padding:0.08rem 0.1rem 0.08rem 0.3rem;line-height:.2rem;font-size:.14rem;color:#333;border:1px solid #e7e7e7;background-color:#f7f7f7;border-radius:.04rem;}
    }
  }
  .btn-emotion{width:.22rem;height:.22rem;margin-top:.07rem;background-image:url(../../assets/image/i-emotion.png);}
  .i-quiz{position:absolute;top:.1rem;left:.1rem;width:.16rem;height:.17rem;vertical-align:middle;background-image:url(../../assets/image/live_ico_quiz.png);}
</style>
