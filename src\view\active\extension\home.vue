<template>
    <div class="content">
        <inviteTop :inviteId="inviteId" :text='intext'/>
        <div class="bg-pic" ref="bgPic">
            <swiperEnroll right=".03"  top=".23" text="购买" textColor="white"/>
            <div class="crad" ref="crad">
                <div class="crad-num">{{detail.buyerCount?detail.buyerCount:0}}人已购买</div>
            </div>
        </div>
        <div class="btn" @click="getStatus" ref="btn">
            <div class="btn-text">{{buttontext}}</div>
            <!-- <s>市场价¥{{marketPrice}}</s> -->
        </div>
        <div class="text-detail">
        </div>
        <div class="gittxt">
        </div>
          <div class="swiper">
                      <swiper :options="swiperOption">
                        <swiper-slide >
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/extension/1.png"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/extension/2.png"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/extension/3.png"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/extension/4.png"
                          >
                        </swiper-slide>
                        <swiper-slide>
                          <img
                            class="swiperImg"
                            src="../../../assets/image/active/extension/5.png"
                          >
                        </swiper-slide>
                      </swiper>
                    </div>
        <div class="gift-title">
        </div>
        <div class="talk-text">
           <div v-for="(item,index) in useRule">{{item}}</div>
            <!-- <div>
            1. 购买成功后，关注【远智教育】公众号，点击购买成功提醒，微信扫码进群，领取办公软件公开课和会员权益；若进群失败，请添加书童老师微信号【shutongyz】;
            </div>
            <div>
             2. 观看课程：【远智教育APP】-【发现】-【上进学社】开启课程学习；
            </div>
            <div>
           3. 会员权益有效期7天*24小时，购买成功即时生效。
            </div> -->
        </div>
        <share
          :title="title"
          :desc="desc"
          :link="shareLink"
          :imgUrl="imgUrl"
          :regOrigin="defaultRegOrigin"
          ref="share"
        />
    </div>
</template>
<script>
import DigitRoll from "@huoyu/vue-digitroll";
import inviteTop from "../../aspirantUniversity/components/invite-top";
import swiperEnroll from "../enrollAggregate/components/swiper-enroll";
import share from "@/components/share";
import config from "../../../config";
import mixins from '../../../mixins/statistic';
import { Toast } from 'vant';
document.title="上进学社";
export default {
   mixins:[mixins],
    data(){
         return{
        inviteId:"",
        intext:"分享了一张上进卡给你",
        swiperOption: {
          initialSlide: 1,
          autoplay: 2000,
          centeredSlides: true,
          loop: true,
          slidesPerView: "auto",
          loopedSlides: 1,
          autoplayDisableOnInteraction: false,
        },
        title:"",
        desc:"",
        shareLink:"",
        imgUrl:"",
        defaultRegOrigin:"30",
        mebId:"",
        detail:"",
        price:"",
        marketPrice:"",
        fullBuy:false,
        actId:"",
        channelId:"",
        buttontext:"",
        useRule:"",
        channelName:"",
        actName:"",
        relation:"",
     }
    },
    created(){
      this.channelId=this.$route.query.channelId||"";
      this.channelName=decodeURIComponent(this.$route.query.channelName)||"";
      this.actName=decodeURIComponent(this.$route.query.actName)||"";
      this.actId=this.$route.query.actId||"";
      this.relation=this.storage.getItem('relation');
      this.inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || '')).replace(/ /g, '+');
      this.shareLink=`${window.location.origin}/extension/home?regChannel=3&channelId=${encodeURIComponent(this.channelId)}&channelName=${encodeURIComponent(this.$route.query.channelName)}&actName=${encodeURIComponent(this.$route.query.actName)}&actId=${this.actId}`;
      this.getActivity();
          let name= this.actName+"-"+this.channelName
      if(!!this.channelId){
            this.statistic('pu.member.act.browse',this.channelId,name).then((res) => {
            return res
        }).catch(()=>{return false})
      }
    },
    methods:{
       getActivity(){
           this.$http.post('/pu/getActInfo/1.0/',{actId:this.actId,channel:this.channelId}).then(res=>{
                if(res.code!="00"){
                   return
                }
                this.$nextTick(() => {
                  if(res.body.actDeploy.backgroundImg){
                        this.$refs.bgPic.style.background = `url("${
                      config.imgPosterBaseURL + res.body.actDeploy.backgroundImg
                    }")`;
                  }
                  if(res.body.actDeploy.explainImg){
                      this.$refs.crad.style.background = `url("${
                      config.imgPosterBaseURL + res.body.actDeploy.explainImg
                    }")`;
                  }
                  if(res.body.actDeploy.buttonColor){
                      this.$refs.btn.style.background=res.body.actDeploy.buttonColor;
                  }
                });
                this.detail=res.body
                this.mebId=this.detail.actDeploy.mebId
                if(this.detail.buyRule.firstBuy){
                    this.price=this.detail.buyRule.firstBuyPrice
                }else{
                    this.price=this.detail.buyRule.commonPrice
                }
                 if(!!this.detail.actDeploy){
                  this.title=this.detail.actDeploy.shareTitle||"上进会员1元抢购";
                  this.desc=this.detail.actDeploy.shareDesc||"1元周卡 · 千元权益～限量3000张，速抢！";
                  this.imgUrl="https:"+config.imgPosterBaseURL+this.detail.actDeploy.shareImg||"http://yzims.oss-cn-shenzhen.aliyuncs.com/progressappshare.png";
                }
                console.log( this.imgUrl)
                this.marketPrice=this.detail.buyRule.marketPrice;
                this.buttontext=this.detail.actDeploy.buttonWord||"元抢购";
                // this.useRule=this.detail.actDeploy.useRule.replace(/[\r\n]/g,'<br />');
                this.useRule=this.detail.actDeploy.useRule.split("\n");
                // console.log(  this.useRule)
                // // this.useRule=`<div>${this.useRule}</div>`
           })
       },
      getStatus(){
          this.$http.post('/pu/getActStatus/1.0/',{mebId:this.mebId,mebDeployId:this.detail.actDeploy.mebDeployId}).then(res=>{
              if(res.code!="00"){
                   return
               }
               const status=res.body.actStatus
               if(status==1){
                   this.$modal({
                    message:'当前活动已经结束！去上进学社逛逛吧',
                    icon:'warning',
                    showCancelButton:false,
                    closeOnClickModal:true,
                    showConfirmButton: true,
                    confirmButtonText: '去上进学社',
                    duration:10000,
                    yes:()=>{
                      this.$router.replace({
                        name: "aspirantUniversity",
                      });
                    }
                  })
                  return
               }else if(status==2){
                  this.$modal({
                      message:'活动太火爆了，会员卡已抢完，去上进学社逛逛吧！',
                      icon:'warning',
                      closeOnClickModal:true,
                      showConfirmButton: true,
                      confirmButtonText: '去上进学社',
                      duration:10000,
                      yes:()=>{
                        this.$router.replace({
                          name: "aspirantUniversity",
                        });
                      }
                    })
                    return
               }else if(status==3){
                   this.$modal({
                      message:'会员卡购买次数超过上限，去上进学社逛逛吧！',
                      icon:'warning',
                      closeOnClickModal:true,
                      showConfirmButton: true,
                      confirmButtonText: '去上进学社',
                      duration:10000,
                      yes:()=>{
                        this.$router.replace({
                          name: "aspirantUniversity",
                        });
                      }
                    })
                    return
               }else if(status==4){
                    this.$modal({
                      message:'活动暂未开始！',
                      icon:'warning',
                    })
                    return
               }else if(status==5){
                      this.$modal({
                      message:'活动已经结束！',
                      icon:'warning',
                    })
                    return
               }else{
                    this.pay()
               }

          })
      },
   async pay(){
            if(this.detail.buyRule.mebFreeType=="pay"){
               let vip=await this.$http.post('/pu/addRuleLog/1.0/',
                {mobile:this.mobile,mebId:this.mebId,buySource:'WECHAT',
                buyerType:this.relation,inviteId:this.inviteId,channelName:this.channelName,actName:this.actName,actId:this.actId,channelId:this.channelId}).then(res=>{
                    if(res.code!="00"){
                      return
                    }
                    return res.body
                })
                if(vip){
                  this.$router.push({path:'/pay/postagePayment',
                  query:{payItem: 'puMember', // 自考补差价
                  mappingId: vip,
                  mebId:this.mebId}})
               }
             }else{
                 let vip=await this.$http.post('/pu/addRuleLog/1.0/',
                {mobile:this.mobile,mebId:this.mebId,buySource:'WECHAT',freeType:'free',
                buyerType:this.relation,inviteId:this.inviteId,channelName:this.channelName,actName:this.actName,actId:this.actId,channelId:this.channelId},).then(res=>{
                    if(res.code!="00"){
                      return
                    }
                    return res.body
                })
                if(vip){
                    Toast('领取成功');
                    setTimeout(()=>{
                        this.$router.push({path:'/aspirantUniversity/PurchaseRecords'})
                    },2000)
              }
             }
       }
    },
    components:{inviteTop,swiperEnroll,share}

}
</script>
<style lang="less" scoped>
.content{
    width: 3.75rem;
    min-height: 100vh;
    padding-bottom: 0.5rem;
    &::-webkit-scrollbar{
        width: 0;
        opacity: 0;
    }
    &::-webkit-scrollbar-thumb{
        width: 0;
        opacity: 0;
    }
    .bg-pic{
        width: 100%;
        height: 3.97rem;
        // background: url('../../../assets/image/active/extension/bg-pic.png');
        background-size: 100% 100% !important;
        position: relative;
        .crad{
            position: absolute;
            width: 3.45rem;
            height: 1.85rem;
            // background: url('../../../assets/image/active/extension/crad.png');
            background-size: 100% 100% !important;
            top: 2.49rem;
            left: 0.15rem;
            border-radius: 0.08rem;
            box-shadow: 0px -0.02rem 0.15rem 0px rgba(143, 57, 14, 0.89);
            .crad-num{
                position: absolute;
                top: 0.29rem;
                right: 0.15rem;
                font-size: 0.10rem;
                font-family: PingFang-SC-Bold, PingFang-SC;
                font-weight: bold;
                font-weight: bold;
                color: #F2D597;
            }
             .enrollCount {
            // width: 0.55rem;
            height: 0.22rem;
            position: absolute;
            top: .11rem;
            text-align: center;
            color: rgba(54, 54, 54, 1);
            font-size: 0.13rem;
            right:.1rem;
            padding-left: 1.2%;
            font-weight: bold;
            z-index: 2;
          }
        }
    }
    .bg-pic/deep/.txt_fr{
        color: #FFFFFF  !important;
    }
    .btn{
        margin: 0 auto;
        margin-top: 0.53rem;
        width: 2.72rem;
        height: 0.55rem;
        text-align: center;
        background: linear-gradient(90deg, #FF8C58 0%, #F85203 100%);
        padding-top: 0.1rem;
        border-radius: 0.28rem;
        -webkit-animation: free_download 0.5s linear alternate infinite;
      animation: free_download 0.5s linear alternate infinite;
      .btn-text{
         font-size: 0.18rem;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #FFFFFD;
      }
      s{
        font-size: 0.10rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFD;
        position: relative;
        top: -0.05rem;
      }
    }
       @-webkit-keyframes free_download {
      0% {
        -webkit-transform: scale(0.9);
      }
      100% {
        -webkit-transform: scale(1);
      }
    }
    @keyframes free_download {
      0% {
        transform: scale(0.9);
      }
      100% {
        transform: scale(1);
      }
    }
  .text-detail{
      width: 3.75rem;
      height: 2.1rem;
      margin-top: 0.31rem;
      background: url('../../../assets/image/active/extension/giftext.png');
      background-size: 100% 100%;
  }
  .gittxt{
      width: 2.42rem;
      height: 0.52rem;
      background: url('../../../assets/image/active/extension/gift-title.png');
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: 0.24rem;
      margin-bottom: 0.26rem;
  }
  .gift-title{
      width: 2.42rem;
      height: 0.52rem;
      background: url('../../../assets/image/active/extension/cradTitlr.png');
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: 0.24rem;
  }
  .talk-text{
    width: 3.47rem;
    margin: 0 auto;
    font-size: 0.13rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 500;
    color: #666666;
    margin-bottom: 0.35rem;
    margin-top: 0.24rem;
    div{
        margin-bottom: 0.1rem;
    }
  }
}
// .swiper{
//     width: 3.75rem;
//     height: 1.55rem;
//     .swiperImg{}
// }
.swiper {
    width: 100%;
    overflow: hidden;
  }
  .swiper-slide {
    width: 80%;
    height: 1.6rem;
  }
  .swiper-slide-active img {
    display: block;
    margin: 2% auto;
    width: 92%;
    height: 96%;
  }
  .swiperImg {
    display: block;
    margin: 0 auto;
    margin-top: 3.5%;
    width: 100%;
    height: 85%;
    border-radius: .05rem;
    background: #FFFFFF;
    // box-shadow: .01rem .03rem .27rem 0 rgba(10, 39, 82, 0.14);
  }
</style>
