<template>
  <transition name="slide">
    <div class="children-page" @click="close" v-if="isShow">
      <div class="inner">
        <div class="cont bc-w">
          <slot></slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  export default {
    data() {
      return {
        isShow: false
      }
    },
    methods: {
      open: function () {
        this.isShow = true;
      },
      close: function () {
        this.isShow = false;
      }
    }
  }
</script>

<style lang="less" scoped>
  .children-page{
    background-color:rgba(0, 0, 0, .5); transition:opacity .2s ease;
    > .inner{
      position:relative; padding:0; overflow:hidden;
      > .cont{ min-height:100vh; margin-left:25%; padding:.12rem; transition:all .2s ease; }
    }
  }
  .slide-enter, .slidel-leave-active{
    opacity:0;
  }
  .slide-enter .cont, .slide-leave-active .cont{
    transform:translate3d(100%, 0, 0);
  }
</style>
