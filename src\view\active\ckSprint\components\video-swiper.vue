<template>
  <div class="yz-school-swiper">
    <!-- 轮播组件 -->
    <big-swiper :list='list' swiperClass='swiper-class' swiperSlideClass='swiper-slide-class'
                pageId='school-swiper-page' ref='bigSwiper' :loop="false" :autoplay='false'>
      <div class="img-shadow" slot-scope="scope" @click="playVideo(scope.item.videoUrl)">
        <img :src="scope.item.videoCover" alt="">
        <div class="mark-box">
          <div class="mark"></div>
        </div>
        <img src="../../../../assets/image/active/ckSprint/play.png" alt="" class="play-icon">
      </div>
    </big-swiper>
    <van-popup v-model="showVideo" class="video-popup" @close="videoClose" :close-on-click-overlay="false">

      <img src="../../../../assets/image/active/ckSprint/close-icon.png" alt="" class="close-icon" @click="close">
      <div v-show="!videoShow" class="video-loading">
        <van-loading type="spinner" size="50px" />
      </div>
      <video v-show="videoShow" :src="videoUrl" ref="video" loop controls="controls" controlslist="nodownload"></video>

    </van-popup>

  </div>
</template>

<script>
import BigSwiper from '@/view/active/2021NewMain/components/big-swiper';

export default {
  components: { BigSwiper },
  data () {
    return {
      showVideo: false,
      videoUrl: '',
      videoShow: false

    }
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },

  },
  mounted () {
  },
  methods: {
    playVideo (url) {
      this.showVideo = true;
      this.videoUrl = url
      this.$yzStatistic('sprintAct.base.click', '2', '首页-图文视频页/播放视频');

      if (this.$refs.video) {
        this.$refs.video.load();
      }
      setTimeout(() => {
        this.$refs.video.play();
        setTimeout(() => {
          this.videoShow = true
        }, 700);
      }, 0);

    },
    close () {
      this.showVideo = false;
      this.videoShow = false

    },
    videoClose () {
      this.$refs.video.pause();
    },
    activeIndex (index) {
      this.$emit('activeIndex', index)

    }
  },
};
</script>

<style lang="less">
.yz-school-swiper {
  .img-shadow {
    height: 100%;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border-radius: 0.1rem;
    border: 0.03rem solid #d92424;
    img {
      width: 100%;
      height: 100%;
      // vertical-align: middle;
    }
    .play-icon {
      width: 0.45rem;
      height: 0.45rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .mark-box {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 0.1rem;
      padding: 0.03rem;
      .mark {
        background-color: rgba(0, 0, 0, 0.3);
        width: 100%;
        height: 100%;
        border-radius: 0.1rem;
      }
    }
  }
  .swiper-class {
    height: 1.65rem;
  }
  .swiper-slide-class {
    width: 80%;
    height: 1.35rem;
    margin-top: 0.17rem;
  }
  .swiper-slide-active {
    width: 2.94rem;
    height: 1.62rem;
    margin-top: 0.03rem;
  }
  .video-popup {
    background-color: rgba(0, 0, 0, 0) !important;
    width: 100%;
    .close-icon {
      position: absolute;
      right: 0;
      width: 36px;
      height: 36px;
    }
    video {
      width: 100%;
      border-radius: 0.05rem;
      margin-top: 0.1rem;
      padding: 0.3rem 0;
    }
    .video-loading {
      height: 1.5rem;
      width: 100%;
      text-align: center;
    }
  }
}
</style>
