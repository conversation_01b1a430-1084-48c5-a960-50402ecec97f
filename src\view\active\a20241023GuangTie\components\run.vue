<template>
  <div class="main-box">
    <el-dialog :visible.sync="showVisible" :close-on-click-modal="false" width="95%">
      <div class="head-box">
        <span @click="cancel()">取消</span>
        <span class="confirm" :class="{ active: digit && currentTime && runImg }" @click="confirm()">完成</span>
      </div>
      <van-uploader :after-read="afterRead" v-model="fileList" preview-size="300px" multiple :max-count="1" />

      <p class="tip">Tips：上传的数据需有<span>跑步轨迹、跑步距离、跑步时长和跑步日期</span></p>
      <van-field v-model="digit" type="number" label="本次跑步距离(单位:km)" @blur="handleInput2" placeholder="点击输入" input-align="right" />
      <div class="time-box">
        <TimeSelection @confirmTime="selectT" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TimeSelection from '../../signAct/components/timeSelection.vue'
import { Toast } from 'vant'

export default {
  data() {
    return {
      showTime: false,
      showVisible: true,
      digit: '',
      fileList: [],
      runImg: '',
      currentTime: null,
      defaultTiming: ''
    }
  },
  components: { TimeSelection },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    show(val) {
      this.showVisible = val
    }
  },
  mounted() {
    let t = this.createTimeStr(new Date(), 'ymd') + ' 00:00:00'
    this.defaultTiming = new Date(t)
  },
  methods: {
    selectT(val) {
      const vas = this.time3(val)
      console.log(vas, 'selectT')
      if (vas <= 0) {
        Toast({ message: '请输入跑步时间', duration: 2000 })
        return
      }
      if (this.digit) {
        const runSecond = Number(val.slice(0, 2)) * 60 + Number(val.slice(3, 5)) + Number(val.slice(6, 8)) / 60
        if ((runSecond / this.digit).toFixed(2) < 3) {
          Toast({ message: '这个配速远超世界记录，自查哦~', duration: 2000 })
          return
        }
      }
      this.currentTime = val
    },
    handleInput2(elmet) {
      const val = Number(elmet?.target?.value || 0)
      console.log(val, 'handleInput2')
      if (val >= 100 || val <= 0) {
        Toast({ message: val ? '暂不支持大于100公里录入哦' : '请输入跑步距离', duration: 2000 })
        return
      }
      this.digit = Number(this.digit).toFixed(2) || null
    },
    confirm() {
      // 为空判断
      if (!this.runImg || !this.currentTime || !Number(this.digit)) {
        const message = !this.runImg ? '请上传图片' : !Number(this.digit) ? '请输入正确的跑步距离' : '请输入正确的跑步时间'
        Toast({ message, duration: 2000 })
        return
      }
      // 条件校验
      if (Number(this.digit).toFixed(2) >= 100) {
        Toast({ message: '暂不支持大于100公里录入哦', duration: 2000 })
        return
      }
      const runSecond = Number(this.currentTime.slice(0, 2)) * 60 + Number(this.currentTime.slice(3, 5)) + Number(this.currentTime.slice(6, 8)) / 60
      if ((runSecond / this.digit).toFixed(2) < 3) {
        Toast({ message: '这个配速远超世界记录，自查哦~', duration: 2000 })
        return
      }
      const runInfo = {
        runImg: this.runImg, // 跑步配图
        runTime: this.currentTime, // 跑步时间
        distance: this.digit, // 公里数
        runSecond: this.time3(this.currentTime), // 跑步时长
        spendDesc: this.time4(this.currentTime, this.digit) // 平均配速
      }
      this.showVisible = false
      this.$emit('confirm', runInfo)
    },
    cancel() {
      this.$router.push({ name: 'a20241023GuangTie' })
    },
    selectTime(time) {
      this.currentTime = time
      console.log(this.currentTime)
    },
    time3(date) {
      return ((date.slice(0, 2) * 60 * 60 + date.slice(3, 5) * 60 + date.slice(6, 8)) / 3600).toFixed(2)
    },
    time4(date, num) {
      console.log(Number(date.slice(0, 2)) * 60 * 60 + Number(date.slice(3, 5)) * 60 + Number(date.slice(6, 8)), num, '666666666')

      return this.formatSeconds((Number(date.slice(0, 2)) * 60 * 60 + Number(date.slice(3, 5)) * 60 + Number(date.slice(6, 8))) / num)
    },

    /**
     * 将时间格式化为标准字符串==HH-MM-DD HH:MI:ss
     *
     *
     * */
    createTimeStr(time = new Date(), type = 'ymdhis') {
      let date = new Date(time)
      let Str = ''
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      if (month < 10) month = '0' + month
      let day = date.getDate()
      if (day < 10) day = '0' + day
      let hours = date.getHours()
      if (hours < 10) hours = '0' + hours
      let minutes = date.getMinutes()
      if (minutes < 10) minutes = '0' + minutes
      let Seconds = date.getSeconds()
      if (Seconds < 10) Seconds = '0' + Seconds

      if (type === 'ymdhis') {
        Str = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + Seconds
      } else if (type === 'ymd') {
        Str = year + '-' + month + '-' + day
      } else if (type === 'his') {
        Str = hours + ':' + minutes + ':' + Seconds
      }
      return Str
    },
    formatSeconds(value) {
      var secondTime = parseInt(value) // 秒
      var minuteTime = 0 // 分
      var hourTime = 0 // 小时
      console.log('secondTime', secondTime)
      if (secondTime > 60) {
        // 如果秒数大于60，将秒数转换成整数
        // 获取分钟，除以60取整数，得到整数分钟
        minuteTime = parseInt(secondTime / 60)
        // 获取秒数，秒数取佘，得到整数秒数
        secondTime = parseInt(secondTime % 60)
        // 如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          // 获取小时，获取分钟除以60，得到整数小时
          hourTime = parseInt(minuteTime / 60)
          // 获取小时后取佘的分，获取分钟除以60取佘的分
          minuteTime = parseInt(minuteTime % 60)
        }
      }
      var result = '' + parseInt(secondTime) + '"'
      if (hourTime > 0) {
        minuteTime += parseInt(hourTime) * 60
      }
      if (minuteTime > 0) {
        result = '' + parseInt(minuteTime) + "'" + result
      }

      console.log('result', result)
      return result
    },
    afterRead(file) {
      this.$indicator.open()
      let formData = new FormData()
      formData.append('file', file.file)
      formData.append('name', file.file.name)
      this.$http.post('/uploadFileNew', formData).then((res) => {
        this.$indicator.close()
        this.runImg = res.body
      })
    }
  }
}
</script>

<style lang="less" scoped>
.main-box /deep/.el-dialog__body {
  color: #646566;
}
.main-box {
  text-align: center;
}
.main-box /deep/ .el-dialog--small {
  width: 95%;
}

.main-box /deep/ .van-field__label {
  width: auto;
}

.main-box /deep/.el-date-editor.el-input {
  width: 153px;
}
.main-box /deep/ .el-dialog__body {
  padding: 10px 20px;
}
.main-box /deep/.el-dialog__header {
  padding: 0;
}
.main-box /deep/ .el-input__inner {
  border: none;
  text-align: right;
  padding-right: 0 !important;
}
.main-box /deep/ .el-icon {
  display: none;
}
.tip {
  font-size: 14px;
  color: #999999;
  text-align: left;
  padding: 0 0.15rem;
  span {
    color: #cc2725;
  }
}
.top {
  margin: 20px 0;
  text-align: left;
  padding-left: 1%;
}

.head-box {
  height: 56px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1%;
  padding: 0 10px;
  img {
    width: 32px;
    height: 32px;
  }
  span {
    // background: #999999;
    padding: 5px 11px;
    font-size: 14px;
    color: #000;
    font-weight: bold;
    cursor: pointer;
  }
  .confirm {
    color: #999999;
    // background: #ea401c;
  }
  .active {
    color: #cc2725;
  }
}
.main-box /deep/ .el-input__inner {
  border: none;
  text-align: right;
  padding-right: 0 !important;
}
.main-box /deep/ .el-input__icon {
  display: none;
}
.main-box /deep/ .van-field__control {
  text-align: right;
}

.main-box /deep/ .van-cell--required::before {
  content: '';
}
</style>
