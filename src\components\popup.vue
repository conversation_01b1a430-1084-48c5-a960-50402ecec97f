<template>
  <div class="popup" v-if="show" @click="close">
    <div class="inner" @click.stop>
      <div class="bd">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      isShow: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        show: this.isShow,
      }
    },
    methods: {
      open: function () {
        this.show = true;
      },
      close: function () {
        this.show = false;
      }
    }
  }
</script>

<style lang="less" scoped>
  .popup{
    position:fixed; top:0; right:0; bottom:0; left:0; z-index:9999; background-color:rgba(0, 0, 0, .5);
    > .inner{ position:absolute; top:50%; right:.16rem; left:.16rem; max-height:100%; overflow-y:auto; background-color:#fff; transform:translateY(-50%); border-radius:.04rem; }
    > .inner > .bd{ padding:.2rem .12rem; }
  }
</style>
