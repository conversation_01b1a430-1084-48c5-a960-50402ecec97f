@color:#F06E6C;
@color2: #F85731;
@borderColor:#e4e4e4;
@grey: #A29B9B;
// @backgroundColor:#f5f6f8;
@backgroundColor:#F8F7F7;
@maxWidth:640px;
@bgColor: linear-gradient(to bottom right, #f86f6b, #ef425c);
@whiteColor: #FFFFFF;
@greyCodor: #333333;

// .5像素的上边框
.borderTop(){
  position:absolute; top:0; left:0;
  width:200%; height:1px;
  background-color:@borderColor;
  transform:scale(0.5); transform-origin:top left;
  content:'';
}

// .5像素的下边框
.borderBottom(){
  position:absolute; bottom:0; left:0;
  width:200%; height:1px;
  background-color:@borderColor;
  transform:scale(0.5); transform-origin:bottom left;
  content:'';
}

// .5像素的左边框
.borderLeft(){
  position:absolute; top:0; left:0;
  width:1px; height:200%;
  background-color:@borderColor;
  transform:scale(0.5); transform-origin:top left;
  content:'';
}

// .5像素的右边框
.borderRight(){
  position:absolute; top:0; right:0;
  width:1px; height:200%;
  background-color:@borderColor;
  transform:scale(0.5); transform-origin:top right;
  content:'';
}

