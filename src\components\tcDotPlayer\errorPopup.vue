<template>
  <div v-show="show" class="popup">
    <div class="popup-mask"></div>
    <div class="popup-content">
      <p class="title">哎呀，网络出错了</p>
      <p class="tip">（点击下方按钮,继续播放）</p>
      <div class="button" @click="switchLine"><span>切换线路</span></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 父组件的上下文this
    parentCom: {
      type: Object,
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    // 切换线路
    switchLine() {
      this.parentCom.switchLine();
    },
  },
};
</script>

<style lang="less" scoped>
/***
  * 1.弹框层组件不要使用rem或者vw
  * 2.如果弹框层组件使用rem和vw, 在全屏模式下, 文字和样式会特别大, 因为rem和vw是根据视宽来自动变化的, 当横屏的时候, 视宽很大, 自然样式和字体也变大了
  * 3.可以使用px或者em, 根据播放器的父元素使用em, 全屏就不会变得很大了
  */
.popup {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 98;
  .popup-mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 1;
  }
  .popup-content {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    .title {
      font-size: 1.4em;
      line-height: 2em;
      font-weight: bold;
      color: #ffffff;
    }
    .tip {
      font-size: 1.2em;
      line-height: 1.6em;
      margin: 0.4em 0 1.6em;
      color: #a9a9a9;
    }
    .button {
      width: 10.2em;
      height: 3.2em;
      line-height: 3.2em;
      border-radius: 1.6em;
      text-align: center;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      font-weight: bold;
      color: #ffffff;
      span {
        font-size: 1.4em;
      }
    }
  }
}
</style>
