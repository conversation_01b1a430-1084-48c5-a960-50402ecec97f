<template>
  <div class="children-page">
    <top-bar title="选择收货地址" >
      <router-link :to="{name:'addressEdit',query:{onlyGD:onlyGD}}" slot="rt" :style="{'padding-right':'0.1rem'}">添加</router-link>
    </top-bar>
    <div class="inner">
      <div class="addressNone txt-c" v-if="!hasAddress">
        <img src="../assets/image/addressNone.png" alt="">
        <p>您还没有添加收货地址</p>
      </div>
      <template v-else>
        <div class="address-list" v-if="!isLoading">
          <div class="hd">请选择您的收货地址：</div>
          <div class="bd bc-w">
            <div class="item"
                 v-for="item in addresses"
                 :key="item.saId"
                 :class="{active:selectedAddress.saId===item.saId}"
                 @click="selected(item)">
              <div class="lf"><i class="icons i-selected"></i></div>
              <div class="mid">
                <div class="name">{{item.saName}} {{item.mobile}}</div>
                <div class="addr">
                  {{item.provinceCode | provinceName}}{{item.cityCode | cityName(item.provinceCode)}}{{item.districtCode | districtName(item.provinceCode, item.cityCode)}}{{item.address}}
                </div>
              </div>
              <div class="rt">
                <router-link class="btn-edit" :to="{name:'addressEdit',query:{saId:item.saId,onlyGD:onlyGD}}"></router-link>
              </div>
            </div>
          </div>
        </div>
        <load-bar :isLoading="isLoading" :allLoaded="allLoaded"/>
        <foot-bar :title="buttonText" v-on:submit="submit"></foot-bar>
      </template>
    </div>
  </div>
</template>

<script>
  import {provinceName, cityName, districtName} from '../common'
  import topBar from '@/components/topBar';
  import footBar from '@/components/footBar';
  import loadBar from '@/components/loadBar';

  export default {
    filters: {provinceName, cityName, districtName},
    props: {
      addressId: null,
      backType:{type: String, default: 'back'},
      buttonText: {type: String, default: '提交'},
      onlyGD:{type: String, default: '0'},
    },
    data() {
      return {
        hasAddress: true,
        addresses: [],
        selectedAddress: {},
        isLoading: true,
        allLoaded: false,
        saName:''
      }
    },
    created(){
      this.getAddress();
      this.saName=this.storage.getItem('realName')||''
    },
    methods: {
      previous:function () {
        if(this.backType=='backToProve'){

        }
      },
      getAddress() {
        this.$http.post('/us/myAddress/1.0/',{saType :'1'}).then(res => {
          this.isLoading = false;
          if (res.code !== '00') return;

          this.addresses = res.body || [];
          this.selectedAddress = this.addresses.find(val => {
            if (this.addressId) {
              return val.saId === this.addressId;
            } else {
              return val.isDefault === '1';
            }
          }) || this.addresses[0];
          this.hasAddress = this.addresses.length > 0;
          // console.log(this.addresses);
        });
      },
      selected: function (item) {
        this.selectedAddress = item;
      },
      submit: function () {
        this.$emit('selected', this.selectedAddress);
      }
    },
    components: {topBar, footBar, loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .addressNone{
    img{ width:1.1rem; margin-top:1.6rem; }
    p{ margin-top:.18rem; color:#999; }
  }
  .address-list{
    .hd{ padding:.12rem .12rem .02rem; color:#999; font-size:.12rem; }
    .item{
      display:flex; align-items:center; position:relative; padding:.22rem 0 .22rem .12rem;
      &:after{ .borderBottom }
    }
    .mid{ flex:1; padding-left:.06rem; }
    .lf,
    .rt{ font-size:0; }
    .addr{ padding-top:.03rem; color:#666; font-size:.13rem; }
    .btn-edit{ display:block; width:.36rem; height:.36rem; background:url(../assets/image/public_ico_open_right.png) no-repeat 0 0; background-size:100% 100%; }
    .i-selected{ width:.16rem; height:.16rem; background-image:url(../assets/image/i-unselected.png); }

    .active{
      .i-selected{ background-image:url(../assets/image/i-selected.png); }
    }
  }
</style>
