!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var n=e();for(var r in n)("object"==typeof exports?exports:t)[r]=n[r]}}(window,function(){return r={},i.m=n=[function(kn,Cn,t){var Sn,e,n;e="undefined"!=typeof window?window:this,n=function(E,t){function e(t,e){return e.toUpperCase()}var p=[],d=E.document,l=p.slice,g=p.concat,a=p.push,i=p.indexOf,n={},r=n.toString,v=n.hasOwnProperty,y={},o="1.12.4",k=function(t,e){return new k.fn.init(t,e)},s=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,c=/^-ms-/,u=/-([\da-z])/gi;function h(t){var e=!!t&&"length"in t&&t.length,n=k.type(t);return"function"!==n&&!k.isWindow(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}k.fn=k.prototype={"jquery":o,"constructor":k,"selector":"","length":0,"toArray":function(){return l.call(this)},"get":function(t){return null!=t?t<0?this[t+this.length]:this[t]:l.call(this)},"pushStack":function(t){var e=k.merge(this.constructor(),t);return e.prevObject=this,e.context=this.context,e},"each":function(t){return k.each(this,t)},"map":function(n){return this.pushStack(k.map(this,function(t,e){return n.call(t,e,t)}))},"slice":function(){return this.pushStack(l.apply(this,arguments))},"first":function(){return this.eq(0)},"last":function(){return this.eq(-1)},"eq":function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(0<=n&&n<e?[this[n]]:[])},"end":function(){return this.prevObject||this.constructor()},"push":a,"sort":p.sort,"splice":p.splice},k.extend=k.fn.extend=function(){var t,e,n,r,i,o,s=arguments[0]||{},a=1,c=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[a]||{},a++),"object"==typeof s||k.isFunction(s)||(s={}),a===c&&(s=this,a--);a<c;a++)if(null!=(i=arguments[a]))for(r in i)t=s[r],s!==(n=i[r])&&(u&&n&&(k.isPlainObject(n)||(e=k.isArray(n)))?(o=e?(e=!1,t&&k.isArray(t)?t:[]):t&&k.isPlainObject(t)?t:{},s[r]=k.extend(u,o,n)):void 0!==n&&(s[r]=n));return s},k.extend({"expando":"jQuery"+(o+Math.random()).replace(/\D/g,""),"isReady":!0,"error":function(t){throw new Error(t)},"noop":function(){},"isFunction":function(t){return"function"===k.type(t)},"isArray":Array.isArray||function(t){return"array"===k.type(t)},"isWindow":function(t){return null!=t&&t==t.window},"isNumeric":function(t){var e=t&&t.toString();return!k.isArray(t)&&0<=e-parseFloat(e)+1},"isEmptyObject":function(t){var e;for(e in t)return!1;return!0},"isPlainObject":function(t){var e;if(!t||"object"!==k.type(t)||t.nodeType||k.isWindow(t))return!1;try{if(t.constructor&&!v.call(t,"constructor")&&!v.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}if(!y.ownFirst)for(e in t)return v.call(t,e);for(e in t);return void 0===e||v.call(t,e)},"type":function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?n[r.call(t)]||"object":typeof t},"globalEval":function(t){t&&k.trim(t)&&(E.execScript||function(t){E["eval"].call(E,t)})(t)},"camelCase":function(t){return t.replace(c,"ms-").replace(u,e)},"nodeName":function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},"each":function(t,e){var n,r=0;if(h(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},"trim":function(t){return null==t?"":(t+"").replace(s,"")},"makeArray":function(t,e){var n=e||[];return null!=t&&(h(Object(t))?k.merge(n,"string"==typeof t?[t]:t):a.call(n,t)),n},"inArray":function(t,e,n){var r;if(e){if(i)return i.call(e,t,n);for(r=e.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in e&&e[n]===t)return n}return-1},"merge":function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;)t[i++]=e[r++];if(n!=n)for(;void 0!==e[r];)t[i++]=e[r++];return t.length=i,t},"grep":function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!=s&&r.push(t[i]);return r},"map":function(t,e,n){var r,i,o=0,s=[];if(h(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&s.push(i);else for(o in t)null!=(i=e(t[o],o,n))&&s.push(i);return g.apply([],s)},"guid":1,"proxy":function(t,e){var n,r,i;if("string"==typeof e&&(i=t[e],e=t,t=i),k.isFunction(t))return n=l.call(arguments,2),(r=function(){return t.apply(e||this,n.concat(l.call(arguments)))}).guid=t.guid=t.guid||k.guid++,r},"now":function(){return+new Date},"support":y}),"function"==typeof Symbol&&(k.fn[Symbol.iterator]=p[Symbol.iterator]),k.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){n["[object "+e+"]"]=e.toLowerCase()});var f=function(n){function p(t,e,n){var r="0x"+e-65536;return r!=r||n?e:r<0?String.fromCharCode(65536+r):String.fromCharCode(r>>10|55296,1023&r|56320)}function i(){T()}var t,d,w,o,s,g,h,v,x,c,u,T,E,a,k,y,l,f,m,C="sizzle"+1*new Date,b=n.document,S=0,r=0,A=it(),N=it(),O=it(),_=function(t,e){return t===e&&(u=!0),0},I={}.hasOwnProperty,e=[],R=e.pop,L=e.push,D=e.push,M=e.slice,j=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},P="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",F="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",U="\\["+B+"*("+F+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+B+"*\\]",H=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+U+")*)|.*)\\)|)",q=new RegExp(B+"+","g"),W=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),z=new RegExp("^"+B+"*,"+B+"*"),G=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),Y=new RegExp("="+B+"*([^\\]'\"]*?)"+B+"*\\]","g"),V=new RegExp(H),Q=new RegExp("^"+F+"$"),$={"ID":new RegExp("^#("+F+")"),"CLASS":new RegExp("^\\.("+F+")"),"TAG":new RegExp("^("+F+"|[*])"),"ATTR":new RegExp("^"+U),"PSEUDO":new RegExp("^"+H),"CHILD":new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),"bool":new RegExp("^(?:"+P+")$","i"),"needsContext":new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,K=/^h\d$/i,J=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=/'|\\/g,nt=new RegExp("\\\\([\\da-f]{1,6}"+B+"?|("+B+")|.)","ig");try{D.apply(e=M.call(b.childNodes),b.childNodes),e[b.childNodes.length].nodeType}catch(t){D={"apply":e.length?function(t,e){L.apply(t,M.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function rt(t,e,n,r){var i,o,s,a,c,u,l,p,h=e&&e.ownerDocument,f=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==f&&9!==f&&11!==f)return n;if(!r&&((e?e.ownerDocument||e:b)!==E&&T(e),e=e||E,k)){if(11!==f&&(u=Z.exec(t)))if(i=u[1]){if(9===f){if(!(s=e.getElementById(i)))return n;if(s.id===i)return n.push(s),n}else if(h&&(s=h.getElementById(i))&&m(e,s)&&s.id===i)return n.push(s),n}else{if(u[2])return D.apply(n,e.getElementsByTagName(t)),n;if((i=u[3])&&d.getElementsByClassName&&e.getElementsByClassName)return D.apply(n,e.getElementsByClassName(i)),n}if(d.qsa&&!O[t+" "]&&(!y||!y.test(t))){if(1!==f)h=e,p=t;else if("object"!==e.nodeName.toLowerCase()){for((a=e.getAttribute("id"))?a=a.replace(et,"\\$&"):e.setAttribute("id",a=C),o=(l=g(t)).length,c=Q.test(a)?"#"+a:"[id='"+a+"']";o--;)l[o]=c+" "+dt(l[o]);p=l.join(","),h=tt.test(t)&&ht(e.parentNode)||e}if(p)try{return D.apply(n,h.querySelectorAll(p)),n}catch(t){}finally{a===C&&e.removeAttribute("id")}}}return v(t.replace(W,"$1"),e,n,r)}function it(){var r=[];return function t(e,n){return r.push(e+" ")>w.cacheLength&&delete t[r.shift()],t[e+" "]=n}}function ot(t){return t[C]=!0,t}function st(t){var e=E.createElement("div");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function at(t,e){for(var n=t.split("|"),r=n.length;r--;)w.attrHandle[n[r]]=e}function ct(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||1<<31)-(~t.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function ut(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function lt(n){return function(t){var e=t.nodeName.toLowerCase();return("input"===e||"button"===e)&&t.type===n}}function pt(s){return ot(function(o){return o=+o,ot(function(t,e){for(var n,r=s([],t.length,o),i=r.length;i--;)t[n=r[i]]&&(t[n]=!(e[n]=t[n]))})})}function ht(t){return t&&void 0!==t.getElementsByTagName&&t}for(t in d=rt.support={},s=rt.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return!!e&&"HTML"!==e.nodeName},T=rt.setDocument=function(t){var e,n,r=t?t.ownerDocument||t:b;return r!==E&&9===r.nodeType&&r.documentElement&&(a=(E=r).documentElement,k=!s(E),(n=E.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",i,!1):n.attachEvent&&n.attachEvent("onunload",i)),d.attributes=st(function(t){return t.className="i",!t.getAttribute("className")}),d.getElementsByTagName=st(function(t){return t.appendChild(E.createComment("")),!t.getElementsByTagName("*").length}),d.getElementsByClassName=J.test(E.getElementsByClassName),d.getById=st(function(t){return a.appendChild(t).id=C,!E.getElementsByName||!E.getElementsByName(C).length}),d.getById?(w.find["ID"]=function(t,e){if(void 0!==e.getElementById&&k){var n=e.getElementById(t);return n?[n]:[]}},w.filter["ID"]=function(t){var e=t.replace(nt,p);return function(t){return t.getAttribute("id")===e}}):(delete w.find["ID"],w.filter["ID"]=function(t){var n=t.replace(nt,p);return function(t){var e=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return e&&e.value===n}}),w.find["TAG"]=d.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):d.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"!==t)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},w.find["CLASS"]=d.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&k)return e.getElementsByClassName(t)},l=[],y=[],(d.qsa=J.test(E.querySelectorAll))&&(st(function(t){a.appendChild(t).innerHTML="<a id='"+C+"'></a><select id='"+C+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&y.push("[*^$]="+B+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||y.push("\\["+B+"*(?:value|"+P+")"),t.querySelectorAll("[id~="+C+"-]").length||y.push("~="),t.querySelectorAll(":checked").length||y.push(":checked"),t.querySelectorAll("a#"+C+"+*").length||y.push(".#.+[+~]")}),st(function(t){var e=E.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&y.push("name"+B+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||y.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),y.push(",.*:")})),(d.matchesSelector=J.test(f=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&st(function(t){d.disconnectedMatch=f.call(t,"div"),f.call(t,"[s!='']:x"),l.push("!=",H)}),y=y.length&&new RegExp(y.join("|")),l=l.length&&new RegExp(l.join("|")),e=J.test(a.compareDocumentPosition),m=e||J.test(a.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,r=e&&e.parentNode;return t===r||!(!r||1!==r.nodeType||!(n.contains?n.contains(r):t.compareDocumentPosition&&16&t.compareDocumentPosition(r)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},_=e?function(t,e){if(t===e)return u=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!d.sortDetached&&e.compareDocumentPosition(t)===n?t===E||t.ownerDocument===b&&m(b,t)?-1:e===E||e.ownerDocument===b&&m(b,e)?1:c?j(c,t)-j(c,e):0:4&n?-1:1)}:function(t,e){if(t===e)return u=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,s=[t],a=[e];if(!i||!o)return t===E?-1:e===E?1:i?-1:o?1:c?j(c,t)-j(c,e):0;if(i===o)return ct(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?ct(s[r],a[r]):s[r]===b?-1:a[r]===b?1:0}),E},rt.matches=function(t,e){return rt(t,null,null,e)},rt.matchesSelector=function(t,e){if((t.ownerDocument||t)!==E&&T(t),e=e.replace(Y,"='$1']"),d.matchesSelector&&k&&!O[e+" "]&&(!l||!l.test(e))&&(!y||!y.test(e)))try{var n=f.call(t,e);if(n||d.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){}return 0<rt(e,E,null,[t]).length},rt.contains=function(t,e){return(t.ownerDocument||t)!==E&&T(t),m(t,e)},rt.attr=function(t,e){(t.ownerDocument||t)!==E&&T(t);var n=w.attrHandle[e.toLowerCase()],r=n&&I.call(w.attrHandle,e.toLowerCase())?n(t,e,!k):void 0;return void 0!==r?r:d.attributes||!k?t.getAttribute(e):(r=t.getAttributeNode(e))&&r.specified?r.value:null},rt.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},rt.uniqueSort=function(t){var e,n=[],r=0,i=0;if(u=!d.detectDuplicates,c=!d.sortStable&&t.slice(0),t.sort(_),u){for(;e=t[i++];)e===t[i]&&(r=n.push(i));for(;r--;)t.splice(n[r],1)}return c=null,t},o=rt.getText=function(t){var e,n="",r=0,i=t.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=o(t)}else if(3===i||4===i)return t.nodeValue}else for(;e=t[r++];)n+=o(e);return n},(w=rt.selectors={"cacheLength":50,"createPseudo":ot,"match":$,"attrHandle":{},"find":{},"relative":{">":{"dir":"parentNode","first":!0}," ":{"dir":"parentNode"},"+":{"dir":"previousSibling","first":!0},"~":{"dir":"previousSibling"}},"preFilter":{"ATTR":function(t){return t[1]=t[1].replace(nt,p),t[3]=(t[3]||t[4]||t[5]||"").replace(nt,p),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},"CHILD":function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||rt.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&rt.error(t[0]),t},"PSEUDO":function(t){var e,n=!t[6]&&t[2];return $["CHILD"].test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&V.test(n)&&(e=g(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},"filter":{"TAG":function(t){var e=t.replace(nt,p).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},"CLASS":function(t){var e=A[t+" "];return e||(e=new RegExp("(^|"+B+")"+t+"("+B+"|$)"))&&A(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},"ATTR":function(n,r,i){return function(t){var e=rt.attr(t,n);return null==e?"!="===r:!r||(e+="","="===r?e===i:"!="===r?e!==i:"^="===r?i&&0===e.indexOf(i):"*="===r?i&&-1<e.indexOf(i):"$="===r?i&&e.slice(-i.length)===i:"~="===r?-1<(" "+e.replace(q," ")+" ").indexOf(i):"|="===r&&(e===i||e.slice(0,i.length+1)===i+"-"))}},"CHILD":function(d,t,e,g,v){var y="nth"!==d.slice(0,3),m="last"!==d.slice(-4),b="of-type"===t;return 1===g&&0===v?function(t){return!!t.parentNode}:function(t,e,n){var r,i,o,s,a,c,u=y!=m?"nextSibling":"previousSibling",l=t.parentNode,p=b&&t.nodeName.toLowerCase(),h=!n&&!b,f=!1;if(l){if(y){for(;u;){for(s=t;s=s[u];)if(b?s.nodeName.toLowerCase()===p:1===s.nodeType)return!1;c=u="only"===d&&!c&&"nextSibling"}return!0}if(c=[m?l.firstChild:l.lastChild],m&&h){for(f=(a=(r=(i=(o=(s=l)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===S&&r[1])&&r[2],s=a&&l.childNodes[a];s=++a&&s&&s[u]||(f=a=0)||c.pop();)if(1===s.nodeType&&++f&&s===t){i[d]=[S,a,f];break}}else if(h&&(f=a=(r=(i=(o=(s=t)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]||[])[0]===S&&r[1]),!1===f)for(;(s=++a&&s&&s[u]||(f=a=0)||c.pop())&&((b?s.nodeName.toLowerCase()!==p:1!==s.nodeType)||!++f||(h&&((i=(o=s[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[d]=[S,f]),s!==t)););return(f-=v)===g||f%g==0&&0<=f/g}}},"PSEUDO":function(t,o){var e,s=w.pseudos[t]||w.setFilters[t.toLowerCase()]||rt.error("unsupported pseudo: "+t);return s[C]?s(o):1<s.length?(e=[t,t,"",o],w.setFilters.hasOwnProperty(t.toLowerCase())?ot(function(t,e){for(var n,r=s(t,o),i=r.length;i--;)t[n=j(t,r[i])]=!(e[n]=r[i])}):function(t){return s(t,0,e)}):s}},"pseudos":{"not":ot(function(t){var r=[],i=[],a=h(t.replace(W,"$1"));return a[C]?ot(function(t,e,n,r){for(var i,o=a(t,null,r,[]),s=t.length;s--;)(i=o[s])&&(t[s]=!(e[s]=i))}):function(t,e,n){return r[0]=t,a(r,null,n,i),r[0]=null,!i.pop()}}),"has":ot(function(e){return function(t){return 0<rt(e,t).length}}),"contains":ot(function(e){return e=e.replace(nt,p),function(t){return-1<(t.textContent||t.innerText||o(t)).indexOf(e)}}),"lang":ot(function(n){return Q.test(n||"")||rt.error("unsupported lang: "+n),n=n.replace(nt,p).toLowerCase(),function(t){var e;do{if(e=k?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(e=e.toLowerCase())===n||0===e.indexOf(n+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),"target":function(t){var e=n.location&&n.location.hash;return e&&e.slice(1)===t.id},"root":function(t){return t===a},"focus":function(t){return t===E.activeElement&&(!E.hasFocus||E.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},"enabled":function(t){return!1===t.disabled},"disabled":function(t){return!0===t.disabled},"checked":function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},"selected":function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},"empty":function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},"parent":function(t){return!w.pseudos["empty"](t)},"header":function(t){return K.test(t.nodeName)},"input":function(t){return X.test(t.nodeName)},"button":function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},"text":function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},"first":pt(function(){return[0]}),"last":pt(function(t,e){return[e-1]}),"eq":pt(function(t,e,n){return[n<0?n+e:n]}),"even":pt(function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t}),"odd":pt(function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t}),"lt":pt(function(t,e,n){for(var r=n<0?n+e:n;0<=--r;)t.push(r);return t}),"gt":pt(function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t})}}).pseudos["nth"]=w.pseudos["eq"],{"radio":!0,"checkbox":!0,"file":!0,"password":!0,"image":!0})w.pseudos[t]=ut(t);for(t in{"submit":!0,"reset":!0})w.pseudos[t]=lt(t);function ft(){}function dt(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function gt(a,t,e){var c=t.dir,u=e&&"parentNode"===c,l=r++;return t.first?function(t,e,n){for(;t=t[c];)if(1===t.nodeType||u)return a(t,e,n)}:function(t,e,n){var r,i,o,s=[S,l];if(n){for(;t=t[c];)if((1===t.nodeType||u)&&a(t,e,n))return!0}else for(;t=t[c];)if(1===t.nodeType||u){if((r=(i=(o=t[C]||(t[C]={}))[t.uniqueID]||(o[t.uniqueID]={}))[c])&&r[0]===S&&r[1]===l)return s[2]=r[2];if((i[c]=s)[2]=a(t,e,n))return!0}}}function vt(i){return 1<i.length?function(t,e,n){for(var r=i.length;r--;)if(!i[r](t,e,n))return!1;return!0}:i[0]}function yt(t,e,n,r,i){for(var o,s=[],a=0,c=t.length,u=null!=e;a<c;a++)(o=t[a])&&(n&&!n(o,r,i)||(s.push(o),u&&e.push(a)));return s}function mt(f,d,g,v,y,t){return v&&!v[C]&&(v=mt(v)),y&&!y[C]&&(y=mt(y,t)),ot(function(t,e,n,r){var i,o,s,a=[],c=[],u=e.length,l=t||function(t,e,n){for(var r=0,i=e.length;r<i;r++)rt(t,e[r],n);return n}(d||"*",n.nodeType?[n]:n,[]),p=!f||!t&&d?l:yt(l,a,f,n,r),h=g?y||(t?f:u||v)?[]:e:p;if(g&&g(p,h,n,r),v)for(i=yt(h,c),v(i,[],n,r),o=i.length;o--;)(s=i[o])&&(h[c[o]]=!(p[c[o]]=s));if(t){if(y||f){if(y){for(i=[],o=h.length;o--;)(s=h[o])&&i.push(p[o]=s);y(null,h=[],i,r)}for(o=h.length;o--;)(s=h[o])&&-1<(i=y?j(t,s):a[o])&&(t[i]=!(e[i]=s))}}else h=yt(h===e?h.splice(u,h.length):h),y?y(null,e,h,r):D.apply(e,h)})}function bt(t){for(var i,e,n,r=t.length,o=w.relative[t[0].type],s=o||w.relative[" "],a=o?1:0,c=gt(function(t){return t===i},s,!0),u=gt(function(t){return-1<j(i,t)},s,!0),l=[function(t,e,n){var r=!o&&(n||e!==x)||((i=e).nodeType?c(t,e,n):u(t,e,n));return i=null,r}];a<r;a++)if(e=w.relative[t[a].type])l=[gt(vt(l),e)];else{if((e=w.filter[t[a].type].apply(null,t[a].matches))[C]){for(n=++a;n<r&&!w.relative[t[n].type];n++);return mt(1<a&&vt(l),1<a&&dt(t.slice(0,a-1).concat({"value":" "===t[a-2].type?"*":""})).replace(W,"$1"),e,a<n&&bt(t.slice(a,n)),n<r&&bt(t=t.slice(n)),n<r&&dt(t))}l.push(e)}return vt(l)}return ft.prototype=w.filters=w.pseudos,w.setFilters=new ft,g=rt.tokenize=function(t,e){var n,r,i,o,s,a,c,u=N[t+" "];if(u)return e?0:u.slice(0);for(s=t,a=[],c=w.preFilter;s;){for(o in n&&!(r=z.exec(s))||(r&&(s=s.slice(r[0].length)||s),a.push(i=[])),n=!1,(r=G.exec(s))&&(n=r.shift(),i.push({"value":n,"type":r[0].replace(W," ")}),s=s.slice(n.length)),w.filter)!(r=$[o].exec(s))||c[o]&&!(r=c[o](r))||(n=r.shift(),i.push({"value":n,"type":o,"matches":r}),s=s.slice(n.length));if(!n)break}return e?s.length:s?rt.error(t):N(t,a).slice(0)},h=rt.compile=function(t,e){var n,r=[],i=[],o=O[t+" "];if(!o){for(n=(e=e||g(t)).length;n--;)(o=bt(e[n]))[C]?r.push(o):i.push(o);(o=O(t,function(v,y){function t(t,e,n,r,i){var o,s,a,c=0,u="0",l=t&&[],p=[],h=x,f=t||b&&w.find["TAG"]("*",i),d=S+=null==h?1:Math.random()||.1,g=f.length;for(i&&(x=e===E||e||i);u!==g&&null!=(o=f[u]);u++){if(b&&o){for(s=0,e||o.ownerDocument===E||(T(o),n=!k);a=v[s++];)if(a(o,e||E,n)){r.push(o);break}i&&(S=d)}m&&((o=!a&&o)&&c--,t&&l.push(o))}if(c+=u,m&&u!==c){for(s=0;a=y[s++];)a(l,p,e,n);if(t){if(0<c)for(;u--;)l[u]||p[u]||(p[u]=R.call(r));p=yt(p)}D.apply(r,p),i&&!t&&0<p.length&&1<c+y.length&&rt.uniqueSort(r)}return i&&(S=d,x=h),l}var m=0<y.length,b=0<v.length;return m?ot(t):t}(i,r))).selector=t}return o},v=rt.select=function(t,e,n,r){var i,o,s,a,c,u="function"==typeof t&&t,l=!r&&g(t=u.selector||t);if(n=n||[],1===l.length){if(2<(o=l[0]=l[0].slice(0)).length&&"ID"===(s=o[0]).type&&d.getById&&9===e.nodeType&&k&&w.relative[o[1].type]){if(!(e=(w.find["ID"](s.matches[0].replace(nt,p),e)||[])[0]))return n;u&&(e=e.parentNode),t=t.slice(o.shift().value.length)}for(i=$["needsContext"].test(t)?0:o.length;i--&&(s=o[i],!w.relative[a=s.type]);)if((c=w.find[a])&&(r=c(s.matches[0].replace(nt,p),tt.test(o[0].type)&&ht(e.parentNode)||e))){if(o.splice(i,1),!(t=r.length&&dt(o)))return D.apply(n,r),n;break}}return(u||h(t,l))(r,e,!k,n,!e||tt.test(t)&&ht(e.parentNode)||e),n},d.sortStable=C.split("").sort(_).join("")===C,d.detectDuplicates=!!u,T(),d.sortDetached=st(function(t){return 1&t.compareDocumentPosition(E.createElement("div"))}),st(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||at("type|href|height|width",function(t,e,n){if(!n)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),d.attributes&&st(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||at("value",function(t,e,n){if(!n&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),st(function(t){return null==t.getAttribute("disabled")})||at(P,function(t,e,n){var r;if(!n)return!0===t[e]?e.toLowerCase():(r=t.getAttributeNode(e))&&r.specified?r.value:null}),rt}(E);k.find=f,k.expr=f.selectors,k.expr[":"]=k.expr.pseudos,k.uniqueSort=k.unique=f.uniqueSort,k.text=f.getText,k.isXMLDoc=f.isXML,k.contains=f.contains;function m(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&k(t).is(n))break;r.push(t)}return r}function b(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n}var w=k.expr.match.needsContext,x=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,T=/^.[^:#\[\.,]*$/;function C(t,n,r){if(k.isFunction(n))return k.grep(t,function(t,e){return!!n.call(t,e,t)!==r});if(n.nodeType)return k.grep(t,function(t){return t===n!==r});if("string"==typeof n){if(T.test(n))return k.filter(n,t,r);n=k.filter(n,t)}return k.grep(t,function(t){return-1<k.inArray(t,n)!==r})}k.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?k.find.matchesSelector(r,t)?[r]:[]:k.find.matches(t,k.grep(e,function(t){return 1===t.nodeType}))},k.fn.extend({"find":function(t){var e,n=[],r=this,i=r.length;if("string"!=typeof t)return this.pushStack(k(t).filter(function(){for(e=0;e<i;e++)if(k.contains(r[e],this))return!0}));for(e=0;e<i;e++)k.find(t,r[e],n);return(n=this.pushStack(1<i?k.unique(n):n)).selector=this.selector?this.selector+" "+t:t,n},"filter":function(t){return this.pushStack(C(this,t||[],!1))},"not":function(t){return this.pushStack(C(this,t||[],!0))},"is":function(t){return!!C(this,"string"==typeof t&&w.test(t)?k(t):t||[],!1).length}});var S,A=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(k.fn.init=function(t,e,n){var r,i;if(!t)return this;if(n=n||S,"string"!=typeof t)return t.nodeType?(this.context=this[0]=t,this.length=1,this):k.isFunction(t)?void 0!==n.ready?n.ready(t):t(k):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),k.makeArray(t,this));if(!(r="<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&3<=t.length?[null,t,null]:A.exec(t))||!r[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(r[1]){if(e=e instanceof k?e[0]:e,k.merge(this,k.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:d,!0)),x.test(r[1])&&k.isPlainObject(e))for(r in e)k.isFunction(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}if((i=d.getElementById(r[2]))&&i.parentNode){if(i.id!==r[2])return S.find(t);this.length=1,this[0]=i}return this.context=d,this.selector=t,this}).prototype=k.fn,S=k(d);var N=/^(?:parents|prev(?:Until|All))/,O={"children":!0,"contents":!0,"next":!0,"prev":!0};function _(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}k.fn.extend({"has":function(t){var e,n=k(t,this),r=n.length;return this.filter(function(){for(e=0;e<r;e++)if(k.contains(this,n[e]))return!0})},"closest":function(t,e){for(var n,r=0,i=this.length,o=[],s=w.test(t)||"string"!=typeof t?k(t,e||this.context):0;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&k.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(1<o.length?k.uniqueSort(o):o)},"index":function(t){return t?"string"==typeof t?k.inArray(this[0],k(t)):k.inArray(t.jquery?t[0]:t,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},"add":function(t,e){return this.pushStack(k.uniqueSort(k.merge(this.get(),k(t,e))))},"addBack":function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),k.each({"parent":function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},"parents":function(t){return m(t,"parentNode")},"parentsUntil":function(t,e,n){return m(t,"parentNode",n)},"next":function(t){return _(t,"nextSibling")},"prev":function(t){return _(t,"previousSibling")},"nextAll":function(t){return m(t,"nextSibling")},"prevAll":function(t){return m(t,"previousSibling")},"nextUntil":function(t,e,n){return m(t,"nextSibling",n)},"prevUntil":function(t,e,n){return m(t,"previousSibling",n)},"siblings":function(t){return b((t.parentNode||{}).firstChild,t)},"children":function(t){return b(t.firstChild)},"contents":function(t){return k.nodeName(t,"iframe")?t.contentDocument||t.contentWindow.document:k.merge([],t.childNodes)}},function(r,i){k.fn[r]=function(t,e){var n=k.map(this,i,t);return"Until"!==r.slice(-5)&&(e=t),e&&"string"==typeof e&&(n=k.filter(e,n)),1<this.length&&(O[r]||(n=k.uniqueSort(n)),N.test(r)&&(n=n.reverse())),this.pushStack(n)}});var I,R,L=/\S+/g;function D(){d.addEventListener?(d.removeEventListener("DOMContentLoaded",M),E.removeEventListener("load",M)):(d.detachEvent("onreadystatechange",M),E.detachEvent("onload",M))}function M(){!d.addEventListener&&"load"!==E.event.type&&"complete"!==d.readyState||(D(),k.ready())}for(R in k.Callbacks=function(r){r="string"==typeof r?function(t){var n={};return k.each(t.match(L)||[],function(t,e){n[e]=!0}),n}(r):k.extend({},r);function n(){for(o=r.once,e=i=!0;a.length;c=-1)for(t=a.shift();++c<s.length;)!1===s[c].apply(t[0],t[1])&&r.stopOnFalse&&(c=s.length,t=!1);r.memory||(t=!1),i=!1,o&&(s=t?[]:"")}var i,t,e,o,s=[],a=[],c=-1,u={"add":function(){return s&&(t&&!i&&(c=s.length-1,a.push(t)),function n(t){k.each(t,function(t,e){k.isFunction(e)?r.unique&&u.has(e)||s.push(e):e&&e.length&&"string"!==k.type(e)&&n(e)})}(arguments),t&&!i&&n()),this},"remove":function(){return k.each(arguments,function(t,e){for(var n;-1<(n=k.inArray(e,s,n));)s.splice(n,1),n<=c&&c--}),this},"has":function(t){return t?-1<k.inArray(t,s):0<s.length},"empty":function(){return s=s&&[],this},"disable":function(){return o=a=[],s=t="",this},"disabled":function(){return!s},"lock":function(){return o=!0,t||u.disable(),this},"locked":function(){return!!o},"fireWith":function(t,e){return o||(e=[t,(e=e||[]).slice?e.slice():e],a.push(e),i||n()),this},"fire":function(){return u.fireWith(this,arguments),this},"fired":function(){return!!e}};return u},k.extend({"Deferred":function(t){var o=[["resolve","done",k.Callbacks("once memory"),"resolved"],["reject","fail",k.Callbacks("once memory"),"rejected"],["notify","progress",k.Callbacks("memory")]],i="pending",s={"state":function(){return i},"always":function(){return a.done(arguments).fail(arguments),this},"then":function(){var i=arguments;return k.Deferred(function(r){k.each(o,function(t,e){var n=k.isFunction(i[t])&&i[t];a[e[1]](function(){var t=n&&n.apply(this,arguments);t&&k.isFunction(t.promise)?t.promise().progress(r.notify).done(r.resolve).fail(r.reject):r[e[0]+"With"](this===s?r.promise():this,n?[t]:arguments)})}),i=null}).promise()},"promise":function(t){return null!=t?k.extend(t,s):s}},a={};return s.pipe=s.then,k.each(o,function(t,e){var n=e[2],r=e[3];s[e[1]]=n.add,r&&n.add(function(){i=r},o[1^t][2].disable,o[2][2].lock),a[e[0]]=function(){return a[e[0]+"With"](this===a?s:this,arguments),this},a[e[0]+"With"]=n.fireWith}),s.promise(a),t&&t.call(a,a),a},"when":function(t){function e(e,n,r){return function(t){n[e]=this,r[e]=1<arguments.length?l.call(arguments):t,r===i?u.notifyWith(n,r):--c||u.resolveWith(n,r)}}var i,n,r,o=0,s=l.call(arguments),a=s.length,c=1!==a||t&&k.isFunction(t.promise)?a:0,u=1===c?t:k.Deferred();if(1<a)for(i=new Array(a),n=new Array(a),r=new Array(a);o<a;o++)s[o]&&k.isFunction(s[o].promise)?s[o].promise().progress(e(o,n,i)).done(e(o,r,s)).fail(u.reject):--c;return c||u.resolveWith(r,s),u.promise()}}),k.fn.ready=function(t){return k.ready.promise().done(t),this},k.extend({"isReady":!1,"readyWait":1,"holdReady":function(t){t?k.readyWait++:k.ready(!0)},"ready":function(t){(!0===t?--k.readyWait:k.isReady)||(k.isReady=!0)!==t&&0<--k.readyWait||(I.resolveWith(d,[k]),k.fn.triggerHandler&&(k(d).triggerHandler("ready"),k(d).off("ready")))}}),k.ready.promise=function(t){if(!I)if(I=k.Deferred(),"complete"===d.readyState||"loading"!==d.readyState&&!d.documentElement.doScroll)E.setTimeout(k.ready);else if(d.addEventListener)d.addEventListener("DOMContentLoaded",M),E.addEventListener("load",M);else{d.attachEvent("onreadystatechange",M),E.attachEvent("onload",M);var n=!1;try{n=null==E.frameElement&&d.documentElement}catch(t){}n&&n.doScroll&&!function e(){if(!k.isReady){try{n.doScroll("left")}catch(t){return E.setTimeout(e,50)}D(),k.ready()}}()}return I.promise(t)},k.ready.promise(),k(y))break;y.ownFirst="0"===R,y.inlineBlockNeedsLayout=!1,k(function(){var t,e,n,r;(n=d.getElementsByTagName("body")[0])&&n.style&&(e=d.createElement("div"),(r=d.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(r).appendChild(e),void 0!==e.style.zoom&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",y.inlineBlockNeedsLayout=t=3===e.offsetWidth,t&&(n.style.zoom=1)),n.removeChild(r))}),function(){var t=d.createElement("div");y.deleteExpando=!0;try{delete t.test}catch(t){y.deleteExpando=!1}t=null}();function j(t){var e=k.noData[(t.nodeName+" ").toLowerCase()],n=+t.nodeType||1;return(1===n||9===n)&&(!e||!0!==e&&t.getAttribute("classid")===e)}var P,B=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,F=/([A-Z])/g;function U(t,e,n){if(void 0===n&&1===t.nodeType){var r="data-"+e.replace(F,"-$1").toLowerCase();if("string"==typeof(n=t.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:B.test(n)?k.parseJSON(n):n)}catch(t){}k.data(t,e,n)}else n=void 0}return n}function H(t){var e;for(e in t)if(("data"!==e||!k.isEmptyObject(t[e]))&&"toJSON"!==e)return!1;return!0}function q(t,e,n,r){if(j(t)){var i,o,s=k.expando,a=t.nodeType,c=a?k.cache:t,u=a?t[s]:t[s]&&s;if(u&&c[u]&&(r||c[u].data)||void 0!==n||"string"!=typeof e)return c[u=u||(a?t[s]=p.pop()||k.guid++:s)]||(c[u]=a?{}:{"toJSON":k.noop}),"object"!=typeof e&&"function"!=typeof e||(r?c[u]=k.extend(c[u],e):c[u].data=k.extend(c[u].data,e)),o=c[u],r||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[k.camelCase(e)]=n),"string"==typeof e?null==(i=o[e])&&(i=o[k.camelCase(e)]):i=o,i}}function W(t,e,n){if(j(t)){var r,i,o=t.nodeType,s=o?k.cache:t,a=o?t[k.expando]:k.expando;if(s[a]){if(e&&(r=n?s[a]:s[a].data)){i=(e=k.isArray(e)?e.concat(k.map(e,k.camelCase)):e in r?[e]:(e=k.camelCase(e))in r?[e]:e.split(" ")).length;for(;i--;)delete r[e[i]];if(n?!H(r):!k.isEmptyObject(r))return}(n||(delete s[a].data,H(s[a])))&&(o?k.cleanData([t],!0):y.deleteExpando||s!=s.window?delete s[a]:s[a]=void 0)}}}k.extend({"cache":{},"noData":{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},"hasData":function(t){return!!(t=t.nodeType?k.cache[t[k.expando]]:t[k.expando])&&!H(t)},"data":function(t,e,n){return q(t,e,n)},"removeData":function(t,e){return W(t,e)},"_data":function(t,e,n){return q(t,e,n,!0)},"_removeData":function(t,e){return W(t,e,!0)}}),k.fn.extend({"data":function(t,e){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0!==t)return"object"==typeof t?this.each(function(){k.data(this,t)}):1<arguments.length?this.each(function(){k.data(this,t,e)}):o?U(o,t,k.data(o,t)):void 0;if(this.length&&(i=k.data(o),1===o.nodeType&&!k._data(o,"parsedAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&U(o,r=k.camelCase(r.slice(5)),i[r]);k._data(o,"parsedAttrs",!0)}return i},"removeData":function(t){return this.each(function(){k.removeData(this,t)})}}),k.extend({"queue":function(t,e,n){var r;if(t)return e=(e||"fx")+"queue",r=k._data(t,e),n&&(!r||k.isArray(n)?r=k._data(t,e,k.makeArray(n)):r.push(n)),r||[]},"dequeue":function(t,e){e=e||"fx";var n=k.queue(t,e),r=n.length,i=n.shift(),o=k._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,function(){k.dequeue(t,e)},o)),!r&&o&&o.empty.fire()},"_queueHooks":function(t,e){var n=e+"queueHooks";return k._data(t,n)||k._data(t,n,{"empty":k.Callbacks("once memory").add(function(){k._removeData(t,e+"queue"),k._removeData(t,n)})})}}),k.fn.extend({"queue":function(e,n){var t=2;return"string"!=typeof e&&(n=e,e="fx",t--),arguments.length<t?k.queue(this[0],e):void 0===n?this:this.each(function(){var t=k.queue(this,e,n);k._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&k.dequeue(this,e)})},"dequeue":function(t){return this.each(function(){k.dequeue(this,t)})},"clearQueue":function(t){return this.queue(t||"fx",[])},"promise":function(t,e){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=k.Deferred(),s=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(r=k._data(s[a],t+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(e)}}),y.shrinkWrapBlocks=function(){return null!=P?P:(P=!1,(e=d.getElementsByTagName("body")[0])&&e.style?(t=d.createElement("div"),(n=d.createElement("div")).style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",e.appendChild(n).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(d.createElement("div")).style.width="5px",P=3!==t.offsetWidth),e.removeChild(n),P):void 0);var t,e,n};function z(t,e){return t=e||t,"none"===k.css(t,"display")||!k.contains(t.ownerDocument,t)}var G=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Y=new RegExp("^(?:([+-])=|)("+G+")([a-z%]*)$","i"),V=["Top","Right","Bottom","Left"];function Q(t,e,n,r){var i,o=1,s=20,a=r?function(){return r.cur()}:function(){return k.css(t,e,"")},c=a(),u=n&&n[3]||(k.cssNumber[e]?"":"px"),l=(k.cssNumber[e]||"px"!==u&&+c)&&Y.exec(k.css(t,e));if(l&&l[3]!==u)for(u=u||l[3],n=n||[],l=+c||1;l/=o=o||".5",k.style(t,e,l+u),o!==(o=a()/c)&&1!==o&&--s;);return n&&(l=+l||+c||0,i=n[1]?l+(n[1]+1)*n[2]:+n[2],r&&(r.unit=u,r.start=l,r.end=i)),i}var $,X,K,J=function(t,e,n,r,i,o,s){var a=0,c=t.length,u=null==n;if("object"===k.type(n))for(a in i=!0,n)J(t,e,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,k.isFunction(r)||(s=!0),u&&(e=s?(e.call(t,r),null):(u=e,function(t,e,n){return u.call(k(t),n)})),e))for(;a<c;a++)e(t[a],n,s?r:r.call(t[a],a,e(t[a],n)));return i?t:u?e.call(t):c?e(t[0],n):o},Z=/^(?:checkbox|radio)$/i,tt=/<([\w:-]+)/,et=/^$|\/(?:java|ecma)script/i,nt=/^\s+/,rt="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function it(t){var e=rt.split("|"),n=t.createDocumentFragment();if(n.createElement)for(;e.length;)n.createElement(e.pop());return n}$=d.createElement("div"),X=d.createDocumentFragment(),K=d.createElement("input"),$.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",y.leadingWhitespace=3===$.firstChild.nodeType,y.tbody=!$.getElementsByTagName("tbody").length,y.htmlSerialize=!!$.getElementsByTagName("link").length,y.html5Clone="<:nav></:nav>"!==d.createElement("nav").cloneNode(!0).outerHTML,K.type="checkbox",K.checked=!0,X.appendChild(K),y.appendChecked=K.checked,$.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!$.cloneNode(!0).lastChild.defaultValue,X.appendChild($),(K=d.createElement("input")).setAttribute("type","radio"),K.setAttribute("checked","checked"),K.setAttribute("name","t"),$.appendChild(K),y.checkClone=$.cloneNode(!0).cloneNode(!0).lastChild.checked,y.noCloneEvent=!!$.addEventListener,$[k.expando]=1,y.attributes=!$.getAttribute(k.expando);var ot={"option":[1,"<select multiple='multiple'>","</select>"],"legend":[1,"<fieldset>","</fieldset>"],"area":[1,"<map>","</map>"],"param":[1,"<object>","</object>"],"thead":[1,"<table>","</table>"],"tr":[2,"<table><tbody>","</tbody></table>"],"col":[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],"td":[3,"<table><tbody><tr>","</tr></tbody></table>"],"_default":y.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};function st(t,e){var n,r,i=0,o=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):void 0;if(!o)for(o=[],n=t.childNodes||t;null!=(r=n[i]);i++)!e||k.nodeName(r,e)?o.push(r):k.merge(o,st(r,e));return void 0===e||e&&k.nodeName(t,e)?k.merge([t],o):o}function at(t,e){for(var n,r=0;null!=(n=t[r]);r++)k._data(n,"globalEval",!e||k._data(e[r],"globalEval"))}ot.optgroup=ot.option,ot.tbody=ot.tfoot=ot.colgroup=ot.caption=ot.thead,ot.th=ot.td;var ct=/<|&#?\w+;/,ut=/<tbody/i;function lt(t){Z.test(t.type)&&(t.defaultChecked=t.checked)}function pt(t,e,n,r,i){for(var o,s,a,c,u,l,p,h=t.length,f=it(e),d=[],g=0;g<h;g++)if((s=t[g])||0===s)if("object"===k.type(s))k.merge(d,s.nodeType?[s]:s);else if(ct.test(s)){for(c=c||f.appendChild(e.createElement("div")),u=(tt.exec(s)||["",""])[1].toLowerCase(),p=ot[u]||ot._default,c.innerHTML=p[1]+k.htmlPrefilter(s)+p[2],o=p[0];o--;)c=c.lastChild;if(!y.leadingWhitespace&&nt.test(s)&&d.push(e.createTextNode(nt.exec(s)[0])),!y.tbody)for(o=(s="table"!==u||ut.test(s)?"<table>"!==p[1]||ut.test(s)?0:c:c.firstChild)&&s.childNodes.length;o--;)k.nodeName(l=s.childNodes[o],"tbody")&&!l.childNodes.length&&s.removeChild(l);for(k.merge(d,c.childNodes),c.textContent="";c.firstChild;)c.removeChild(c.firstChild);c=f.lastChild}else d.push(e.createTextNode(s));for(c&&f.removeChild(c),y.appendChecked||k.grep(st(d,"input"),lt),g=0;s=d[g++];)if(r&&-1<k.inArray(s,r))i&&i.push(s);else if(a=k.contains(s.ownerDocument,s),c=st(f.appendChild(s),"script"),a&&at(c),n)for(o=0;s=c[o++];)et.test(s.type||"")&&n.push(s);return c=null,f}!function(){var t,e,n=d.createElement("div");for(t in{"submit":!0,"change":!0,"focusin":!0})e="on"+t,(y[t]=e in E)||(n.setAttribute(e,"t"),y[t]=!1===n.attributes[e].expando);n=null}();var ht=/^(?:input|select|textarea)$/i,ft=/^key/,dt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,gt=/^(?:focusinfocus|focusoutblur)$/,vt=/^([^.]*)(?:\.(.+)|)/;function yt(){return!0}function mt(){return!1}function bt(){try{return d.activeElement}catch(t){}}function wt(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)wt(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=mt;else if(!i)return t;return 1===o&&(s=i,(i=function(t){return k().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=k.guid++)),t.each(function(){k.event.add(this,e,i,r,n)})}k.event={"global":{},"add":function(t,e,n,r,i){var o,s,a,c,u,l,p,h,f,d,g,v=k._data(t);if(v){for(n.handler&&(n=(c=n).handler,i=c.selector),n.guid||(n.guid=k.guid++),(s=v.events)||(s=v.events={}),(l=v.handle)||((l=v.handle=function(t){return void 0===k||t&&k.event.triggered===t.type?void 0:k.event.dispatch.apply(l.elem,arguments)}).elem=t),a=(e=(e||"").match(L)||[""]).length;a--;)f=g=(o=vt.exec(e[a])||[])[1],d=(o[2]||"").split(".").sort(),f&&(u=k.event.special[f]||{},f=(i?u.delegateType:u.bindType)||f,u=k.event.special[f]||{},p=k.extend({"type":f,"origType":g,"data":r,"handler":n,"guid":n.guid,"selector":i,"needsContext":i&&k.expr.match.needsContext.test(i),"namespace":d.join(".")},c),(h=s[f])||((h=s[f]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,r,d,l)||(t.addEventListener?t.addEventListener(f,l,!1):t.attachEvent&&t.attachEvent("on"+f,l))),u.add&&(u.add.call(t,p),p.handler.guid||(p.handler.guid=n.guid)),i?h.splice(h.delegateCount++,0,p):h.push(p),k.event.global[f]=!0);t=null}},"remove":function(t,e,n,r,i){var o,s,a,c,u,l,p,h,f,d,g,v=k.hasData(t)&&k._data(t);if(v&&(l=v.events)){for(u=(e=(e||"").match(L)||[""]).length;u--;)if(f=g=(a=vt.exec(e[u])||[])[1],d=(a[2]||"").split(".").sort(),f){for(p=k.event.special[f]||{},h=l[f=(r?p.delegateType:p.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),c=o=h.length;o--;)s=h[o],!i&&g!==s.origType||n&&n.guid!==s.guid||a&&!a.test(s.namespace)||r&&r!==s.selector&&("**"!==r||!s.selector)||(h.splice(o,1),s.selector&&h.delegateCount--,p.remove&&p.remove.call(t,s));c&&!h.length&&(p.teardown&&!1!==p.teardown.call(t,d,v.handle)||k.removeEvent(t,f,v.handle),delete l[f])}else for(f in l)k.event.remove(t,f+e[u],n,r,!0);k.isEmptyObject(l)&&(delete v.handle,k._removeData(t,"events"))}},"trigger":function(t,e,n,r){var i,o,s,a,c,u,l,p=[n||d],h=v.call(t,"type")?t.type:t,f=v.call(t,"namespace")?t.namespace.split("."):[];if(s=u=n=n||d,3!==n.nodeType&&8!==n.nodeType&&!gt.test(h+k.event.triggered)&&(-1<h.indexOf(".")&&(h=(f=h.split(".")).shift(),f.sort()),o=h.indexOf(":")<0&&"on"+h,(t=t[k.expando]?t:new k.Event(h,"object"==typeof t&&t)).isTrigger=r?2:3,t.namespace=f.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=n),e=null==e?[t]:k.makeArray(e,[t]),c=k.event.special[h]||{},r||!c.trigger||!1!==c.trigger.apply(n,e))){if(!r&&!c.noBubble&&!k.isWindow(n)){for(a=c.delegateType||h,gt.test(a+h)||(s=s.parentNode);s;s=s.parentNode)p.push(s),u=s;u===(n.ownerDocument||d)&&p.push(u.defaultView||u.parentWindow||E)}for(l=0;(s=p[l++])&&!t.isPropagationStopped();)t.type=1<l?a:c.bindType||h,(i=(k._data(s,"events")||{})[t.type]&&k._data(s,"handle"))&&i.apply(s,e),(i=o&&s[o])&&i.apply&&j(s)&&(t.result=i.apply(s,e),!1===t.result&&t.preventDefault());if(t.type=h,!r&&!t.isDefaultPrevented()&&(!c._default||!1===c._default.apply(p.pop(),e))&&j(n)&&o&&n[h]&&!k.isWindow(n)){(u=n[o])&&(n[o]=null),k.event.triggered=h;try{n[h]()}catch(t){}k.event.triggered=void 0,u&&(n[o]=u)}return t.result}},"dispatch":function(t){t=k.event.fix(t);var e,n,r,i,o,s,a=l.call(arguments),c=(k._data(this,"events")||{})[t.type]||[],u=k.event.special[t.type]||{};if((a[0]=t).delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,t)){for(s=k.event.handlers.call(this,t,c),e=0;(i=s[e++])&&!t.isPropagationStopped();)for(t.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(o.namespace)||(t.handleObj=o,t.data=o.data,void 0!==(r=((k.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(t.result=r)&&(t.preventDefault(),t.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,t),t.result}},"handlers":function(t,e){var n,r,i,o,s=[],a=e.delegateCount,c=t.target;if(a&&c.nodeType&&("click"!==t.type||isNaN(t.button)||t.button<1))for(;c!=this;c=c.parentNode||this)if(1===c.nodeType&&(!0!==c.disabled||"click"!==t.type)){for(r=[],n=0;n<a;n++)void 0===r[i=(o=e[n]).selector+" "]&&(r[i]=o.needsContext?-1<k(i,this).index(c):k.find(i,this,null,[c]).length),r[i]&&r.push(o);r.length&&s.push({"elem":c,"handlers":r})}return a<e.length&&s.push({"elem":this,"handlers":e.slice(a)}),s},"fix":function(t){if(t[k.expando])return t;var e,n,r,i=t.type,o=t,s=this.fixHooks[i];for(s||(this.fixHooks[i]=s=dt.test(i)?this.mouseHooks:ft.test(i)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,t=new k.Event(o),e=r.length;e--;)t[n=r[e]]=o[n];return t.target||(t.target=o.srcElement||d),3===t.target.nodeType&&(t.target=t.target.parentNode),t.metaKey=!!t.metaKey,s.filter?s.filter(t,o):t},"props":"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),"fixHooks":{},"keyHooks":{"props":"char charCode key keyCode".split(" "),"filter":function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},"mouseHooks":{"props":"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),"filter":function(t,e){var n,r,i,o=e.button,s=e.fromElement;return null==t.pageX&&null!=e.clientX&&(i=(r=t.target.ownerDocument||d).documentElement,n=r.body,t.pageX=e.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),t.pageY=e.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!t.relatedTarget&&s&&(t.relatedTarget=s===t.target?e.toElement:s),t.which||void 0===o||(t.which=1&o?1:2&o?3:4&o?2:0),t}},"special":{"load":{"noBubble":!0},"focus":{"trigger":function(){if(this!==bt()&&this.focus)try{return this.focus(),!1}catch(t){}},"delegateType":"focusin"},"blur":{"trigger":function(){if(this===bt()&&this.blur)return this.blur(),!1},"delegateType":"focusout"},"click":{"trigger":function(){if(k.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},"_default":function(t){return k.nodeName(t.target,"a")}},"beforeunload":{"postDispatch":function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}},"simulate":function(t,e,n){var r=k.extend(new k.Event,n,{"type":t,"isSimulated":!0});k.event.trigger(r,null,e),r.isDefaultPrevented()&&n.preventDefault()}},k.removeEvent=d.removeEventListener?function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)}:function(t,e,n){var r="on"+e;t.detachEvent&&(void 0===t[r]&&(t[r]=null),t.detachEvent(r,n))},k.Event=function(t,e){if(!(this instanceof k.Event))return new k.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?yt:mt):this.type=t,e&&k.extend(this,e),this.timeStamp=t&&t.timeStamp||k.now(),this[k.expando]=!0},k.Event.prototype={"constructor":k.Event,"isDefaultPrevented":mt,"isPropagationStopped":mt,"isImmediatePropagationStopped":mt,"preventDefault":function(){var t=this.originalEvent;this.isDefaultPrevented=yt,t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},"stopPropagation":function(){var t=this.originalEvent;this.isPropagationStopped=yt,t&&!this.isSimulated&&(t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0)},"stopImmediatePropagation":function(){var t=this.originalEvent;this.isImmediatePropagationStopped=yt,t&&t.stopImmediatePropagation&&t.stopImmediatePropagation(),this.stopPropagation()}},k.each({"mouseenter":"mouseover","mouseleave":"mouseout","pointerenter":"pointerover","pointerleave":"pointerout"},function(t,i){k.event.special[t]={"delegateType":i,"bindType":i,"handle":function(t){var e,n=t.relatedTarget,r=t.handleObj;return n&&(n===this||k.contains(this,n))||(t.type=r.origType,e=r.handler.apply(this,arguments),t.type=i),e}}}),y.submit||(k.event.special.submit={"setup":function(){if(k.nodeName(this,"form"))return!1;k.event.add(this,"click._submit keypress._submit",function(t){var e=t.target,n=k.nodeName(e,"input")||k.nodeName(e,"button")?k.prop(e,"form"):void 0;n&&!k._data(n,"submit")&&(k.event.add(n,"submit._submit",function(t){t._submitBubble=!0}),k._data(n,"submit",!0))})},"postDispatch":function(t){t._submitBubble&&(delete t._submitBubble,this.parentNode&&!t.isTrigger&&k.event.simulate("submit",this.parentNode,t))},"teardown":function(){if(k.nodeName(this,"form"))return!1;k.event.remove(this,"._submit")}}),y.change||(k.event.special.change={"setup":function(){if(ht.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(k.event.add(this,"propertychange._change",function(t){"checked"===t.originalEvent.propertyName&&(this._justChanged=!0)}),k.event.add(this,"click._change",function(t){this._justChanged&&!t.isTrigger&&(this._justChanged=!1),k.event.simulate("change",this,t)})),!1;k.event.add(this,"beforeactivate._change",function(t){var e=t.target;ht.test(e.nodeName)&&!k._data(e,"change")&&(k.event.add(e,"change._change",function(t){!this.parentNode||t.isSimulated||t.isTrigger||k.event.simulate("change",this.parentNode,t)}),k._data(e,"change",!0))})},"handle":function(t){var e=t.target;if(this!==e||t.isSimulated||t.isTrigger||"radio"!==e.type&&"checkbox"!==e.type)return t.handleObj.handler.apply(this,arguments)},"teardown":function(){return k.event.remove(this,"._change"),!ht.test(this.nodeName)}}),y.focusin||k.each({"focus":"focusin","blur":"focusout"},function(n,r){function i(t){k.event.simulate(r,t.target,k.event.fix(t))}k.event.special[r]={"setup":function(){var t=this.ownerDocument||this,e=k._data(t,r);e||t.addEventListener(n,i,!0),k._data(t,r,(e||0)+1)},"teardown":function(){var t=this.ownerDocument||this,e=k._data(t,r)-1;e?k._data(t,r,e):(t.removeEventListener(n,i,!0),k._removeData(t,r))}}}),k.fn.extend({"on":function(t,e,n,r){return wt(this,t,e,n,r)},"one":function(t,e,n,r){return wt(this,t,e,n,r,1)},"off":function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,k(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=mt),this.each(function(){k.event.remove(this,t,n,e)});for(i in t)this.off(i,e,t[i]);return this},"trigger":function(t,e){return this.each(function(){k.event.trigger(t,e,this)})},"triggerHandler":function(t,e){var n=this[0];if(n)return k.event.trigger(t,e,n,!0)}});var xt=/ jQuery\d+="(?:null|\d+)"/g,Tt=new RegExp("<(?:"+rt+")[\\s/>]","i"),Et=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,kt=/<script|<style|<link/i,Ct=/checked\s*(?:[^=]|=\s*.checked.)/i,St=/^true\/(.*)/,At=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Nt=it(d).appendChild(d.createElement("div"));function Ot(t,e){return k.nodeName(t,"table")&&k.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function _t(t){return t.type=(null!==k.find.attr(t,"type"))+"/"+t.type,t}function It(t){var e=St.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function Rt(t,e){if(1===e.nodeType&&k.hasData(t)){var n,r,i,o=k._data(t),s=k._data(e,o),a=o.events;if(a)for(n in delete s.handle,s.events={},a)for(r=0,i=a[n].length;r<i;r++)k.event.add(e,n,a[n][r]);s.data&&(s.data=k.extend({},s.data))}}function Lt(t,e){var n,r,i;if(1===e.nodeType){if(n=e.nodeName.toLowerCase(),!y.noCloneEvent&&e[k.expando]){for(r in(i=k._data(e)).events)k.removeEvent(e,r,i.handle);e.removeAttribute(k.expando)}"script"===n&&e.text!==t.text?(_t(e).text=t.text,It(e)):"object"===n?(e.parentNode&&(e.outerHTML=t.outerHTML),y.html5Clone&&t.innerHTML&&!k.trim(e.innerHTML)&&(e.innerHTML=t.innerHTML)):"input"===n&&Z.test(t.type)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===n?e.defaultSelected=e.selected=t.defaultSelected:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}}function Dt(n,r,i,o){r=g.apply([],r);var t,e,s,a,c,u,l=0,p=n.length,h=p-1,f=r[0],d=k.isFunction(f);if(d||1<p&&"string"==typeof f&&!y.checkClone&&Ct.test(f))return n.each(function(t){var e=n.eq(t);d&&(r[0]=f.call(this,t,e.html())),Dt(e,r,i,o)});if(p&&(t=(u=pt(r,n[0].ownerDocument,!1,n,o)).firstChild,1===u.childNodes.length&&(u=t),t||o)){for(s=(a=k.map(st(u,"script"),_t)).length;l<p;l++)e=u,l!==h&&(e=k.clone(e,!0,!0),s&&k.merge(a,st(e,"script"))),i.call(n[l],e,l);if(s)for(c=a[a.length-1].ownerDocument,k.map(a,It),l=0;l<s;l++)e=a[l],et.test(e.type||"")&&!k._data(e,"globalEval")&&k.contains(c,e)&&(e.src?k._evalUrl&&k._evalUrl(e.src):k.globalEval((e.text||e.textContent||e.innerHTML||"").replace(At,"")));u=t=null}return n}function Mt(t,e,n){for(var r,i=e?k.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||k.cleanData(st(r)),r.parentNode&&(n&&k.contains(r.ownerDocument,r)&&at(st(r,"script")),r.parentNode.removeChild(r));return t}k.extend({"htmlPrefilter":function(t){return t.replace(Et,"<$1></$2>")},"clone":function(t,e,n){var r,i,o,s,a,c=k.contains(t.ownerDocument,t);if(y.html5Clone||k.isXMLDoc(t)||!Tt.test("<"+t.nodeName+">")?o=t.cloneNode(!0):(Nt.innerHTML=t.outerHTML,Nt.removeChild(o=Nt.firstChild)),!(y.noCloneEvent&&y.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||k.isXMLDoc(t)))for(r=st(o),a=st(t),s=0;null!=(i=a[s]);++s)r[s]&&Lt(i,r[s]);if(e)if(n)for(a=a||st(t),r=r||st(o),s=0;null!=(i=a[s]);s++)Rt(i,r[s]);else Rt(t,o);return 0<(r=st(o,"script")).length&&at(r,!c&&st(t,"script")),r=a=i=null,o},"cleanData":function(t,e){for(var n,r,i,o,s=0,a=k.expando,c=k.cache,u=y.attributes,l=k.event.special;null!=(n=t[s]);s++)if((e||j(n))&&(o=(i=n[a])&&c[i])){if(o.events)for(r in o.events)l[r]?k.event.remove(n,r):k.removeEvent(n,r,o.handle);c[i]&&(delete c[i],u||void 0===n.removeAttribute?n[a]=void 0:n.removeAttribute(a),p.push(i))}}}),k.fn.extend({"domManip":Dt,"detach":function(t){return Mt(this,t,!0)},"remove":function(t){return Mt(this,t)},"text":function(t){return J(this,function(t){return void 0===t?k.text(this):this.empty().append((this[0]&&this[0].ownerDocument||d).createTextNode(t))},null,t,arguments.length)},"append":function(){return Dt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Ot(this,t).appendChild(t)})},"prepend":function(){return Dt(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Ot(this,t);e.insertBefore(t,e.firstChild)}})},"before":function(){return Dt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},"after":function(){return Dt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},"empty":function(){for(var t,e=0;null!=(t=this[e]);e++){for(1===t.nodeType&&k.cleanData(st(t,!1));t.firstChild;)t.removeChild(t.firstChild);t.options&&k.nodeName(t,"select")&&(t.options.length=0)}return this},"clone":function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return k.clone(this,t,e)})},"html":function(t){return J(this,function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t)return 1===e.nodeType?e.innerHTML.replace(xt,""):void 0;if("string"==typeof t&&!kt.test(t)&&(y.htmlSerialize||!Tt.test(t))&&(y.leadingWhitespace||!nt.test(t))&&!ot[(tt.exec(t)||["",""])[1].toLowerCase()]){t=k.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(k.cleanData(st(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},"replaceWith":function(){var n=[];return Dt(this,arguments,function(t){var e=this.parentNode;k.inArray(this,n)<0&&(k.cleanData(st(this)),e&&e.replaceChild(t,this))},n)}}),k.each({"appendTo":"append","prependTo":"prepend","insertBefore":"before","insertAfter":"after","replaceAll":"replaceWith"},function(t,s){k.fn[t]=function(t){for(var e,n=0,r=[],i=k(t),o=i.length-1;n<=o;n++)e=n===o?this:this.clone(!0),k(i[n])[s](e),a.apply(r,e.get());return this.pushStack(r)}});var jt,Pt={"HTML":"block","BODY":"block"};function Bt(t,e){var n=k(e.createElement(t)).appendTo(e.body),r=k.css(n[0],"display");return n.detach(),r}function Ft(t){var e=d,n=Pt[t];return n||("none"!==(n=Bt(t,e))&&n||((e=((jt=(jt||k("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement))[0].contentWindow||jt[0].contentDocument).document).write(),e.close(),n=Bt(t,e),jt.detach()),Pt[t]=n),n}function Ut(t,e,n,r){var i,o,s={};for(o in e)s[o]=t.style[o],t.style[o]=e[o];for(o in i=n.apply(t,r||[]),e)t.style[o]=s[o];return i}var Ht,qt,Wt,zt,Gt,Yt,Vt,Qt,$t=/^margin/,Xt=new RegExp("^("+G+")(?!px)[a-z%]+$","i"),Kt=d.documentElement;function Jt(){var t,e,n=d.documentElement;n.appendChild(Vt),Qt.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",Ht=Wt=Yt=!1,qt=Gt=!0,E.getComputedStyle&&(e=E.getComputedStyle(Qt),Ht="1%"!==(e||{}).top,Yt="2px"===(e||{}).marginLeft,Wt="4px"===(e||{"width":"4px"}).width,Qt.style.marginRight="50%",qt="4px"===(e||{"marginRight":"4px"}).marginRight,(t=Qt.appendChild(d.createElement("div"))).style.cssText=Qt.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",t.style.marginRight=t.style.width="0",Qt.style.width="1px",Gt=!parseFloat((E.getComputedStyle(t)||{}).marginRight),Qt.removeChild(t)),Qt.style.display="none",(zt=0===Qt.getClientRects().length)&&(Qt.style.display="",Qt.innerHTML="<table><tr><td></td><td>t</td></tr></table>",Qt.childNodes[0].style.borderCollapse="separate",(t=Qt.getElementsByTagName("td"))[0].style.cssText="margin:0;border:0;padding:0;display:none",(zt=0===t[0].offsetHeight)&&(t[0].style.display="",t[1].style.display="none",zt=0===t[0].offsetHeight)),n.removeChild(Vt)}Vt=d.createElement("div"),(Qt=d.createElement("div")).style&&(Qt.style.cssText="float:left;opacity:.5",y.opacity="0.5"===Qt.style.opacity,y.cssFloat=!!Qt.style.cssFloat,Qt.style.backgroundClip="content-box",Qt.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===Qt.style.backgroundClip,(Vt=d.createElement("div")).style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",Qt.innerHTML="",Vt.appendChild(Qt),y.boxSizing=""===Qt.style.boxSizing||""===Qt.style.MozBoxSizing||""===Qt.style.WebkitBoxSizing,k.extend(y,{"reliableHiddenOffsets":function(){return null==Ht&&Jt(),zt},"boxSizingReliable":function(){return null==Ht&&Jt(),Wt},"pixelMarginRight":function(){return null==Ht&&Jt(),qt},"pixelPosition":function(){return null==Ht&&Jt(),Ht},"reliableMarginRight":function(){return null==Ht&&Jt(),Gt},"reliableMarginLeft":function(){return null==Ht&&Jt(),Yt}}));var Zt,te,ee=/^(top|right|bottom|left)$/;function ne(t,e){return{"get":function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}E.getComputedStyle?(Zt=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=E),e.getComputedStyle(t)},te=function(t,e,n){var r,i,o,s,a=t.style;return""!==(s=(n=n||Zt(t))?n.getPropertyValue(e)||n[e]:void 0)&&void 0!==s||k.contains(t.ownerDocument,t)||(s=k.style(t,e)),n&&!y.pixelMarginRight()&&Xt.test(s)&&$t.test(e)&&(r=a.width,i=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=r,a.minWidth=i,a.maxWidth=o),void 0===s?s:s+""}):Kt.currentStyle&&(Zt=function(t){return t.currentStyle},te=function(t,e,n){var r,i,o,s,a=t.style;return null==(s=(n=n||Zt(t))?n[e]:void 0)&&a&&a[e]&&(s=a[e]),Xt.test(s)&&!ee.test(e)&&(r=a.left,(o=(i=t.runtimeStyle)&&i.left)&&(i.left=t.currentStyle.left),a.left="fontSize"===e?"1em":s,s=a.pixelLeft+"px",a.left=r,o&&(i.left=o)),void 0===s?s:s+""||"auto"});var re=/alpha\([^)]*\)/i,ie=/opacity\s*=\s*([^)]*)/i,oe=/^(none|table(?!-c[ea]).+)/,se=new RegExp("^("+G+")(.*)$","i"),ae={"position":"absolute","visibility":"hidden","display":"block"},ce={"letterSpacing":"0","fontWeight":"400"},ue=["Webkit","O","Moz","ms"],le=d.createElement("div").style;function pe(t){if(t in le)return t;for(var e=t.charAt(0).toUpperCase()+t.slice(1),n=ue.length;n--;)if((t=ue[n]+e)in le)return t}function he(t,e){for(var n,r,i,o=[],s=0,a=t.length;s<a;s++)(r=t[s]).style&&(o[s]=k._data(r,"olddisplay"),n=r.style.display,e?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&z(r)&&(o[s]=k._data(r,"olddisplay",Ft(r.nodeName)))):(i=z(r),(n&&"none"!==n||!i)&&k._data(r,"olddisplay",i?n:k.css(r,"display"))));for(s=0;s<a;s++)(r=t[s]).style&&(e&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=e?o[s]||"":"none"));return t}function fe(t,e,n){var r=se.exec(e);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):e}function de(t,e,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===e?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=k.css(t,n+V[o],!0,i)),r?("content"===n&&(s-=k.css(t,"padding"+V[o],!0,i)),"margin"!==n&&(s-=k.css(t,"border"+V[o]+"Width",!0,i))):(s+=k.css(t,"padding"+V[o],!0,i),"padding"!==n&&(s+=k.css(t,"border"+V[o]+"Width",!0,i)));return s}function ge(t,e,n){var r=!0,i="width"===e?t.offsetWidth:t.offsetHeight,o=Zt(t),s=y.boxSizing&&"border-box"===k.css(t,"boxSizing",!1,o);if(i<=0||null==i){if(((i=te(t,e,o))<0||null==i)&&(i=t.style[e]),Xt.test(i))return i;r=s&&(y.boxSizingReliable()||i===t.style[e]),i=parseFloat(i)||0}return i+de(t,e,n||(s?"border":"content"),r,o)+"px"}function ve(t,e,n,r,i){return new ve.prototype.init(t,e,n,r,i)}k.extend({"cssHooks":{"opacity":{"get":function(t,e){if(e){var n=te(t,"opacity");return""===n?"1":n}}}},"cssNumber":{"animationIterationCount":!0,"columnCount":!0,"fillOpacity":!0,"flexGrow":!0,"flexShrink":!0,"fontWeight":!0,"lineHeight":!0,"opacity":!0,"order":!0,"orphans":!0,"widows":!0,"zIndex":!0,"zoom":!0},"cssProps":{"float":y.cssFloat?"cssFloat":"styleFloat"},"style":function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,o,s,a=k.camelCase(e),c=t.style;if(e=k.cssProps[a]||(k.cssProps[a]=pe(a)||a),s=k.cssHooks[e]||k.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:c[e];if("string"===(o=typeof n)&&(i=Y.exec(n))&&i[1]&&(n=Q(t,e,i),o="number"),null!=n&&n==n&&("number"===o&&(n+=i&&i[3]||(k.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),!(s&&"set"in s&&void 0===(n=s.set(t,n,r)))))try{c[e]=n}catch(t){}}},"css":function(t,e,n,r){var i,o,s,a=k.camelCase(e);return e=k.cssProps[a]||(k.cssProps[a]=pe(a)||a),(s=k.cssHooks[e]||k.cssHooks[a])&&"get"in s&&(o=s.get(t,!0,n)),void 0===o&&(o=te(t,e,r)),"normal"===o&&e in ce&&(o=ce[e]),""===n||n?(i=parseFloat(o),!0===n||isFinite(i)?i||0:o):o}}),k.each(["height","width"],function(t,i){k.cssHooks[i]={"get":function(t,e,n){if(e)return oe.test(k.css(t,"display"))&&0===t.offsetWidth?Ut(t,ae,function(){return ge(t,i,n)}):ge(t,i,n)},"set":function(t,e,n){var r=n&&Zt(t);return fe(0,e,n?de(t,i,n,y.boxSizing&&"border-box"===k.css(t,"boxSizing",!1,r),r):0)}}}),y.opacity||(k.cssHooks.opacity={"get":function(t,e){return ie.test((e&&t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":e?"1":""},"set":function(t,e){var n=t.style,r=t.currentStyle,i=k.isNumeric(e)?"alpha(opacity="+100*e+")":"",o=r&&r.filter||n.filter||"";((n.zoom=1)<=e||""===e)&&""===k.trim(o.replace(re,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===e||r&&!r.filter)||(n.filter=re.test(o)?o.replace(re,i):o+" "+i)}}),k.cssHooks.marginRight=ne(y.reliableMarginRight,function(t,e){if(e)return Ut(t,{"display":"inline-block"},te,[t,"marginRight"])}),k.cssHooks.marginLeft=ne(y.reliableMarginLeft,function(t,e){if(e)return(parseFloat(te(t,"marginLeft"))||(k.contains(t.ownerDocument,t)?t.getBoundingClientRect().left-Ut(t,{"marginLeft":0},function(){return t.getBoundingClientRect().left}):0))+"px"}),k.each({"margin":"","padding":"","border":"Width"},function(i,o){k.cssHooks[i+o]={"expand":function(t){for(var e=0,n={},r="string"==typeof t?t.split(" "):[t];e<4;e++)n[i+V[e]+o]=r[e]||r[e-2]||r[0];return n}},$t.test(i)||(k.cssHooks[i+o].set=fe)}),k.fn.extend({"css":function(t,e){return J(this,function(t,e,n){var r,i,o={},s=0;if(k.isArray(e)){for(r=Zt(t),i=e.length;s<i;s++)o[e[s]]=k.css(t,e[s],!1,r);return o}return void 0!==n?k.style(t,e,n):k.css(t,e)},t,e,1<arguments.length)},"show":function(){return he(this,!0)},"hide":function(){return he(this)},"toggle":function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){z(this)?k(this).show():k(this).hide()})}}),((k.Tween=ve).prototype={"constructor":ve,"init":function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||k.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(k.cssNumber[n]?"":"px")},"cur":function(){var t=ve.propHooks[this.prop];return t&&t.get?t.get(this):ve.propHooks._default.get(this)},"run":function(t){var e,n=ve.propHooks[this.prop];return this.options.duration?this.pos=e=k.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ve.propHooks._default.set(this),this}}).init.prototype=ve.prototype,(ve.propHooks={"_default":{"get":function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=k.css(t.elem,t.prop,""))&&"auto"!==e?e:0},"set":function(t){k.fx.step[t.prop]?k.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[k.cssProps[t.prop]]&&!k.cssHooks[t.prop]?t.elem[t.prop]=t.now:k.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=ve.propHooks.scrollLeft={"set":function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},k.easing={"linear":function(t){return t},"swing":function(t){return.5-Math.cos(t*Math.PI)/2},"_default":"swing"},k.fx=ve.prototype.init,k.fx.step={};var ye,me,be,we,xe,Te,Ee,ke=/^(?:toggle|show|hide)$/,Ce=/queueHooks$/;function Se(){return E.setTimeout(function(){ye=void 0}),ye=k.now()}function Ae(t,e){var n,r={"height":t},i=0;for(e=e?1:0;i<4;i+=2-e)r["margin"+(n=V[i])]=r["padding"+n]=t;return e&&(r.opacity=r.width=t),r}function Ne(t,e,n){for(var r,i=(Oe.tweeners[e]||[]).concat(Oe.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function Oe(o,t,e){var n,s,r=0,i=Oe.prefilters.length,a=k.Deferred().always(function(){delete c.elem}),c=function(){if(s)return!1;for(var t=ye||Se(),e=Math.max(0,u.startTime+u.duration-t),n=1-(e/u.duration||0),r=0,i=u.tweens.length;r<i;r++)u.tweens[r].run(n);return a.notifyWith(o,[u,n,e]),n<1&&i?e:(a.resolveWith(o,[u]),!1)},u=a.promise({"elem":o,"props":k.extend({},t),"opts":k.extend(!0,{"specialEasing":{},"easing":k.easing._default},e),"originalProperties":t,"originalOptions":e,"startTime":ye||Se(),"duration":e.duration,"tweens":[],"createTween":function(t,e){var n=k.Tween(o,u.opts,t,e,u.opts.specialEasing[t]||u.opts.easing);return u.tweens.push(n),n},"stop":function(t){var e=0,n=t?u.tweens.length:0;if(s)return this;for(s=!0;e<n;e++)u.tweens[e].run(1);return t?(a.notifyWith(o,[u,1,0]),a.resolveWith(o,[u,t])):a.rejectWith(o,[u,t]),this}}),l=u.props;for(!function(t,e){var n,r,i,o,s;for(n in t)if(i=e[r=k.camelCase(n)],o=t[n],k.isArray(o)&&(i=o[1],o=t[n]=o[0]),n!==r&&(t[r]=o,delete t[n]),(s=k.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete t[r],o)n in t||(t[n]=o[n],e[n]=i);else e[r]=i}(l,u.opts.specialEasing);r<i;r++)if(n=Oe.prefilters[r].call(u,o,l,u.opts))return k.isFunction(n.stop)&&(k._queueHooks(u.elem,u.opts.queue).stop=k.proxy(n.stop,n)),n;return k.map(l,Ne,u),k.isFunction(u.opts.start)&&u.opts.start.call(o,u),k.fx.timer(k.extend(c,{"elem":o,"anim":u,"queue":u.opts.queue})),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always)}k.Animation=k.extend(Oe,{"tweeners":{"*":[function(t,e){var n=this.createTween(t,e);return Q(n.elem,t,Y.exec(e),n),n}]},"tweener":function(t,e){for(var n,r=0,i=(t=k.isFunction(t)?(e=t,["*"]):t.match(L)).length;r<i;r++)n=t[r],Oe.tweeners[n]=Oe.tweeners[n]||[],Oe.tweeners[n].unshift(e)},"prefilters":[function(e,t,n){var r,i,o,s,a,c,u,l=this,p={},h=e.style,f=e.nodeType&&z(e),d=k._data(e,"fxshow");for(r in n.queue||(null==(a=k._queueHooks(e,"fx")).unqueued&&(a.unqueued=0,c=a.empty.fire,a.empty.fire=function(){a.unqueued||c()}),a.unqueued++,l.always(function(){l.always(function(){a.unqueued--,k.queue(e,"fx").length||a.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],"inline"===("none"===(u=k.css(e,"display"))?k._data(e,"olddisplay")||Ft(e.nodeName):u)&&"none"===k.css(e,"float")&&(y.inlineBlockNeedsLayout&&"inline"!==Ft(e.nodeName)?h.zoom=1:h.display="inline-block")),n.overflow&&(h.overflow="hidden",y.shrinkWrapBlocks()||l.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),t)if(i=t[r],ke.exec(i)){if(delete t[r],o=o||"toggle"===i,i===(f?"hide":"show")){if("show"!==i||!d||void 0===d[r])continue;f=!0}p[r]=d&&d[r]||k.style(e,r)}else u=void 0;if(k.isEmptyObject(p))"inline"===("none"===u?Ft(e.nodeName):u)&&(h.display=u);else for(r in d?"hidden"in d&&(f=d.hidden):d=k._data(e,"fxshow",{}),o&&(d.hidden=!f),f?k(e).show():l.done(function(){k(e).hide()}),l.done(function(){var t;for(t in k._removeData(e,"fxshow"),p)k.style(e,t,p[t])}),p)s=Ne(f?d[r]:0,r,l),r in d||(d[r]=s.start,f&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}],"prefilter":function(t,e){e?Oe.prefilters.unshift(t):Oe.prefilters.push(t)}}),k.speed=function(t,e,n){var r=t&&"object"==typeof t?k.extend({},t):{"complete":n||!n&&e||k.isFunction(t)&&t,"duration":t,"easing":n&&e||e&&!k.isFunction(e)&&e};return r.duration=k.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in k.fx.speeds?k.fx.speeds[r.duration]:k.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){k.isFunction(r.old)&&r.old.call(this),r.queue&&k.dequeue(this,r.queue)},r},k.fn.extend({"fadeTo":function(t,e,n,r){return this.filter(z).css("opacity",0).show().end().animate({"opacity":e},t,n,r)},"animate":function(e,t,n,r){function i(){var t=Oe(this,k.extend({},e),s);(o||k._data(this,"finish"))&&t.stop(!0)}var o=k.isEmptyObject(e),s=k.speed(t,n,r);return i.finish=i,o||!1===s.queue?this.each(i):this.queue(s.queue,i)},"stop":function(i,t,o){function s(t){var e=t.stop;delete t.stop,e(o)}return"string"!=typeof i&&(o=t,t=i,i=void 0),t&&!1!==i&&this.queue(i||"fx",[]),this.each(function(){var t=!0,e=null!=i&&i+"queueHooks",n=k.timers,r=k._data(this);if(e)r[e]&&r[e].stop&&s(r[e]);else for(e in r)r[e]&&r[e].stop&&Ce.test(e)&&s(r[e]);for(e=n.length;e--;)n[e].elem!==this||null!=i&&n[e].queue!==i||(n[e].anim.stop(o),t=!1,n.splice(e,1));!t&&o||k.dequeue(this,i)})},"finish":function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var t,e=k._data(this),n=e[s+"queue"],r=e[s+"queueHooks"],i=k.timers,o=n?n.length:0;for(e.finish=!0,k.queue(this,s,[]),r&&r.stop&&r.stop.call(this,!0),t=i.length;t--;)i[t].elem===this&&i[t].queue===s&&(i[t].anim.stop(!0),i.splice(t,1));for(t=0;t<o;t++)n[t]&&n[t].finish&&n[t].finish.call(this);delete e.finish})}}),k.each(["toggle","show","hide"],function(t,r){var i=k.fn[r];k.fn[r]=function(t,e,n){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(Ae(r,!0),t,e,n)}}),k.each({"slideDown":Ae("show"),"slideUp":Ae("hide"),"slideToggle":Ae("toggle"),"fadeIn":{"opacity":"show"},"fadeOut":{"opacity":"hide"},"fadeToggle":{"opacity":"toggle"}},function(t,r){k.fn[t]=function(t,e,n){return this.animate(r,t,e,n)}}),k.timers=[],k.fx.tick=function(){var t,e=k.timers,n=0;for(ye=k.now();n<e.length;n++)(t=e[n])()||e[n]!==t||e.splice(n--,1);e.length||k.fx.stop(),ye=void 0},k.fx.timer=function(t){k.timers.push(t),t()?k.fx.start():k.timers.pop()},k.fx.interval=13,k.fx.start=function(){me=me||E.setInterval(k.fx.tick,k.fx.interval)},k.fx.stop=function(){E.clearInterval(me),me=null},k.fx.speeds={"slow":600,"fast":200,"_default":400},k.fn.delay=function(r,t){return r=k.fx&&k.fx.speeds[r]||r,t=t||"fx",this.queue(t,function(t,e){var n=E.setTimeout(t,r);e.stop=function(){E.clearTimeout(n)}})},we=d.createElement("input"),xe=d.createElement("div"),Te=d.createElement("select"),Ee=Te.appendChild(d.createElement("option")),(xe=d.createElement("div")).setAttribute("className","t"),xe.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",be=xe.getElementsByTagName("a")[0],we.setAttribute("type","checkbox"),xe.appendChild(we),(be=xe.getElementsByTagName("a")[0]).style.cssText="top:1px",y.getSetAttribute="t"!==xe.className,y.style=/top/.test(be.getAttribute("style")),y.hrefNormalized="/a"===be.getAttribute("href"),y.checkOn=!!we.value,y.optSelected=Ee.selected,y.enctype=!!d.createElement("form").enctype,Te.disabled=!0,y.optDisabled=!Ee.disabled,(we=d.createElement("input")).setAttribute("value",""),y.input=""===we.getAttribute("value"),we.value="t",we.setAttribute("type","radio"),y.radioValue="t"===we.value;var _e=/\r/g,Ie=/[\x20\t\r\n\f]+/g;k.fn.extend({"val":function(n){var r,t,i,e=this[0];return arguments.length?(i=k.isFunction(n),this.each(function(t){var e;1===this.nodeType&&(null==(e=i?n.call(this,t,k(this).val()):n)?e="":"number"==typeof e?e+="":k.isArray(e)&&(e=k.map(e,function(t){return null==t?"":t+""})),(r=k.valHooks[this.type]||k.valHooks[this.nodeName.toLowerCase()])&&"set"in r&&void 0!==r.set(this,e,"value")||(this.value=e))})):e?(r=k.valHooks[e.type]||k.valHooks[e.nodeName.toLowerCase()])&&"get"in r&&void 0!==(t=r.get(e,"value"))?t:"string"==typeof(t=e.value)?t.replace(_e,""):null==t?"":t:void 0}}),k.extend({"valHooks":{"option":{"get":function(t){var e=k.find.attr(t,"value");return null!=e?e:k.trim(k.text(t)).replace(Ie," ")}},"select":{"get":function(t){for(var e,n,r=t.options,i=t.selectedIndex,o="select-one"===t.type||i<0,s=o?null:[],a=o?i+1:r.length,c=i<0?a:o?i:0;c<a;c++)if(((n=r[c]).selected||c===i)&&(y.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!k.nodeName(n.parentNode,"optgroup"))){if(e=k(n).val(),o)return e;s.push(e)}return s},"set":function(t,e){for(var n,r,i=t.options,o=k.makeArray(e),s=i.length;s--;)if(r=i[s],-1<k.inArray(k.valHooks.option.get(r),o))try{r.selected=n=!0}catch(t){r.scrollHeight}else r.selected=!1;return n||(t.selectedIndex=-1),i}}}}),k.each(["radio","checkbox"],function(){k.valHooks[this]={"set":function(t,e){if(k.isArray(e))return t.checked=-1<k.inArray(k(t).val(),e)}},y.checkOn||(k.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var Re,Le,De=k.expr.attrHandle,Me=/^(?:checked|selected)$/i,je=y.getSetAttribute,Pe=y.input;k.fn.extend({"attr":function(t,e){return J(this,k.attr,t,e,1<arguments.length)},"removeAttr":function(t){return this.each(function(){k.removeAttr(this,t)})}}),k.extend({"attr":function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?k.prop(t,e,n):(1===o&&k.isXMLDoc(t)||(e=e.toLowerCase(),i=k.attrHooks[e]||(k.expr.match.bool.test(e)?Le:Re)),void 0!==n?null===n?void k.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))?r:null==(r=k.find.attr(t,e))?void 0:r)},"attrHooks":{"type":{"set":function(t,e){if(!y.radioValue&&"radio"===e&&k.nodeName(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},"removeAttr":function(t,e){var n,r,i=0,o=e&&e.match(L);if(o&&1===t.nodeType)for(;n=o[i++];)r=k.propFix[n]||n,k.expr.match.bool.test(n)?Pe&&je||!Me.test(n)?t[r]=!1:t[k.camelCase("default-"+n)]=t[r]=!1:k.attr(t,n,""),t.removeAttribute(je?n:r)}}),Le={"set":function(t,e,n){return!1===e?k.removeAttr(t,n):Pe&&je||!Me.test(n)?t.setAttribute(!je&&k.propFix[n]||n,n):t[k.camelCase("default-"+n)]=t[n]=!0,n}},k.each(k.expr.match.bool.source.match(/\w+/g),function(t,e){var o=De[e]||k.find.attr;Pe&&je||!Me.test(e)?De[e]=function(t,e,n){var r,i;return n||(i=De[e],De[e]=r,r=null!=o(t,e,n)?e.toLowerCase():null,De[e]=i),r}:De[e]=function(t,e,n){if(!n)return t[k.camelCase("default-"+e)]?e.toLowerCase():null}}),Pe&&je||(k.attrHooks.value={"set":function(t,e,n){if(!k.nodeName(t,"input"))return Re&&Re.set(t,e,n);t.defaultValue=e}}),je||(Re={"set":function(t,e,n){var r=t.getAttributeNode(n);if(r||t.setAttributeNode(r=t.ownerDocument.createAttribute(n)),r.value=e+="","value"===n||e===t.getAttribute(n))return e}},De.id=De.name=De.coords=function(t,e,n){var r;if(!n)return(r=t.getAttributeNode(e))&&""!==r.value?r.value:null},k.valHooks.button={"get":function(t,e){var n=t.getAttributeNode(e);if(n&&n.specified)return n.value},"set":Re.set},k.attrHooks.contenteditable={"set":function(t,e,n){Re.set(t,""!==e&&e,n)}},k.each(["width","height"],function(t,n){k.attrHooks[n]={"set":function(t,e){if(""===e)return t.setAttribute(n,"auto"),e}}})),y.style||(k.attrHooks.style={"get":function(t){return t.style.cssText||void 0},"set":function(t,e){return t.style.cssText=e+""}});var Be=/^(?:input|select|textarea|button|object)$/i,Fe=/^(?:a|area)$/i;k.fn.extend({"prop":function(t,e){return J(this,k.prop,t,e,1<arguments.length)},"removeProp":function(t){return t=k.propFix[t]||t,this.each(function(){try{this[t]=void 0,delete this[t]}catch(t){}})}}),k.extend({"prop":function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&k.isXMLDoc(t)||(e=k.propFix[e]||e,i=k.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},"propHooks":{"tabIndex":{"get":function(t){var e=k.find.attr(t,"tabindex");return e?parseInt(e,10):Be.test(t.nodeName)||Fe.test(t.nodeName)&&t.href?0:-1}}},"propFix":{"for":"htmlFor","class":"className"}}),y.hrefNormalized||k.each(["href","src"],function(t,e){k.propHooks[e]={"get":function(t){return t.getAttribute(e,4)}}}),y.optSelected||(k.propHooks.selected={"get":function(t){var e=t.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null},"set":function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),k.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){k.propFix[this.toLowerCase()]=this}),y.enctype||(k.propFix.enctype="encoding");var Ue=/[\t\r\n\f]/g;function He(t){return k.attr(t,"class")||""}k.fn.extend({"addClass":function(e){var t,n,r,i,o,s,a,c=0;if(k.isFunction(e))return this.each(function(t){k(this).addClass(e.call(this,t,He(this)))});if("string"==typeof e&&e)for(t=e.match(L)||[];n=this[c++];)if(i=He(n),r=1===n.nodeType&&(" "+i+" ").replace(Ue," ")){for(s=0;o=t[s++];)r.indexOf(" "+o+" ")<0&&(r+=o+" ");i!==(a=k.trim(r))&&k.attr(n,"class",a)}return this},"removeClass":function(e){var t,n,r,i,o,s,a,c=0;if(k.isFunction(e))return this.each(function(t){k(this).removeClass(e.call(this,t,He(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(L)||[];n=this[c++];)if(i=He(n),r=1===n.nodeType&&(" "+i+" ").replace(Ue," ")){for(s=0;o=t[s++];)for(;-1<r.indexOf(" "+o+" ");)r=r.replace(" "+o+" "," ");i!==(a=k.trim(r))&&k.attr(n,"class",a)}return this},"toggleClass":function(i,e){var o=typeof i;return"boolean"==typeof e&&"string"==o?e?this.addClass(i):this.removeClass(i):k.isFunction(i)?this.each(function(t){k(this).toggleClass(i.call(this,t,He(this),e),e)}):this.each(function(){var t,e,n,r;if("string"==o)for(e=0,n=k(this),r=i.match(L)||[];t=r[e++];)n.hasClass(t)?n.removeClass(t):n.addClass(t);else void 0!==i&&"boolean"!=o||((t=He(this))&&k._data(this,"__className__",t),k.attr(this,"class",t||!1===i?"":k._data(this,"__className__")||""))})},"hasClass":function(t){var e,n,r=0;for(e=" "+t+" ";n=this[r++];)if(1===n.nodeType&&-1<(" "+He(n)+" ").replace(Ue," ").indexOf(e))return!0;return!1}}),k.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,n){k.fn[n]=function(t,e){return 0<arguments.length?this.on(n,null,t,e):this.trigger(n)}}),k.fn.extend({"hover":function(t,e){return this.mouseenter(t).mouseleave(e||t)}});var qe=E.location,We=k.now(),ze=/\?/,Ge=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;k.parseJSON=function(t){if(E.JSON&&E.JSON.parse)return E.JSON.parse(t+"");var i,o=null,e=k.trim(t+"");return e&&!k.trim(e.replace(Ge,function(t,e,n,r){return i&&e&&(o=0),0===o?t:(i=n||e,o+=!r-!n,"")}))?Function("return "+e)():k.error("Invalid JSON: "+t)},k.parseXML=function(t){var e;if(!t||"string"!=typeof t)return null;try{E.DOMParser?e=(new E.DOMParser).parseFromString(t,"text/xml"):((e=new E.ActiveXObject("Microsoft.XMLDOM")).async="false",e.loadXML(t))}catch(t){e=void 0}return e&&e.documentElement&&!e.getElementsByTagName("parsererror").length||k.error("Invalid XML: "+t),e};var Ye=/#.*$/,Ve=/([?&])_=[^&]*/,Qe=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,$e=/^(?:GET|HEAD)$/,Xe=/^\/\//,Ke=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Je={},Ze={},tn="*/".concat("*"),en=qe.href,nn=Ke.exec(en.toLowerCase())||[];function rn(o){return function(t,e){"string"!=typeof t&&(e=t,t="*");var n,r=0,i=t.toLowerCase().match(L)||[];if(k.isFunction(e))for(;n=i[r++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(e)):(o[n]=o[n]||[]).push(e)}}function on(e,i,o,s){var a={},c=e===Ze;function u(t){var r;return a[t]=!0,k.each(e[t]||[],function(t,e){var n=e(i,o,s);return"string"!=typeof n||c||a[n]?c?!(r=n):void 0:(i.dataTypes.unshift(n),u(n),!1)}),r}return u(i.dataTypes[0])||!a["*"]&&u("*")}function sn(t,e){var n,r,i=k.ajaxSettings.flatOptions||{};for(r in e)void 0!==e[r]&&((i[r]?t:n=n||{})[r]=e[r]);return n&&k.extend(!0,t,n),t}k.extend({"active":0,"lastModified":{},"etag":{},"ajaxSettings":{"url":en,"type":"GET","isLocal":/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(nn[1]),"global":!0,"processData":!0,"async":!0,"contentType":"application/x-www-form-urlencoded; charset=UTF-8","accepts":{"*":tn,"text":"text/plain","html":"text/html","xml":"application/xml, text/xml","json":"application/json, text/javascript"},"contents":{"xml":/\bxml\b/,"html":/\bhtml/,"json":/\bjson\b/},"responseFields":{"xml":"responseXML","text":"responseText","json":"responseJSON"},"converters":{"* text":String,"text html":!0,"text json":k.parseJSON,"text xml":k.parseXML},"flatOptions":{"url":!0,"context":!0}},"ajaxSetup":function(t,e){return e?sn(sn(t,k.ajaxSettings),e):sn(k.ajaxSettings,t)},"ajaxPrefilter":rn(Je),"ajaxTransport":rn(Ze),"ajax":function(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,r,l,p,h,f,d,i,g=k.ajaxSetup({},e),v=g.context||g,y=g.context&&(v.nodeType||v.jquery)?k(v):k.event,m=k.Deferred(),b=k.Callbacks("once memory"),w=g.statusCode||{},o={},s={},x=0,a="canceled",T={"readyState":0,"getResponseHeader":function(t){var e;if(2===x){if(!i)for(i={};e=Qe.exec(p);)i[e[1].toLowerCase()]=e[2];e=i[t.toLowerCase()]}return null==e?null:e},"getAllResponseHeaders":function(){return 2===x?p:null},"setRequestHeader":function(t,e){var n=t.toLowerCase();return x||(t=s[n]=s[n]||t,o[t]=e),this},"overrideMimeType":function(t){return x||(g.mimeType=t),this},"statusCode":function(t){var e;if(t)if(x<2)for(e in t)w[e]=[w[e],t[e]];else T.always(t[T.status]);return this},"abort":function(t){var e=t||a;return d&&d.abort(e),c(0,e),this}};if(m.promise(T).complete=b.add,T.success=T.done,T.error=T.fail,g.url=((t||g.url||en)+"").replace(Ye,"").replace(Xe,nn[1]+"//"),g.type=e.method||e.type||g.method||g.type,g.dataTypes=k.trim(g.dataType||"*").toLowerCase().match(L)||[""],null==g.crossDomain&&(n=Ke.exec(g.url.toLowerCase()),g.crossDomain=!(!n||n[1]===nn[1]&&n[2]===nn[2]&&(n[3]||("http:"===n[1]?"80":"443"))===(nn[3]||("http:"===nn[1]?"80":"443")))),g.data&&g.processData&&"string"!=typeof g.data&&(g.data=k.param(g.data,g.traditional)),on(Je,g,e,T),2===x)return T;for(r in(f=k.event&&g.global)&&0==k.active++&&k.event.trigger("ajaxStart"),g.type=g.type.toUpperCase(),g.hasContent=!$e.test(g.type),l=g.url,g.hasContent||(g.data&&(l=g.url+=(ze.test(l)?"&":"?")+g.data,delete g.data),!1===g.cache&&(g.url=Ve.test(l)?l.replace(Ve,"$1_="+We++):l+(ze.test(l)?"&":"?")+"_="+We++)),g.ifModified&&(k.lastModified[l]&&T.setRequestHeader("If-Modified-Since",k.lastModified[l]),k.etag[l]&&T.setRequestHeader("If-None-Match",k.etag[l])),(g.data&&g.hasContent&&!1!==g.contentType||e.contentType)&&T.setRequestHeader("Content-Type",g.contentType),T.setRequestHeader("Accept",g.dataTypes[0]&&g.accepts[g.dataTypes[0]]?g.accepts[g.dataTypes[0]]+("*"!==g.dataTypes[0]?", "+tn+"; q=0.01":""):g.accepts["*"]),g.headers)T.setRequestHeader(r,g.headers[r]);if(g.beforeSend&&(!1===g.beforeSend.call(v,T,g)||2===x))return T.abort();for(r in a="abort",{"success":1,"error":1,"complete":1})T[r](g[r]);if(d=on(Ze,g,e,T)){if(T.readyState=1,f&&y.trigger("ajaxSend",[T,g]),2===x)return T;g.async&&0<g.timeout&&(h=E.setTimeout(function(){T.abort("timeout")},g.timeout));try{x=1,d.send(o,c)}catch(t){if(!(x<2))throw t;c(-1,t)}}else c(-1,"No Transport");function c(t,e,n,r){var i,o,s,a,c,u=e;2!==x&&(x=2,h&&E.clearTimeout(h),d=void 0,p=r||"",T.readyState=0<t?4:0,i=200<=t&&t<300||304===t,n&&(a=function(t,e,n){for(var r,i,o,s,a=t.contents,c=t.dataTypes;"*"===c[0];)c.shift(),void 0===i&&(i=t.mimeType||e.getResponseHeader("Content-Type"));if(i)for(s in a)if(a[s]&&a[s].test(i)){c.unshift(s);break}if(c[0]in n)o=c[0];else{for(s in n){if(!c[0]||t.converters[s+" "+c[0]]){o=s;break}r=r||s}o=o||r}if(o)return o!==c[0]&&c.unshift(o),n[o]}(g,T,n)),a=function(t,e,n,r){var i,o,s,a,c,u={},l=t.dataTypes.slice();if(l[1])for(s in t.converters)u[s.toLowerCase()]=t.converters[s];for(o=l.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!c&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),c=o,o=l.shift())if("*"===o)o=c;else if("*"!==c&&c!==o){if(!(s=u[c+" "+o]||u["* "+o]))for(i in u)if((a=i.split(" "))[1]===o&&(s=u[c+" "+a[0]]||u["* "+a[0]])){!0===s?s=u[i]:!0!==u[i]&&(o=a[0],l.unshift(a[1]));break}if(!0!==s)if(s&&t["throws"])e=s(e);else try{e=s(e)}catch(t){return{"state":"parsererror","error":s?t:"No conversion from "+c+" to "+o}}}return{"state":"success","data":e}}(g,a,T,i),i?(g.ifModified&&((c=T.getResponseHeader("Last-Modified"))&&(k.lastModified[l]=c),(c=T.getResponseHeader("etag"))&&(k.etag[l]=c)),204===t||"HEAD"===g.type?u="nocontent":304===t?u="notmodified":(u=a.state,o=a.data,i=!(s=a.error))):(s=u,!t&&u||(u="error",t<0&&(t=0))),T.status=t,T.statusText=(e||u)+"",i?m.resolveWith(v,[o,u,T]):m.rejectWith(v,[T,u,s]),T.statusCode(w),w=void 0,f&&y.trigger(i?"ajaxSuccess":"ajaxError",[T,g,i?o:s]),b.fireWith(v,[T,u]),f&&(y.trigger("ajaxComplete",[T,g]),--k.active||k.event.trigger("ajaxStop")))}return T},"getJSON":function(t,e,n){return k.get(t,e,n,"json")},"getScript":function(t,e){return k.get(t,void 0,e,"script")}}),k.each(["get","post"],function(t,i){k[i]=function(t,e,n,r){return k.isFunction(e)&&(r=r||n,n=e,e=void 0),k.ajax(k.extend({"url":t,"type":i,"dataType":r,"data":e,"success":n},k.isPlainObject(t)&&t))}}),k._evalUrl=function(t){return k.ajax({"url":t,"type":"GET","dataType":"script","cache":!0,"async":!1,"global":!1,"throws":!0})},k.fn.extend({"wrapAll":function(e){if(k.isFunction(e))return this.each(function(t){k(this).wrapAll(e.call(this,t))});if(this[0]){var t=k(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var t=this;t.firstChild&&1===t.firstChild.nodeType;)t=t.firstChild;return t}).append(this)}return this},"wrapInner":function(n){return k.isFunction(n)?this.each(function(t){k(this).wrapInner(n.call(this,t))}):this.each(function(){var t=k(this),e=t.contents();e.length?e.wrapAll(n):t.append(n)})},"wrap":function(e){var n=k.isFunction(e);return this.each(function(t){k(this).wrapAll(n?e.call(this,t):e)})},"unwrap":function(){return this.parent().each(function(){k.nodeName(this,"body")||k(this).replaceWith(this.childNodes)}).end()}}),k.expr.filters.hidden=function(t){return y.reliableHiddenOffsets()?t.offsetWidth<=0&&t.offsetHeight<=0&&!t.getClientRects().length:function(t){if(!k.contains(t.ownerDocument||d,t))return!0;for(;t&&1===t.nodeType;){if("none"===((e=t).style&&e.style.display||k.css(e,"display"))||"hidden"===t.type)return!0;t=t.parentNode}var e;return!1}(t)},k.expr.filters.visible=function(t){return!k.expr.filters.hidden(t)};var an=/%20/g,cn=/\[\]$/,un=/\r?\n/g,ln=/^(?:submit|button|image|reset|file)$/i,pn=/^(?:input|select|textarea|keygen)/i;function hn(n,t,r,i){var e;if(k.isArray(t))k.each(t,function(t,e){r||cn.test(n)?i(n,e):hn(n+"["+("object"==typeof e&&null!=e?t:"")+"]",e,r,i)});else if(r||"object"!==k.type(t))i(n,t);else for(e in t)hn(n+"["+e+"]",t[e],r,i)}k.param=function(t,e){function n(t,e){e=k.isFunction(e)?e():null==e?"":e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)}var r,i=[];if(void 0===e&&(e=k.ajaxSettings&&k.ajaxSettings.traditional),k.isArray(t)||t.jquery&&!k.isPlainObject(t))k.each(t,function(){n(this.name,this.value)});else for(r in t)hn(r,t[r],e,n);return i.join("&").replace(an,"+")},k.fn.extend({"serialize":function(){return k.param(this.serializeArray())},"serializeArray":function(){return this.map(function(){var t=k.prop(this,"elements");return t?k.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!k(this).is(":disabled")&&pn.test(this.nodeName)&&!ln.test(t)&&(this.checked||!Z.test(t))}).map(function(t,e){var n=k(this).val();return null==n?null:k.isArray(n)?k.map(n,function(t){return{"name":e.name,"value":t.replace(un,"\r\n")}}):{"name":e.name,"value":n.replace(un,"\r\n")}}).get()}}),k.ajaxSettings.xhr=void 0!==E.ActiveXObject?function(){return this.isLocal?yn():8<d.documentMode?vn():/^(get|post|head|put|delete|options)$/i.test(this.type)&&vn()||yn()}:vn;var fn=0,dn={},gn=k.ajaxSettings.xhr();function vn(){try{return new E.XMLHttpRequest}catch(t){}}function yn(){try{return new E.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}E.attachEvent&&E.attachEvent("onunload",function(){for(var t in dn)dn[t](void 0,!0)}),y.cors=!!gn&&"withCredentials"in gn,(gn=y.ajax=!!gn)&&k.ajaxTransport(function(c){var u;if(!c.crossDomain||y.cors)return{"send":function(t,o){var e,s=c.xhr(),a=++fn;if(s.open(c.type,c.url,c.async,c.username,c.password),c.xhrFields)for(e in c.xhrFields)s[e]=c.xhrFields[e];for(e in c.mimeType&&s.overrideMimeType&&s.overrideMimeType(c.mimeType),c.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t)void 0!==t[e]&&s.setRequestHeader(e,t[e]+"");s.send(c.hasContent&&c.data||null),u=function(t,e){var n,r,i;if(u&&(e||4===s.readyState))if(delete dn[a],u=void 0,s.onreadystatechange=k.noop,e)4!==s.readyState&&s.abort();else{i={},n=s.status,"string"==typeof s.responseText&&(i.text=s.responseText);try{r=s.statusText}catch(t){r=""}n||!c.isLocal||c.crossDomain?1223===n&&(n=204):n=i.text?200:404}i&&o(n,r,i,s.getAllResponseHeaders())},c.async?4===s.readyState?E.setTimeout(u):s.onreadystatechange=dn[a]=u:u()},"abort":function(){u&&u(void 0,!0)}}}),k.ajaxSetup({"accepts":{"script":"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},"contents":{"script":/\b(?:java|ecma)script\b/},"converters":{"text script":function(t){return k.globalEval(t),t}}}),k.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET",t.global=!1)}),k.ajaxTransport("script",function(e){if(e.crossDomain){var r,i=d.head||k("head")[0]||d.documentElement;return{"send":function(t,n){(r=d.createElement("script")).async=!0,e.scriptCharset&&(r.charset=e.scriptCharset),r.src=e.url,r.onload=r.onreadystatechange=function(t,e){!e&&r.readyState&&!/loaded|complete/.test(r.readyState)||(r.onload=r.onreadystatechange=null,r.parentNode&&r.parentNode.removeChild(r),r=null,e||n(200,"success"))},i.insertBefore(r,i.firstChild)},"abort":function(){r&&r.onload(void 0,!0)}}}});var mn=[],bn=/(=)\?(?=&|$)|\?\?/;k.ajaxSetup({"jsonp":"callback","jsonpCallback":function(){var t=mn.pop()||k.expando+"_"+We++;return this[t]=!0,t}}),k.ajaxPrefilter("json jsonp",function(t,e,n){var r,i,o,s=!1!==t.jsonp&&(bn.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&bn.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return r=t.jsonpCallback=k.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(bn,"$1"+r):!1!==t.jsonp&&(t.url+=(ze.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return o||k.error(r+" was not called"),o[0]},t.dataTypes[0]="json",i=E[r],E[r]=function(){o=arguments},n.always(function(){void 0===i?k(E).removeProp(r):E[r]=i,t[r]&&(t.jsonpCallback=e.jsonpCallback,mn.push(r)),o&&k.isFunction(i)&&i(o[0]),o=i=void 0}),"script"}),k.parseHTML=function(t,e,n){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(n=e,e=!1),e=e||d;var r=x.exec(t),i=!n&&[];return r?[e.createElement(r[1])]:(r=pt([t],e,i),i&&i.length&&k(i).remove(),k.merge([],r.childNodes))};var wn=k.fn.load;function xn(t){return k.isWindow(t)?t:9===t.nodeType&&(t.defaultView||t.parentWindow)}k.fn.load=function(t,e,n){if("string"!=typeof t&&wn)return wn.apply(this,arguments);var r,i,o,s=this,a=t.indexOf(" ");return-1<a&&(r=k.trim(t.slice(a,t.length)),t=t.slice(0,a)),k.isFunction(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),0<s.length&&k.ajax({"url":t,"type":i||"GET","dataType":"html","data":e}).done(function(t){o=arguments,s.html(r?k("<div>").append(k.parseHTML(t)).find(r):t)}).always(n&&function(t,e){s.each(function(){n.apply(this,o||[t.responseText,e,t])})}),this},k.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){k.fn[e]=function(t){return this.on(e,t)}}),k.expr.filters.animated=function(e){return k.grep(k.timers,function(t){return e===t.elem}).length},k.offset={"setOffset":function(t,e,n){var r,i,o,s,a,c,u=k.css(t,"position"),l=k(t),p={};"static"===u&&(t.style.position="relative"),a=l.offset(),o=k.css(t,"top"),c=k.css(t,"left"),i=("absolute"===u||"fixed"===u)&&-1<k.inArray("auto",[o,c])?(s=(r=l.position()).top,r.left):(s=parseFloat(o)||0,parseFloat(c)||0),k.isFunction(e)&&(e=e.call(t,n,k.extend({},a))),null!=e.top&&(p.top=e.top-a.top+s),null!=e.left&&(p.left=e.left-a.left+i),"using"in e?e.using.call(t,p):l.css(p)}},k.fn.extend({"offset":function(e){if(arguments.length)return void 0===e?this:this.each(function(t){k.offset.setOffset(this,e,t)});var t,n,r={"top":0,"left":0},i=this[0],o=i&&i.ownerDocument;return o?(t=o.documentElement,k.contains(t,i)?(void 0!==i.getBoundingClientRect&&(r=i.getBoundingClientRect()),n=xn(o),{"top":r.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),"left":r.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):r):void 0},"position":function(){if(this[0]){var t,e,n={"top":0,"left":0},r=this[0];return"fixed"===k.css(r,"position")?e=r.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),k.nodeName(t[0],"html")||(n=t.offset()),n.top+=k.css(t[0],"borderTopWidth",!0),n.left+=k.css(t[0],"borderLeftWidth",!0)),{"top":e.top-n.top-k.css(r,"marginTop",!0),"left":e.left-n.left-k.css(r,"marginLeft",!0)}}},"offsetParent":function(){return this.map(function(){for(var t=this.offsetParent;t&&!k.nodeName(t,"html")&&"static"===k.css(t,"position");)t=t.offsetParent;return t||Kt})}}),k.each({"scrollLeft":"pageXOffset","scrollTop":"pageYOffset"},function(e,i){var o=/Y/.test(i);k.fn[e]=function(t){return J(this,function(t,e,n){var r=xn(t);if(void 0===n)return r?i in r?r[i]:r.document.documentElement[e]:t[e];r?r.scrollTo(o?k(r).scrollLeft():n,o?n:k(r).scrollTop()):t[e]=n},e,t,arguments.length,null)}}),k.each(["top","left"],function(t,n){k.cssHooks[n]=ne(y.pixelPosition,function(t,e){if(e)return e=te(t,n),Xt.test(e)?k(t).position()[n]+"px":e})}),k.each({"Height":"height","Width":"width"},function(o,s){k.each({"padding":"inner"+o,"content":s,"":"outer"+o},function(r,t){k.fn[t]=function(t,e){var n=arguments.length&&(r||"boolean"!=typeof t),i=r||(!0===t||!0===e?"margin":"border");return J(this,function(t,e,n){var r;return k.isWindow(t)?t.document.documentElement["client"+o]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+o],r["scroll"+o],t.body["offset"+o],r["offset"+o],r["client"+o])):void 0===n?k.css(t,e,i):k.style(t,e,n,i)},s,n?t:void 0,n,null)}})}),k.fn.extend({"bind":function(t,e,n){return this.on(t,null,e,n)},"unbind":function(t,e){return this.off(t,null,e)},"delegate":function(t,e,n,r){return this.on(e,t,n,r)},"undelegate":function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)}}),k.fn.size=function(){return this.length},k.fn.andSelf=k.fn.addBack,void 0===(Sn=function(){return k}.apply(Cn,[]))||(kn.exports=Sn);var Tn=E.jQuery,En=E.$;return k.noConflict=function(t){return E.$===k&&(E.$=En),t&&E.jQuery===k&&(E.jQuery=Tn),k},t||(E.jQuery=E.$=k),k},"object"==typeof kn.exports?kn.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)},function(t,e,n){"use strict";e.__esModule=!0;var r,i=n(55),o=(r=i)&&r.__esModule?r:{"default":r};e["default"]=function(t,e,n){return e in t?(0,o["default"])(t,e,{"value":n,"enumerable":!0,"configurable":!0,"writable":!0}):t[e]=n,t}},function(t,e,n){"use strict";e.__esModule=!0;var r,i=n(55),o=(r=i)&&r.__esModule?r:{"default":r};function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),(0,o["default"])(t,r.key,r)}}e["default"]=function(t,e,n){return e&&s(t.prototype,e),n&&s(t,n),t}},function(t,e,n){"use strict";e.__esModule=!0,e["default"]=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},function(t,e,n){t.exports={"default":n(88),"__esModule":!0}},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e){var n=t.exports={"version":"2.5.7"};"number"==typeof __e&&(__e=n)},function(t,e,n){var r=n(53)("wks"),i=n(41),o=n(5).Symbol,s="function"==typeof o;(t.exports=function(t){return r[t]||(r[t]=s&&o[t]||(s?o:i)("Symbol."+t))}).store=r},function(t,e,n){t.exports={"default":n(87),"__esModule":!0}},function(t,e,n){var g=n(5),v=n(7),y=n(14),m=n(16),b=n(22),w="prototype",x=function(t,e,n){var r,i,o,s=t&x.F,a=t&x.G,c=t&x.S,u=t&x.P,l=t&x.B,p=t&x.W,h=a?v:v[e]||(v[e]={}),f=h[w],d=a?g:c?g[e]:(g[e]||{})[w];for(r in a&&(n=e),n)(i=!s&&d&&void 0!==d[r])&&b(h,r)||(o=i?d[r]:n[r],h[r]=a&&"function"!=typeof d[r]?n[r]:l&&i?y(o,g):p&&d[r]==o?function(r){function t(t,e,n){if(this instanceof r){switch(arguments.length){case 0:return new r;case 1:return new r(t);case 2:return new r(t,e)}return new r(t,e,n)}return r.apply(this,arguments)}return t[w]=r[w],t}(o):u&&"function"==typeof o?y(Function.call,o):o,u&&((h.virtual||(h.virtual={}))[r]=o,t&x.R&&f&&!f[r]&&m(f,r,o)))};x.F=1,x.G=2,x.S=4,x.P=8,x.B=16,x.W=32,x.U=64,x.R=128,t.exports=x},function(t,e,n){var r=n(17),i=n(90),o=n(91),s=Object.defineProperty;e.f=n(15)?Object.defineProperty:function(t,e,n){if(r(t),e=o(e,!0),r(n),i)try{return s(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(n,o,r){(function(e){function t(){var t;try{t=o.storage.debug}catch(t){}return!t&&void 0!==e&&"env"in e&&(t=e.env.DEBUG),t}(o=n.exports=r(117)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},o.formatArgs=function(t){var e=this.useColors;if(t[0]=(e?"%c":"")+this.namespace+(e?" %c":" ")+t[0]+(e?"%c ":" ")+"+"+o.humanize(this.diff),!e)return;var n="color: "+this.color;t.splice(1,0,n,"color: inherit");var r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,function(t){"%%"!==t&&(r++,"%c"===t&&(i=r))}),t.splice(i,0,n)},o.save=function(t){try{null==t?o.storage.removeItem("debug"):o.storage.debug=t}catch(t){}},o.load=t,o.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&31<=parseInt(RegExp.$1,10)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},o.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(t){}}(),o.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],o.formatters.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}},o.enable(t())}).call(this,r(116))},function(t,e,n){var o=n(20);t.exports=function(r,i,t){if(o(r),void 0===i)return r;switch(t){case 1:return function(t){return r.call(i,t)};case 2:return function(t,e){return r.call(i,t,e)};case 3:return function(t,e,n){return r.call(i,t,e,n)}}return function(){return r.apply(i,arguments)}}},function(t,e,n){t.exports=!n(21)(function(){return 7!=Object.defineProperty({},"a",{"get":function(){return 7}}).a})},function(t,e,n){var r=n(11),i=n(34);t.exports=n(15)?function(t,e,n){return r.f(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){var r=n(12);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,e,n){function r(t){if(t)return function(t){for(var e in r.prototype)t[e]=r.prototype[e];return t}(t)}(t.exports=r).prototype.on=r.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},r.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},r.prototype.off=r.prototype.removeListener=r.prototype.removeAllListeners=r.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+t];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var i=0;i<r.length;i++)if((n=r[i])===e||n.fn===e){r.splice(i,1);break}return this},r.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),n=this._callbacks["$"+t];if(n)for(var r=0,i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,e);return this},r.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},r.prototype.hasListeners=function(t){return!!this.listeners(t).length}},function(t,g,v){(function(u){var i,t=v(124),e=v(75),h=v(129),a=v(130),s=v(131);u&&u.ArrayBuffer&&(i=v(133));var n="undefined"!=typeof navigator&&/Android/i.test(navigator.userAgent),r="undefined"!=typeof navigator&&/PhantomJS/i.test(navigator.userAgent),c=n||r;g.protocol=3;var l=g.packets={"open":0,"close":1,"ping":2,"pong":3,"message":4,"upgrade":5,"noop":6},o=t(l),f={"type":"error","data":"parser error"},p=v(134);function d(t,e,n){function r(n,t,r){e(t,function(t,e){i[n]=e,r(t,i)})}for(var i=new Array(t.length),o=a(t.length,n),s=0;s<t.length;s++)r(s,t[s],o)}g.encodePacket=function(t,e,n,r){"function"==typeof e&&(r=e,e=!1),"function"==typeof n&&(r=n,n=null);var i=void 0===t.data?void 0:t.data.buffer||t.data;if(u.ArrayBuffer&&i instanceof ArrayBuffer)return function(t,e,n){if(!e)return g.encodeBase64Packet(t,n);var r=t.data,i=new Uint8Array(r),o=new Uint8Array(1+r.byteLength);o[0]=l[t.type];for(var s=0;s<i.length;s++)o[s+1]=i[s];return n(o.buffer)}(t,e,r);if(p&&i instanceof u.Blob)return function(t,e,n){if(!e)return g.encodeBase64Packet(t,n);if(c)return function(t,e,n){if(!e)return g.encodeBase64Packet(t,n);var r=new FileReader;return r.onload=function(){t.data=r.result,g.encodePacket(t,e,!0,n)},r.readAsArrayBuffer(t.data)}(t,e,n);var r=new Uint8Array(1);r[0]=l[t.type];var i=new p([r.buffer,t.data]);return n(i)}(t,e,r);if(i&&i.base64)return function(t,e){var n="b"+g.packets[t.type]+t.data.data;return e(n)}(t,r);var o=l[t.type];return void 0!==t.data&&(o+=n?s.encode(String(t.data),{"strict":!1}):String(t.data)),r(""+o)},g.encodeBase64Packet=function(e,n){var r,i="b"+g.packets[e.type];if(p&&e.data instanceof u.Blob){var o=new FileReader;return o.onload=function(){var t=o.result.split(",")[1];n(i+t)},o.readAsDataURL(e.data)}try{r=String.fromCharCode.apply(null,new Uint8Array(e.data))}catch(t){for(var s=new Uint8Array(e.data),a=new Array(s.length),c=0;c<s.length;c++)a[c]=s[c];r=String.fromCharCode.apply(null,a)}return i+=u.btoa(r),n(i)},g.decodePacket=function(t,e,n){if(void 0===t)return f;if("string"==typeof t){if("b"===t.charAt(0))return g.decodeBase64Packet(t.substr(1),e);if(n&&!1===(t=function(t){try{t=s.decode(t,{"strict":!1})}catch(t){return!1}return t}(t)))return f;var r=t.charAt(0);return Number(r)==r&&o[r]?1<t.length?{"type":o[r],"data":t.substring(1)}:{"type":o[r]}:f}r=new Uint8Array(t)[0];var i=h(t,1);return p&&"blob"===e&&(i=new p([i])),{"type":o[r],"data":i}},g.decodeBase64Packet=function(t,e){var n=o[t.charAt(0)];if(!i)return{"type":n,"data":{"base64":!0,"data":t.substr(1)}};var r=i.decode(t.substr(1));return"blob"===e&&p&&(r=new p([r])),{"type":n,"data":r}},g.encodePayload=function(t,n,r){"function"==typeof n&&(r=n,n=null);var i=e(t);if(n&&i)return p&&!c?g.encodePayloadAsBlob(t,r):g.encodePayloadAsArrayBuffer(t,r);if(!t.length)return r("0:");d(t,function(t,e){g.encodePacket(t,!!i&&n,!1,function(t){e(null,function(t){return t.length+":"+t}(t))})},function(t,e){return r(e.join(""))})},g.decodePayload=function(t,e,n){if("string"!=typeof t)return g.decodePayloadAsBinary(t,e,n);var r;if("function"==typeof e&&(n=e,e=null),""===t)return n(f,0,1);for(var i,o,s="",a=0,c=t.length;a<c;a++){var u=t.charAt(a);if(":"===u){if(""===s||s!=(i=Number(s)))return n(f,0,1);if(s!=(o=t.substr(a+1,i)).length)return n(f,0,1);if(o.length){if(r=g.decodePacket(o,e,!1),f.type===r.type&&f.data===r.data)return n(f,0,1);if(!1===n(r,a+i,c))return}a+=i,s=""}else s+=u}return""!==s?n(f,0,1):void 0},g.encodePayloadAsArrayBuffer=function(t,r){if(!t.length)return r(new ArrayBuffer(0));d(t,function(t,e){g.encodePacket(t,!0,!0,function(t){return e(null,t)})},function(t,e){var n=e.reduce(function(t,e){var n;return t+(n="string"==typeof e?e.length:e.byteLength).toString().length+n+2},0),s=new Uint8Array(n),a=0;return e.forEach(function(t){var e="string"==typeof t,n=t;if(e){for(var r=new Uint8Array(t.length),i=0;i<t.length;i++)r[i]=t.charCodeAt(i);n=r.buffer}s[a++]=e?0:1;var o=n.byteLength.toString();for(i=0;i<o.length;i++)s[a++]=parseInt(o[i]);s[a++]=255;for(r=new Uint8Array(n),i=0;i<r.length;i++)s[a++]=r[i]}),r(s.buffer)})},g.encodePayloadAsBlob=function(t,n){d(t,function(t,a){g.encodePacket(t,!0,!0,function(t){var e=new Uint8Array(1);if(e[0]=1,"string"==typeof t){for(var n=new Uint8Array(t.length),r=0;r<t.length;r++)n[r]=t.charCodeAt(r);t=n.buffer,e[0]=0}var i=(t instanceof ArrayBuffer?t.byteLength:t.size).toString(),o=new Uint8Array(i.length+1);for(r=0;r<i.length;r++)o[r]=parseInt(i[r]);if(o[i.length]=255,p){var s=new p([e.buffer,o.buffer,t]);a(null,s)}})},function(t,e){return n(new p(e))})},g.decodePayloadAsBinary=function(t,n,r){"function"==typeof n&&(r=n,n=null);for(var e=t,i=[];0<e.byteLength;){for(var o=new Uint8Array(e),s=0===o[0],a="",c=1;255!==o[c];c++){if(310<a.length)return r(f,0,1);a+=o[c]}e=h(e,2+a.length),a=parseInt(a);var u=h(e,0,a);if(s)try{u=String.fromCharCode.apply(null,new Uint8Array(u))}catch(t){var l=new Uint8Array(u);u="";for(c=0;c<l.length;c++)u+=String.fromCharCode(l[c])}i.push(u),e=h(e,a)}var p=i.length;i.forEach(function(t,e){r(g.decodePacket(t,n,!0),e,p)})}}).call(this,v(6))},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e){t.exports={}},function(t,e,n){var h=n(14),f=n(61),d=n(62),g=n(17),v=n(27),y=n(63),m={},b={};(e=t.exports=function(t,e,n,r,i){var o,s,a,c,u=i?function(){return t}:y(t),l=h(n,r,e?2:1),p=0;if("function"!=typeof u)throw TypeError(t+" is not iterable!");if(d(u)){for(o=v(t.length);p<o;p++)if((c=e?l(g(s=t[p])[0],s[1]):l(t[p]))===m||c===b)return c}else for(a=u.call(t);!(s=a.next()).done;)if((c=f(a,l,s.value,e))===m||c===b)return c}).BREAK=m,e.RETURN=b},function(t,e,n){function i(t){if(o[t])return o[t].exports;var e=o[t]={"i":t,"l":!1,"exports":{}};return r[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var r,o;t.exports=(o={},i.m=r=[function(t,e,n){"use strict";e.__esModule=!0,e.parseEmotions=function(t,r){void 0===r&&(r=1);return String(t).replace(/\[(.+?)\]/g,function(t,e){if(!i.map.hasOwnProperty(e))return t;var n=i.map[e];switch(r){case 1:return'<img src="'+n.url+'" alt="'+n.title+'" class="plv-emotion-img" />';case 2:return"[["+n.url+"]]"}})},e.genDOMList=function(){var r="";i.list.forEach(function(t,e){var n=-e*o;r+='<li class="plv-emotion-panel__item">\n      <i class="plv-emotion-panel__item__ico" title="'+t.title+'" data-title="'+t.title+'" style="background-position: '+n+'px 0;"></i>\n    </li>'});var t=document.createElement("ul");return t.className="plv-emotion-panel",t.innerHTML=r,t};var i=n(1);n(2);var o=48},function(t,e,n){"use strict";e.__esModule=!0,e.map=e.list=void 0;var r=[{"url":"1.png","title":"微笑"},{"url":"2.png","title":"撇嘴"},{"url":"3.png","title":"色"},{"url":"4.png","title":"发呆"},{"url":"5.png","title":"得意"},{"url":"6.png","title":"流泪"},{"url":"7.png","title":"害羞"},{"url":"8.png","title":"闭嘴"},{"url":"9.png","title":"睡"},{"url":"10.png","title":"大哭"},{"url":"11.png","title":"尴尬"},{"url":"12.png","title":"发怒"},{"url":"13.png","title":"调皮"},{"url":"14.png","title":"呲牙"},{"url":"15.png","title":"惊讶"},{"url":"16.png","title":"难过"},{"url":"17.png","title":"酷"},{"url":"18.png","title":"冷汗"},{"url":"19.png","title":"抓狂"},{"url":"20.png","title":"吐"},{"url":"21.png","title":"偷笑"},{"url":"22.png","title":"可爱"},{"url":"23.png","title":"白眼"},{"url":"24.png","title":"傲慢"},{"url":"25.png","title":"饥饿"},{"url":"26.png","title":"困"},{"url":"27.png","title":"惊恐"},{"url":"28.png","title":"流汗"},{"url":"29.png","title":"憨笑"},{"url":"30.png","title":"大兵"},{"url":"31.png","title":"奋斗"},{"url":"32.png","title":"咒骂"},{"url":"33.png","title":"疑问"},{"url":"34.png","title":"嘘"},{"url":"35.png","title":"晕"},{"url":"36.png","title":"折磨"},{"url":"37.png","title":"衰"},{"url":"38.png","title":"骷髅"},{"url":"39.png","title":"敲打"},{"url":"40.png","title":"再见"},{"url":"41.png","title":"擦汗"},{"url":"42.png","title":"抠鼻"},{"url":"43.png","title":"鼓掌"},{"url":"44.png","title":"糗大了"},{"url":"45.png","title":"坏笑"},{"url":"46.png","title":"左哼哼"},{"url":"47.png","title":"右哼哼"},{"url":"48.png","title":"哈欠"},{"url":"49.png","title":"鄙视"},{"url":"50.png","title":"委屈"},{"url":"51.png","title":"快哭了"},{"url":"52.png","title":"阴险"},{"url":"53.png","title":"亲亲"},{"url":"54.png","title":"吓"},{"url":"55.png","title":"可怜"},{"url":"56.png","title":"菜刀"},{"url":"57.png","title":"西瓜"},{"url":"58.png","title":"啤酒"},{"url":"59.png","title":"篮球"},{"url":"60.png","title":"乒乓"},{"url":"61.png","title":"咖啡"},{"url":"62.png","title":"饭"},{"url":"63.png","title":"猪头"},{"url":"64.png","title":"玫瑰"},{"url":"65.png","title":"凋谢"},{"url":"66.png","title":"示爱"},{"url":"67.png","title":"爱心"},{"url":"68.png","title":"心碎"},{"url":"69.png","title":"蛋糕"},{"url":"70.png","title":"闪电"},{"url":"71.png","title":"炸弹"},{"url":"72.png","title":"刀"},{"url":"73.png","title":"足球"},{"url":"74.png","title":"瓢虫"},{"url":"75.png","title":"便便"},{"url":"76.png","title":"月亮"},{"url":"77.png","title":"太阳"},{"url":"78.png","title":"礼物"},{"url":"79.png","title":"拥抱"},{"url":"80.png","title":"强"},{"url":"81.png","title":"弱"},{"url":"82.png","title":"握手"},{"url":"83.png","title":"胜利"},{"url":"84.png","title":"抱拳"},{"url":"85.png","title":"勾引"},{"url":"86.png","title":"拳头"},{"url":"87.png","title":"差劲"},{"url":"88.png","title":"爱你"},{"url":"89.png","title":"NO"},{"url":"90.png","title":"OK"},{"url":"91.png","title":"爱情"},{"url":"92.png","title":"飞吻"},{"url":"93.png","title":"跳跳"},{"url":"94.png","title":"发抖"},{"url":"95.png","title":"怄火"},{"url":"96.png","title":"转圈"},{"url":"97.png","title":"磕头"},{"url":"98.png","title":"回头"},{"url":"99.png","title":"跳绳"},{"url":"100.png","title":"挥手"}];e.list=r;var i={};e.map=i,r.forEach(function(t){t.url="https://s2.videocc.net/emotion-sdk/v1/"+t.url,i[t.title]=t})},function(t,e,n){var r=n(3);"string"==typeof r&&(r=[[t.i,r,""]]);var i={"insert":"head","singleton":!1};n(5)(r,i);r.locals&&(t.exports=r.locals)},function(t,e,n){(t.exports=n(4)(!1)).push([t.i,'.plv-emotion-panel{margin:0;padding:0;overflow:auto;overflow-x:hidden;zoom:1}.plv-emotion-panel:after{display:block;overflow:hidden;clear:both;height:0;visibility:hidden;content:"."}.plv-emotion-panel__item{float:left;margin:0;padding:0;font-size:0}.plv-emotion-panel__item__ico{display:inline-block;width:48px;height:48px;-webkit-transform:scale(.625);-ms-transform:scale(.625);transform:scale(.625);background:url(https://s2.videocc.net/emotion-sdk/v1/all.png) no-repeat;cursor:pointer}.plv-emotion-img{width:30px;height:30px;vertical-align:middle}',""])},function(t,e,n){"use strict";t.exports=function(n){var a=[];return a.toString=function(){return this.map(function(t){var e=function(t,e){var n=t[1]||"",r=t[3];if(!r)return n;if(e&&"function"==typeof btoa){var i=function(t){var e=btoa(unescape(encodeURIComponent(JSON.stringify(t)))),n="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(e);return"/*# ".concat(n," */")}(r),o=r.sources.map(function(t){return"/*# sourceURL=".concat(r.sourceRoot).concat(t," */")});return[n].concat(o).concat([i]).join("\n")}return[n].join("\n")}(t,n);return t[2]?"@media ".concat(t[2],"{").concat(e,"}"):e}).join("")},a.i=function(t,e){"string"==typeof t&&(t=[[null,t,""]]);for(var n={},r=0;r<this.length;r++){var i=this[r][0];null!=i&&(n[i]=!0)}for(var o=0;o<t.length;o++){var s=t[o];null!=s[0]&&n[s[0]]||(e&&!s[2]?s[2]=e:e&&(s[2]="(".concat(s[2],") and (").concat(e,")")),a.push(s))}},a}},function(t,e,i){"use strict";var n,r,l={},o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},s=(r={},function(t){if(void 0===r[t]){var e=document.querySelector(t);if(window.HTMLIFrameElement&&e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}r[t]=e}return r[t]});function p(t,e){for(var n=[],r={},i=0;i<t.length;i++){var o=t[i],s=e.base?o[0]+e.base:o[0],a={"css":o[1],"media":o[2],"sourceMap":o[3]};r[s]?r[s].parts.push(a):n.push(r[s]={"id":s,"parts":[a]})}return n}function h(t,e){for(var n=0;n<t.length;n++){var r=t[n],i=l[r.id],o=0;if(i){for(i.refs++;o<i.parts.length;o++)i.parts[o](r.parts[o]);for(;o<r.parts.length;o++)i.parts.push(v(r.parts[o],e))}else{for(var s=[];o<r.parts.length;o++)s.push(v(r.parts[o],e));l[r.id]={"id":r.id,"refs":1,"parts":s}}}}function a(e){var n=document.createElement("style");if(void 0===e.attributes.nonce){var t=i.nc;t&&(e.attributes.nonce=t)}if(Object.keys(e.attributes).forEach(function(t){n.setAttribute(t,e.attributes[t])}),"function"==typeof e.insert)e.insert(n);else{var r=s(e.insert||"head");if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}return n}var c,u=(c=[],function(t,e){return c[t]=e,c.filter(Boolean).join("\n")});function f(t,e,n,r){var i=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=u(e,i);else{var o=document.createTextNode(i),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(o,s[e]):t.appendChild(o)}}var d=null,g=0;function v(e,t){var n,r,i;if(t.singleton){var o=g++;n=d=d||a(t),r=f.bind(null,n,o,!1),i=f.bind(null,n,o,!0)}else n=a(t),r=function(t,e,n){var r=n.css,i=n.media,o=n.sourceMap;if(i&&t.setAttribute("media",i),o&&btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}.bind(null,n,t),i=function(){!function(t){if(null===t.parentNode)return;t.parentNode.removeChild(t)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}t.exports=function(t,c){(c=c||{}).attributes="object"==typeof c.attributes?c.attributes:{},c.singleton||"boolean"==typeof c.singleton||(c.singleton=o());var u=p(t,c);return h(u,c),function(t){for(var e=[],n=0;n<u.length;n++){var r=u[n],i=l[r.id];i&&(i.refs--,e.push(i))}t&&h(p(t,c),c);for(var o=0;o<e.length;o++){var s=e[o];if(0===s.refs){for(var a=0;a<s.parts.length;a++)s.parts[a]();delete l[s.id]}}}}}],i.c=o,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{"enumerable":!0,"get":n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{"value":"Module"}),Object.defineProperty(t,"__esModule",{"value":!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{"enumerable":!0,"value":e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=0))},function(t,e,n){var r=n(38),i=Math.min;t.exports=function(t){return 0<t?i(r(t),9007199254740991):0}},function(t,e,n){var r=n(37);t.exports=function(t){return Object(r(t))}},function(t,e,n){var r=n(11).f,i=n(22),o=n(8)("toStringTag");t.exports=function(t,e,n){t&&!i(t=n?t:t.prototype,o)&&r(t,o,{"configurable":!0,"value":e})}},function(t,e){e.encode=function(t){var e="";for(var n in t)t.hasOwnProperty(n)&&(e.length&&(e+="&"),e+=encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e},e.decode=function(t){for(var e={},n=t.split("&"),r=0,i=n.length;r<i;r++){var o=n[r].split("=");e[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return e}},function(t,e){t.exports=function(t,e){function n(){}n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e,n){t.exports={"default":n(101),"__esModule":!0}},function(t,e,n){var r=n(12),i=n(5).document,o=r(i)&&r(i.createElement);t.exports=function(t){return o?i.createElement(t):{}}},function(t,e){t.exports=function(t,e){return{"enumerable":!(1&t),"configurable":!(2&t),"writable":!(4&t),"value":e}}},function(t,e,n){var r=n(36),i=n(37);t.exports=function(t){return r(i(t))}},function(t,e,n){var r=n(23);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(0<t?r:n)(t)}},function(t,e,n){var r=n(53)("keys"),i=n(41);t.exports=function(t){return r[t]||(r[t]=i(t))}},function(t,e){t.exports=!0},function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},function(t,e,n){"use strict";var r=n(102)(!0);n(43)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,n=this._i;return n>=e.length?{"value":void 0,"done":!0}:(t=r(e,n),this._i+=t.length,{"value":t,"done":!1})})},function(t,e,n){"use strict";function b(){return this}var w=n(40),x=n(10),T=n(103),E=n(16),k=n(24),C=n(104),S=n(29),A=n(106),N=n(8)("iterator"),O=!([].keys&&"next"in[].keys()),_="values";t.exports=function(t,e,n,r,i,o,s){C(n,e,r);function a(t){if(!O&&t in d)return d[t];switch(t){case"keys":case _:return function(){return new n(this,t)}}return function(){return new n(this,t)}}var c,u,l,p=e+" Iterator",h=i==_,f=!1,d=t.prototype,g=d[N]||d["@@iterator"]||i&&d[i],v=g||a(i),y=i?h?a("entries"):v:void 0,m="Array"==e&&d.entries||g;if(m&&(l=A(m.call(new t)))!==Object.prototype&&l.next&&(S(l,p,!0),w||"function"==typeof l[N]||E(l,N,b)),h&&g&&g.name!==_&&(f=!0,v=function(){return g.call(this)}),w&&!s||!O&&!f&&d[N]||E(d,N,v),k[e]=v,k[p]=b,i)if(c={"values":h?v:a(_),"keys":o?v:a("keys"),"entries":y},s)for(u in c)u in d||T(d,u,c[u]);else x(x.P+x.F*(O||f),e,c);return c}},function(t,e,n){var i=n(23),o=n(8)("toStringTag"),s="Arguments"==i(function(){return arguments}());t.exports=function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),o))?n:s?i(e):"Object"==(r=i(e))&&"function"==typeof e.callee?"Arguments":r}},function(t,e){t.exports=function(t,e,n,r){if(!(t instanceof e)||void 0!==r&&r in t)throw TypeError(n+": incorrect invocation!");return t}},function(t,e,n){"use strict";var i=n(20);function r(t){var n,r;this.promise=new t(function(t,e){if(void 0!==n||void 0!==r)throw TypeError("Bad Promise constructor");n=t,r=e}),this.resolve=i(n),this.reject=i(r)}t.exports.f=function(t){return new r(t)}},function(t,e,n){var i=n(16);t.exports=function(t,e,n){for(var r in e)n&&t[r]?t[r]=e[r]:i(t,r,e[r]);return t}},function(t,a,e){var c=e(13)("socket.io-parser"),n=e(18),o=e(119),u=e(49),r=e(71);function i(){}a.protocol=4,a.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],a.CONNECT=0,a.DISCONNECT=1,a.EVENT=2,a.ACK=3,a.ERROR=4,a.BINARY_EVENT=5,a.BINARY_ACK=6,a.Encoder=i,a.Decoder=p;var s=a.ERROR+'"encode error"';function l(t){var e=""+t.type;if(a.BINARY_EVENT!==t.type&&a.BINARY_ACK!==t.type||(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data){var n=function(t){try{return JSON.stringify(t)}catch(t){return!1}}(t.data);if(!1===n)return s;e+=n}return c("encoded %j as %s",t,e),e}function p(){this.reconstructor=null}function h(t){this.reconPack=t,this.buffers=[]}function f(t){return{"type":a.ERROR,"data":"parser error: "+t}}i.prototype.encode=function(t,e){(c("encoding packet %j",t),a.BINARY_EVENT===t.type||a.BINARY_ACK===t.type)?function(t,i){o.removeBlobs(t,function(t){var e=o.deconstructPacket(t),n=l(e.packet),r=e.buffers;r.unshift(n),i(r)})}(t,e):e([l(t)])},n(p.prototype),p.prototype.add=function(t){var e;if("string"==typeof t)e=function(t){var e=0,n={"type":Number(t.charAt(0))};if(null==a.types[n.type])return f("unknown packet type "+n.type);if(a.BINARY_EVENT===n.type||a.BINARY_ACK===n.type){for(var r="";"-"!==t.charAt(++e)&&(r+=t.charAt(e),e!=t.length););if(r!=Number(r)||"-"!==t.charAt(e))throw new Error("Illegal attachments");n.attachments=Number(r)}if("/"===t.charAt(e+1))for(n.nsp="";++e;){if(","===(o=t.charAt(e)))break;if(n.nsp+=o,e===t.length)break}else n.nsp="/";var i=t.charAt(e+1);if(""!==i&&Number(i)==i){for(n.id="";++e;){var o;if(null==(o=t.charAt(e))||Number(o)!=o){--e;break}if(n.id+=t.charAt(e),e===t.length)break}n.id=Number(n.id)}if(t.charAt(++e)){var s=function(t){try{return JSON.parse(t)}catch(t){return!1}}(t.substr(e));if(!(!1!==s&&(n.type===a.ERROR||u(s))))return f("invalid payload");n.data=s}return c("decoded %s as %j",t,n),n}(t),a.BINARY_EVENT===e.type||a.BINARY_ACK===e.type?(this.reconstructor=new h(e),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",e)):this.emit("decoded",e);else{if(!r(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,this.emit("decoded",e))}},p.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},h.prototype.takeBinaryData=function(t){if(this.buffers.push(t),this.buffers.length!==this.reconPack.attachments)return null;var e=o.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),e},h.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e,n){(function(i){var o=n(122);t.exports=function(t){var e=t.xdomain,n=t.xscheme,r=t.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!e||o))return new XMLHttpRequest}catch(t){}try{if("undefined"!=typeof XDomainRequest&&!n&&r)return new XDomainRequest}catch(t){}if(!e)try{return new(i[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}}).call(this,n(6))},function(t,e,n){var r=n(19);function i(t){this.path=t.path,this.hostname=t.hostname,this.port=t.port,this.secure=t.secure,this.query=t.query,this.timestampParam=t.timestampParam,this.timestampRequests=t.timestampRequests,this.readyState="",this.agent=t.agent||!1,this.socket=t.socket,this.enablesXDR=t.enablesXDR,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.forceNode=t.forceNode,this.extraHeaders=t.extraHeaders,this.localAddress=t.localAddress}n(18)((t.exports=i).prototype),i.prototype.onError=function(t,e){var n=new Error(t);return n.type="TransportError",n.description=e,this.emit("error",n),this},i.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},i.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},i.prototype.send=function(t){if("open"!==this.readyState)throw new Error("Transport not open");this.write(t)},i.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},i.prototype.onData=function(t){var e=r.decodePacket(t,this.socket.binaryType);this.onPacket(e)},i.prototype.onPacket=function(t){this.emit("packet",t)},i.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},function(t,e,n){var r=n(93),i=n(54);t.exports=Object.keys||function(t){return r(t,i)}},function(t,e,n){var r=n(7),i=n(5),o="__core-js_shared__",s=i[o]||(i[o]={});(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({"version":r.version,"mode":n(40)?"pure":"global","copyright":"© 2018 Denis Pushkarev (zloirock.ru)"})},function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,e,n){t.exports={"default":n(98),"__esModule":!0}},function(t,e){},function(t,e,r){function i(){}var o=r(17),s=r(105),a=r(54),c=r(39)("IE_PROTO"),u="prototype",l=function(){var t,e=r(33)("iframe"),n=a.length;for(e.style.display="none",r(58).appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),l=t.F;n--;)delete l[u][a[n]];return l()};t.exports=Object.create||function(t,e){var n;return null!==t?(i[u]=o(t),n=new i,i[u]=null,n[c]=t):n=l(),void 0===e?n:s(n,e)}},function(t,e,n){var r=n(5).document;t.exports=r&&r.documentElement},function(t,e,n){n(107);for(var r=n(5),i=n(16),o=n(24),s=n(8)("toStringTag"),a="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),c=0;c<a.length;c++){var u=a[c],l=r[u],p=l&&l.prototype;p&&!p[s]&&i(p,s,u),o[u]=o.Array}},function(t,e){t.exports=function(t,e){return{"value":e,"done":!!t}}},function(t,e,n){var o=n(17);t.exports=function(e,t,n,r){try{return r?t(o(n)[0],n[1]):t(n)}catch(t){var i=e["return"];throw void 0!==i&&o(i.call(e)),t}}},function(t,e,n){var r=n(24),i=n(8)("iterator"),o=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||o[i]===t)}},function(t,e,n){var r=n(44),i=n(8)("iterator"),o=n(24);t.exports=n(7).getIteratorMethod=function(t){if(null!=t)return t[i]||t["@@iterator"]||o[r(t)]}},function(t,e,n){var i=n(17),o=n(20),s=n(8)("species");t.exports=function(t,e){var n,r=i(t).constructor;return void 0===r||null==(n=i(r)[s])?e:o(n)}},function(t,e,n){function r(){var t=+this;if(b.hasOwnProperty(t)){var e=b[t];delete b[t],e()}}function i(t){r.call(t.data)}var o,s,a,c=n(14),u=n(110),l=n(58),p=n(33),h=n(5),f=h.process,d=h.setImmediate,g=h.clearImmediate,v=h.MessageChannel,y=h.Dispatch,m=0,b={},w="onreadystatechange";d&&g||(d=function(t){for(var e=[],n=1;n<arguments.length;)e.push(arguments[n++]);return b[++m]=function(){u("function"==typeof t?t:Function(t),e)},o(m),m},g=function(t){delete b[t]},"process"==n(23)(f)?o=function(t){f.nextTick(c(r,t,1))}:y&&y.now?o=function(t){y.now(c(r,t,1))}:v?(a=(s=new v).port2,s.port1.onmessage=i,o=c(a.postMessage,a,1)):h.addEventListener&&"function"==typeof postMessage&&!h.importScripts?(o=function(t){h.postMessage(t+"","*")},h.addEventListener("message",i,!1)):o=w in p("script")?function(t){l.appendChild(p("script"))[w]=function(){l.removeChild(this),r.call(t)}}:function(t){setTimeout(c(r,t,1),0)}),t.exports={"set":d,"clear":g}},function(t,e){t.exports=function(t){try{return{"e":!1,"v":t()}}catch(t){return{"e":!0,"v":t}}}},function(t,e,n){var r=n(17),i=n(12),o=n(46);t.exports=function(t,e){if(r(t),i(e)&&e.constructor===t)return e;var n=o.f(t);return(0,n.resolve)(e),n.promise}},function(t,e,n){"use strict";var r=n(5),i=n(7),o=n(11),s=n(15),a=n(8)("species");t.exports=function(t){var e="function"==typeof i[t]?i[t]:r[t];s&&e&&!e[a]&&o.f(e,a,{"configurable":!0,"get":function(){return this}})}},function(t,e,n){var o=n(8)("iterator"),s=!1;try{var r=[7][o]();r["return"]=function(){s=!0},Array.from(r,function(){throw 2})}catch(t){}t.exports=function(t,e){if(!e&&!s)return!1;var n=!1;try{var r=[7],i=r[o]();i.next=function(){return{"done":n=!0}},r[o]=function(){return i},t(r)}catch(t){}return n}},function(t,e){var a=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,c=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];t.exports=function(t){var e=t,n=t.indexOf("["),r=t.indexOf("]");-1!=n&&-1!=r&&(t=t.substring(0,n)+t.substring(n,r).replace(/:/g,";")+t.substring(r,t.length));for(var i=a.exec(t||""),o={},s=14;s--;)o[c[s]]=i[s]||"";return-1!=n&&-1!=r&&(o.source=e,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o}},function(t,e,n){(function(e){t.exports=function(t){return n&&e.Buffer.isBuffer(t)||r&&(t instanceof e.ArrayBuffer||i(t))};var n="function"==typeof e.Buffer&&"function"==typeof e.Buffer.isBuffer,r="function"==typeof e.ArrayBuffer,i=r&&"function"==typeof e.ArrayBuffer.isView?e.ArrayBuffer.isView:function(t){return t.buffer instanceof e.ArrayBuffer}}).call(this,n(6))},function(t,e,n){var c=n(120),o=n(78),r=n(18),i=n(48),u=n(79),s=n(80),l=n(13)("socket.io-client:manager"),a=n(77),p=n(139),h=Object.prototype.hasOwnProperty;function f(t,e){if(!(this instanceof f))return new f(t,e);t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.nsps={},this.subs=[],this.opts=e,this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(e.randomizationFactor||.5),this.backoff=new p({"min":this.reconnectionDelay(),"max":this.reconnectionDelayMax(),"jitter":this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this.readyState="closed",this.uri=t,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[];var n=e.parser||i;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this.autoConnect=!1!==e.autoConnect,this.autoConnect&&this.open()}(t.exports=f).prototype.emitAll=function(){for(var t in this.emit.apply(this,arguments),this.nsps)h.call(this.nsps,t)&&this.nsps[t].emit.apply(this.nsps[t],arguments)},f.prototype.updateSocketIds=function(){for(var t in this.nsps)h.call(this.nsps,t)&&(this.nsps[t].id=this.generateId(t))},f.prototype.generateId=function(t){return("/"===t?"":t+"#")+this.engine.id},r(f.prototype),f.prototype.reconnection=function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection},f.prototype.reconnectionAttempts=function(t){return arguments.length?(this._reconnectionAttempts=t,this):this._reconnectionAttempts},f.prototype.reconnectionDelay=function(t){return arguments.length?(this._reconnectionDelay=t,this.backoff&&this.backoff.setMin(t),this):this._reconnectionDelay},f.prototype.randomizationFactor=function(t){return arguments.length?(this._randomizationFactor=t,this.backoff&&this.backoff.setJitter(t),this):this._randomizationFactor},f.prototype.reconnectionDelayMax=function(t){return arguments.length?(this._reconnectionDelayMax=t,this.backoff&&this.backoff.setMax(t),this):this._reconnectionDelayMax},f.prototype.timeout=function(t){return arguments.length?(this._timeout=t,this):this._timeout},f.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},f.prototype.open=f.prototype.connect=function(n,t){if(l("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;l("opening %s",this.uri),this.engine=c(this.uri,this.opts);var e=this.engine,r=this;this.readyState="opening",this.skipReconnect=!1;var i=u(e,"open",function(){r.onopen(),n&&n()}),o=u(e,"error",function(t){if(l("connect_error"),r.cleanup(),r.readyState="closed",r.emitAll("connect_error",t),n){var e=new Error("Connection error");e.data=t,n(e)}else r.maybeReconnectOnOpen()});if(!1!==this._timeout){var s=this._timeout;l("connect attempt will timeout after %d",s);var a=setTimeout(function(){l("connect attempt timed out after %d",s),i.destroy(),e.close(),e.emit("error","timeout"),r.emitAll("connect_timeout",s)},s);this.subs.push({"destroy":function(){clearTimeout(a)}})}return this.subs.push(i),this.subs.push(o),this},f.prototype.onopen=function(){l("open"),this.cleanup(),this.readyState="open",this.emit("open");var t=this.engine;this.subs.push(u(t,"data",s(this,"ondata"))),this.subs.push(u(t,"ping",s(this,"onping"))),this.subs.push(u(t,"pong",s(this,"onpong"))),this.subs.push(u(t,"error",s(this,"onerror"))),this.subs.push(u(t,"close",s(this,"onclose"))),this.subs.push(u(this.decoder,"decoded",s(this,"ondecoded")))},f.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},f.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},f.prototype.ondata=function(t){this.decoder.add(t)},f.prototype.ondecoded=function(t){this.emit("packet",t)},f.prototype.onerror=function(t){l("error",t),this.emitAll("error",t)},f.prototype.socket=function(t,e){var n=this.nsps[t];if(!n){n=new o(this,t,e),this.nsps[t]=n;var r=this;n.on("connecting",i),n.on("connect",function(){n.id=r.generateId(t)}),this.autoConnect&&i()}function i(){~a(r.connecting,n)||r.connecting.push(n)}return n},f.prototype.destroy=function(t){var e=a(this.connecting,t);~e&&this.connecting.splice(e,1),this.connecting.length||this.close()},f.prototype.packet=function(n){l("writing packet %j",n);var r=this;n.query&&0===n.type&&(n.nsp+="?"+n.query),r.encoding?r.packetBuffer.push(n):(r.encoding=!0,this.encoder.encode(n,function(t){for(var e=0;e<t.length;e++)r.engine.write(t[e],n.options);r.encoding=!1,r.processPacketQueue()}))},f.prototype.processPacketQueue=function(){if(0<this.packetBuffer.length&&!this.encoding){var t=this.packetBuffer.shift();this.packet(t)}},f.prototype.cleanup=function(){l("cleanup");for(var t=this.subs.length,e=0;e<t;e++){this.subs.shift().destroy()}this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},f.prototype.close=f.prototype.disconnect=function(){l("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},f.prototype.onclose=function(t){l("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",t),this._reconnection&&!this.skipReconnect&&this.reconnect()},f.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var e=this;if(this.backoff.attempts>=this._reconnectionAttempts)l("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var t=this.backoff.duration();l("will wait %dms before reconnect attempt",t),this.reconnecting=!0;var n=setTimeout(function(){e.skipReconnect||(l("attempting reconnect"),e.emitAll("reconnect_attempt",e.backoff.attempts),e.emitAll("reconnecting",e.backoff.attempts),e.skipReconnect||e.open(function(t){t?(l("reconnect attempt error"),e.reconnecting=!1,e.reconnect(),e.emitAll("reconnect_error",t.data)):(l("reconnect success"),e.onreconnect())}))},t);this.subs.push({"destroy":function(){clearTimeout(n)}})}},f.prototype.onreconnect=function(){var t=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",t)}},function(t,e,n){(function(s){var a=n(50),c=n(123),u=n(135),t=n(136);e.polling=function(t){var e=!1,n=!1,r=!1!==t.jsonp;if(s.location){var i="https:"===location.protocol,o=location.port;o=o||(i?443:80),e=t.hostname!==location.hostname||o!==t.port,n=t.secure!==i}{if(t.xdomain=e,t.xscheme=n,"open"in new a(t)&&!t.forceJSONP)return new c(t);if(!r)throw new Error("JSONP disabled");return new u(t)}},e.websocket=t}).call(this,n(6))},function(t,e,n){var r=n(51),i=n(30),o=n(19),s=n(31),a=n(76),c=n(13)("engine.io-client:polling");t.exports=l;var u=null!=new(n(50))({"xdomain":!1}).responseType;function l(t){var e=t&&t.forceBase64;u&&!e||(this.supportsBinary=!1),r.call(this,t)}s(l,r),l.prototype.name="polling",l.prototype.doOpen=function(){this.poll()},l.prototype.pause=function(t){var e=this;function n(){c("paused"),e.readyState="paused",t()}if(this.readyState="pausing",this.polling||!this.writable){var r=0;this.polling&&(c("we are currently polling - waiting to pause"),r++,this.once("pollComplete",function(){c("pre-pause polling complete"),--r||n()})),this.writable||(c("we are currently writing - waiting to pause"),r++,this.once("drain",function(){c("pre-pause writing complete"),--r||n()}))}else n()},l.prototype.poll=function(){c("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},l.prototype.onData=function(t){var r=this;c("polling got data %s",t);o.decodePayload(t,this.socket.binaryType,function(t,e,n){if("opening"===r.readyState&&r.onOpen(),"close"===t.type)return r.onClose(),!1;r.onPacket(t)}),"closed"!==this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"===this.readyState?this.poll():c('ignoring poll - transport state "%s"',this.readyState))},l.prototype.doClose=function(){var t=this;function e(){c("writing close packet"),t.write([{"type":"close"}])}"open"===this.readyState?(c("transport open - closing"),e()):(c("transport not open - deferring close"),this.once("open",e))},l.prototype.write=function(t){var e=this;this.writable=!1;function n(){e.writable=!0,e.emit("drain")}o.encodePayload(t,this.supportsBinary,function(t){e.doWrite(t,n)})},l.prototype.uri=function(){var t=this.query||{},e=this.secure?"https":"http",n="";return!1!==this.timestampRequests&&(t[this.timestampParam]=a()),this.supportsBinary||t.sid||(t.b64=1),t=i.encode(t),this.port&&("https"==e&&443!==Number(this.port)||"http"==e&&80!==Number(this.port))&&(n=":"+this.port),t.length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+t}},function(e,t,n){(function(o){var s=n(49),t=Object.prototype.toString,a="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===t.call(Blob),c="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===t.call(File);e.exports=function t(e){if(!e||"object"!=typeof e)return!1;if(s(e)){for(var n=0,r=e.length;n<r;n++)if(t(e[n]))return!0;return!1}if("function"==typeof o&&o.isBuffer&&o.isBuffer(e)||"function"==typeof ArrayBuffer&&e instanceof ArrayBuffer||a&&e instanceof Blob||c&&e instanceof File)return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1===arguments.length)return t(e.toJSON(),!0);for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t(e[i]))return!0;return!1}}).call(this,n(125).Buffer)},function(t,e,n){"use strict";var r,i="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),o=64,s={},a=0,c=0;function u(t){for(var e="";e=i[t%o]+e,0<(t=Math.floor(t/o)););return e}function l(){var t=u(+new Date);return t!==r?(a=0,r=t):t+"."+u(a++)}for(;c<o;c++)s[i[c]]=c;l.encode=u,l.decode=function(t){var e=0;for(c=0;c<t.length;c++)e=e*o+s[t.charAt(c)];return e},t.exports=l},function(t,e){var r=[].indexOf;t.exports=function(t,e){if(r)return t.indexOf(e);for(var n=0;n<t.length;++n)if(t[n]===e)return n;return-1}},function(t,e,n){var i=n(48),r=n(18),o=n(138),s=n(79),a=n(80),c=n(13)("socket.io-client:socket"),u=n(30),l=n(75);t.exports=f;var p={"connect":1,"connect_error":1,"connect_timeout":1,"connecting":1,"disconnect":1,"error":1,"reconnect":1,"reconnect_attempt":1,"reconnect_failed":1,"reconnect_error":1,"reconnecting":1,"ping":1,"pong":1},h=r.prototype.emit;function f(t,e,n){this.io=t,this.nsp=e,(this.json=this).ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,this.flags={},n&&n.query&&(this.query=n.query),this.io.autoConnect&&this.open()}r(f.prototype),f.prototype.subEvents=function(){if(!this.subs){var t=this.io;this.subs=[s(t,"open",a(this,"onopen")),s(t,"packet",a(this,"onpacket")),s(t,"close",a(this,"onclose"))]}},f.prototype.open=f.prototype.connect=function(){return this.connected||(this.subEvents(),this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting")),this},f.prototype.send=function(){var t=o(arguments);return t.unshift("message"),this.emit.apply(this,t),this},f.prototype.emit=function(t){if(p.hasOwnProperty(t))return h.apply(this,arguments),this;var e=o(arguments),n={"type":(void 0!==this.flags.binary?this.flags.binary:l(e))?i.BINARY_EVENT:i.EVENT,"data":e,"options":{}};return n.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof e[e.length-1]&&(c("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),n.id=this.ids++),this.connected?this.packet(n):this.sendBuffer.push(n),this.flags={},this},f.prototype.packet=function(t){t.nsp=this.nsp,this.io.packet(t)},f.prototype.onopen=function(){if(c("transport is open - connecting"),"/"!==this.nsp)if(this.query){var t="object"==typeof this.query?u.encode(this.query):this.query;c("sending connect packet with query %s",t),this.packet({"type":i.CONNECT,"query":t})}else this.packet({"type":i.CONNECT})},f.prototype.onclose=function(t){c("close (%s)",t),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",t)},f.prototype.onpacket=function(t){var e=t.nsp===this.nsp,n=t.type===i.ERROR&&"/"===t.nsp;if(e||n)switch(t.type){case i.CONNECT:this.onconnect();break;case i.EVENT:case i.BINARY_EVENT:this.onevent(t);break;case i.ACK:case i.BINARY_ACK:this.onack(t);break;case i.DISCONNECT:this.ondisconnect();break;case i.ERROR:this.emit("error",t.data)}},f.prototype.onevent=function(t){var e=t.data||[];c("emitting event %j",e),null!=t.id&&(c("attaching ack callback to event"),e.push(this.ack(t.id))),this.connected?h.apply(this,e):this.receiveBuffer.push(e)},f.prototype.ack=function(e){var n=this,r=!1;return function(){if(!r){r=!0;var t=o(arguments);c("sending ack %j",t),n.packet({"type":l(t)?i.BINARY_ACK:i.ACK,"id":e,"data":t})}}},f.prototype.onack=function(t){var e=this.acks[t.id];"function"==typeof e?(c("calling ack %s with %j",t.id,t.data),e.apply(this,t.data),delete this.acks[t.id]):c("bad ack %s",t.id)},f.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},f.prototype.emitBuffered=function(){var t;for(t=0;t<this.receiveBuffer.length;t++)h.apply(this,this.receiveBuffer[t]);for(this.receiveBuffer=[],t=0;t<this.sendBuffer.length;t++)this.packet(this.sendBuffer[t]);this.sendBuffer=[]},f.prototype.ondisconnect=function(){c("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},f.prototype.destroy=function(){if(this.subs){for(var t=0;t<this.subs.length;t++)this.subs[t].destroy();this.subs=null}this.io.destroy(this)},f.prototype.close=f.prototype.disconnect=function(){return this.connected&&(c("performing disconnect (%s)",this.nsp),this.packet({"type":i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},f.prototype.compress=function(t){return this.flags.compress=t,this},f.prototype.binary=function(t){return this.flags.binary=t,this}},function(t,e){t.exports=function(t,e,n){return t.on(e,n),{"destroy":function(){t.removeListener(e,n)}}}},function(t,e){var r=[].slice;t.exports=function(t,e){if("string"==typeof e&&(e=t[e]),"function"!=typeof e)throw new Error("bind() requires a function");var n=r.call(arguments,2);return function(){return e.apply(t,n.concat(r.call(arguments)))}}},function(t,e,n){function r(t){a(t,i,{"value":{"i":"O"+ ++c,"w":{}}})}var i=n(41)("meta"),o=n(12),s=n(22),a=n(11).f,c=0,u=Object.isExtensible||function(){return!0},l=!n(21)(function(){return u(Object.preventExtensions({}))}),p=t.exports={"KEY":i,"NEED":!1,"fastKey":function(t,e){if(!o(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,i)){if(!u(t))return"F";if(!e)return"E";r(t)}return t[i].i},"getWeak":function(t,e){if(!s(t,i)){if(!u(t))return!0;if(!e)return!1;r(t)}return t[i].w},"onFreeze":function(t){return l&&p.NEED&&u(t)&&!s(t,i)&&r(t),t}}},function(t,e,n){var r=n(12);t.exports=function(t,e){if(!r(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},function(t,e,n){"use strict";var r,i="object"==typeof Reflect?Reflect:null,l=i&&"function"==typeof i.apply?i.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};r=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var o=Number.isNaN||function(t){return t!=t};function s(){s.init.call(this)}((t.exports=s).EventEmitter=s).prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var a=10;function c(t){return void 0===t._maxListeners?s.defaultMaxListeners:t._maxListeners}function u(t,e,n,r){var i,o,s;if("function"!=typeof n)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n);if(void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),o=t._events),s=o[e]),void 0===s)s=o[e]=n,++t._eventsCount;else if("function"==typeof s?s=o[e]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),0<(i=c(t))&&s.length>i&&!s.warned){s.warned=!0;var a=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");a.name="MaxListenersExceededWarning",a.emitter=t,a.type=e,a.count=s.length,function(t){console&&console.warn&&console.warn(t)}(a)}return t}function p(t,e,n){var r={"fired":!1,"wrapFn":void 0,"target":t,"type":e,"listener":n},i=function(){for(var t=[],e=0;e<arguments.length;e++)t.push(arguments[e]);this.fired||(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,l(this.listener,this.target,t))}.bind(r);return i.listener=n,r.wrapFn=i}function h(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"==typeof i?n?[i.listener||i]:[i]:n?function(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}(i):d(i,i.length)}function f(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function d(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}Object.defineProperty(s,"defaultMaxListeners",{"enumerable":!0,"get":function(){return a},"set":function(t){if("number"!=typeof t||t<0||o(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");a=t}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(t){if("number"!=typeof t||t<0||o(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var r="error"===t,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var o;if(0<e.length&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var a=i[t];if(void 0===a)return!1;if("function"==typeof a)l(a,this,e);else{var c=a.length,u=d(a,c);for(n=0;n<c;++n)l(u[n],this,e)}return!0},s.prototype.on=s.prototype.addListener=function(t,e){return u(this,t,e,!1)},s.prototype.prependListener=function(t,e){return u(this,t,e,!0)},s.prototype.once=function(t,e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e);return this.on(t,p(this,t,e)),this},s.prototype.prependOnceListener=function(t,e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e);return this.prependListener(t,p(this,t,e)),this},s.prototype.off=s.prototype.removeListener=function(t,e){var n,r,i,o,s;if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e);if(void 0===(r=this._events))return this;if(void 0===(n=r[t]))return this;if(n===e||n.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!=typeof n){for(i=-1,o=n.length-1;0<=o;o--)if(n[o]===e||n[o].listener===e){s=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,s||e)}return this},s.prototype.removeAllListeners=function(t){var e,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)"removeListener"!==(i=o[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=n[t]))this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;0<=r;r--)this.removeListener(t,e[r]);return this},s.prototype.listeners=function(t){return h(this,t,!0)},s.prototype.rawListeners=function(t){return h(this,t,!1)},s.listenerCount=function(t,e){return"function"==typeof t.listenerCount?t.listenerCount(e):f.call(t,e)},s.prototype.listenerCount=f,s.prototype.eventNames=function(){return 0<this._eventsCount?r(this._events):[]}},function(t,e,n){var c=n(115),r=n(48),u=n(72),l=n(13)("socket.io-client");t.exports=e=i;var p=e.managers={};function i(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,r=c(t),i=r.source,o=r.id,s=r.path,a=p[o]&&s in p[o].nsps;return n=e.forceNew||e["force new connection"]||!1===e.multiplex||a?(l("ignoring socket cache for %s",i),u(i,e)):(p[o]||(l("new io instance for %s",i),p[o]=u(i,e)),p[o]),r.query&&!e.query&&(e.query=r.query),n.socket(r.path,e)}e.protocol=r.protocol,e.connect=i,e.Manager=n(72),e.Socket=n(78)},function(t,e,n){t.exports={"default":n(141),"__esModule":!0}},function(t,e,n){t.exports={"default":n(156),"__esModule":!0}},function(t,e,n){var r=n(7),i=r.JSON||(r.JSON={"stringify":JSON.stringify});t.exports=function(t){return i.stringify.apply(i,arguments)}},function(t,e,n){n(89),t.exports=n(7).Object.assign},function(t,e,n){var r=n(10);r(r.S+r.F,"Object",{"assign":n(92)})},function(t,e,n){t.exports=!n(15)&&!n(21)(function(){return 7!=Object.defineProperty(n(33)("div"),"a",{"get":function(){return 7}}).a})},function(t,e,n){var i=n(12);t.exports=function(t,e){if(!i(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!i(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!i(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){"use strict";var h=n(52),f=n(96),d=n(97),g=n(28),v=n(36),i=Object.assign;t.exports=!i||n(21)(function(){var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach(function(t){e[t]=t}),7!=i({},t)[n]||Object.keys(i({},e)).join("")!=r})?function(t,e){for(var n=g(t),r=arguments.length,i=1,o=f.f,s=d.f;i<r;)for(var a,c=v(arguments[i++]),u=o?h(c).concat(o(c)):h(c),l=u.length,p=0;p<l;)s.call(c,a=u[p++])&&(n[a]=c[a]);return n}:i},function(t,e,n){var s=n(22),a=n(35),c=n(94)(!1),u=n(39)("IE_PROTO");t.exports=function(t,e){var n,r=a(t),i=0,o=[];for(n in r)n!=u&&s(r,n)&&o.push(n);for(;e.length>i;)s(r,n=e[i++])&&(~c(o,n)||o.push(n));return o}},function(t,e,n){var c=n(35),u=n(27),l=n(95);t.exports=function(a){return function(t,e,n){var r,i=c(t),o=u(i.length),s=l(n,o);if(a&&e!=e){for(;s<o;)if((r=i[s++])!=r)return!0}else for(;s<o;s++)if((a||s in i)&&i[s]===e)return a||s||0;return!a&&-1}}},function(t,e,n){var r=n(38),i=Math.max,o=Math.min;t.exports=function(t,e){return(t=r(t))<0?i(t+e,0):o(t,e)}},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e){e.f={}.propertyIsEnumerable},function(t,e,n){n(99);var r=n(7).Object;t.exports=function(t,e,n){return r.defineProperty(t,e,n)}},function(t,e,n){var r=n(10);r(r.S+r.F*!n(15),"Object",{"defineProperty":n(11).f})},function(t,e,n){},function(t,e,n){n(56),n(42),n(59),n(109),n(113),n(114),t.exports=n(7).Promise},function(t,e,n){var c=n(38),u=n(37);t.exports=function(a){return function(t,e){var n,r,i=String(u(t)),o=c(e),s=i.length;return o<0||s<=o?a?"":void 0:(n=i.charCodeAt(o))<55296||56319<n||o+1===s||(r=i.charCodeAt(o+1))<56320||57343<r?a?i.charAt(o):n:a?i.slice(o,o+2):r-56320+(n-55296<<10)+65536}}},function(t,e,n){t.exports=n(16)},function(t,e,n){"use strict";var r=n(57),i=n(34),o=n(29),s={};n(16)(s,n(8)("iterator"),function(){return this}),t.exports=function(t,e,n){t.prototype=r(s,{"next":i(1,n)}),o(t,e+" Iterator")}},function(t,e,n){var s=n(11),a=n(17),c=n(52);t.exports=n(15)?Object.defineProperties:function(t,e){a(t);for(var n,r=c(e),i=r.length,o=0;o<i;)s.f(t,n=r[o++],e[n]);return t}},function(t,e,n){var r=n(22),i=n(28),o=n(39)("IE_PROTO"),s=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=i(t),r(t,o)?t[o]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?s:null}},function(t,e,n){"use strict";var r=n(108),i=n(60),o=n(24),s=n(35);t.exports=n(43)(Array,"Array",function(t,e){this._t=s(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,i(1)):i(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(t,e){t.exports=function(){}},function(t,e,n){"use strict";function r(){}function p(t){var e;return!(!v(t)||"function"!=typeof(e=t.then))&&e}function i(l,n){if(!l._n){l._n=!0;var r=l._c;T(function(){for(var c=l._v,u=1==l._s,t=0,e=function(t){var e,n,r,i=u?t.ok:t.fail,o=t.resolve,s=t.reject,a=t.domain;try{i?(u||(2==l._h&&B(l),l._h=1),!0===i?e=c:(a&&a.enter(),e=i(c),a&&(a.exit(),r=!0)),e===t.promise?s(N("Promise-chain cycle")):(n=p(e))?n.call(e,o,s):o(e)):s(c)}catch(t){a&&!r&&a.exit(),s(t)}};r.length>t;)e(r[t++]);l._c=[],l._n=!1,n&&!l._h&&j(l)})}}function o(t){var e=this;e._d||(e._d=!0,(e=e._w||e)._v=t,e._s=2,e._a||(e._a=e._c.slice()),i(e,!0))}var s,a,c,u,l=n(40),h=n(5),f=n(14),d=n(44),g=n(10),v=n(12),y=n(20),m=n(45),b=n(25),w=n(64),x=n(65).set,T=n(111)(),E=n(46),k=n(66),C=n(112),S=n(67),A="Promise",N=h.TypeError,O=h.process,_=O&&O.versions,I=_&&_.v8||"",R=h[A],L="process"==d(O),D=a=E.f,M=!!function(){try{var t=R.resolve(1),e=(t.constructor={})[n(8)("species")]=function(t){t(r,r)};return(L||"function"==typeof PromiseRejectionEvent)&&t.then(r)instanceof e&&0!==I.indexOf("6.6")&&-1===C.indexOf("Chrome/66")}catch(t){}}(),j=function(o){x.call(h,function(){var t,e,n,r=o._v,i=P(o);if(i&&(t=k(function(){L?O.emit("unhandledRejection",r,o):(e=h.onunhandledrejection)?e({"promise":o,"reason":r}):(n=h.console)&&n.error&&n.error("Unhandled promise rejection",r)}),o._h=L||P(o)?2:1),o._a=void 0,i&&t.e)throw t.v})},P=function(t){return 1!==t._h&&0===(t._a||t._c).length},B=function(e){x.call(h,function(){var t;L?O.emit("rejectionHandled",e):(t=h.onrejectionhandled)&&t({"promise":e,"reason":e._v})})},F=function(t){var n,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===t)throw N("Promise can't be resolved itself");(n=p(t))?T(function(){var e={"_w":r,"_d":!1};try{n.call(t,f(F,e,1),f(o,e,1))}catch(t){o.call(e,t)}}):(r._v=t,r._s=1,i(r,!1))}catch(t){o.call({"_w":r,"_d":!1},t)}}};M||(R=function(t){m(this,R,A,"_h"),y(t),s.call(this);try{t(f(F,this,1),f(o,this,1))}catch(t){o.call(this,t)}},(s=function(t){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(47)(R.prototype,{"then":function(t,e){var n=D(w(this,R));return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,n.domain=L?O.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&i(this,!1),n.promise},"catch":function(t){return this.then(void 0,t)}}),c=function(){var t=new s;this.promise=t,this.resolve=f(F,t,1),this.reject=f(o,t,1)},E.f=D=function(t){return t===R||t===u?new c(t):a(t)}),g(g.G+g.W+g.F*!M,{"Promise":R}),n(29)(R,A),n(68)(A),u=n(7)[A],g(g.S+g.F*!M,A,{"reject":function(t){var e=D(this);return(0,e.reject)(t),e.promise}}),g(g.S+g.F*(l||!M),A,{"resolve":function(t){return S(l&&this===u?R:this,t)}}),g(g.S+g.F*!(M&&n(69)(function(t){R.all(t)["catch"](r)})),A,{"all":function(t){var s=this,e=D(s),a=e.resolve,c=e.reject,n=k(function(){var r=[],i=0,o=1;b(t,!1,function(t){var e=i++,n=!1;r.push(void 0),o++,s.resolve(t).then(function(t){n||(n=!0,r[e]=t,--o||a(r))},c)}),--o||a(r)});return n.e&&c(n.v),e.promise},"race":function(t){var e=this,n=D(e),r=n.reject,i=k(function(){b(t,!1,function(t){e.resolve(t).then(n.resolve,r)})});return i.e&&r(i.v),n.promise}})},function(t,e){t.exports=function(t,e,n){var r=void 0===n;switch(e.length){case 0:return r?t():t.call(n);case 1:return r?t(e[0]):t.call(n,e[0]);case 2:return r?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return r?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return r?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},function(t,e,n){var a=n(5),c=n(65).set,u=a.MutationObserver||a.WebKitMutationObserver,l=a.process,p=a.Promise,h="process"==n(23)(l);t.exports=function(){function t(){var t,e;for(h&&(t=l.domain)&&t.exit();n;){e=n.fn,n=n.next;try{e()}catch(t){throw n?i():r=void 0,t}}r=void 0,t&&t.enter()}var n,r,i;if(h)i=function(){l.nextTick(t)};else if(!u||a.navigator&&a.navigator.standalone)if(p&&p.resolve){var e=p.resolve(void 0);i=function(){e.then(t)}}else i=function(){c.call(a,t)};else{var o=!0,s=document.createTextNode("");new u(t).observe(s,{"characterData":!0}),i=function(){s.data=o=!o}}return function(t){var e={"fn":t,"next":void 0};r&&(r.next=e),n||(n=e,i()),r=e}}},function(t,e,n){var r=n(5).navigator;t.exports=r&&r.userAgent||""},function(t,e,n){"use strict";var r=n(10),i=n(7),o=n(5),s=n(64),a=n(67);r(r.P+r.R,"Promise",{"finally":function(e){var n=s(this,i.Promise||o.Promise),t="function"==typeof e;return this.then(t?function(t){return a(n,e()).then(function(){return t})}:e,t?function(t){return a(n,e()).then(function(){throw t})}:e)}})},function(t,e,n){"use strict";var r=n(10),i=n(46),o=n(66);r(r.S,"Promise",{"try":function(t){var e=i.f(this),n=o(t);return(n.e?e.reject:e.resolve)(n.v),e.promise}})},function(t,e,n){(function(i){var o=n(70),s=n(13)("socket.io-client:url");t.exports=function(t,e){var n=t;e=e||i.location,null==t&&(t=e.protocol+"//"+e.host);"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?e.protocol+t:e.host+t),/^(https?|wss?):\/\//.test(t)||(s("protocol-less url %s",t),t=void 0!==e?e.protocol+"//"+t:"https://"+t),s("parse %s",t),n=o(t));n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443"));n.path=n.path||"/";var r=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+r+":"+n.port,n.href=n.protocol+"://"+r+(e&&e.port===n.port?"":":"+n.port),n}}).call(this,n(6))},function(t,e){var n,r,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(t){n=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(t){r=s}}();var c,u=[],l=!1,p=-1;function h(){l&&c&&(l=!1,c.length?u=c.concat(u):p=-1,u.length&&f())}function f(){if(!l){var t=a(h);l=!0;for(var e=u.length;e;){for(c=u,u=[];++p<e;)c&&c[p].run();p=-1,e=u.length}c=null,l=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function g(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];u.push(new d(t,e)),1!==u.length||l||a(f)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(t,c,e){function n(t){var r;function a(){if(a.enabled){var i=a,t=+new Date,e=t-(r||t);i.diff=e,i.prev=r,i.curr=t,r=t;for(var o=new Array(arguments.length),n=0;n<o.length;n++)o[n]=arguments[n];o[0]=c.coerce(o[0]),"string"!=typeof o[0]&&o.unshift("%O");var s=0;o[0]=o[0].replace(/%([a-zA-Z%])/g,function(t,e){if("%%"===t)return t;s++;var n=c.formatters[e];if("function"==typeof n){var r=o[s];t=n.call(i,r),o.splice(s,1),s--}return t}),c.formatArgs.call(i,o),(a.log||c.log||console.log.bind(console)).apply(i,o)}}return a.namespace=t,a.enabled=c.enabled(t),a.useColors=c.useColors(),a.color=function(t){var e,n=0;for(e in t)n=(n<<5)-n+t.charCodeAt(e),n|=0;return c.colors[Math.abs(n)%c.colors.length]}(t),a.destroy=i,"function"==typeof c.init&&c.init(a),c.instances.push(a),a}function i(){var t=c.instances.indexOf(this);return-1!==t&&(c.instances.splice(t,1),!0)}(c=t.exports=n.debug=n["default"]=n).coerce=function(t){return t instanceof Error?t.stack||t.message:t},c.disable=function(){c.enable("")},c.enable=function(t){var e;c.save(t),c.names=[],c.skips=[];var n=("string"==typeof t?t:"").split(/[\s,]+/),r=n.length;for(e=0;e<r;e++)n[e]&&("-"===(t=n[e].replace(/\*/g,".*?"))[0]?c.skips.push(new RegExp("^"+t.substr(1)+"$")):c.names.push(new RegExp("^"+t+"$")));for(e=0;e<c.instances.length;e++){var i=c.instances[e];i.enabled=c.enabled(i.namespace)}},c.enabled=function(t){if("*"===t[t.length-1])return!0;var e,n;for(e=0,n=c.skips.length;e<n;e++)if(c.skips[e].test(t))return!1;for(e=0,n=c.names.length;e<n;e++)if(c.names[e].test(t))return!0;return!1},c.humanize=e(118),c.instances=[],c.names=[],c.skips=[],c.formatters={}},function(t,e){function r(t,e,n){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}t.exports=function(t,e){e=e||{};var n=typeof t;if("string"==n&&0<t.length)return function(t){if(100<(t=String(t)).length)return;var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(!e)return;var n=parseFloat(e[1]);switch((e[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"days":case"day":case"d":return 864e5*n;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*n;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}(t);if("number"==n&&!1===isNaN(t))return e["long"]?function(t){return r(t,864e5,"day")||r(t,36e5,"hour")||r(t,6e4,"minute")||r(t,1e3,"second")||t+" ms"}(t):function(t){if(864e5<=t)return Math.round(t/864e5)+"d";if(36e5<=t)return Math.round(t/36e5)+"h";if(6e4<=t)return Math.round(t/6e4)+"m";if(1e3<=t)return Math.round(t/1e3)+"s";return t+"ms"}(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},function(t,n,r){(function(t){var l=r(49),p=r(71),e=Object.prototype.toString,h="function"==typeof t.Blob||"[object BlobConstructor]"===e.call(t.Blob),f="function"==typeof t.File||"[object FileConstructor]"===e.call(t.File);n.deconstructPacket=function(t){var e=[],n=t.data,r=t;return r.data=function t(e,n){if(!e)return e;{if(p(e)){var r={"_placeholder":!0,"num":n.length};return n.push(e),r}if(l(e)){for(var i=new Array(e.length),o=0;o<e.length;o++)i[o]=t(e[o],n);return i}if("object"==typeof e&&!(e instanceof Date)){var i={};for(var s in e)i[s]=t(e[s],n);return i}}return e}(n,e),r.attachments=e.length,{"packet":r,"buffers":e}},n.reconstructPacket=function(t,e){return t.data=function t(e,n){if(!e)return e;{if(e&&e._placeholder)return n[e.num];if(l(e))for(var r=0;r<e.length;r++)e[r]=t(e[r],n);else if("object"==typeof e)for(var i in e)e[i]=t(e[i],n)}return e}(t.data,e),t.attachments=void 0,t},n.removeBlobs=function(t,a){var c=0,u=t;!function t(e,n,r){if(!e)return e;if(h&&e instanceof Blob||f&&e instanceof File){c++;var i=new FileReader;i.onload=function(){r?r[n]=this.result:u=this.result,--c||a(u)},i.readAsArrayBuffer(e)}else if(l(e))for(var o=0;o<e.length;o++)t(e[o],o,e);else if("object"==typeof e&&!p(e))for(var s in e)t(e[s],s,e)}(u),c||a(u)}}).call(this,r(6))},function(t,e,n){t.exports=n(121),t.exports.parser=n(19)},function(e,t,n){(function(r){var i=n(73),t=n(18),p=n(13)("engine.io-client:socket"),o=n(77),s=n(19),a=n(70),c=n(30);function h(t,e){if(!(this instanceof h))return new h(t,e);e=e||{},t&&"object"==typeof t&&(e=t,t=null),t?(t=a(t),e.hostname=t.host,e.secure="https"===t.protocol||"wss"===t.protocol,e.port=t.port,t.query&&(e.query=t.query)):e.host&&(e.hostname=a(e.host).host),this.secure=null!=e.secure?e.secure:r.location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.agent=e.agent||!1,this.hostname=e.hostname||(r.location?location.hostname:"localhost"),this.port=e.port||(r.location&&location.port?location.port:this.secure?443:80),this.query=e.query||{},"string"==typeof this.query&&(this.query=c.decode(this.query)),this.upgrade=!1!==e.upgrade,this.path=(e.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!e.forceJSONP,this.jsonp=!1!==e.jsonp,this.forceBase64=!!e.forceBase64,this.enablesXDR=!!e.enablesXDR,this.timestampParam=e.timestampParam||"t",this.timestampRequests=e.timestampRequests,this.transports=e.transports||["polling","websocket"],this.transportOptions=e.transportOptions||{},this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=e.policyPort||843,this.rememberUpgrade=e.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=e.onlyBinaryUpgrades,this.perMessageDeflate=!1!==e.perMessageDeflate&&(e.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=e.pfx||null,this.key=e.key||null,this.passphrase=e.passphrase||null,this.cert=e.cert||null,this.ca=e.ca||null,this.ciphers=e.ciphers||null,this.rejectUnauthorized=void 0===e.rejectUnauthorized||e.rejectUnauthorized,this.forceNode=!!e.forceNode;var n="object"==typeof r&&r;n.global===n&&(e.extraHeaders&&0<Object.keys(e.extraHeaders).length&&(this.extraHeaders=e.extraHeaders),e.localAddress&&(this.localAddress=e.localAddress)),this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,this.open()}(e.exports=h).priorWebsocketSuccess=!1,t(h.prototype),h.protocol=s.protocol,(h.Socket=h).Transport=n(51),h.transports=n(73),h.parser=n(19),h.prototype.createTransport=function(t){p('creating transport "%s"',t);var e=function(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}(this.query);e.EIO=s.protocol,e.transport=t;var n=this.transportOptions[t]||{};return this.id&&(e.sid=this.id),new i[t]({"query":e,"socket":this,"agent":n.agent||this.agent,"hostname":n.hostname||this.hostname,"port":n.port||this.port,"secure":n.secure||this.secure,"path":n.path||this.path,"forceJSONP":n.forceJSONP||this.forceJSONP,"jsonp":n.jsonp||this.jsonp,"forceBase64":n.forceBase64||this.forceBase64,"enablesXDR":n.enablesXDR||this.enablesXDR,"timestampRequests":n.timestampRequests||this.timestampRequests,"timestampParam":n.timestampParam||this.timestampParam,"policyPort":n.policyPort||this.policyPort,"pfx":n.pfx||this.pfx,"key":n.key||this.key,"passphrase":n.passphrase||this.passphrase,"cert":n.cert||this.cert,"ca":n.ca||this.ca,"ciphers":n.ciphers||this.ciphers,"rejectUnauthorized":n.rejectUnauthorized||this.rejectUnauthorized,"perMessageDeflate":n.perMessageDeflate||this.perMessageDeflate,"extraHeaders":n.extraHeaders||this.extraHeaders,"forceNode":n.forceNode||this.forceNode,"localAddress":n.localAddress||this.localAddress,"requestTimeout":n.requestTimeout||this.requestTimeout,"protocols":n.protocols||void 0})},h.prototype.open=function(){var t;if(this.rememberUpgrade&&h.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket"))t="websocket";else{if(0===this.transports.length){var e=this;return void setTimeout(function(){e.emit("error","No transports available")},0)}t=this.transports[0]}this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},h.prototype.setTransport=function(t){p("setting transport %s",t.name);var e=this;this.transport&&(p("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),(this.transport=t).on("drain",function(){e.onDrain()}).on("packet",function(t){e.onPacket(t)}).on("error",function(t){e.onError(t)}).on("close",function(){e.onClose("transport close")})},h.prototype.probe=function(n){p('probing transport "%s"',n);var r=this.createTransport(n,{"probe":1}),i=!1,o=this;function t(){if(o.onlyBinaryUpgrades){var t=!this.supportsBinary&&o.transport.supportsBinary;i=i||t}i||(p('probe transport "%s" opened',n),r.send([{"type":"ping","data":"probe"}]),r.once("packet",function(t){if(!i)if("pong"===t.type&&"probe"===t.data){if(p('probe transport "%s" pong',n),o.upgrading=!0,o.emit("upgrading",r),!r)return;h.priorWebsocketSuccess="websocket"===r.name,p('pausing current transport "%s"',o.transport.name),o.transport.pause(function(){i||"closed"!==o.readyState&&(p("changing transport and sending upgrade packet"),l(),o.setTransport(r),r.send([{"type":"upgrade"}]),o.emit("upgrade",r),r=null,o.upgrading=!1,o.flush())})}else{p('probe transport "%s" failed',n);var e=new Error("probe error");e.transport=r.name,o.emit("upgradeError",e)}}))}function s(){i||(i=!0,l(),r.close(),r=null)}function e(t){var e=new Error("probe error: "+t);e.transport=r.name,s(),p('probe transport "%s" failed because of error: %s',n,t),o.emit("upgradeError",e)}function a(){e("transport closed")}function c(){e("socket closed")}function u(t){r&&t.name!==r.name&&(p('"%s" works - aborting "%s"',t.name,r.name),s())}function l(){r.removeListener("open",t),r.removeListener("error",e),r.removeListener("close",a),o.removeListener("close",c),o.removeListener("upgrading",u)}h.priorWebsocketSuccess=!1,r.once("open",t),r.once("error",e),r.once("close",a),this.once("close",c),this.once("upgrading",u),r.open()},h.prototype.onOpen=function(){if(p("socket open"),this.readyState="open",h.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause){p("starting upgrade probes");for(var t=0,e=this.upgrades.length;t<e;t++)this.probe(this.upgrades[t])}},h.prototype.onPacket=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(p('socket receive: type "%s", data "%s"',t.type,t.data),this.emit("packet",t),this.emit("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var e=new Error("server error");e.code=t.data,this.onError(e);break;case"message":this.emit("data",t.data),this.emit("message",t.data)}else p('packet received with socket readyState "%s"',this.readyState)},h.prototype.onHandshake=function(t){this.emit("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},h.prototype.onHeartbeat=function(t){clearTimeout(this.pingTimeoutTimer);var e=this;e.pingTimeoutTimer=setTimeout(function(){"closed"!==e.readyState&&e.onClose("ping timeout")},t||e.pingInterval+e.pingTimeout)},h.prototype.setPing=function(){var t=this;clearTimeout(t.pingIntervalTimer),t.pingIntervalTimer=setTimeout(function(){p("writing ping packet - expecting pong within %sms",t.pingTimeout),t.ping(),t.onHeartbeat(t.pingTimeout)},t.pingInterval)},h.prototype.ping=function(){var t=this;this.sendPacket("ping",function(){t.emit("ping")})},h.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),(this.prevBufferLen=0)===this.writeBuffer.length?this.emit("drain"):this.flush()},h.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(p("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},h.prototype.write=h.prototype.send=function(t,e,n){return this.sendPacket("message",t,e,n),this},h.prototype.sendPacket=function(t,e,n,r){if("function"==typeof e&&(r=e,e=void 0),"function"==typeof n&&(r=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState){(n=n||{}).compress=!1!==n.compress;var i={"type":t,"data":e,"options":n};this.emit("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}},h.prototype.close=function(){if("opening"===this.readyState||"open"===this.readyState){this.readyState="closing";var t=this;this.writeBuffer.length?this.once("drain",function(){this.upgrading?r():e()}):this.upgrading?r():e()}function e(){t.onClose("forced close"),p("socket closing - telling transport to close"),t.transport.close()}function n(){t.removeListener("upgrade",n),t.removeListener("upgradeError",n),e()}function r(){t.once("upgrade",n),t.once("upgradeError",n)}return this},h.prototype.onError=function(t){p("socket error %j",t),h.priorWebsocketSuccess=!1,this.emit("error",t),this.onClose("transport error",t)},h.prototype.onClose=function(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){p('socket close with reason: "%s"',t);clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",t,e),this.writeBuffer=[],this.prevBufferLen=0}},h.prototype.filterUpgrades=function(t){for(var e=[],n=0,r=t.length;n<r;n++)~o(this.transports,t[n])&&e.push(t[n]);return e}}).call(this,n(6))},function(e,t){try{e.exports="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){e.exports=!1}},function(l,t,p){(function(i){var o=p(50),r=p(74),t=p(18),e=p(31),s=p(13)("engine.io-client:polling-xhr");function n(){}function a(t){if(r.call(this,t),this.requestTimeout=t.requestTimeout,this.extraHeaders=t.extraHeaders,i.location){var e="https:"===location.protocol,n=location.port;n=n||(e?443:80),this.xd=t.hostname!==i.location.hostname||n!==t.port,this.xs=t.secure!==e}}function c(t){this.method=t.method||"GET",this.uri=t.uri,this.xd=!!t.xd,this.xs=!!t.xs,this.async=!1!==t.async,this.data=void 0!==t.data?t.data:null,this.agent=t.agent,this.isBinary=t.isBinary,this.supportsBinary=t.supportsBinary,this.enablesXDR=t.enablesXDR,this.requestTimeout=t.requestTimeout,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.extraHeaders=t.extraHeaders,this.create()}function u(){for(var t in c.requests)c.requests.hasOwnProperty(t)&&c.requests[t].abort()}l.exports=a,l.exports.Request=c,e(a,r),a.prototype.supportsBinary=!0,a.prototype.request=function(t){return(t=t||{}).uri=this.uri(),t.xd=this.xd,t.xs=this.xs,t.agent=this.agent||!1,t.supportsBinary=this.supportsBinary,t.enablesXDR=this.enablesXDR,t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized,t.requestTimeout=this.requestTimeout,t.extraHeaders=this.extraHeaders,new c(t)},a.prototype.doWrite=function(t,e){var n="string"!=typeof t&&void 0!==t,r=this.request({"method":"POST","data":t,"isBinary":n}),i=this;r.on("success",e),r.on("error",function(t){i.onError("xhr post error",t)}),this.sendXhr=r},a.prototype.doPoll=function(){s("xhr poll");var t=this.request(),e=this;t.on("data",function(t){e.onData(t)}),t.on("error",function(t){e.onError("xhr poll error",t)}),this.pollXhr=t},t(c.prototype),c.prototype.create=function(){var t={"agent":this.agent,"xdomain":this.xd,"xscheme":this.xs,"enablesXDR":this.enablesXDR};t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized;var e=this.xhr=new o(t),n=this;try{s("xhr open %s: %s",this.method,this.uri),e.open(this.method,this.uri,this.async);try{if(this.extraHeaders)for(var r in e.setDisableHeaderCheck&&e.setDisableHeaderCheck(!0),this.extraHeaders)this.extraHeaders.hasOwnProperty(r)&&e.setRequestHeader(r,this.extraHeaders[r])}catch(t){}if("POST"===this.method)try{this.isBinary?e.setRequestHeader("Content-type","application/octet-stream"):e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{e.setRequestHeader("Accept","*/*")}catch(t){}"withCredentials"in e&&(e.withCredentials=!0),this.requestTimeout&&(e.timeout=this.requestTimeout),this.hasXDR()?(e.onload=function(){n.onLoad()},e.onerror=function(){n.onError(e.responseText)}):e.onreadystatechange=function(){if(2===e.readyState)try{var t=e.getResponseHeader("Content-Type");n.supportsBinary&&"application/octet-stream"===t&&(e.responseType="arraybuffer")}catch(t){}4===e.readyState&&(200===e.status||1223===e.status?n.onLoad():setTimeout(function(){n.onError(e.status)},0))},s("xhr data %s",this.data),e.send(this.data)}catch(t){return void setTimeout(function(){n.onError(t)},0)}i.document&&(this.index=c.requestsCount++,c.requests[this.index]=this)},c.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},c.prototype.onData=function(t){this.emit("data",t),this.onSuccess()},c.prototype.onError=function(t){this.emit("error",t),this.cleanup(!0)},c.prototype.cleanup=function(t){if(void 0!==this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=n:this.xhr.onreadystatechange=n,t)try{this.xhr.abort()}catch(t){}i.document&&delete c.requests[this.index],this.xhr=null}},c.prototype.onLoad=function(){var t;try{var e;try{e=this.xhr.getResponseHeader("Content-Type")}catch(t){}t="application/octet-stream"===e&&this.xhr.response||this.xhr.responseText}catch(t){this.onError(t)}null!=t&&this.onData(t)},c.prototype.hasXDR=function(){return void 0!==i.XDomainRequest&&!this.xs&&this.enablesXDR},c.prototype.abort=function(){this.cleanup()},c.requestsCount=0,c.requests={},i.document&&(i.attachEvent?i.attachEvent("onunload",u):i.addEventListener&&i.addEventListener("beforeunload",u,!1))}).call(this,p(6))},function(t,e){t.exports=Object.keys||function(t){var e=[],n=Object.prototype.hasOwnProperty;for(var r in t)n.call(t,r)&&e.push(r);return e}},function(t,P,B){"use strict";(function(t){var r=B(126),o=B(127),s=B(128);function n(){return p.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function a(t,e){if(n()<e)throw new RangeError("Invalid typed array length");return p.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=p.prototype:(null===t&&(t=new p(e)),t.length=e),t}function p(t,e,n){if(!(p.TYPED_ARRAY_SUPPORT||this instanceof p))return new p(t,e,n);if("number"!=typeof t)return i(this,t,e,n);if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return u(this,t)}function i(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r);p.TYPED_ARRAY_SUPPORT?(t=e).__proto__=p.prototype:t=l(t,e);return t}(t,e,n,r):"string"==typeof e?function(t,e,n){"string"==typeof n&&""!==n||(n="utf8");if(!p.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|f(e,n),i=(t=a(t,r)).write(e,n);i!==r&&(t=t.slice(0,i));return t}(t,e,n):function(t,e){if(p.isBuffer(e)){var n=0|h(e.length);return 0===(t=a(t,n)).length||e.copy(t,0,0,n),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||function(t){return t!=t}(e.length)?a(t,0):l(t,e);if("Buffer"===e.type&&s(e.data))return l(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function u(t,e){if(c(e),t=a(t,e<0?0:0|h(e)),!p.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function l(t,e){var n=e.length<0?0:0|h(e.length);t=a(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function h(t){if(t>=n())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n().toString(16)+" bytes");return 0|t}function f(t,e){if(p.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return D(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return M(t).length;default:if(r)return D(t).length;e=(""+e).toLowerCase(),r=!0}}function d(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function g(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):2147483647<n?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=p.from(e,r)),p.isBuffer(e))return 0===e.length?-1:v(t,e,n,r,i);if("number"==typeof e)return e&=255,p.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):v(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function v(t,e,n,r,i){var o,s=1,a=t.length,c=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;a/=s=2,c/=2,n/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var l=-1;for(o=n;o<a;o++)if(u(t,o)===u(e,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===c)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(a<n+c&&(n=a-c),o=n;0<=o;o--){for(var p=!0,h=0;h<c;h++)if(u(t,o+h)!==u(e,h)){p=!1;break}if(p)return o}return-1}function y(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?i<(r=Number(r))&&(r=i):r=i;var o=e.length;if(o%2!=0)throw new TypeError("Invalid hex string");o/2<r&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(e.substr(2*s,2),16);if(isNaN(a))return s;t[n+s]=a}return s}function m(t,e,n,r){return j(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function b(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function w(t,e,n){n=Math.min(t.length,n);for(var r=[],i=e;i<n;){var o,s,a,c,u=t[i],l=null,p=239<u?4:223<u?3:191<u?2:1;if(i+p<=n)switch(p){case 1:u<128&&(l=u);break;case 2:128==(192&(o=t[i+1]))&&127<(c=(31&u)<<6|63&o)&&(l=c);break;case 3:o=t[i+1],s=t[i+2],128==(192&o)&&128==(192&s)&&2047<(c=(15&u)<<12|(63&o)<<6|63&s)&&(c<55296||57343<c)&&(l=c);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&65535<(c=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)&&c<1114112&&(l=c)}null===l?(l=65533,p=1):65535<l&&(l-=65536,r.push(l>>>10&1023|55296),l=56320|1023&l),r.push(l),i+=p}return function(t){var e=t.length;if(e<=x)return String.fromCharCode.apply(String,t);var n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=x));return n}(r)}P.Buffer=p,P.SlowBuffer=function(t){+t!=t&&(t=0);return p.alloc(+t)},P.INSPECT_MAX_BYTES=50,p.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={"__proto__":Uint8Array.prototype,"foo":function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),P.kMaxLength=n(),p.poolSize=8192,p._augment=function(t){return t.__proto__=p.prototype,t},p.from=function(t,e,n){return i(null,t,e,n)},p.TYPED_ARRAY_SUPPORT&&(p.prototype.__proto__=Uint8Array.prototype,p.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&p[Symbol.species]===p&&Object.defineProperty(p,Symbol.species,{"value":null,"configurable":!0})),p.alloc=function(t,e,n){return function(t,e,n,r){return c(e),e<=0?a(t,e):void 0!==n?"string"==typeof r?a(t,e).fill(n,r):a(t,e).fill(n):a(t,e)}(null,t,e,n)},p.allocUnsafe=function(t){return u(null,t)},p.allocUnsafeSlow=function(t){return u(null,t)},p.isBuffer=function(t){return!(null==t||!t._isBuffer)},p.compare=function(t,e){if(!p.isBuffer(t)||!p.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},p.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},p.concat=function(t,e){if(!s(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return p.alloc(0);var n;if(void 0===e)for(n=e=0;n<t.length;++n)e+=t[n].length;var r=p.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(!p.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},p.byteLength=f,p.prototype._isBuffer=!0,p.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)d(this,e,e+1);return this},p.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)d(this,e,e+3),d(this,e+1,e+2);return this},p.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)d(this,e,e+7),d(this,e+1,e+6),d(this,e+2,e+5),d(this,e+3,e+4);return this},p.prototype.toString=function(){var t=0|this.length;return 0==t?"":0===arguments.length?w(this,0,t):function(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t=t||"utf8";;)switch(t){case"hex":return k(this,e,n);case"utf8":case"utf-8":return w(this,e,n);case"ascii":return T(this,e,n);case"latin1":case"binary":return E(this,e,n);case"base64":return b(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}.apply(this,arguments)},p.prototype.equals=function(t){if(!p.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===p.compare(this,t)},p.prototype.inspect=function(){var t="",e=P.INSPECT_MAX_BYTES;return 0<this.length&&(t=this.toString("hex",0,e).match(/.{2}/g).join(" "),this.length>e&&(t+=" ... ")),"<Buffer "+t+">"},p.prototype.compare=function(t,e,n,r,i){if(!p.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(i<=r&&n<=e)return 0;if(i<=r)return-1;if(n<=e)return 1;if(this===t)return 0;for(var o=(i>>>=0)-(r>>>=0),s=(n>>>=0)-(e>>>=0),a=Math.min(o,s),c=this.slice(r,i),u=t.slice(e,n),l=0;l<a;++l)if(c[l]!==u[l]){o=c[l],s=u[l];break}return o<s?-1:s<o?1:0},p.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},p.prototype.indexOf=function(t,e,n){return g(this,t,e,n,!0)},p.prototype.lastIndexOf=function(t,e,n){return g(this,t,e,n,!1)},p.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||i<n)&&(n=i),0<t.length&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r=r||"utf8";for(var o,s,a,c,u,l,p,h,f,d=!1;;)switch(r){case"hex":return y(this,t,e,n);case"utf8":case"utf-8":return h=e,f=n,j(D(t,(p=this).length-h),p,h,f);case"ascii":return m(this,t,e,n);case"latin1":case"binary":return m(this,t,e,n);case"base64":return c=this,u=e,l=n,j(M(t),c,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=e,a=n,j(function(t,e){for(var n,r,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)n=t.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}(t,(o=this).length-s),o,s,a);default:if(d)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),d=!0}},p.prototype.toJSON=function(){return{"type":"Buffer","data":Array.prototype.slice.call(this._arr||this,0)}};var x=4096;function T(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function E(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function k(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||r<n)&&(n=r);for(var i="",o=e;o<n;++o)i+=L(t[o]);return i}function C(t,e,n){for(var r=t.slice(e,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function S(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(n<t+e)throw new RangeError("Trying to access beyond buffer length")}function A(t,e,n,r,i,o){if(!p.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(i<e||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function N(t,e,n,r){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-n,2);i<o;++i)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function O(t,e,n,r){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-n,4);i<o;++i)t[n+i]=e>>>8*(r?i:3-i)&255}function _(t,e,n,r){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function I(t,e,n,r,i){return i||_(t,0,n,4),o.write(t,e,n,r,23,4),n+4}function R(t,e,n,r,i){return i||_(t,0,n,8),o.write(t,e,n,r,52,8),n+8}p.prototype.slice=function(t,e){var n,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):r<t&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):r<e&&(e=r),e<t&&(e=t),p.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,e)).__proto__=p.prototype;else{var i=e-t;n=new p(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+t]}return n},p.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||S(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return r},p.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||S(t,e,this.length);for(var r=this[t+--e],i=1;0<e&&(i*=256);)r+=this[t+--e]*i;return r},p.prototype.readUInt8=function(t,e){return e||S(t,1,this.length),this[t]},p.prototype.readUInt16LE=function(t,e){return e||S(t,2,this.length),this[t]|this[t+1]<<8},p.prototype.readUInt16BE=function(t,e){return e||S(t,2,this.length),this[t]<<8|this[t+1]},p.prototype.readUInt32LE=function(t,e){return e||S(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},p.prototype.readUInt32BE=function(t,e){return e||S(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},p.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||S(t,e,this.length);for(var r=this[t],i=1,o=0;++o<e&&(i*=256);)r+=this[t+o]*i;return(i*=128)<=r&&(r-=Math.pow(2,8*e)),r},p.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||S(t,e,this.length);for(var r=e,i=1,o=this[t+--r];0<r&&(i*=256);)o+=this[t+--r]*i;return(i*=128)<=o&&(o-=Math.pow(2,8*e)),o},p.prototype.readInt8=function(t,e){return e||S(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},p.prototype.readInt16LE=function(t,e){e||S(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},p.prototype.readInt16BE=function(t,e){e||S(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},p.prototype.readInt32LE=function(t,e){return e||S(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},p.prototype.readInt32BE=function(t,e){return e||S(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},p.prototype.readFloatLE=function(t,e){return e||S(t,4,this.length),o.read(this,t,!0,23,4)},p.prototype.readFloatBE=function(t,e){return e||S(t,4,this.length),o.read(this,t,!1,23,4)},p.prototype.readDoubleLE=function(t,e){return e||S(t,8,this.length),o.read(this,t,!0,52,8)},p.prototype.readDoubleBE=function(t,e){return e||S(t,8,this.length),o.read(this,t,!1,52,8)},p.prototype.writeUIntLE=function(t,e,n,r){t=+t,e|=0,n|=0,r||A(this,t,e,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[e]=255&t;++o<n&&(i*=256);)this[e+o]=t/i&255;return e+n},p.prototype.writeUIntBE=function(t,e,n,r){t=+t,e|=0,n|=0,r||A(this,t,e,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[e+i]=255&t;0<=--i&&(o*=256);)this[e+i]=t/o&255;return e+n},p.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,1,255,0),p.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},p.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):N(this,t,e,!0),e+2},p.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):N(this,t,e,!1),e+2},p.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):O(this,t,e,!0),e+4},p.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):O(this,t,e,!1),e+4},p.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);A(this,t,e,n,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<n&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+n},p.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);A(this,t,e,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[e+o]=255&t;0<=--o&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+n},p.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,1,127,-128),p.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},p.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):N(this,t,e,!0),e+2},p.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):N(this,t,e,!1),e+2},p.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,4,2147483647,-2147483648),p.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):O(this,t,e,!0),e+4},p.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||A(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),p.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):O(this,t,e,!1),e+4},p.prototype.writeFloatLE=function(t,e,n){return I(this,t,e,!0,n)},p.prototype.writeFloatBE=function(t,e,n){return I(this,t,e,!1,n)},p.prototype.writeDoubleLE=function(t,e,n){return R(this,t,e,!0,n)},p.prototype.writeDoubleBE=function(t,e,n){return R(this,t,e,!1,n)},p.prototype.copy=function(t,e,n,r){if(n=n||0,r||0===r||(r=this.length),e>=t.length&&(e=t.length),e=e||0,0<r&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i,o=r-n;if(this===t&&n<e&&e<r)for(i=o-1;0<=i;--i)t[i+e]=this[i+n];else if(o<1e3||!p.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+o),e);return o},p.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!p.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var o;if(e>>>=0,n=void 0===n?this.length:n>>>0,"number"==typeof(t=t||0))for(o=e;o<n;++o)this[o]=t;else{var s=p.isBuffer(t)?t:D(new p(t,r).toString()),a=s.length;for(o=0;o<n-e;++o)this[o+e]=s[o%a]}return this};var e=/[^+\/0-9A-Za-z-_]/g;function L(t){return t<16?"0"+t.toString(16):t.toString(16)}function D(t,e){var n;e=e||1/0;for(var r=t.length,i=null,o=[],s=0;s<r;++s){if(55295<(n=t.charCodeAt(s))&&n<57344){if(!i){if(56319<n){-1<(e-=3)&&o.push(239,191,189);continue}if(s+1===r){-1<(e-=3)&&o.push(239,191,189);continue}i=n;continue}if(n<56320){-1<(e-=3)&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&-1<(e-=3)&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function M(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(e,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function j(t,e,n,r){for(var i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}}).call(this,B(6))},function(t,e,n){"use strict";e.byteLength=function(t){var e=p(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){for(var e,n=p(t),r=n[0],i=n[1],o=new l(function(t,e,n){return 3*(e+n)/4-n}(0,r,i)),s=0,a=0<i?r-4:r,c=0;c<a;c+=4)e=u[t.charCodeAt(c)]<<18|u[t.charCodeAt(c+1)]<<12|u[t.charCodeAt(c+2)]<<6|u[t.charCodeAt(c+3)],o[s++]=e>>16&255,o[s++]=e>>8&255,o[s++]=255&e;2===i&&(e=u[t.charCodeAt(c)]<<2|u[t.charCodeAt(c+1)]>>4,o[s++]=255&e);1===i&&(e=u[t.charCodeAt(c)]<<10|u[t.charCodeAt(c+1)]<<4|u[t.charCodeAt(c+2)]>>2,o[s++]=e>>8&255,o[s++]=255&e);return o},e.fromByteArray=function(t){for(var e,n=t.length,r=n%3,i=[],o=0,s=n-r;o<s;o+=16383)i.push(c(t,o,s<o+16383?s:o+16383));1==r?(e=t[n-1],i.push(a[e>>2]+a[e<<4&63]+"==")):2==r&&(e=(t[n-2]<<8)+t[n-1],i.push(a[e>>10]+a[e>>4&63]+a[e<<2&63]+"="));return i.join("")};for(var a=[],u=[],l="undefined"!=typeof Uint8Array?Uint8Array:Array,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",i=0,o=r.length;i<o;++i)a[i]=r[i],u[r.charCodeAt(i)]=i;function p(t){var e=t.length;if(0<e%4)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function c(t,e,n){for(var r,i,o=[],s=e;s<n;s+=3)r=(t[s]<<16&16711680)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(a[(i=r)>>18&63]+a[i>>12&63]+a[i>>6&63]+a[63&i]);return o.join("")}u["-".charCodeAt(0)]=62,u["_".charCodeAt(0)]=63},function(t,e){e.read=function(t,e,n,r,i){var o,s,a=8*i-r-1,c=(1<<a)-1,u=c>>1,l=-7,p=n?i-1:0,h=n?-1:1,f=t[e+p];for(p+=h,o=f&(1<<-l)-1,f>>=-l,l+=a;0<l;o=256*o+t[e+p],p+=h,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=r;0<l;s=256*s+t[e+p],p+=h,l-=8);if(0===o)o=1-u;else{if(o===c)return s?NaN:1/0*(f?-1:1);s+=Math.pow(2,r),o-=u}return(f?-1:1)*s*Math.pow(2,o-r)},e.write=function(t,e,n,r,i,o){var s,a,c,u=8*o-i-1,l=(1<<u)-1,p=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:o-1,d=r?1:-1,g=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,s=l):(s=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-s))<1&&(s--,c*=2),2<=(e+=1<=s+p?h/c:h*Math.pow(2,1-p))*c&&(s++,c/=2),l<=s+p?(a=0,s=l):1<=s+p?(a=(e*c-1)*Math.pow(2,i),s+=p):(a=e*Math.pow(2,p-1)*Math.pow(2,i),s=0));8<=i;t[n+f]=255&a,f+=d,a/=256,i-=8);for(s=s<<i|a,u+=i;0<u;t[n+f]=255&s,f+=d,s/=256,u-=8);t[n+f-d]|=128*g}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e){t.exports=function(t,e,n){var r=t.byteLength;if(e=e||0,n=n||r,t.slice)return t.slice(e,n);if(e<0&&(e+=r),n<0&&(n+=r),r<n&&(n=r),r<=e||n<=e||0===r)return new ArrayBuffer(0);for(var i=new Uint8Array(t),o=new Uint8Array(n-e),s=e,a=0;s<n;s++,a++)o[a]=i[s];return o.buffer}},function(t,e){function s(){}t.exports=function(t,n,r){var i=!1;return r=r||s,0===(o.count=t)?n():o;function o(t,e){if(o.count<=0)throw new Error("after called too many times");--o.count,t?(i=!0,n(t),n=r):0!==o.count||i||n(null,e)}}},function(t,v,y){(function(f,d){var g;!function(){var t=v,e=(f&&f.exports,"object"==typeof d&&d);e.global!==e&&e.window;var o,s,a,c=String.fromCharCode;function u(t){for(var e,n,r=[],i=0,o=t.length;i<o;)55296<=(e=t.charCodeAt(i++))&&e<=56319&&i<o?56320==(64512&(n=t.charCodeAt(i++)))?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),i--):r.push(e);return r}function r(t,e){if(55296<=t&&t<=57343){if(e)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value");return!1}return!0}function i(t,e){return c(t>>e&63|128)}function l(t,e){if(0==(4294967168&t))return c(t);var n="";return 0==(4294965248&t)?n=c(t>>6&31|192):0==(4294901760&t)?(r(t,e)||(t=65533),n=c(t>>12&15|224),n+=i(t,6)):0==(4292870144&t)&&(n=c(t>>18&7|240),n+=i(t,12),n+=i(t,6)),n+=c(63&t|128)}function p(){if(s<=a)throw Error("Invalid byte index");var t=255&o[a];if(a++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function h(t){var e,n;if(s<a)throw Error("Invalid byte index");if(a==s)return!1;if(e=255&o[a],a++,0==(128&e))return e;if(192==(224&e)){if(128<=(n=(31&e)<<6|p()))return n;throw Error("Invalid continuation byte")}if(224==(240&e)){if(2048<=(n=(15&e)<<12|p()<<6|p()))return r(n,t)?n:65533;throw Error("Invalid continuation byte")}if(240==(248&e)&&65536<=(n=(7&e)<<18|p()<<12|p()<<6|p())&&n<=1114111)return n;throw Error("Invalid UTF-8 detected")}var n={"version":"2.1.2","encode":function(t,e){for(var n=!1!==(e=e||{}).strict,r=u(t),i=r.length,o=-1,s="";++o<i;)s+=l(r[o],n);return s},"decode":function(t,e){var n=!1!==(e=e||{}).strict;o=u(t),s=o.length,a=0;for(var r,i=[];!1!==(r=h(n));)i.push(r);return function(t){for(var e,n=t.length,r=-1,i="";++r<n;)65535<(e=t[r])&&(i+=c((e-=65536)>>>10&1023|55296),e=56320|1023&e),i+=c(e);return i}(i)}};void 0===(g=function(){return n}.call(v,y,v,f))||(f.exports=g)}()}).call(this,y(132)(t),y(6))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{"enumerable":!0,"get":function(){return t.l}}),Object.defineProperty(t,"id",{"enumerable":!0,"get":function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e){!function(){"use strict";for(var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=new Uint8Array(256),t=0;t<o.length;t++)p[o.charCodeAt(t)]=t;e.encode=function(t){var e,n=new Uint8Array(t),r=n.length,i="";for(e=0;e<r;e+=3)i+=o[n[e]>>2],i+=o[(3&n[e])<<4|n[e+1]>>4],i+=o[(15&n[e+1])<<2|n[e+2]>>6],i+=o[63&n[e+2]];return r%3==2?i=i.substring(0,i.length-1)+"=":r%3==1&&(i=i.substring(0,i.length-2)+"=="),i},e.decode=function(t){var e,n,r,i,o,s=.75*t.length,a=t.length,c=0;"="===t[t.length-1]&&(s--,"="===t[t.length-2]&&s--);var u=new ArrayBuffer(s),l=new Uint8Array(u);for(e=0;e<a;e+=4)n=p[t.charCodeAt(e)],r=p[t.charCodeAt(e+1)],i=p[t.charCodeAt(e+2)],o=p[t.charCodeAt(e+3)],l[c++]=n<<2|r>>4,l[c++]=(15&r)<<4|i>>2,l[c++]=(3&i)<<6|63&o;return u}}()},function(c,t,e){(function(t){var i=t.BlobBuilder||t.WebKitBlobBuilder||t.MSBlobBuilder||t.MozBlobBuilder,e=function(){try{return 2===new Blob(["hi"]).size}catch(t){return!1}}(),n=e&&function(){try{return 2===new Blob([new Uint8Array([1,2])]).size}catch(t){return!1}}(),r=i&&i.prototype.append&&i.prototype.getBlob;function o(t){for(var e=0;e<t.length;e++){var n=t[e];if(n.buffer instanceof ArrayBuffer){var r=n.buffer;if(n.byteLength!==r.byteLength){var i=new Uint8Array(n.byteLength);i.set(new Uint8Array(r,n.byteOffset,n.byteLength)),r=i.buffer}t[e]=r}}}function s(t,e){e=e||{};var n=new i;o(t);for(var r=0;r<t.length;r++)n.append(t[r]);return e.type?n.getBlob(e.type):n.getBlob()}function a(t,e){return o(t),new Blob(t,e||{})}c.exports=e?n?t.Blob:a:r?s:void 0}).call(this,e(6))},function(s,t,a){(function(n){var r=a(74),t=a(31);s.exports=e;var i,u=/\n/g,l=/\\n/g;function o(){}function e(t){r.call(this,t),this.query=this.query||{},i||(n.___eio||(n.___eio=[]),i=n.___eio),this.index=i.length;var e=this;i.push(function(t){e.onData(t)}),this.query.j=this.index,n.document&&n.addEventListener&&n.addEventListener("beforeunload",function(){e.script&&(e.script.onerror=o)},!1)}t(e,r),e.prototype.supportsBinary=!1,e.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),r.prototype.doClose.call(this)},e.prototype.doPoll=function(){var e=this,t=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),t.async=!0,t.src=this.uri(),t.onerror=function(t){e.onError("jsonp poll error",t)};var n=document.getElementsByTagName("script")[0];n?n.parentNode.insertBefore(t,n):(document.head||document.body).appendChild(t),this.script=t,"undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent)&&setTimeout(function(){var t=document.createElement("iframe");document.body.appendChild(t),document.body.removeChild(t)},100)},e.prototype.doWrite=function(t,e){var n=this;if(!this.form){var r,i=document.createElement("form"),o=document.createElement("textarea"),s=this.iframeId="eio_iframe_"+this.index;i.className="socketio",i.style.position="absolute",i.style.top="-1000px",i.style.left="-1000px",i.target=s,i.method="POST",i.setAttribute("accept-charset","utf-8"),o.name="d",i.appendChild(o),document.body.appendChild(i),this.form=i,this.area=o}function a(){c(),e()}function c(){if(n.iframe)try{n.form.removeChild(n.iframe)}catch(t){n.onError("jsonp polling iframe removal error",t)}try{var t='<iframe src="javascript:0" name="'+n.iframeId+'">';r=document.createElement(t)}catch(t){(r=document.createElement("iframe")).name=n.iframeId,r.src="javascript:0"}r.id=n.iframeId,n.form.appendChild(r),n.iframe=r}this.form.action=this.uri(),c(),t=t.replace(l,"\\\n"),this.area.value=t.replace(u,"\\n");try{this.form.submit()}catch(t){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"===n.iframe.readyState&&a()}:this.iframe.onload=a}}).call(this,a(6))},function(p,t,h){(function(s){var e,n=h(51),a=h(19),r=h(30),t=h(31),i=h(76),c=h(13)("engine.io-client:websocket"),o=s.WebSocket||s.MozWebSocket;if("undefined"==typeof window)try{e=h(137)}catch(t){}var u=o;function l(t){t&&t.forceBase64&&(this.supportsBinary=!1),this.perMessageDeflate=t.perMessageDeflate,this.usingBrowserWebSocket=o&&!t.forceNode,this.protocols=t.protocols,this.usingBrowserWebSocket||(u=e),n.call(this,t)}u||"undefined"!=typeof window||(u=e),t(p.exports=l,n),l.prototype.name="websocket",l.prototype.supportsBinary=!0,l.prototype.doOpen=function(){if(this.check()){var t=this.uri(),e=this.protocols,n={"agent":this.agent,"perMessageDeflate":this.perMessageDeflate};n.pfx=this.pfx,n.key=this.key,n.passphrase=this.passphrase,n.cert=this.cert,n.ca=this.ca,n.ciphers=this.ciphers,n.rejectUnauthorized=this.rejectUnauthorized,this.extraHeaders&&(n.headers=this.extraHeaders),this.localAddress&&(n.localAddress=this.localAddress);try{this.ws=this.usingBrowserWebSocket?e?new u(t,e):new u(t):new u(t,e,n)}catch(t){return this.emit("error",t)}void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},l.prototype.addEventListeners=function(){var e=this;this.ws.onopen=function(){e.onOpen()},this.ws.onclose=function(){e.onClose()},this.ws.onmessage=function(t){e.onData(t.data)},this.ws.onerror=function(t){e.onError("websocket error",t)}},l.prototype.write=function(t){var r=this;this.writable=!1;for(var i=t.length,e=0,n=i;e<n;e++)!function(n){a.encodePacket(n,r.supportsBinary,function(t){if(!r.usingBrowserWebSocket){var e={};if(n.options&&(e.compress=n.options.compress),r.perMessageDeflate)("string"==typeof t?s.Buffer.byteLength(t):t.length)<r.perMessageDeflate.threshold&&(e.compress=!1)}try{r.usingBrowserWebSocket?r.ws.send(t):r.ws.send(t,e)}catch(t){c("websocket closed before onclose event")}--i||o()})}(t[e]);function o(){r.emit("flush"),setTimeout(function(){r.writable=!0,r.emit("drain")},0)}},l.prototype.onClose=function(){n.prototype.onClose.call(this)},l.prototype.doClose=function(){void 0!==this.ws&&this.ws.close()},l.prototype.uri=function(){var t=this.query||{},e=this.secure?"wss":"ws",n="";return this.port&&("wss"==e&&443!==Number(this.port)||"ws"==e&&80!==Number(this.port))&&(n=":"+this.port),this.timestampRequests&&(t[this.timestampParam]=i()),this.supportsBinary||(t.b64=1),(t=r.encode(t)).length&&(t="?"+t),e+"://"+(-1!==this.hostname.indexOf(":")?"["+this.hostname+"]":this.hostname)+n+this.path+t},l.prototype.check=function(){return!(!u||"__initialize"in u&&this.name===l.prototype.name)}}).call(this,h(6))},function(t,e){},function(t,e){t.exports=function(t,e){for(var n=[],r=(e=e||0)||0;r<t.length;r++)n[r-e]=t[r];return n}},function(t,e){function n(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=0<t.jitter&&t.jitter<=1?t.jitter:0,this.attempts=0}(t.exports=n).prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),n=Math.floor(e*this.jitter*t);t=0==(1&Math.floor(10*e))?t-n:t+n}return 0|Math.min(t,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(t){this.ms=t},n.prototype.setMax=function(t){this.max=t},n.prototype.setJitter=function(t){this.jitter=t}},function(t,e){t.exports="data:image/png;base64,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"},function(t,e,n){n(56),n(42),n(59),n(142),n(149),n(152),n(154),t.exports=n(7).Set},function(t,e,n){"use strict";var r=n(143),i=n(82);t.exports=n(144)("Set",function(e){return function(t){return e(this,0<arguments.length?t:void 0)}},{"add":function(t){return r.def(i(this,"Set"),t=0===t?0:t,t)}},r)},function(t,e,n){"use strict";function s(t,e){var n,r=d(e);if("F"!==r)return t._i[r];for(n=t._f;n;n=n.n)if(n.k==e)return n}var a=n(11).f,c=n(57),u=n(47),l=n(14),p=n(45),h=n(25),r=n(43),i=n(60),o=n(68),f=n(15),d=n(81).fastKey,g=n(82),v=f?"_s":"size";t.exports={"getConstructor":function(t,o,n,r){var i=t(function(t,e){p(t,i,o,"_i"),t._t=o,t._i=c(null),t._f=void 0,t._l=void 0,t[v]=0,null!=e&&h(e,n,t[r],t)});return u(i.prototype,{"clear":function(){for(var t=g(this,o),e=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete e[n.i];t._f=t._l=void 0,t[v]=0},"delete":function(t){var e=g(this,o),n=s(e,t);if(n){var r=n.n,i=n.p;delete e._i[n.i],n.r=!0,i&&(i.n=r),r&&(r.p=i),e._f==n&&(e._f=r),e._l==n&&(e._l=i),e[v]--}return!!n},"forEach":function(t,e){g(this,o);for(var n,r=l(t,1<arguments.length?e:void 0,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},"has":function(t){return!!s(g(this,o),t)}}),f&&a(i.prototype,"size",{"get":function(){return g(this,o)[v]}}),i},"def":function(t,e,n){var r,i,o=s(t,e);return o?o.v=n:(t._l=o={"i":i=d(e,!0),"k":e,"v":n,"p":r=t._l,"n":void 0,"r":!1},t._f||(t._f=o),r&&(r.n=o),t[v]++,"F"!==i&&(t._i[i]=o)),t},"getEntry":s,"setStrong":function(t,n,e){r(t,n,function(t,e){this._t=g(t,n),this._k=e,this._l=void 0},function(){for(var t=this._k,e=this._l;e&&e.r;)e=e.p;return this._t&&(this._l=e=e?e.n:this._t._f)?i(0,"keys"==t?e.k:"values"==t?e.v:[e.k,e.v]):(this._t=void 0,i(1))},e?"entries":"values",!e,!0),o(n)}}},function(t,e,n){"use strict";var p=n(5),h=n(10),f=n(81),d=n(21),g=n(16),v=n(47),y=n(25),m=n(45),b=n(12),w=n(29),x=n(11).f,T=n(145)(0),E=n(15);t.exports=function(n,t,e,r,i,o){var s=p[n],a=s,c=i?"set":"add",u=a&&a.prototype,l={};return E&&"function"==typeof a&&(o||u.forEach&&!d(function(){(new a).entries().next()}))?(a=t(function(t,e){m(t,a,n,"_c"),t._c=new s,null!=e&&y(e,i,t[c],t)}),T("add,clear,delete,forEach,get,has,set,keys,values,entries,toJSON".split(","),function(r){var i="add"==r||"set"==r;r in u&&(!o||"clear"!=r)&&g(a.prototype,r,function(t,e){if(m(this,a,r),!i&&o&&!b(t))return"get"==r&&void 0;var n=this._c[r](0===t?0:t,e);return i?this:n})}),o||x(a.prototype,"size",{"get":function(){return this._c.size}})):(a=r.getConstructor(t,n,i,c),v(a.prototype,e),f.NEED=!0),w(a,n),l[n]=a,h(h.G+h.W+h.F,l),o||r.setStrong(a,n,i),a}},function(t,e,n){var b=n(14),w=n(36),x=n(28),T=n(27),r=n(146);t.exports=function(p,t){var h=1==p,f=2==p,d=3==p,g=4==p,v=6==p,y=5==p||v,m=t||r;return function(t,e,n){for(var r,i,o=x(t),s=w(o),a=b(e,n,3),c=T(s.length),u=0,l=h?m(t,c):f?m(t,0):void 0;u<c;u++)if((y||u in s)&&(i=a(r=s[u],u,o),p))if(h)l[u]=i;else if(i)switch(p){case 3:return!0;case 5:return r;case 6:return u;case 2:l.push(r)}else if(g)return!1;return v?-1:d||g?g:l}}},function(t,e,n){var r=n(147);t.exports=function(t,e){return new(r(t))(e)}},function(t,e,n){var r=n(12),i=n(148),o=n(8)("species");t.exports=function(t){var e;return i(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!i(e.prototype)||(e=void 0),r(e)&&null===(e=e[o])&&(e=void 0)),void 0===e?Array:e}},function(t,e,n){var r=n(23);t.exports=Array.isArray||function(t){return"Array"==r(t)}},function(t,e,n){var r=n(10);r(r.P+r.R,"Set",{"toJSON":n(150)("Set")})},function(t,e,n){var r=n(44),i=n(151);t.exports=function(t){return function(){if(r(this)!=t)throw TypeError(t+"#toJSON isn't generic");return i(this)}}},function(t,e,n){var r=n(25);t.exports=function(t,e){var n=[];return r(t,!1,n.push,n,e),n}},function(t,e,n){n(153)("Set")},function(t,e,n){"use strict";var r=n(10);t.exports=function(t){r(r.S,t,{"of":function(){for(var t=arguments.length,e=new Array(t);t--;)e[t]=arguments[t];return new this(e)}})}},function(t,e,n){n(155)("Set")},function(t,e,n){"use strict";var r=n(10),c=n(20),u=n(14),l=n(25);t.exports=function(t){r(r.S,t,{"from":function(t,e,n){var r,i,o,s,a=e;return c(this),(r=void 0!==a)&&c(a),null==t?new this:(i=[],r?(o=0,s=u(a,n,2),l(t,!1,function(t){i.push(s(t,o++))})):l(t,!1,i.push,i),new this(i))}})}},function(t,e,n){n(42),n(157),t.exports=n(7).Array.from},function(t,e,n){"use strict";var d=n(14),r=n(10),g=n(28),v=n(61),y=n(62),m=n(27),b=n(158),w=n(63);r(r.S+r.F*!n(69)(function(t){Array.from(t)}),"Array",{"from":function(t,e,n){var r,i,o,s,a=g(t),c="function"==typeof this?this:Array,u=arguments.length,l=1<u?e:void 0,p=void 0!==l,h=0,f=w(a);if(p&&(l=d(l,2<u?n:void 0,2)),null==f||c==Array&&y(f))for(i=new c(r=m(a.length));h<r;h++)b(i,h,p?l(a[h],h):a[h]);else for(s=f.call(a),i=new c;!(o=s.next()).done;h++)b(i,h,p?v(s,l,[o.value,h],!0):o.value);return i.length=h,i}})},function(t,e,n){"use strict";var r=n(11),i=n(34);t.exports=function(t,e,n){e in t?r.f(t,e,i(0,n)):t[e]=n}},function(t,e,n){"use strict";n.r(e);var r,i,o=n(9),c=n.n(o),s=n(4),a=n.n(s),u=n(3),l=n.n(u),p=n(2),h=n.n(p),f=n(0),T=n.n(f),d=n(32),g=n.n(d),v=n(1),y=n.n(v),m=n(83),b=n.n(m),w=n(84),x=n.n(w),E=(r={"CONNECT":"connect","DISCONNECT":"disconnect","RECONNECT":"reconnect","RECONNECT_ATTEMPT":"reconnect_attempt","ERROR":"error","CLOSE_ROOM":"CLOSEROOM","OPEN_ROOM":"openRoom","GONGGAO":"GONGGAO","SPEAK":"SPEAK","SPEAK_ERROR":"speakError","REWARD":"REWARD","QUESTION":"QUESTION","CLOSE_QUESTION":"CLOSE_QUESTION","ANSWER":"ANSWER","CUSTOMER_MESSAGE":"CUSTOMER_MESSAGE","SERVER_ERROR":"serverError","KICK_USER":"KICK","REMOVE_HISTORY":"REMOVE_HISTORY","REMOVE_CONTENT":"REMOVE_CONTENT","CLOSE_DANMU":"CLOSE_DANMU"},y()(r,"ERROR","error"),y()(r,"HISTORY_MESSAGE","historyMessage"),y()(r,"SEND_MESSAGE","sendMessage"),y()(r,"PROHIBIT_TO_SPEAK","prohibitToSpeak"),y()(r,"LOGIN","LOGIN"),y()(r,"LOGIN_CB","LOGIN_CB"),y()(r,"LOGOUT","LOGOUT"),y()(r,"UPDATEUSER","updateUser"),y()(r,"CLASSSTART","onClassStart"),y()(r,"CLASSEND","onClassEnd"),y()(r,"SLICEID","onSliceID"),y()(r,"SLICESTART","onSliceStart"),y()(r,"CLASSCONTROL","onClassControl"),y()(r,"MUTEALLVIDEO","muteAllVideo"),y()(r,"MUTEALLAUDIO","muteAllAudio"),y()(r,"MUTEUSERAUDIO","muteUserAudio"),y()(r,"MUTEUSERVIDEO","muteUserVideo"),y()(r,"SLICECONTROL","onSliceControl"),y()(r,"SLICEDRAW","onSliceDraw"),y()(r,"SLICEDOPEN","onSliceOpen"),y()(r,"S_QUESTION","S_QUESTION"),y()(r,"T_ANSWER","T_ANSWER"),y()(r,"UPDATE_QUESTION_HISTROY","UPDATE_QUESTION_HISTROY"),y()(r,"BULLETIN","BULLETIN"),y()(r,"REMOVE_BULLETIN","REMOVE_BULLETIN"),y()(r,"FLOWERS","FLOWERS"),y()(r,"REDPAPER","REDPAPER"),y()(r,"SIGN_IN","SIGN_IN"),y()(r,"STOP_SIGN_IN","STOP_SIGN_IN"),y()(r,"TO_SIGN_IN","TO_SIGN_IN"),y()(r,"LIKES","LIKES"),y()(r,"MICROPHONE","OPEN_MICROPHONE"),y()(r,"ALLOW_MICROPHONE","ALLOW_MICROPHONE"),y()(r,"SUCCESS_MICROPHONE","SUCCESS_MICROPHONE"),y()(r,"JOIN_CHANNEL_FAIL","JOIN_CHANNEL_FAIL"),y()(r,"SET_NICK","SET_NICK"),y()(r,"ONLINE_TEACHERINFO","O_TEACHER_INFO"),y()(r,"TEACHER_INFO","TEACHER_INFO"),y()(r,"OVERTIMECONNECT","overtimeConnect"),y()(r,"SET_MANAGER_INFO","SET_MANAGER_INFO"),y()(r,"GET_TEST_QUESTION_CONTENT","GET_TEST_QUESTION_CONTENT"),y()(r,"GET_TEST_QUESTION_RESULT","GET_TEST_QUESTION_RESULT"),y()(r,"BAN_USER_ROOM","ban_user_room"),y()(r,"CHAT_IMG","CHAT_IMG"),y()(r,"CHAT_IMG_LOADING","CHAT_IMG_LOADING"),y()(r,"SWITCHVIEW","switchView"),y()(r,"STOP_TEST_QUESTION","STOP_TEST_QUESTION"),y()(r,"LOTTERY_START","LotteryStart"),y()(r,"LOTTERY_END","LotteryEnd"),y()(r,"LOTTERY_WINNER","LotteryWinner"),y()(r,"ON_LOTTERY","ON_LOTTERY"),y()(r,"START_QUESTIONNAIRE","START_QUESTIONNAIRE"),y()(r,"STOP_QUESTIONNAIRE","STOP_QUESTIONNAIRE"),y()(r,"REMOVE_SHIELD","REMOVE_SHIELD"),y()(r,"ADD_SHIELD","ADD_SHIELD"),y()(r,"CENSOR","CENSOR"),y()(r,"SELF_SPEAK","SELF_SPEAK"),r),k=n(26),C=(i={},y()(i,E.LOGIN,function(t,e){e.user.userId==this.userId&&(this.loginClock&&(clearTimeout(this.loginClock),this.loginClock=null),this.ip=e.user.clientIp)}),y()(i,E.SET_NICK,function(t,e){"success"===e.status&&e.userId===this.userId&&(this.userInfo.nick=this.options.nick=e.nick)}),y()(i,E.ADD_SHIELD,function(t,e){e.data&&(e.data.userId==this.userId?this.isUserIdShield=!0:e.data.ip===this.ip&&(this.isIpShield=!0))}),y()(i,E.REMOVE_SHIELD,function(t,e){e.data&&(e.data.userId==this.userId?(this.isShield=!1,this.isUserIdShield=!1):e.data.ip===this.ip&&(this.isShield=!1,this.isIpShield=!1))}),i),S=(h()(A,null,[{"key":"parseString","value":function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").trim():t}},{"key":"FormatTime","value":function(t){function e(t){return t<10?"0"+t:t}var n=new Date(t/1);return n.getFullYear()+"-"+e(n.getMonth()+1)+"-"+e(n.getDate())+" "+e(n.getHours())+":"+e(n.getMinutes())}}]),h()(A,[{"key":"queryDomain","value":function(){var e=this;return new g.a(function(t){T.a.ajax({"url":e.protocol+"//api.polyv.net/live/front/watch/get-chat-domain?channelId="+e.roomId,"dataType":"json"}).done(function(t){200===t.code&&(e.apiPrefix.socketHost=t.data.chatDomain,e.apiPrefix.chatApi=t.data.chatApiDomain)}).always(function(){t()})})}},{"key":"supportsWebSockets","value":function(){return"WebSocket"in window||"MozWebSocket"in window}},{"key":"getReconnectionDelay","value":function(){return[2e3,3e3,4e3,5e3][Math.floor(4*Math.random())]}},{"key":"overtimeConnect","value":function(t){var e=this,n=0<arguments.length&&void 0!==t?t:30;this.loginClock=setTimeout(function(){e.trigger(E.OVERTIMECONNECT),clearTimeout(e.loginClock),e.loginClock=null},1e3*n)}},{"key":"createSocket","value":function(){var t={};return this.version&&(t.token=this.options.token,t.version="2.0"),x()(this.protocol+"//"+this.apiPrefix.socketHost,{"query":t,"transports":[this.supportsWebSockets()?"websocket":"polling"],"reconnectionDelay":this.getReconnectionDelay(),"randomizationFactor":Math.random()})}},{"key":"setUp","value":function(){var e=this,t=!1;this.socket=this.createSocket(),this.overtimeConnect(30),this.socket.on("connect",function(){e.trigger(E.CONNECT).login(function(t){e.trigger(E.LOGIN_CB,t),"2"===(t=t.toString())[0]&&(clearTimeout(e.loginClock),e.loginClock=null),"0"===t[4]&&(e.isShield=!0)}),t||(e.receiveMessage(),e.heartBeat(e.socket.id),e.socketEvent(),t=!0)})}},{"key":"heartBeat","value":function(e,t){var n=this,r=1<arguments.length&&void 0!==t?t:18e5,i=null;i=setTimeout(function t(){n.socket.emit("message",c()({"EVENT":"HEARTBEAT","uid":e})),clearTimeout(i),i=setTimeout(t,r)},r)}},{"key":"socketEvent","value":function(){var e=this;return this.socket.on("disconnect",function(){e.trigger(E.DISCONNECT)}),this.socket.on("error",function(){e.trigger(E.ERROR)}),this.socket.on("reconnect",function(t){e.trigger(E.RECONNECT)}),this.socket.on("reconnect_attempt",function(t){e.trigger(E.RECONNECT_ATTEMPT)}),this}},{"key":"handleSocketMessage","value":function(t){var e=void 0;switch(t.EVENT){case"CLOSEROOM":this.roomClosed=t.value.closed,e=t.value.closed?E.CLOSE_ROOM:E.OPEN_ROOM;break;case"GONGGAO":e=E.SYSTEM_ANNOUNCEMENT;break;case"SPEAK":e="error"===t.status?E.SPEAK_ERROR:"censor"===t.status?E.CENSOR:t.user.userId.toString()===this.userId?E.SELF_SPEAK:E.SPEAK;break;case"CHAT_IMG":e=E.CHAT_IMG;break;case"REWARD":e=E.REWARD;break;case"QUESTION":e=E.QUESTION;break;case"CLOSE_QUESTION":e=E.CLOSE_QUESTION;break;case"ANSWER":e=E.ANSWER;break;case"CUSTOMER_MESSAGE":e=E.CUSTOMER_MESSAGE;break;case"ERROR":e=E.SERVER_ERROR;break;case"KICK":e=E.KICK_USER;break;case"REMOVE_HISTORY":e=E.REMOVE_HISTORY;break;case"REMOVE_CONTENT":e=E.REMOVE_CONTENT;break;case"CLOSE_DANMU":e=E.CLOSE_DANMU;break;case"LOGIN":e=E.LOGIN;break;case"LOGOUT":e=E.LOGOUT;break;case"onSliceID":e=E.SLICEID;break;case"onSliceControl":e=E.SLICECONTROL;break;case"onSliceDraw":e=E.SLICEDRAW;break;case"onSliceOpen":e=E.SLICEDOPEN;break;case"S_QUESTION":e=E.S_QUESTION;break;case"T_ANSWER":e=E.T_ANSWER;break;case"BULLETIN":e=E.BULLETIN;break;case"REMOVE_BULLETIN":e=E.REMOVE_BULLETIN;break;case"FLOWERS":e=E.FLOWERS;break;case"LIKES":e=E.LIKES;break;case"REDPAPER":e=E.REDPAPER;break;case"SIGN_IN":e=E.SIGN_IN;break;case"STOP_SIGN_IN":e=E.STOP_SIGN_IN;break;case"TO_SIGN_IN":e=E.TO_SIGN_IN;break;case"ADD_SHIELD":e=E.ADD_SHIELD;break;case"REMOVE_SHIELD":e=E.REMOVE_SHIELD;break;case"SET_NICK":e=E.SET_NICK;break;case"TEACHER_INFO":e=E.TEACHER_INFO;break;case"O_TEACHER_INFO":e=E.ONLINE_TEACHERINFO;break;case"SET_MANAGER_INFO":e=E.SET_MANAGER_INFO;break;case"GET_TEST_QUESTION_CONTENT":e=E.GET_TEST_QUESTION_CONTENT;break;case"GET_TEST_QUESTION_RESULT":e=E.GET_TEST_QUESTION_RESULT;break;case"CHAT_IMG":e=E.CHAT_IMG;break;case"STOP_TEST_QUESTION":e=E.STOP_TEST_QUESTION;break;case"LotteryStart":e=E.LOTTERY_START;break;case"LotteryEnd":e=E.LOTTERY_END;break;case"LotteryWinner":e=E.LOTTERY_WINNER;break;case"ON_LOTTERY":e=E.ON_LOTTERY;break;case"START_QUESTIONNAIRE":e=E.START_QUESTIONNAIRE;break;case"STOP_QUESTIONNAIRE":e=E.STOP_QUESTIONNAIRE}e&&((t.EVENT=e)===E.SPEAK&&(t.content=t.values[0],delete t.values,t=this.parseData([t])[0]),e===E.FLOWERS&&(t.flowers=!0),e===E.REWARD&&(t.reward=!0),e===E.CUSTOMER_MESSAGE&&(t.custom=!0),this.trigger(e,t),"function"==typeof this.options.roomMessage&&this.options.roomMessage(t))}},{"key":"receiveMessage","value":function(){var r=this;return this.socket.on("message",function(t){var e=JSON.parse(t);if(e&&e.EVENT)switch(e.EVENT){case"MESSAGES":case"SPEAKS":if(!e.data)break;for(var n=0;n<e.data.length;n++)r.handleSocketMessage(e.data[n]);break;default:r.handleSocketMessage(e)}}),this}},{"key":"parseData","value":function(t){var e=this;return t.map(function(t){return t.reward=t.user&&"1"==t.user.uid,t.custom=t.user&&"2"==t.user.uid,t.currentUser=t.user&&t.user.userId==e.options.userId,t.imgchat="chatImg"===t.msgSource,t.content=t.reward?t.content:A.parseString(t.content),t.formatTime=A.FormatTime(t.time),t.specialUser=t.user&&("teacher"===t.user.userType||"manager"===t.user.userType||"assistant"===t.user.userType||"guest"===t.user.userType),t})}},{"key":"sortUsers","value":function(t){var n=this;return t.forEach(function(t,e){return t.userId==n.options.userId?(t.nick=t.nick+"（"+n.langData.me+"）",t.index=1):"manager"===t.userType?t.index=2:"teacher"===t.userType?(t.index=3,n.hasTeacher=!0):"assistant"===t.userType?t.index=4:t.index=5,t}),t.sort(function(t,e){return t.index-e.index}),t}},{"key":"isEspecialUser","value":function(){var t=this.userType;return"teacher"===t||"manager"===t||"assistant"===t}},{"key":"login","value":function(t){var e=0<arguments.length&&void 0!==t?t:function(){},n=this.options;return this.socket.emit("message",c()({"EVENT":"LOGIN","values":[n.nick,n.pic,n.userId],"roomId":n.roomId,"type":n.userType,"actor":n.actor,"channelId":n.channelId,"sibRooms":n.sibRooms,"authorization":n.authorization,"userSource":n.userSource}),e),this}},{"key":"send","value":function(t){if(t&&(!this.roomClosed||this.isEspecialUser())){var e=this.options;this.isUserIdShield||this.isIpShield||this.isShield||this.socket.emit("message",c()({"EVENT":"SPEAK","roomId":e.roomId,"values":[t],"accountId":e.accountId,"sessionId":e.sessionId}));var n=this.parseData([{"content":t,"time":(new Date).getTime(),"mySelfSend":!0,"user":{"nick":e.nick,"pic":e.pic,"userId":e.userId,"roomId":e.roomId,"channelId":e.channelId||e.roomId}}])[0];n.EVENT="sendMessage",this.trigger(E.SEND_MESSAGE,n),"function"==typeof this.options.roomMessage&&this.options.roomMessage(n)}}},{"key":"sendFlower","value":function(){var t=this.options;this.socket.emit("message",c()({"EVENT":"FLOWERS","roomId":t.roomId,"nick":t.nick,"uimg":t.pic}))}},{"key":"like","value":function(){var t=this.options;this.socket.emit("message",c()({"EVENT":"LIKES","roomId":t.roomId,"nick":t.nick,"userId":t.userId,"count":1}))}},{"key":"setNickname","value":function(t){var e=this.options;this.socket.emit("message",c()({"EVENT":"SET_NICK","roomId":e.roomId,"userId":e.userId,"nick":t}))}},{"key":"sendAnswerCardAnswer","value":function(t,e){var n=1<arguments.length&&void 0!==e?e:[];if(t&&n instanceof Array){n=n.sort().join("");var r=this.options;this.socket.emit("message",c()({"EVENT":"ANSWER_TEST_QUESTION","roomId":r.roomId,"nick":r.nick,"userId":r.userId,"option":n,"questionId":t}))}}},{"key":"closeChat","value":function(t){var e=0<arguments.length&&void 0!==t&&t;"boolean"==typeof e&&this.socket.emit("message",c()({"EVENT":"CLOSEROOM","vale":{"closed":e,"roomId":this.options.roomId}}))}},{"key":"getHistoryMessage","value":function(t,e){var n=this,r=0<arguments.length&&void 0!==t?t:0,i=1<arguments.length&&void 0!==e?e:10;return new g.a(function(e,t){T.a.ajax({"url":n.protocol+"//"+n.apiPrefix.chatApi+"/front/history","type":"get","dataType":"jsonp","data":{"roomId":n.roomId,"start":r,"end":i,"toGetSubRooms":n.isTransmitChannel}}).done(function(t){e(t)})})}},{"key":"getUserList","value":function(t,e){var n=this,r=0<arguments.length&&void 0!==t?t:1,i=1<arguments.length&&void 0!==e?e:100;return new g.a(function(e,t){T.a.ajax({"url":n.protocol+"//"+n.apiPrefix.chatApi+"/front/listUsers","type":"get","dataType":"jsonp","data":{"roomId":n.roomId,"page":r,"len":i,"toGetSubRooms":n.isTransmitChannel}}).done(function(t){e(t)})})}},{"key":"sendAskContent","value":function(t){if(t){var e=this.userInfo;e.actor=this.langData.student,this.socket.emit("message",c()({"EVENT":"S_QUESTION","roomId":this.roomId,"user":e,"content":t}))}}},{"key":"updateSessionId","value":function(t){this.options.sessionId=t}}]),A);function A(t){l()(this,A),this.options=a()({},t),this.isTransmitChannel="transmit"===t.channelType,this.userId=t.userId,this.roomId=t.roomId,this.userType=t.userType,this.userInfo={"nick":t.nick,"pic":t.pic,"userId":t.userId,"userType":t.userType,"accountId":t.accountId,"actor":t.actor},this.userSetChatHost=!1,this.userSetChatApiHost=!1,t.apiPrefix&&(t.apiPrefix.socketHost&&"chat.polyv.net"!==t.apiPrefix.socketHost&&(this.userSetChatHost=!0),t.apiPrefix.chatApi&&"apichat.polyv.net"!==t.apiPrefix.chatApi&&(this.userSetChatApiHost=!0)),this.protocol="http:"===location.protocol?"http:":"https:",this.apiPrefix=a()({"socketHost":"chat.polyv.net","chatApi":"apichat.polyv.net"},t.apiPrefix),this.ip=void 0,this.isShield=!1,this.isUserIdShield=!1,this.isIpShield=!1,this.version=t.version,this.langData=t.langData;var i=this,o=this.observer=new b.a;for(var e in o.trigger=function(t){for(var e=arguments.length,n=Array(1<e?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return o.emit.apply(o,[t,t].concat(n)),i},o.off=function(t){for(var e=arguments.length,n=Array(1<e?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return o.removeListener.apply(o,[t].concat(n)),i},this.on=function(t,e){return o.on(t,e),i},this.off=o.off.bind(o),this.trigger=o.trigger.bind(o),this.events=E,C)C.hasOwnProperty(e)&&this.on(e,C[e].bind(this))}S.events=E;var N=S;function O(t){l()(this,O),this.options=a()({},t),this.$el=T()(t.el),this.$tabContainer=T()(this.$el.data("target")),this.data=t.data,this.activeClass=t.activeClass,this.countEnable=t.countEnable||!0,this.currentNav=0,this.setUp().initDomEvent()}var _=(h()(O,[{"key":"setUp","value":function(){var i=this,o=[],s=[],a=100*(1/this.data.length).toFixed(4);return this.data.forEach(function(t,e){var n="user-list"===t.type?"userList":t.type,r="user-list"===t.type&&i.countEnable?'(<span class="polyv-user-count">0</span>)':"";o.push('<li style="width:'+a+'%" data-index="'+e+'" data-type="'+t.type+'"><span data-lang="'+n+'">'+t.name+"</span>"+r+"</li>"),s.push('<div class="tab-'+t.type+'" id="tab-'+t.type+'"></div>')}),this.$el.append(o.join("")),this.$tabContainer.append(s.join("")),this.navList=this.$el.children(),this.tabList=this.$tabContainer.children(),this.navList.eq(this.currentNav).addClass(this.activeClass+" js-"+this.activeClass),this.tabList.eq(this.currentNav).show(),this}},{"key":"initDomEvent","value":function(){var n=this;this.$el.on("click","li",function(t){var e=T()(t.currentTarget).data("index");n.currentNav!==e&&(n.navList.eq(n.currentNav).removeClass(n.activeClass+" js-"+n.activeClass),n.tabList.eq(n.currentNav).hide(),n.currentNav=e,n.navList.eq(n.currentNav).addClass(n.activeClass+" js-"+n.activeClass),n.tabList.eq(n.currentNav).show())})}}]),O),I=navigator.userAgent||navigator.vendor||window.opera,R=function(){var t=!1;return(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|Pad|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(I)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(I.substr(0,4)))&&(t=!0),t},L=function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").trim():t},D=function(t,n){if("string"!=typeof t)return t;1!==n&&2!==n&&(n=1);var r=t,e=r.match(/\[[^\[\]]+?\]/g);return e&&e.forEach(function(t){var e=Object(k["parseEmotions"])(t,n);t!==e&&(r=r.replace(t,e))}),r},M=function(t,e,n){var r=n?";expires="+n.toGMTString():"";document.cookie=t+"="+e+r},j=function(t){var e=document.cookie,n=e.indexOf(t),r=(e=e.substring(n)).indexOf(";");return-1===n?"":e.substring(t.length+1,-1===r?e.length:r)},P=function(){return-1!==I.indexOf("MicroMessenger")},B=function(t){if(!t)return t;return t.replace(/(http:\/\/|https:\/\/)([\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:\/~\+#]*[\w\-\@?^=%&\/~\+#])?)/g,'<a target="_blank" href="$1$2">$1$2</a>')},F=function(){return/iphone/i.test(navigator.userAgent)},U={"en":{"chat":"Chat","userList":"Online","ask":"Q&A","speakFast":"Speak too fast,please try again later","participateInChat":"Join the chat","send":"send","welcome":"Welcome","presentFlower":"send a flower","clickToSeeMore":"Click to see more","viewMore":"More","anyQuestion":"Hello,is there any question？","setNickname":"Enter your nickname","cancel":"Cancel","successfullySet":"Successfully set","nicknameAlreadyExists":"Nickname exist","justTheHost":"Just the host","viewAll":"View all","askTheTeacher":"I want ask","chatroomClosed":"Chatroom closed temporarily","chatroomReopens":"Chatroom opened","picturesIllegal":"Illegal image","waitingFor":"and","peopleJoin":"people entered","join":"join","nicknamesCannotBeEmpty":"Nicknames cannot be empty","nicknamesSpecail":"Nicknames contain special characters","maxCharacters":"Not longer than 8 characters","checkAll":"All","onlyHost":"Presenter","questionAsk":"Hi, what can I do for you","teacher":"teacher","manager":"manager","assistant":"assistant","student":"student","etc":"etc.","man":"","twoMan":"","likeText":"thinks the host speaks very well","presented":"presented","rewarded":"rewarded","yuan":"yuan","notice":"Announcement","chatroomConnectionFailed1":"Chatroom connection failure","chatroomConnectionFailed2":"try to refresh the page","chatroomConnectionFailed3":"If the problem persists, please contact the administrator","me":"me","askFast":"Too fast to ask questions","shieldReward":"Hide reward notifications","prompt":"Reminder"},"zh_CN":{"chat":"聊天","userList":"在线","ask":"提问","speakFast":"您的发言过快，请稍后再试","participateInChat":"我也来参与一下互动","send":"发送","welcome":"欢迎","presentFlower":"赠送了 鲜花","clickToSeeMore":"有更多新消息，点击查看","viewMore":"查看更多","anyQuestion":"同学，您好！请问有什么问题吗？","setNickname":"设置简单易记的名称有助于让大家认识你哦","cancel":"取消","successfullySet":"设置成功","nicknameAlreadyExists":"昵称已存在","justTheHost":"只显示主持人","viewAll":"显示所有","askTheTeacher":"我想问讲师","chatroomClosed":"聊天室暂时关闭","chatroomReopens":"聊天室已经开启","picturesIllegal":"图片不合法","waitingFor":"等","peopleJoin":"人加入","join":"加入","nicknamesCannotBeEmpty":"昵称不能为空","nicknamesSpecail":"昵称包含非法字符","maxCharacters":"昵称不能超过8个字符","checkAll":"查看全部","onlyHost":"只看主持人","questionAsk":"您好！请问有什么问题吗","teacher":"讲师","manager":"管理员","assistant":"助教","student":"学生","etc":"等","man":"人","twoMan":"等2人","likeText":"觉得主持人讲得很棒","presented":"赠送了","rewarded":"打赏了","yuan":"元","notice":"公告","chatroomConnectionFailed1":"聊天室连接失败","chatroomConnectionFailed2":"请尝试刷新解决","chatroomConnectionFailed3":"如无法解决请联系管理员","me":"我","askFast":"您的提问速度过快","shieldReward":"屏蔽打赏","prompt":"温馨提示"}};function H(t){l()(this,H),this.options=a()({},t),this.$el=T()(t.el),this.chat=t.chat,this.enableFlower=!1!==t.enableFlower,this.enableLike=!1!==t.enableLike,this.enableOnlyTeacher=!1!==t.enableOnlyTeacher,this.enableWordsCount=!1!==t.enableWordsCount,this.enableOnlyChat=!0===t.enableOnlyChat,this.lang=t.lang,this.langData=U[this.lang],this.errorTip=t.errorTip||this.langData.speakFast,this.hasMorePanel=!0===t.hasMorePanel,this.isMobile=R(),this.create().initDomEvent()}var q=(h()(H,[{"key":"updateLang","value":function(t){if(t!==this.lang){var e=this.errorTip===this.langData.speakFast;this.lang=t,this.langData=U[this.lang],e&&(this.errorTip=this.langData.speakFast)}}},{"key":"create","value":function(){var t=this.enableFlower?'<span class="polyv-icon polyv-icon-flower" data-type="flower"></span>':"",e=this.enableLike?'<span class="polyv-icon polyv-icon-like" data-type="like"></span>':"",n=this.enableOnlyTeacher?this.isMobile?'<li>\n                                              <span class="polyv-input-more-icon polyv-only-teacher" data-status="false" data-type="onlyhost"></span>\n                                              <span data-lang="onlyHost">'+this.langData.onlyHost+"</span>\n                                            </li>":'<div class="polyv-only-teacher polyv-pc-only-teacher" data-lang="onlyHost">'+this.langData.onlyHost+"</div>":"",r='<div class="polyv-chat-input">\n                  <div class="polyv-chat-input-main">\n                    <div class="polyv-chat-input-top clearfix">\n                      <div class="polyv-fl">\n                        <span class="polyv-icon polyv-icon-emotion"></span>\n                        '+t+"\n                        "+e+'\n                      </div>\n                      <div class="polyv-fr">\n                        '+n+"\n                        "+(this.enableOnlyChat&&!this.isMobile?'<div class="polyv-input-only-chat" data-lang="shieldReward">'+this.langData.shieldReward+"</div>":"")+'\n                      </div>\n                    </div>\n                    <div class="polyv-chat-input-middle">\n                      <textarea data-lang="participateInChat" placeholder="'+this.langData.participateInChat+'" class="polyv-input-ele"></textarea>\n                    </div>\n                    <div class="polyv-chat-input-bottom clearfix">\n                      <div class="polyv-fl" id="js-polyv-chat-input-bottom-left"></div>\n                      <div class="polyv-fr">\n                        <span><span class="polyv-words-count">0</span>/200</span>\n                        <button type="button" class="polyv-btn polyv-btn-info polyv-send polyv-pc-send" data-lang="send">'+this.langData.send+'</button>\n                      </div>\n                    </div>\n                  </div>\n                  <div class="polyv-emotion-wrap">\n                    <div class="polyv-emotion-list js-polyv-emotion-list"></div>\n                  </div>\n                </div>';if(this.isMobile){var i=0;t&&i++;var o="",s="",a="polyv-btn polyv-send polyv-mobile-send";this.hasMorePanel?(o='<span class="polyv-icon polyv-icon-more" data-type="more"></span>',s='\n          <div class="polyv-chat-input-more">\n            <div>\n              <ul class="polyv-more-control-list">\n                '+n+"\n              </ul>\n            </div>\n          </div>"):a+=" polyv-show-send",r='<div class="polyv-chat-input">\n                <div class="polyv-chat-input-main show-'+i+'-icon">\n                  <div>\n                    <div class="polyv-chat-input-content">\n                      <input data-lang="participateInChat" type="text" placeholder="'+this.langData.participateInChat+'" class="polyv-input-ele">\n                    </div>\n                    <div>\n                      <span class="polyv-icon polyv-icon-emotion" data-type="emotion"></span>\n                      '+t+"\n                      "+o+'\n                      <span data-lang="send" class="'+a+'">'+this.langData.send+'</span>\n                    </div>\n                  </div>\n                </div>\n                <div class="polyv-emotion-wrap">\n                  <div class="polyv-emotion-list js-polyv-emotion-list"></div>\n                </div>\n                '+s+"\n              </div>"}return this.$el.append(r.trim()),this.$el.find(".js-polyv-emotion-list").html(Object(k["genDOMList"])()),this}},{"key":"initDomEvent","value":function(){var n=this,t=T()("html"),r=this.$el.find(".polyv-chat-input"),e=this.$el.find(".polyv-emotion-wrap"),i=this.$el.find(".polyv-emotion-list"),o=this.$el.find(".polyv-icon-emotion"),s=this.$el.find(".polyv-icon-flower"),a=this.$el.find(".polyv-icon-like"),c=this.$el.find(".polyv-icon-more"),u=this.$el.find(".polyv-input-ele"),l=this.$el.find(".polyv-words-count"),p=this.$el.find(".polyv-send"),h=this.$el.find(".polyv-only-teacher>input"),f=this.$el.find(".polyv-chat-input-more"),d=0,g=this.isMobile?"touchend":"click",v=F()&&P(),y=!1,m=0,b=0;t.on("click",function(){e.hide(),n.$el.removeClass("polyv-show-more")}),o.on("click",function(){return e.toggle(),!1}),i.on("click",function(t){var e=T()(t.target);return e.hasClass("plv-emotion-panel__item__ico")&&u.val(u.val()+"["+e.data("title")+"]").trigger("input"),!1}),u.on("input porpertychange",function(t){t.stopPropagation();var e=u.val(),n=e.length;n?r.addClass("polyv-typing"):r.removeClass("polyv-typing"),200<n&&u.val(e.substring(0,200)),l.text(200<n?200:n)}).on("keypress",function(t){if(t.stopPropagation(),13===(t.keyCode||t.which)&&!n.isMobile){if(!n.isMobile)return p.trigger("click"),!1;v&&T()(t.target).blur()}}).on("blur",function(t){if(v){var e=void 0,n=void 0,r=self===top?window:window.top;n=setTimeout(function(){e=self===top?document.documentElement.scrollTop||document.body.scrollTop:T()(window.parent.document.scrollingElemen).scrollTop(),e-=1,r.scrollTo(0,e),e+=1,r.scrollTo(0,e),clearTimeout(n),n=null},1)}}),p.on(g,function(){if(u.val()&&"function"==typeof n.options.sendMsg){var t=(new Date).getTime();0!==d&&t-d<1e3?n.options.sendMsg.call(null,n.errorTip):(n.options.sendMsg.call(null,void 0,L(u.val())),u.val(""),l.text(0),r.removeClass("polyv-typing"),d=t)}}),s.on("click",function(){if(void 0!==n.options.sendFlower){var t=(new Date).getTime();1e3<t-m&&(n.options.sendFlower.call(null),m=t)}}),a.on("click",function(){if(void 0!==n.options.like){var t=(new Date).getTime();1e3<t-b&&(n.options.like.call(null),b=t)}}),h.on("change",function(){void 0!==n.options.onlyTeacher&&n.options.onlyTeacher.call(null)}),c.on(g,function(){return n.$el.toggleClass("polyv-show-more"),!1}),f.on("click",".polyv-input-more-icon",function(t){var e=T()(t.target);return"onlyhost"===e.data("type")&&void 0!==n.options.onlyTeacher&&(y=!y,e.toggleClass("polyv-all-users"),e.next("span").html(y?n.langData.checkAll:n.langData.onlyHost),n.options.onlyTeacher.call(null)),!1});var w=!1;this.$el.find(".polyv-input-only-chat").on("click",function(t){T()(t.target).toggleClass("polyv-input-only-chat-enabled"),w=!w,"function"==typeof n.options.onlyChat&&n.options.onlyChat.call(null,w)});var x=!1;this.$el.find(".polyv-pc-only-teacher").on("click",function(t){T()(t.target).toggleClass("polyv-pc-only-teacher-enabled"),x=!x,void 0!==n.options.onlyTeacher&&n.options.onlyTeacher.call(null)})}}]),H),W="https://livestatic.polyv.net/assets/images/em/",z=[{"url":W+"1.png","title":"微笑","position":"0px 0px"},{"url":W+"2.png","title":"撇嘴","position":"-48px 0px"},{"url":W+"3.png","title":"色","position":"-96px 0px"},{"url":W+"4.png","title":"发呆","position":"-144px 0px"},{"url":W+"5.png","title":"得意","position":"-192px 0px"},{"url":W+"6.png","title":"流泪","position":"-240px 0px"},{"url":W+"7.png","title":"害羞","position":"-288px 0px"},{"url":W+"8.png","title":"闭嘴","position":"-336px 0px"},{"url":W+"9.png","title":"睡","position":"-384px 0px"},{"url":W+"10.png","title":"大哭","position":"-432px 0px"},{"url":W+"11.png","title":"尴尬","position":"-480px 0px"},{"url":W+"12.png","title":"发怒","position":"-528px 0px"},{"url":W+"13.png","title":"调皮","position":"-576px 0px"},{"url":W+"14.png","title":"呲牙","position":"-624px 0px"},{"url":W+"15.png","title":"惊讶","position":"-672px 0px"},{"url":W+"16.png","title":"难过","position":"-720px 0px"},{"url":W+"17.png","title":"酷","position":"-768px 0px"},{"url":W+"18.png","title":"冷汗","position":"-816px 0px"},{"url":W+"19.png","title":"抓狂","position":"-864px 0px"},{"url":W+"20.png","title":"吐","position":"-912px 0px"},{"url":W+"21.png","title":"偷笑","position":"-960px 0px"},{"url":W+"22.png","title":"可爱","position":"-1008px 0px"},{"url":W+"23.png","title":"白眼","position":"-1056px 0px"},{"url":W+"24.png","title":"傲慢","position":"-1104px 0px"},{"url":W+"25.png","title":"饥饿","position":"-1152px 0px"},{"url":W+"26.png","title":"困","position":"-1200px 0px"},{"url":W+"27.png","title":"惊恐","position":"-1248px 0px"},{"url":W+"28.png","title":"流汗","position":"-1296px 0px"},{"url":W+"29.png","title":"憨笑","position":"-1344px 0px"},{"url":W+"30.png","title":"大兵","position":"-1392px 0px"},{"url":W+"31.png","title":"奋斗","position":"-1440px 0px"},{"url":W+"32.png","title":"咒骂","position":"-1488px 0px"},{"url":W+"33.png","title":"疑问","position":"-1536px 0px"},{"url":W+"34.png","title":"嘘","position":"-1584px 0px"},{"url":W+"35.png","title":"晕","position":"-1632px 0px"},{"url":W+"36.png","title":"折磨","position":"-1680px 0px"},{"url":W+"37.png","title":"衰","position":"-1728px 0px"},{"url":W+"38.png","title":"骷髅","position":"-1776px 0px"},{"url":W+"39.png","title":"敲打","position":"-1824px 0px"},{"url":W+"40.png","title":"再见","position":"-1872px 0px"},{"url":W+"41.png","title":"擦汗","position":"-1920px 0px"},{"url":W+"42.png","title":"抠鼻","position":"-1968px 0px"},{"url":W+"43.png","title":"鼓掌","position":"-2016px 0px"},{"url":W+"44.png","title":"糗大了","position":"-2064px 0px"},{"url":W+"45.png","title":"坏笑","position":"-2112px 0px"},{"url":W+"46.png","title":"左哼哼","position":"-2160px 0px"},{"url":W+"47.png","title":"右哼哼","position":"-2208px 0px"},{"url":W+"48.png","title":"哈欠","position":"-2256px 0px"},{"url":W+"49.png","title":"鄙视","position":"-2304px 0px"},{"url":W+"50.png","title":"委屈","position":"-2352px 0px"},{"url":W+"51.png","title":"快哭了","position":"-2400px 0px"},{"url":W+"52.png","title":"阴险","position":"-2448px 0px"},{"url":W+"53.png","title":"亲亲","position":"-2496px 0px"},{"url":W+"54.png","title":"吓","position":"-2544px 0px"},{"url":W+"55.png","title":"可怜","position":"-2592px 0px"},{"url":W+"56.png","title":"菜刀","position":"-2640px 0px"},{"url":W+"57.png","title":"西瓜","position":"-2688px 0px"},{"url":W+"58.png","title":"啤酒","position":"-2736px 0px"},{"url":W+"59.png","title":"篮球","position":"-2784px 0px"},{"url":W+"60.png","title":"乒乓","position":"-2832px 0px"},{"url":W+"61.png","title":"咖啡","position":"-2880px 0px"},{"url":W+"62.png","title":"饭","position":"-2928px 0px"},{"url":W+"63.png","title":"猪头","position":"-2976px 0px"},{"url":W+"64.png","title":"玫瑰","position":"-3024px 0px"},{"url":W+"65.png","title":"凋谢","position":"-3072px 0px"},{"url":W+"66.png","title":"示爱","position":"-3120px 0px"},{"url":W+"67.png","title":"爱心","position":"-3168px 0px"},{"url":W+"68.png","title":"心碎","position":"-3216px 0px"},{"url":W+"69.png","title":"蛋糕","position":"-3264px 0px"},{"url":W+"70.png","title":"闪电","position":"-3312px 0px"},{"url":W+"71.png","title":"炸弹","position":"-3360px 0px"},{"url":W+"72.png","title":"刀","position":"-3408px 0px"},{"url":W+"73.png","title":"足球","position":"-3456px 0px"},{"url":W+"74.png","title":"瓢虫","position":"-3504px 0px"},{"url":W+"75.png","title":"便便","position":"-3552px 0px"},{"url":W+"76.png","title":"月亮","position":"-3600px 0px"},{"url":W+"77.png","title":"太阳","position":"-3648px 0px"},{"url":W+"78.png","title":"礼物","position":"-3696px 0px"},{"url":W+"79.png","title":"拥抱","position":"-3744px 0px"},{"url":W+"80.png","title":"强","position":"-3792px 0px"},{"url":W+"81.png","title":"弱","position":"-3840px 0px"},{"url":W+"82.png","title":"握手","position":"-3888px 0px"},{"url":W+"83.png","title":"胜利","position":"-3936px 0px"},{"url":W+"84.png","title":"抱拳","position":"-3984px 0px"},{"url":W+"85.png","title":"勾引","position":"-4032px 0px"},{"url":W+"86.png","title":"拳头","position":"-4080px 0px"},{"url":W+"87.png","title":"差劲","position":"-4128px 0px"},{"url":W+"88.png","title":"爱你","position":"-4176px 0px"},{"url":W+"89.png","title":"NO","position":"-4224px 0px"},{"url":W+"90.png","title":"OK","position":"-4272px 0px"},{"url":W+"91.png","title":"爱情","position":"-4320px 0px"},{"url":W+"92.png","title":"飞吻","position":"-4368px 0px"},{"url":W+"93.png","title":"跳跳","position":"-4416px 0px"},{"url":W+"94.png","title":"发抖","position":"-4464px 0px"},{"url":W+"95.png","title":"怄火","position":"-4512px 0px"},{"url":W+"96.png","title":"转圈","position":"-4560px 0px"},{"url":W+"97.png","title":"磕头","position":"-4608px 0px"},{"url":W+"98.png","title":"回头","position":"-4656px 0px"},{"url":W+"99.png","title":"跳绳","position":"-4704px 0px"},{"url":W+"100.png","title":"挥手","position":"-4752px 0px"}],G={};z.forEach(function(t){G[t.title]=t});var Y=n(140),V=["manager","teacher","assistant","guest"],Q="http:"===window.location.protocol?"http:":"https:";function $(t){l()(this,$),this.options=a()({},t),this.chat=t.chat,this.isMobile=R(),this.isWechat=P(),this.clickTouch=this.isMobile?"touchend":"click",this.$container=T()(t.container),this.tabData=t.tabData,this.$chatTab=null,this.$chatList=null,this.welcomeClock=null,this.likeClock=null,this.width=this.options.width||300,this.height=this.options.height||600,this.lang=this.options.lang,this.langData=U[this.lang],this.newMsgTipTextCN=this.options.newMsgTipText,this.newMsgTipTextEn=this.options.newMsgTipTextEn,this.newMsgTipText="en"===this.lang&&this.newMsgTipTextEn?this.newMsgTipTextEn:this.newMsgTipTextCN||this.langData.clickToSeeMore,this.tdTime=(new Date).toLocaleDateString(),this.time=0,this.customChatColor={"selfBgColor":"#8bc34a","selfColor":"#fff","otherBgColor":"#fff","otherColor":"#546e7a","specialBgColor":"#fff","specialColor":"#2196f3","managerBgColor":"","managerColor":"","teacherBgColor":"","teacherColor":"","assistantBgColor":"","assistantColor":"","guestBgColor":"","guestColor":""},a()(this.customChatColor,t.customChatColor),this.createChatRoom().initDomEvent()}var X=(h()($,null,[{"key":"parseString","value":function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").trim():t}},{"key":"nl2br","value":function(t){return"string"==typeof t?t.replace(/\r?\n/g,"<br/>"):t}},{"key":"parseLink","value":function(t){return t?t.replace(/(http:\/\/|https:\/\/)([\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:\/~\+#]*[\w\-\@?^=%&\/~\+#])?)/g,'<a target="_blank" href="$1$2">$1$2</a>'):t}},{"key":"formatChatImg","value":function(t){var e=t.uploadImgUrl,n=e.indexOf("//");return-1<n&&e.substring(0,n)!==Q&&(e=Q+e.substring(n)),'<img id="'+t.id+'" class="polyv-img-chat" src="'+e+'">'}}]),h()($,[{"key":"updateLang","value":function(t){t!==this.lang&&(this.lang=t,this.langData=U[this.lang],this.chatInput.updateLang(this.lang),this.newMsgTipText="en"===this.lang&&this.newMsgTipTextEn?this.newMsgTipTextEn:this.newMsgTipTextCN||this.langData.clickToSeeMore,T()(".js-change-tip-text").text(this.newMsgTipText))}},{"key":"getColors","value":function(t){var e=0<arguments.length&&void 0!==t?t:"other",n=-1!==V.indexOf(e);return{"textColor":this.customChatColor[e+"Color"]||this.customChatColor[n?"specialColor":"otherColor"],"textBgColor":this.customChatColor[e+"BgColor"]||this.customChatColor[n?"specialBgColor":"otherBgColor"]}}},{"key":"other","value":function(t){var e=t.user.userType,n=t.imgchat?$.formatChatImg(t.content):D(t.content),r="teacher"===e?"polyv-teacher-msg":"";"teacher"===e||"manager"===e||"assistant"===e?(r+=" polyv-special-user",-1===n.indexOf("plv-emotion-img")&&-1===n.indexOf("polyv-img-chat")&&(n=$.parseLink(n))):r="polyv-other-msg";var i="";t.user.actor?i=t.user.actor:"teacher"===e?i=this.langData.teacher:"manager"===e?i=this.langData.manager:"assistant"===e&&(i=this.langData.assistant);var o=i?'<span class="polyv-user-actor" title="'+i+'">'+i+"</span>":"",s=this.getColors(e);return n=$.nl2br(n),('<li class="'+r+'" data-chatid="'+t.id+'" data-type="'+e+'" data-relative-other="'+t.relativeOther+'">\n              <div class="polyv-user-logo">\n                <img src="'+t.user.pic+'">\n              </div>\n              <div class="polyv-msg-main">\n                <div class="polyv-user-info">\n                  '+o+'\n                  <span class="polyv-user-name" title="'+t.user.nick+'">'+t.user.nick+'</span>\n                </div>\n                <div class="polyv-msg-content" style="background-color: '+s.textBgColor+';">\n                  <p class="polyv-msg-text" rel="'+t.user.nick+'" style="color: '+s.textColor+';">'+n+"</p>\n                </div>\n              </div>\n            </li>").trim()}},{"key":"own","value":function(t){var e=this.getColors("self"),n=t.content,r=t.imgchat?$.formatChatImg(n):D(n);return r=$.nl2br(r),('<li class="polyv-self-msg clearfix" data-chatid="'+t.id+'" data-type="self" data-relative-other="'+t.relativeOther+'" data-content="'+n+'">\n              <div class="polyv-msg-main">\n                <div class="polyv-msg-content" style="background-color: '+e.textBgColor+'">\n                  <p class="polyv-msg-text" style="color: '+e.textColor+';" rel="'+t.user.nick+'">'+r+"</p>\n                </div>\n              </div>\n            </li>").trim()}},{"key":"flowers","value":function(t){return('<li class="polyv-msg" data-relative-other="Y">\n              '+t.nick+"  "+this.langData.presentFlower+'<img src="'+Y+'">\n            </li>').trim()}},{"key":"like","value":function(t){var e=this;if(t.length){var n="";n=2<t.length?'<span class="user-info">'+t[0]+"、"+t[1]+"、"+t[2]+"</span>"+this.langData.etc+t.length+this.langData.man+this.langData.likeText:1<t.length?'<span class="user-info">'+t[0]+"、"+t[1]+"</span>"+this.langData.twoMan+this.langData.likeText:'<span class="user-info">'+t[0]+"</span>"+this.langData.likeText,this.likeClock&&(clearTimeout(this.likeClock),this.likeClock=null),this.$likeMsg.html(n).show(function(){e.likeClock=setTimeout(function(){e.$likeMsg.html("").hide(),clearTimeout(e.likeClock),e.likeClock=null},3e3)})}}},{"key":"welcome","value":function(t){var e=this;if(t.length){var n="";n=2<t.length?this.langData.welcome+'<span class="user-info">'+t[0].nick+"、"+t[1].nick+"、"+t[2].nick+"</span>"+this.langData.etc+t.length+this.langData.peopleJoin:1<t.length?this.langData.welcome+'<span class="user-info">'+t[0].nick+"、"+t[1].nick+"</span>"+this.langData.etc+"2"+this.langData.peopleJoin:this.langData.welcome+'<span class="user-info">'+t[0].nick+"</span>"+this.langData.join,this.welcomeClock&&(clearTimeout(this.welcomeClock),this.welcomeClock=null),this.$welcomeMsg.html(n).show(function(){e.welcomeClock=setTimeout(function(){e.$welcomeMsg.html("").hide(),clearTimeout(e.welcomeClock),e.welcomeClock=null},3e3)})}}},{"key":"custom","value":function(t){var e=t.image?'<img src="'+t.image+'">':"";return('<li class="polyv-msg" data-chatid="'+t.id+'" data-relative-other="Y">\n              '+t.content+e+"\n            </li>").trim()}},{"key":"reward","value":function(t){var e="";return e=t.content.gimg?t.content.unick+" "+this.langData.presented+" "+t.content.rewardContent+'<img src="'+t.content.gimg+'">':'<img src="https://livestatic.videocc.net/assets/wimages/icon-redpaper.png" style="border-radius: 0;">'+t.content.unick+" "+this.langData.rewarded+" "+t.content.rewardContent+this.langData.yuan,('<li class="polyv-msg" data-chatid="'+t.id+'" data-relative-other="Y">'+e+"</li>").trim()}},{"key":"redpaper","value":function(t){}},{"key":"system","value":function(t,e,n){return('<li class="polyv-msg '+(1<arguments.length&&void 0!==e?e:"")+'" data-relative-other="'+(2<arguments.length&&void 0!==n?n:"N")+'">'+t+"</li>").trim()}},{"key":"gonggao","value":function(t){return"<div>\n\n            </div>".trim()}},{"key":"bulletin","value":function(t){this.$bulletinMsg.html(('<div class="bulletin-hd">\n        <div class="">'+this.langData.notice+'</div>\n        <div class="bulletin-hd-right">\n          <span class="dropdown dropup" data-type="drop"></span>\n          <span class="close" data-type="close">&times</span>\n        </div>\n      </div>\n      <div class="bulletin-bd">\n        <p>'+$.parseString(t.content)+"</p>\n      </div>").trim()).show()}},{"key":"isBottom","value":function(){var t=T()(".tab-chat-content");return t[0].scrollHeight-t.height()-50<t.scrollTop()}},{"key":"scrollTop","value":function(){var t=T()(".tab-chat-content"),e=t[0].scrollHeight;t.scrollTop(e)}},{"key":"emptyMessageList","value":function(){this.$chatList.html(""),this.$lookMore.hide()}},{"key":"createMessageTime","value":function(t,e,n,r){var i=new Date(t).toLocaleDateString();return this.tdTime!=i?this.system(e,n,r):this.system(e.substring(11),n,r)}},{"key":"createMessageList","value":function(t,e){var r=this;if(!t.length)return this;var i=[],o=!1,s=!1,a=["reward","customerMessage","redpaper","get_redpaper","customMessage"];t.forEach(function(t){var e=void 0,n="";t.relativeOther="N",-1!==a.indexOf(t.msgSource)&&(t.relativeOther="Y"),t.system?e=r.system(t.content):t.reward?e=r.reward(t):t.custom?e=r.custom(t):t.redpaper?e=r.redpaper(t):t.flowers?e=r.flowers(t):t.currentUser?(e=r.own(t),s=!0):(e=r.other(t),o=!0),(s||o)&&(12e4<t.time-r.time&&(n=r.createMessageTime(t.time,t.formatTime,t.specialUser||t.currentUser?"":"polyv-other-msg-time",t.relativeOther)),r.time=t.time),i.push(n,e)});var n=this.isBottom();return!0===e?this.$chatList.prepend(i.join("")):this.$chatList.append(i.join("")),n?setTimeout(function(){r.scrollTop()},200):o&&!e?this.$newMsg.show():s&&!e&&this.scrollTop(),this}},{"key":"createUserList","value":function(t){var i=this,o=[],e=!1,n=t;if(t instanceof Array||(e=!0,t=[t]),t.forEach(function(t){var e=t.userType,n="";t.actor?n=t.actor:"teacher"===e?n=i.langData.teacher:"manager"===e?n=i.langData.manager:"assistant"===e&&(n=i.langData.assistant);var r=n?'<span class="polyv-user-actor">'+n+"</span>":"";o.push('<li id="'+t.userId+'" class="" data-socketid="'+t.uid+'" data-type="'+e+'">\n          <div class="polyv-user-logo"><img src="'+t.pic+'"></div>\n          <div class="polyv-user-info">'+r+' <span class="polyv-user-name">'+t.nick+"</span></div>\n        </li>")}),e){var r=n.userType,s=T()(o.join("")),a=this.$userList.children("li").eq(0),c=void 0,u=void 0,l=void 0;"manager"===r?s.insertAfter(a):"teacher"===r?((c=T()('li[data-type="manager"]')).length&&(a=c),s.insertAfter(a)):"assistant"===r?(c=T()('li[data-type="manager"]').last(),u=T()('li[data-type="teacher"]').last(),(l=T()('li[data-type="assistant"]').last()).length?a=l:u.length?a=u:c.length&&(a=c),s.insertAfter(a)):this.$userList.append(o.join(""))}else this.$userList.append(o.join(""));return this}},{"key":"setUserCount","value":function(t){return this.$userCount.text(t),this}},{"key":"createChatRoom","value":function(){var t=this.isMobile?"mobile-wrap":"";return this.$container.append(('\n      <div class="polyv-chat-room '+t+'">\n        <div class="polyv-cr-head"><ul class="polyv-cr-navbar clearfix" data-target=".polyv-cr-body"></ul></div>\n        <div class="polyv-cr-body js-polyv-cr-body"></div>\n      </div>').trim()),T()(".polyv-chat-room").css({"width":this.width,"height":this.height}),this.createTabs().createInput(),this}},{"key":"createTabs","value":function(){var e=this;return new _({"el":".polyv-cr-navbar","activeClass":"polyv-crn-active","data":this.tabData}),this.tabData.forEach(function(t){"chat"===t.type&&(e.$chatTab=T()("#tab-chat"),e.$chatTab.append(('<div class="tab-chat-content">\n                                <div class="polyv-more-msg">'+e.langData.viewMore+'</div>\n                                <ul class="polyv-chat-list" id="js-polyv-chat-list"></ul>\n                              </div>\n                              <div class="polyv-new-msg js-change-tip-text">'+e.newMsgTipText+'</div>\n                              <div class="other-message">\n                                <div class="welcome-msg"></div>\n                                <div class="like-msg"></div>\n                                <div class="bulletin-msg"></div>\n                              </div>\n                              <div class="polyv-unconnect-tip" id="js-polyv-unconnect-tip">\n                                <div class="polyv-unconnect-tip-main">\n                                  <div class="polyv-unconnect-tip-head">'+e.langData.prompt+'</div>\n                                  <div class="polyv-unconnect-tip-body">\n                                    <p>'+e.langData.chatroomConnectionFailed1+"</p>\n                                    <p>"+e.langData.chatroomConnectionFailed2+"</p>\n                                    <p>"+e.langData.chatroomConnectionFailed3+"</p>\n                                  </div>\n                                </div>\n                              </div>").trim()),e.$chatList=T()("#js-polyv-chat-list"),e.$newMsg=T()(".polyv-new-msg"),e.$welcomeMsg=T()(".welcome-msg"),e.$likeMsg=T()(".like-msg"),e.$bulletinMsg=T()(".bulletin-msg"),e.$lookMore=T()(".polyv-more-msg"),e.$unconnectTip=T()("#js-polyv-unconnect-tip")),"user-list"===t.type&&(e.$userListTab=T()("#tab-user-list"),e.$userListTab.append('<ul class="polyv-user-list"></ul>'),e.$userList=T()(".polyv-user-list"),e.$userCount=T()(".polyv-user-count"))}),this}},{"key":"createInput","value":function(){var n=this;return this.chatInput=new q({"el":"#tab-chat","chat":this.chat,"lang":this.lang,"enableFlower":this.options.enableFlower,"enableLike":this.options.enableLike,"enableOnlyTeacher":this.options.enableOnlyTeacher,"enableOnlyChat":this.options.enableOnlyChat,"hasMorePanel":!0,"sendMsg":function(t,e){t?(n.$chatList.append(n.system(t)),n.scrollTop()):n.options.sendMsg.call(null,e)},"sendFlower":function(){n.options.sendFlower.call(null)},"like":function(){n.options.like.call(null)},"onlyTeacher":function(){n.$chatList.toggleClass("hide-other-msg"),n.scrollTop()},"onlyChat":function(){n.$chatList.toggleClass("hide-relative-other-msg"),n.scrollTop()}}),this}},{"key":"handleUnconnectTip","value":function(t){0<arguments.length&&void 0!==t&&t?this.$unconnectTip.show():this.$unconnectTip.hide()}},{"key":"showLookMore","value":function(t){!0===t?this.$lookMore.show():this.$lookMore.hide()}},{"key":"showBigImg","value":function(t){if(this.isMobile&&this.isWechat)WeixinJSBridge.invoke("imagePreview",{"current":t,"urls":this.bigImg});else{T()(".polyv-img-fullpage").remove(),T()("#wrap").append('<div class="polyv-img-fullpage"><img src="'+t+'"/></div>');var e=!1;T()(".polyv-img-fullpage").on("touchstart",function(){e=!1}).on("touchmove",function(){e=!0}).on(this.clickTouch,function(t){if(!e)return T()(t.currentTarget).remove(),!1})}}},{"key":"initDomEvent","value":function(){var t=this,e=T()(".tab-chat-content"),n=0;e.on("scroll",function(){t.isBottom()&&t.$newMsg.hide()}),this.$newMsg.on("click",function(){t.scrollTop(),t.$newMsg.hide()}),this.$lookMore.on("click",function(){void 0!==t.options.lookMore&&(n+=10,t.options.lookMore.call(null,n))})}}]),$);function K(t){l()(this,K),this.container=T()(t.container),this.chat=t.chat,this.langData=t.langData,this.init()}var J=(h()(K,[{"key":"init","value":function(){var n=this,t=this.chat,e=t.events;t.on(e.BULLETIN,function(t,e){n.create(e)}),t.on(e.REMOVE_BULLETIN,function(t,e){n.remove()})}},{"key":"create","value":function(t){this.container.html(('<div class="bulletin-hd">\n        <div class="">'+this.langData.notice+'</div>\n        <div class="bulletin-hd-right">\n          <span class="dropdown" data-type="drop"></span>\n          <span class="close" data-type="close">&times</span>\n        </div>\n      </div>\n      <div class="bulletin-bd">\n        <p>'+t.content+"</p>\n      </div>").trim()).show(),this.bindDomEvents()}},{"key":"remove","value":function(){this.container.hide().empty()}},{"key":"bindDomEvents","value":function(){var r=this;T()(".bulletin-hd-right").on("click","span",function(t){var e=T()(t.target),n=e.data("type");"drop"===n?(e.toggleClass("dropup"),e.hasClass("dropup")?T()(".bulletin-bd").slideUp():T()(".bulletin-bd").slideDown()):"close"===n&&r.remove()})}}]),K),Z=n(85),tt=n.n(Z),et=n(86),nt=n.n(et);function rt(t){l()(this,rt),this.options=a()({},t),this.container=T()(t.container),this.chat=t.chat,this.roomId=t.roomId,this.userId=t.userId,this.lang=t.lang,this.langData=U[this.lang];var e=["chat","ask"];t.showSetNickTab instanceof Array&&(e=e.concat(t.showSetNickTab)),this.showSetNickTab=nt()(new tt.a(e)),this.isMobile=R(),this.create().bindDomEvent().initRoomEvents()}var it=(h()(rt,null,[{"key":"specialCharacters","value":function(t){var e=new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]");return"string"==typeof t?t.match(e):null}}]),h()(rt,[{"key":"updateLang","value":function(t){this.lang=t,this.langData=U[this.lang]}},{"key":"create","value":function(){var t='<div class="polyv-set-nickname">\n                  <div>\n                    <input data-lang="setNickname" type="text" id="polyv-nickname" placeholder="'+this.langData.setNickname+'">\n                  </div>\n                  <div>\n                    <button type="button" class="polyv-btn" data-type="cancel">取消</button>\n                    <button type="button" class="polyv-btn polyv-btn-info" data-type="ensure">确认</button>\n                    <p id="polyv-nickname-tip" class="polyv-nickname-tip"></p>\n                  </div>\n                </div>';return this.isMobile&&(t='<div class="polyv-set-nickname">\n                <div>\n                  <input data-lang="setNickname" type="text" id="polyv-nickname" placeholder="'+this.langData.setNickname+'">\n                  <button type="button" class="polyv-btn" data-type="ensure">确认</button>\n                </div>\n                <p id="polyv-nickname-tip" class="polyv-nickname-tip"></p>\n              </div>'),this.container.append(t.trim()),this.$tipContainer=T()("#polyv-nickname-tip"),this}},{"key":"bindDomEvent","value":function(){var i=this,t=T()("html"),o=T()(".polyv-set-nickname"),s=T()("#polyv-nickname");return t.on("click",function(){o.removeClass("show"),s.val("")}),o.on("click",function(t){return-1!==i.showSetNickTab.indexOf(T()(".js-polyv-crn-active").data("type"))&&o.addClass("show"),!1}).on("click","button",function(t){var e=T()(t.currentTarget);if("cancel"===e.data("type"))o.removeClass("show"),s.val("");else{e.attr("disabled",!0);var n=s.val().replace(/\s/g,"");if(!n)return i.showTip(i.langData.nicknamesCannotBeEmpty,"error"),s.focus(),e.removeAttr("disabled"),!1;if(8<n.length)return i.showTip(i.langData.maxCharacters,"error"),s.focus(),e.removeAttr("disabled"),!1;var r=rt.specialCharacters(n);if(r)return i.showTip(i.langData.nicknamesSpecail+"："+r[0],"error"),s.focus(),e.removeAttr("disabled"),!1;i.chat.setNickname(n)}return!1}),this}},{"key":"initRoomEvents","value":function(){var a=this,t=this.chat,e=this.chat.events,c=T()("#polyv-nickname"),u=T()(".polyv-set-nickname");return t.on(e.SET_NICK,function(t,e){if(T()('.polyv-set-nickname [data-type="ensure"]').removeAttr("disabled"),"error"===e.status)return a.showTip(e.message,"error"),void c.focus();var n=e.userId,r=e.nick,i=r;if(n===a.userId){var o=new Date;o.setTime(o.getTime()+108e5),M(a.roomId+"_nickname",encodeURI(r),o),a.showTip(a.langData.successfullySet,"success"),i+="（我）";var s=setTimeout(function(){u.remove(),c.val(""),clearTimeout(s),s=null},1e3)}T()("#"+n).find(".polyv-user-name").text(i)}),this}},{"key":"showTip","value":function(t,e){var n=this,r=1<arguments.length&&void 0!==e?e:"default";clearTimeout(this.tipClock),this.tipClock=setTimeout(function(){n.$tipContainer.fadeOut(function(){n.$tipContainer.html("")})},2e3),this.$tipContainer.addClass("polyv-nickname-tip-"+r).html(t).fadeIn()}}]),rt);function ot(t){l()(this,ot),this.container=T()(t.container),this.chat=t.chat,this.roomId=t.roomId,this.userId=t.userId,this.protocol="http:"===location.protocol?"http:":"https:",this.apiPrefix=this.chat.apiPrefix,this.lang=t.lang,this.langData=t.langData,this.teacher={"actor":this.langData.teacher,"nick":this.langData.teacher,"pic":this.protocol+"//livestatic.polyv.net/assets/images/teacher.png","userType":"teacher"},this.eventListent(),this.init()}var st=(h()(ot,[{"key":"init","value":function(){var e=this;return T.a.ajax({"url":this.protocol+"//"+this.apiPrefix.chatApi+"/front/getQuestionContent","type":"get","dataType":"json","data":{"roomId":this.roomId}}).done(function(t){(t=t.filter(function(t){return t.s_userId===e.userId||t.user.userId===e.userId})).length||t.push({"content":e.langData.anyQuestion,"s_userId":e.userId,"time":(new Date).getTime(),"user":e.teacher}),e.create(t).createInput()}),this}},{"key":"eventListent","value":function(){var n=this;if(this.chat){var t=this.chat,e=t.events;t.on(e.TEACHER_INFO,function(t,e){n.teacher=e.data}),t.on(e.S_QUESTION,function(t,e){e.user.userId===n.userId&&(n.$askList.append(n.createQuestion(e)),n.scrollTop())}),t.on(e.T_ANSWER,function(t,e){e.s_userId===n.userId&&(n.$askList.append(n.createAnswer(e)),n.scrollTop())})}}},{"key":"create","value":function(t){var e=('\n      <div class="tab-ask-content">\n        <ul class="polyv-chat-list" id="js-plv-ask-list">'+this.createList(t)+"</ul>\n      </div>\n      <div></div>\n    ").trim();return this.container.append(e),this.$askList=T()("#js-plv-ask-list"),this}},{"key":"createList","value":function(t){var n=this,r=[];return t.forEach(function(t){var e="";e=t.s_userId?n.createAnswer(t):n.createQuestion(t),r.push(e)}),r.join("")}},{"key":"createQuestion","value":function(t){return('<li class="polyv-self-msg clearfix" data-chatid="'+t.id+'">\n                <div class="polyv-msg-main">\n                  <div class="polyv-msg-content">\n                    <p class="polyv-msg-text" rel="'+t.user.nick+'">'+D(t.content)+"</p>\n                  </div>\n                </div>\n              </li>").trim()}},{"key":"createAnswer","value":function(t){var e=t.user.userType,n=D(t.content);"teacher"!==e&&"manager"!==e&&"assistant"!==e||-1===n.indexOf("plv-emotion-img")&&-1===n.indexOf("polyv-img-chat")&&(n=B(n));var r="";t.user.actor?r=t.user.actor:"teacher"===e?r=this.langData.teacher:"manager"===e?r=this.langData.manager:"assistant"===e&&(r=this.langData.assistant);var i=r?'<span class="polyv-user-actor" title="'+r+'">'+r+"</span>":"";return('<li class="polyv-special-user" data-chatid="'+t.id+'">\n              <div class="polyv-user-logo">\n                <img src="'+t.user.pic+'">\n              </div>\n              <div class="polyv-msg-main">\n                <div class="polyv-user-info">\n                  <span class="polyv-user-name" title="'+t.user.nick+'">'+t.user.nick+"</span>\n                  "+i+'\n                </div>\n                <div class="polyv-msg-content">\n                  <p class="polyv-msg-text" rel="'+t.user.nick+'">'+n+"</p>\n                </div>\n              </div>\n            </li>").trim()}},{"key":"system","value":function(t){return('<li class="polyv-msg">'+t+"</li>").trim()}},{"key":"scrollTop","value":function(){var t=T()(".tab-ask-content"),e=t[0].scrollHeight;t.scrollTop(e)}},{"key":"createInput","value":function(){var n=this;this.input=new q({"el":"#tab-ask","chat":this.chat,"enableFlower":!1,"enableLike":!1,"enableOnlyTeacher":!1,"lang":this.lang,"langData":this.langData,"errorTip":this.langData.askFast,"sendMsg":function(t,e){t?(n.$askList.append(n.system(t)),n.scrollTop()):n.chat.sendAskContent(e)}})}}]),ot);n.d(e,"PolyvChatRoom",function(){return at}),n(100);var at=(h()(ct,[{"key":"setup","value":function(){var e=this;if(this.chat.setUp(),this.options.container){var t=this.options;this.handleTabData(t.tabData).initRoomEvents(t).updateUser().updateMessage().showWelcomeMessage().showLikesMessage(),this.chatRoomTemplate=new X({"chat":this.chat,"container":t.container,"width":t.width,"height":t.height,"lang":this.lang,"tabData":this.tabData,"enableFlower":this.enableFlower,"enableLike":this.enableLike,"enableOnlyTeacher":this.enableOnlyTeacher,"newMsgTipText":this.newMsgTipText,"newMsgTipTextEn":this.newMsgTipTextEn,"enableChatRoomControl":this.enableChatRoomControl,"globalSwitchEnable":this.globalSwitchEnable,"customChatColor":t.customChatColor,"enableOnlyChat":this.enableOnlyChat,"sendMsg":function(t){e.sendMsg(t)},"sendFlower":function(){e.sendFlower()},"like":function(){e.like()},"lookMore":function(t){e.chat.getHistoryMessage(t,t+10).then(function(t){e.chatRoomTemplate.createMessageList(e.chat.parseData(t.slice(0,10).reverse()),!0).showLookMore(10<t.length)})}}),this.chat.getHistoryMessage().then(function(t){e.chatRoomTemplate.createMessageList(e.chat.parseData(t.slice(0,10).reverse())).showLookMore(10<t.length)}),this.enableAsk&&(this.askTab=new st({"container":"#tab-ask","chat":this.chat,"roomId":this.roomId,"userId":this.userId,"apiPrefix":t.apiPrefix,"lang":this.lang,"langData":t.langData})),this.enableBulletin&&(this.bulletin=new J({"container":".bulletin-msg","chat":this.chat,"lang":this.lang,"langData":t.langData})),this.enableSetNickname&&"游客"===this.user.nick&&new it({"container":".js-polyv-cr-body","chat":this.chat,"roomId":this.roomId,"userId":this.userId,"showSetNickTab":t.showSetNickTab,"lang":this.lang})}}},{"key":"handleTabData","value":function(t){var e=this.options.langData,n=e.chat,r=e.userList,i=e.ask;if(!(t instanceof Array))return this.tabData=[{"name":n,"type":"chat"}],this.showUserList&&this.tabData.push({"name":r,"type":"user-list"}),this.enableAsk&&this.tabData.push({"name":i,"type":"ask"}),this;var o=-1,s=-1,a=-1;return this.tabData=JSON.parse(c()(t)),t.forEach(function(t,e){"chat"===t.type&&(o=e),"user-list"===t.type&&(s=e),"ask"===t.type&&(a=e)}),-1===o&&this.tabData.push({"name":n,"type":"chat"}),this.showUserList?-1===s&&this.tabData.push({"name":r,"type":"user-list"}):-1!==s&&this.tabData.splice(s,1),this.enableAsk?-1===a&&this.tabData.push({"name":i,"type":"ask"}):-1!==a&&this.tabData.splice(a,1),this}},{"key":"initRoomEvents","value":function(){var i=this,t=this.chat,e=this.chat.events;return t.on(e.LOGIN,function(t,e){i.enableWelcome&&i.welcomeMessage.push(e.user);var n=e.user,r=T()(".polyv-user-list").children('li[id="'+n.userId+'"]');r.length?r.attr("data-socketid",n.uid):n.userId==i.userId?(i.user.ip=n.clientIp,i.user.socketId=n.uid,i.showUserList&&i.chat.getUserList().then(function(t){i.userCount=t.count,i.chatRoomTemplate.createUserList(i.chat.sortUsers(t.userlist)).setUserCount(i.userCount)})):i.showUserList&&i.userList.push(n)}),t.on(e.LOGOUT,function(t,e){i.showUserList&&(T()('li[data-socketid="'+e.uid+'"]').remove(),i.chatRoomTemplate.setUserCount(--i.userCount))}),t.on(e.SPEAK,function(t,e){T()("#js-polyv-chat-list").children('[data-chatid="'+e.id+'"]').length||i.chatList.push(e)}),t.on(e.SELF_SPEAK,function(t,e){var n=T()('.polyv-self-msg[data-content="'+e.values[0]+'"]');n.length&&n.attr("data-chatid",e.id)}),t.on(e.SPEAK_ERROR,function(t,e){i.chatList.unshift({"system":!0,"content":e.message+"："+e.value})}),t.on(e.CHAT_IMG,function(t,e){i.chatList.push({"msgSource":"chatImg","id":e.id,"time":e.time,"content":e.values[0],"user":e.user})}),t.on(e.FLOWERS,function(t,e){i.chatList.push({"flowers":!0,"nick":e.nick,"user":{},"time":(new Date).getTime,"content":""})}),t.on(e.LIKES,function(t,e){(i.enableLike||i.showLikeMsg)&&i.likesMessage.push(e.nick)}),t.on(e.REWARD,function(t,e){i.chatList.push({"user":{"uid":"1"},"time":(new Date).getTime,"content":e.content})}),t.on(e.GONGGAO,function(t,e){i.enableGonggao&&i.chatRoomTemplate.gonggao(e)}),t.on(e.REMOVE_CONTENT,function(t,e){T()('[data-chatid="'+e.id+'"]').remove()}),t.on(e.REMOVE_HISTORY,function(){i.chatRoomTemplate.emptyMessageList()}),t.on(e.CLOSE_ROOM,function(t,e){i.chatList.unshift({"system":!0,"content":i.options.langData.chatroomClosed})}),t.on(e.OPEN_ROOM,function(t,e){i.chatList.unshift({"system":!0,"content":i.options.langData.chatroomReopens})}),t.on(e.CUSTOMER_MESSAGE,function(t,e){i.chatList.push(e)}),t.on(e.OVERTIMECONNECT,function(t,e){i.handleUnconnectTip(!0)}),t.on(e.RECONNECT_ATTEMPT,function(t,e){i.handleUnconnectTip(!0)}),t.on(e.RECONNECT,function(t,e){i.handleUnconnectTip(!1)}),t.on(e.CONNECT,function(t,e){i.handleUnconnectTip(!1)}),t.on(e.CENSOR,function(t,e){var n=T()("#js-polyv-chat-list").children('[data-type="self"]:last');n.length&&n.attr("data-chatid",e.id)}),this}},{"key":"handleUnconnectTip","value":function(t){var e=0<arguments.length&&void 0!==t&&t;this.chatRoomTemplate.handleUnconnectTip(e)}},{"key":"updateMessage","value":function(){var t=this;return setInterval(function(){t.chatRoomTemplate.createMessageList(t.chat.parseData(t.chatList.splice(0,20)))},200),this}},{"key":"updateUser","value":function(){var e=this;return this.showUserList&&setInterval(function(){var t=e.userList.shift();!t||"teacher"===t.userType&&"chatroom"===t.userSource||(e.userCount<e.maxUserCount&&e.chatRoomTemplate.createUserList(t),e.chatRoomTemplate.setUserCount(++e.userCount))},200),this}},{"key":"showWelcomeMessage","value":function(){var t=this;return this.enableWelcome&&setInterval(function(){t.chatRoomTemplate.welcome(t.welcomeMessage.splice(0,20))},200),this}},{"key":"showLikesMessage","value":function(){var t=this;return(this.enableLike||this.showLikeMsg)&&setInterval(function(){t.chatRoomTemplate.like(t.likesMessage.splice(0,20))},200),this}},{"key":"sendMsg","value":function(t){if(t)return this.chat.send(t),this.chatRoomTemplate.createMessageList(this.chat.parseData([{"user":this.user,"time":(new Date).getTime(),"content":t}])),this}},{"key":"sendFlower","value":function(){this.chat.sendFlower()}},{"key":"like","value":function(){this.chat.like()}},{"key":"updateSessionId","value":function(t){this.options.sessionId=t,this.chat.updateSessionId(t)}},{"key":"updateLang","value":function(t){var i=this;t!==this.lang&&(this.lang=t,this.langData=U[this.lang],this.chatRoomTemplate.updateLang(this.lang),T()("[data-lang]").each(function(t,e){var n=T()(e),r=n.data("lang");"participateInChat"===r?n.attr("placeholder",i.langData[r]):n.text(i.langData[r])}))}}]),ct);function ct(t){if(l()(this,ct),!t.token)return console.error("缺少参数token"),!1;this.options=a()({},t),this.options.nick=this.nick=t.nick||decodeURI(j(t.roomId+"_nickname"))||"游客",this.options.userType=this.userType=t.userType||"student",this.options.userId=this.userId=t.userId.toString(),this.options.roomId=this.roomId=t.roomId.toString(),this.user={"nick":this.nick,"pic":t.pic,"userId":this.userId,"roomId":this.roomId,"userType":this.userType,"actor":t.actor,"accountId":t.accountId},this.isMobile=R(),this.showUserList=!this.isMobile&&!1!==t.showUserList||this.isMobile&&!0===t.showUserList,this.enableWelcome=!1!==t.enableWelcome,this.enableFlower=!1!==t.enableFlower,this.enableLike=!1!==t.enableLike,this.showLikeMsg=!0===t.showLikeMsg,this.enableOnlyTeacher=!1!==t.enableOnlyTeacher,this.enableBulletin=!1!==t.enableBulletin,this.enableGonggao=!1!==t.enableGonggao,this.enableSetNickname=!0===t.enableSetNickname,this.enableChatRoomControl=!0===t.enableChatRoomControl,this.enableOnlyChat=!0===t.enableOnlyChat,this.enableAsk=!1!==t.enableAsk&&("student"===this.userType||"slice"===this.userType),this.globalSwitchEnable="Y"===t.globalSwitchEnable,this.newMsgTipText=t.newMsgTipText,this.newMsgTipTextEn=t.newMsgTipTextEn,this.lang="en"===t.lang?"en":"zh_CN",this.options.langData=U[this.lang],this.hasTeacher=!1,this.maxUserCount=100,this.userCount=0,this.userList=[],this.chatList=[],this.welcomeMessage=[],this.likesMessage=[],this.chat=new N(this.options),this.setup()}console.info(20200325)}],i.c=r,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{"enumerable":!0,"get":n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{"value":"Module"}),Object.defineProperty(t,"__esModule",{"value":!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{"enumerable":!0,"value":e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=159);function i(t){if(r[t])return r[t].exports;var e=r[t]={"i":t,"l":!1,"exports":{}};return n[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var n,r});