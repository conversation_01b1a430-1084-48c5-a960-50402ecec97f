/**
 * 贷款计算工具类
 * 提供等额本息和等本等息两种还款方式的计算
 */

/**
 * 还款方式枚举
 */
const RepaymentMethod = {
  EQUAL_PRINCIPAL_INTEREST: 'EQUAL_PRINCIPAL_INTEREST', // 等额本息
  EQUAL_PRINCIPAL: 'EQUAL_PRINCIPAL'                    // 等本等息
};

/**
 * 还款计划明细类
 */
class RepaymentDetail {
  constructor() {
    this.period = 0;              // 期数
    this.principal = 0;           // 本金
    this.interest = 0;            // 利息
    this.actualInterest = 0;      // 实际利息(利息-贴息)
    this.subsidy = 0;             // 贴息
    this.totalPayment = 0;        // 总还款(本金+利息-贴息)
    this.remainingPrincipal = 0;   // 剩余本金
  }
}

/**
 * 还款计划汇总类
 */
class RepaymentSummary {
  constructor() {
    this.totalPrincipal = 0;      // 总本金
    this.totalInterest = 0;       // 总利息
    this.totalActualInterest = 0; // 总实际利息
    this.totalSubsidy = 0;        // 总贴息
    this.totalPayment = 0;        // 总还款
    this.monthlyRate = 0;         // 月利率
    this.subsidyRate = 0;         // 贴息比例
    this.details = [];            // 还款明细
  }
}

/**
 * 计算还款计划
 * @param {number} principal - 贷款本金
 * @param {number} actualAnnualRate - 年化利率（百分比）
 * @param {number} periods - 贷款期数(月)
 * @param {string} method - 还款方式
 * @param {number} subsidyRate - 贴息比例（百分比，0-100）
 * @returns {RepaymentSummary} 还款计划汇总和明细
 */
function calculateRepayment(principal, actualAnnualRate, periods, method, subsidyRate, customerAnnualRate) {
  // 参数验证
  if (principal <= 0 || actualAnnualRate <= 0 || periods <= 0) {
    throw new Error('参数必须大于零');
  }

  if (subsidyRate < 0 || subsidyRate > 100) {
    throw new Error('贴息比例必须在0-100之间');
  }

  // 计算月利率
  const monthlyRate = Number(actualAnnualRate / 100 / periods);

  let summary;
  switch (method) {
    case RepaymentMethod.EQUAL_PRINCIPAL_INTEREST:
      console.log('等额本息')
      summary = calculateEqualPrincipalInterest(principal, monthlyRate, periods, subsidyRate, customerAnnualRate);
      break;
    case RepaymentMethod.EQUAL_PRINCIPAL:
      console.log('等本等息')
      summary = calculateEqualPrincipal(principal, monthlyRate, periods, subsidyRate, customerAnnualRate);
      break;
    default:
      throw new Error('不支持的还款方式');
  }

  // 设置公共汇总信息
  summary.monthlyRate = monthlyRate;
  summary.subsidyRate = subsidyRate / 100;
  return summary;
}

/**
 * 等额本息计算
 * @param {number} principal - 贷款本金
 * @param {number} monthlyRate - 月利率
 * @param {number} periods - 贷款期数(月)
 * @param {number} subsidyRate - 贴息比例（百分比，0-100）
 * @returns {RepaymentSummary} 还款计划汇总和明细
 */
function calculateEqualPrincipalInterest(principal, monthlyRate, periods, subsidyRate,customerAnnualRate) {
  // 每月还款额 = [贷款本金×月利率×(1+月利率)^还款月数]÷[(1+月利率)^还款月数-1]
  const temp = Math.pow(1 + monthlyRate, periods);
  const monthlyPayment = principal * monthlyRate * temp / (temp - 1);

  const details = [];
  let remainingPrincipal = principal;
  let totalInterest = 0;
  let totalActualInterest = 0;
  let totalSubsidy = 0;

  for (let i = 1; i <= periods; i++) {
    // 每月利息 = 剩余本金 × 月利率
    let interest = Number((remainingPrincipal * monthlyRate).toFixed(2));

    // 每月本金 = 月供 - 利息
    let principalPart = Number((monthlyPayment - interest).toFixed(2));

    // 最后一期调整可能存在的舍入误差
    let finalPayment = monthlyPayment;
    if (i === periods) {
      principalPart = remainingPrincipal;
      finalPayment = principalPart + interest;
      interest = Number((principal * customerAnnualRate / 100 - totalInterest).toFixed(2));
    }

    // 贴息 = 利息 × 贴息比例
    const subsidy = Number((interest * subsidyRate / 100).toFixed(2));

    // 实际利息 = 利息 - 贴息
    const actualInterest = Number((interest - subsidy).toFixed(2));

    // 实际还款 = 月供 - 贴息
    const actualPayment = Number((finalPayment - subsidy).toFixed(2));

    // 剩余本金
    remainingPrincipal = Number((remainingPrincipal - principalPart).toFixed(2));

    // 累计总利息、实际利息和总贴息
    totalInterest += interest;
    totalActualInterest += actualInterest;
    totalSubsidy += subsidy;

    // 添加明细
    const detail = new RepaymentDetail();
    detail.period = i;
    detail.principal = principalPart;
    detail.interest = interest;
    detail.actualInterest = actualInterest;
    detail.subsidy = subsidy;
    detail.totalPayment = actualPayment;
    detail.remainingPrincipal = Math.max(0, remainingPrincipal);
    details.push(detail);
  }

  // 创建汇总信息
  const summary = new RepaymentSummary();
  summary.totalPrincipal = principal;
  summary.totalInterest = Number(totalInterest.toFixed(2));
  summary.totalActualInterest = Number(totalActualInterest.toFixed(2));
  summary.totalSubsidy = Number(totalSubsidy.toFixed(2));
  summary.totalPayment = Number((principal + totalActualInterest).toFixed(2));
  summary.details = details;

  return summary;
}

/**
 * 等本等息计算
 * @param {number} principal - 贷款本金
 * @param {number} monthlyRate - 月利率
 * @param {number} periods - 贷款期数(月)
 * @param {number} subsidyRate - 贴息比例（百分比，0-100）
 * @returns {RepaymentSummary} 还款计划汇总和明细
 */
function calculateEqualPrincipal(principal, monthlyRate, periods, subsidyRate, customerAnnualRate) {
  // 精确计算每月本金（保留6位小数中间结果）
  const exactMonthlyPrincipal = principal / periods;

  // 每月利息 = 总本金 × 月利率（等本等息的特点）
  let monthlyInterest = Number((principal * monthlyRate).toFixed(2));
  console.log('等本等息计算-interest', principal, monthlyRate, monthlyInterest)

  const details = [];
  let remainingPrincipal = principal;
  let totalInterest = 0;
  let totalActualInterest = 0;
  let totalSubsidy = 0;

  for (let i = 1; i <= periods; i++) {
    // 处理本金：前N-1期用舍入值，最后一期用剩余本金
    let currentPrincipal;
    if (i < periods) {
      currentPrincipal = Number(exactMonthlyPrincipal.toFixed(2));
    } else {
      currentPrincipal = remainingPrincipal;
      //最后一期利息等于，总本金*年化利率 - 已计算利息
      monthlyInterest = Number((principal * customerAnnualRate / 100 - totalInterest).toFixed(2));
    }

    // 贴息 = 利息 × 贴息比例
    console.log('等本等息计算-interest', subsidyRate)
    const subsidy = Number((monthlyInterest * subsidyRate / 100).toFixed(2));
    console.log('等本等息计算-subsidy', subsidy)

    // 实际利息 = 利息 - 贴息
    const actualInterest = Number((monthlyInterest - subsidy).toFixed(2));

    // 实际还款 = 本金 + 实际利息
    const actualPayment = Number((currentPrincipal + actualInterest).toFixed(2));

    // 更新剩余本金
    remainingPrincipal = Number((remainingPrincipal - currentPrincipal).toFixed(2));

    // 累计统计
    totalInterest += monthlyInterest;
    totalActualInterest += actualInterest;
    totalSubsidy += subsidy;

    // 添加明细
    const detail = new RepaymentDetail();
    detail.period = i;
    detail.principal = currentPrincipal;
    detail.interest = monthlyInterest;
    detail.actualInterest = actualInterest;
    detail.subsidy = subsidy;
    detail.totalPayment = actualPayment;
    detail.remainingPrincipal = Math.max(0, remainingPrincipal);
    details.push(detail);
  }

  // 创建汇总信息
  const summary = new RepaymentSummary();
  summary.totalPrincipal = principal;
  summary.totalInterest = Number(totalInterest.toFixed(2));
  summary.totalActualInterest = Number(totalActualInterest.toFixed(2));
  summary.totalSubsidy = Number(totalSubsidy.toFixed(2));
  summary.totalPayment = Number((principal + totalActualInterest).toFixed(2));
  summary.details = details;

  return summary;
}

/**
 * 打印还款计划
 * @param {RepaymentSummary} summary - 还款计划汇总
 */
function printRepaymentPlan(summary) {
  console.log('\n========== 还款计划汇总 ==========');
  console.log(`贷款本金: ${summary.totalPrincipal.toFixed(2)}`);
  console.log(`年利率: ${(summary.monthlyRate * 12 * 100).toFixed(2)}%`);
  console.log(`月利率: ${(summary.monthlyRate * 100).toFixed(4)}%`);
  console.log(`贴息比例: ${(summary.subsidyRate * 100).toFixed(2)}%`);
  console.log(`总利息: ${summary.totalInterest.toFixed(2)}`);
  console.log(`总贴息: ${summary.totalSubsidy.toFixed(2)}`);
  console.log(`总实际利息: ${summary.totalActualInterest.toFixed(2)}`);
  console.log(`总还款额: ${summary.totalPayment.toFixed(2)}`);

  console.log('\n========== 每月还款明细 ==========');
  console.log('期数\t本金\t利息\t贴息\t实际利息\t实还金额\t剩余本金');
  summary.details.forEach(detail => {
    console.log(
      `${detail.period}\t${detail.principal.toFixed(2)}\t${detail.interest.toFixed(2)}\t${detail.subsidy.toFixed(2)}\t${detail.actualInterest.toFixed(2)}\t${detail.totalPayment.toFixed(2)}\t${detail.remainingPrincipal.toFixed(2)}`
    );
  });
}


/**
 * 计算贷款的每期利率（等额本息专用版）
 * 使用等额本息公式直接计算
 * 
 * @param {number} nper - 总期数
 * @param {number} pmt - 每期付款金额
 * @param {number} pv - 现值（贷款金额）
 * @returns {number} 每期利率
 */
function calculateEqualInstallmentRate(nper, pmt, pv) {
  // 等额本息公式：pmt = pv * r * (1 + r)^n / ((1 + r)^n - 1)
  // 其中r为每期利率，n为期数

  // 使用二分法求解
  let low = 0.0001
  let high = 1
  let mid

  // 设置最大迭代次数和精度
  const MAX_ITERATIONS = 100
  const PRECISION = 0.0000001

  for (let i = 0; i < MAX_ITERATIONS; i++) {
    mid = (low + high) / 2

    // 计算等额本息公式的右侧
    const pvif = Math.pow(1 + mid, nper)
    const calculatedPmt = (pv * mid * pvif) / (pvif - 1)

    // 检查是否收敛
    if (Math.abs(calculatedPmt - pmt) < PRECISION) {
      return mid
    }

    // 更新搜索范围
    if (calculatedPmt > pmt) {
      high = mid
    } else {
      low = mid
    }
  }

  // 如果未收敛，返回最后一次迭代的结果
  return mid
}

// 导出函数和类
export {
  RepaymentMethod,
  RepaymentDetail,
  RepaymentSummary,
  calculateRepayment,
  printRepaymentPlan,
  calculateEqualInstallmentRate
}; 