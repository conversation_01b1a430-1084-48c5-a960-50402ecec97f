<template>
  <div class="transitPage-wrapper" @click="toActivePage"></div>
</template>

<!-- 自动打开小程序 -->
<script>
import openApp from '@/mixins/openApp'

export default {
  mixins: [openApp],
  data() {
    return {}
  },
  mounted() {
    this.wxCallAppInit()
    this.toActivePage()
  },
  methods: {
    async toActivePage() {
      const res = await this.$http.post('/us/getAppletsToLink/1.0/', {
        appletsUrl: '/pages/active/20240305/index',
        query: 'initTeacher=3'
      })
      console.log('自动打开小程序页面-res2：', res)
      if (res.body) {
        location.href = res.body
      }
    }
  }
}
</script>

<style lang="less" scoped>
.transitPage-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: hidden;
}
</style>
