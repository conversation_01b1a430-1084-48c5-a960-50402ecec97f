<template>
  <div class="yz-certificate-swiper">
    <!-- 轮播组件 -->
    <big-swiper
      v-if='show'
      :list='list'
      swiperClass='swiper-class'
      swiperSlideClass='swiper-slide-class'
    />
  </div>
</template>

<script>
import BigSwiper from './big-swiper';

export default {
  components: { BigSwiper },
  data() {
    return {
      show: false,
      list: [
        {
          image: require("../../../../assets/image/active/2021NewMain/g1.png")
        },
        {
          image: require("../../../../assets/image/active/2021NewMain/g2.png")
        },
        {
          image: require("../../../../assets/image/active/2021NewMain/g3.png")
        },
        {
          image: require("../../../../assets/image/active/2021NewMain/g4.png")
        },
        {
          image: require("../../../../assets/image/active/2021NewMain/g5.png")
        },
      ],
    }
  },
  mounted() {
    setTimeout(() => {
      this.show = true;
    }, 100);
  },
};
</script>

<style lang="less">
  .yz-certificate-swiper{
    margin-top: 0.1rem;
    height: 1.9rem;
    .swiper-class{
      height: 1.7rem;
    }
    .swiper-slide-class{
      width: 74%;
      height: 1.3rem;
      margin-top: 0.2rem;
    }
    .swiper-slide-active{
      width: 2.64rem;
      height: 1.7rem;
      margin-top: 0;
    }
  }
</style>
