<template>
<div>
  <div class="footerbar nav-bar" :class="{active:isIphoneX}">
    <div class="wrap" :class="{active:isIphoneX, 'no-first': noFirst}">
      <div class="inner plr">
        <a class="item" @click="toEnroll" v-if='!noFirst'>
          <i class="icons i-home"></i>
          <span class="label" v-if="tabName=='newintroduce'">免费报名</span>
          <span class="label" v-else>马上报读</span>
        </a>
        <a class="item" @click="toInvitePage" >
          <i class="icons i-order"></i>
          <span class="label">点击有礼</span>
        </a>
        <a class="item" @click="tel"  >
          <i class="icons i-integral"></i>
          <span class="label">咨询老师</span>
        </a>
        <a class="item" @click="toSetting" >
          <i class="icons ">
            <img :src="headImg |defaultAvatar" alt="">
          </i>
          <span class="label">我的</span>
        </a>
      </div>
    </div>

  </div>
  <div class="iphonex_bottom" v-if="isIphoneX"></div>
</div>
</template>
<script>
import {toLogin} from "@/common";
import bridge from '@/plugins/bridge';
import recruitType from '../recruitType'
export default {
  mixins:[ recruitType ],
  props:["inviteId","scholarship","actName","tabName","openactName","unvs","Expired","oneYear", 'noFirst','actStartTime','actEndTime'],
  data() {
    return {
      headImg:'',
      from:'',
      isIphoneX:false,
      isAppOpen: false,
    }
  },
  created(){
    this.headImg=this.storage.getItem('headImg');
    var devicePixelRatio=window.devicePixelRatio;//设备像素比 ipx＝3

    var scrWidth=window.document.documentElement.getBoundingClientRect().width;

    var scrheight=window.screen.height;
    if(devicePixelRatio===3&&scrWidth===375&&scrheight===812){
      this.isIphoneX=true;
      this.$emit('isIphoneX')
    }
    if(this.oneYear){
      this.from='oneYear'
    }
  },
  mounted() {
    this.setIsAppOpen();
  },
  watch:{
    tabName(newValue){
      switch (newValue) {
        case 'newintroduce':this.from='dreamBuild' ;console.log(this.from) ;break;
        case 'selfTought':this.from='selfTought';break;
        case 'openUniversity':this.from='openUniversity';break;
        case 'graduate':this.from='graduate';break;
      //  case 'gdhuaxia':this.from='gdhuaxia';break;
        default:break;
      }
    },
    isAppOpen(val) {
      if (val) {
        this.getUserInfo();
      }
    },
  },
  methods:{

    toInvitePage(){
      this.$router.push({name:'dreamBuildInvite',query:{from:this.from}})
    },
    setIsAppOpen() {
      bridge.callHandler('isAppOpen').then((res) => {
        if (res.appOpen) {
          this.isAppOpen = true;
        }
      });
    },
    tel() {
      if (this.storage.getItem('cardMobile')) {
        window.location.href = "tel:" + this.storage.getItem('cardMobile');
        return;
      }
      window.location.href = "tel:4008336013";
    },
    tips() {
      this.$modal({ message: "活动已结束~下次赶早哦！", icon: "warning" });
    },
    toSetting(){
      if(this.storage.getItem('authToken')){
        if (this.isAppOpen) {
          window.location.href = 'yuanzhiapp://yzwill.cn/Home?params={"tab":3}';
          return;
        }
        this.$router.push({name:'settings'})
      }else{
        toLogin.call(this,null,'login','/Newsettings')
      }
    },
    getCookie(name) {
      var arr,
        reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
      if ((arr = document.cookie.match(reg))) {
        return arr[2];
      } else {
        return null;
      }
    },
    toEnroll(){
      const now = new Date().getTime();
      //活动前
      if(now < this.actStartTime){
        this.$modal({ message: "活动还未开始~请耐心等待噢！", icon: "warning" });
        return;
      }
      if( now > this.actEndTime){
        this.tips();
        return;
      };
      // if(!this.actStarted){
      //   this.$modal({ message: "活动还未开始~请耐心等待噢！", icon: "warning" });
      //   return;
      // }

      // if(this.Expired){
      //   this.tips()
      //   return;
      // }

      if(this.isAppOpen) {
        //是否报读国开/成教
        if(this.isSeikyo) {
          window.location.href =`yuanzhiapp://yzwill.cn/Home?params={"tab":3}`;
          return
        }else {
          window.location.href = `yuanzhiapp://yzwill.cn/Enroll/InfoInput?params={"activeName":"2022级广东机电职业技术学院奖学金","grade":"2022","scholarship":"130"}`;
          return;
        }
      };

      let recruitType,scholarship,actName;
      switch (this.tabName) {
        case 'selfTought':this.toForm();return;
        case 'graduate':this.toGraduate();return;
        case 'openUniversity':recruitType='2';scholarship='1';actName=this.openactName;break;
        default :recruitType='1';
                scholarship=this.scholarship || '164';
                actName=this.actName;
                break;
      }
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: scholarship,
          actName:actName,
          recruitType:recruitType,
        }
      };

      this.$router.push(url);
    },
    toGraduate() {
      if(this.storage.getItem('recruitType') !=='5') {
        this.$router.push({name:'graduateForm'});
      }else {
        this.$router.push({name:'stuInfo'})
      }

    },
    toForm() {
      if(this.storage.getItem('recruitType') !== '4' ) {
        this.$router.push('/active/selfTought/form');
      }else {
          this.$router.push({name:'stuInfo'})
      }

    },
    async getUserInfo() {
      const res = await this.$http.post('/us/getUserInfoById/1.0/');
      if (res.code == '00') {
        this.headImg = res.body.headImg;
      }
    },
  }
}
</script>
<style lang="less" scoped>
  .footerbar{
    height: .6rem;
    position: fixed;
    bottom:0;
    background-color: #fff;
    width: 100%;
    max-width: 640px;
    z-index: 9999;
    border-top: solid 1px rgba(54, 54, 54, .08);
    &.active{
      bottom:34px;
    }
    .wrap{
      &.no-first{
        .item{
          width: 33.33%
        }
      }
      width: 100%;
      height: 100%;
      .item{ float:left; width:25%; height:100%; text-align:center;padding-top: .1rem }
      .label{ display:block; padding-top:.03rem; line-height:1; font-size:.12rem;
       color: #A6080B;font-weight: 600 }

      .icons{ width:.25rem; height:.25rem; border-radius: 50%;overflow: hidden}
      .icons>img{width: 100%;height: 100%}
      .i-home{ background-image:url(../../../../assets/image/active/enrollAggregate/ic-free-enroll.png); }
      .i-order{ background-image:url(../../../../assets/image/active/enrollAggregate/ic-invite.png); }
      .i-integral{ background-image:url(../../../../assets/image/active/enrollAggregate/ic-concact.png); }
    }
  }
  .iphonex_bottom{
    position: fixed;
    width: 100%;
    height: 34px;
    background-color: white;
    bottom:0;
    left:0;
    z-index: 9999;
  }
</style>
