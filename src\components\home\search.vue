<template>
  <van-search
    v-bind="$attrs"
    v-on="$listeners"
    show-action
    placeholder="输入感兴趣的商品名"
    class="search"
  >
    <template #left-icon>
      <div class="icon-query" />
    </template>
    <slot v-for="(name) in Object.keys(this.$slots)"  :name="name" :slot="name"></slot>
  </van-search>
</template>

<script>
export default {
  data() {
    return {}
  }
}
</script>

<style scoped lang="scss">
.search {
  padding: 0.1rem 0.15rem;
}

.icon-query {
  width: 0.2rem;
  height: 0.2rem;
  background: url('../../assets/image/server/icon-query.png') no-repeat;
  background-size: 100% 100%;
  margin-top: 0.01rem;
}

::v-deep .van-icon-search {
  font-size: 0.2rem;
  color: #F06E6C;
}

::v-deep .van-icon-clear {
  padding: 0 0.12rem;
}

::v-deep .van-search__content {
  background-color: #fff;
  border: 1px solid #F06E6C;
  border-radius: 0.18rem 0 0 0.18rem;
  height: 0.32rem;
  align-items: center;
  padding-left: 0.12rem;
}

::v-deep .van-search__action {
  padding: 0;
  line-height: normal;

  &:active {
    background-color: #fff;
  }

}

//::v-deep .van-search .van-cell {
//  padding: 0.05rem 0.08rem 0.05rem 0;
//}

::v-deep .van-cell {
  font-size: 0.14rem;
  padding: 0.05rem 0.08rem 0.05rem 0;
}

.search-but {
  width: 0.55rem;
  height: 0.32rem;
  background: #F06E6C;
  border-radius: 0 0.18rem 0.18rem 0;
  font-size: 0.14rem;
  color: #FFFFFF;
  outline: none;
}

</style>
