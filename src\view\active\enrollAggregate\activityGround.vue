<template>
<div>
      <div class="banner_wrap">
        <div class="banner">
          <img :src="item.actBanner | imgBaseURL" alt="" v-for="item in bannerList" :key="item.id" @click="toUrl(item.actUrl)">
        </div>
        <div class="nothing" v-if="bannerList.length==0">暂无活动~</div>
      </div>
</div>
</template>

<script>
    export default {
        name: "activityGroung",
      data() {
        return{
          bannerList:[]
        }
      },
      created(){
          this.getActivity()
      },
      methods:{
          getActivity(){
            this.$http.post('/mkt/getExistActivityBanner/1.0/',{pfsnLevel:this.$route.query.pfsnLevel}).then(res=>{
              if(res.code=='00'){
                  this.bannerList=res.body
              }
            })
          },
        toUrl(url){
            window.location.href=url
        }
      }
    }
</script>

<style scoped lang="less">
.banner_wrap{
  width: 100%;
  height: auto;
  overflow: hidden;
  max-width: 640px;
  margin: 0 auto;
  .banner{
    width: 100%;
    height: auto;
    overflow: hidden;
    img{
      margin: .1rem auto 0;
      width: 3.55rem;
      height: 1.12rem;
      border-radius: .05rem;
    }
  }
  .nothing{
    width: 100%;
    padding-top: 1.7rem;
    margin: 1.4rem auto;
    background-image: url("../../../assets/image/active/enrollAggregate/<EMAIL>");
    background-size: 1.65rem 1.65rem;
    background-repeat: no-repeat;
    background-position: 1.05rem 0;
    text-align: center;
    color: rgba(23,6,6,.6);
  }
}
</style>
