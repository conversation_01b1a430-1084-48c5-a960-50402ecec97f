
import { isLogin } from '@/common'
export default {
  created(){
    if(isLogin()){
      this._getUserReadInfo()
    }
  },
  data(){
    return {
      isSeikyo:false,//是否报读成人/国开
    }
  },
  methods:{
  //获取用户信息
  _getUserReadInfo(){
    this.$http.post('/mkt/stdLearnInfo/1.0/').then(res=>{
      const {code, body} = res;
      if(code == '00'){
        for(let i = 0;i<body.learnInfos.length;i++){
          let item = body.learnInfos[i];
          if(item.recruitType == '1' || item.recruitType == '2'){
            this.isSeikyo = true;
            break;
          }
        }
      } 
    })
  },
  }
}