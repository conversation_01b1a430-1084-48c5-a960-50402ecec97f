<template>
  <div class="yz-down-app" v-if="downShow">
    <div class="left-box">
      <img src="../assets/image/ic_register_logo.png" alt="" />
      <span> 直播回放、随堂测试、复习资源下载等丰富功能等你来体验~</span>
    </div>
    <wx-open-launch-app
      id="launch-btn"
      @error="callAppError"
      :appid="OpenAppId"
      extinfo="extinfo"
    >
      <script type="text/wxtag-template">
        <style>
          .down {
            width: 1.78rem;
            height: .4rem;
            background: #FBEEEE;
            border-radius: .25rem;
            font-size: .14rem;
            color: #CC2725;
            margin: .17rem 0;
            position: relative;
            z-index: 3;
          }
        </style>
        <span class='down'>前往远智教育APP</span>
      </script>
    </wx-open-launch-app>
  </div>
</template>
<script>
import openApp from '@/mixins/openApp';

export default {
  data() {
    return {
      downShow: true,
    };
  },
  props: {
    eventName: {
      type: String,
      default: "downapp",
    },
    show: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [openApp],
  mounted() {
    this.getStuInfo();
    this.wxCallAppInit(); // 微信标签唤起app
  },
  methods: {
    // down() {
    //   MtaH5.clickStat(this.eventName);
    //   window.location.href =
    //     "https://sj.qq.com/myapp/detail.htm?apkName=cn.yzou.yzxt";
    // },
    getStuInfo() {
      if (!this.show) {
        const learnId = this.storage.getItem("learnId");
        this.$http.post("/mkt/stdLearnInfo/1.0/").then((res) => {
          if (res.code == "00" && res.body) {
            const current = res.body.learnInfos.find(
              (item) => item.stdStage != 10 && item.learnId == learnId
            );
            this.downShow = !(
              current.hasUnpaid == 1 && !["10", "8"].includes(current.stdStage)
            );
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.yz-down-app {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  z-index: 2;
  padding: 0.08rem 0.1rem;
  border-top: 1px solid #edebeb;
  .left-box {
    display: flex;
    align-items: center;
    width: 2.75rem;
    font-size: 0.12rem;
    img {
      width: 0.5rem;
      height: 0.5rem;
      margin-right: 0.1rem;
    }
  }
}
#launch-btn {
  position: absolute;
  top: .2rem;
  left: 2.8rem;
  right: 0;
  bottom: 0;
}
</style>
