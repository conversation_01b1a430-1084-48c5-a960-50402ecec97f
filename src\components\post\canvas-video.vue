<template>
  <div :class="{'markpops':isfull}">
    <!-- <video :src="scVideoUrl+'?'+videoSign" style="position: relative;  object-fit: fill ;" loop autoplay :muted="muted"
           webkit-playsinline="true" playsinline="true" x-webkit-airplay="true" x5-video-player-type="h5" controls
           x5-video-player-fullscreen="true" x5-video-ignore-metadata="true"></video> -->
    <div id="video-box" v-if="scVideoUrl" :ref="'videoBox'+vid" :class="{'fill':isfull}">
      <div :style="{height:picUrlsImgWHObj.h,width:picUrlsImgWHObj.w}" v-show="!isplay&&!isfull"
           @click.stop="playVideoClick" class="mask-box" :id="'mask-box'+vid">
        <div class="mask-img"></div>
        <img :src="videoCover" alt="" :height="picUrlsImgWHObj.h" :width="picUrlsImgWHObj.w" class="imgcover">
        <img src="../../assets/image/app/circleAd/play.png" alt="" class="startBtn">
        <div v-if="!isWifi" class="iswifi-text">非wifi环境，点击流量播放</div>
      </div>
      <video :src="scVideoUrl+'?'+videoSign" style="position: relative;  object-fit: fill ;display:none" loop
             :muted="muted" :id="'video1'+vid" webkit-playsinline="true" playsinline="true" x-webkit-airplay="true"
             x5-video-player-type="h5" x5-video-player-fullscreen="true" x5-video-ignore-metadata="true"></video>
      <div class="canvas-box"
           :style="{height:isfull?'100%':picUrlsImgWHObj.h+'px',width:isfull?'100%':picUrlsImgWHObj.w+'px'}">
        <canvas :id="'mycanvas1'+vid" class="canvas-video" :height="picUrlsImgWHObj.initH"
                :width="picUrlsImgWHObj.initW" @click.stop="videoStatusClick"
                :style="{height:picUrlsImgWHObj.h,width:picUrlsImgWHObj.w}">
        </canvas>
        <div class="totalTime" v-show="!isfull">{{videoTime | trans}}</div>
      </div>
      <van-loading type="spinner" size="40px" v-show="loadWaitng&&isfull" class="startBtn" />
      <img src="../../assets/image/app/circleAd/play.png" alt="" class="startBtn" v-show="!isplay&&isfull">
      <img src="../../assets/image/app/circleAd/muted.png" alt="" v-show="muted&&!isfull" class="muted-icon"
           @click.stop="muted=!muted">
      <img src="../../assets/image/app/circleAd/video.png" alt="" v-show="!muted&&!isfull" class="muted-icon"
           @click.stop="muted=!muted">
      <!-- 进度条 -->
      <!-- <div id="videoControl" :style="{height:isdetail?'32px':'2px'}">
        <div class="btn"></div>
        <div id="lineBox" @touchstart.stop="dragStart" @touchmove="lineDrag"
             :style="isdetail?{width:'80%',position:'unset'}:{width:'100%'}">
          <div id="line" :style="{width:`${lineW.width}%`}"></div>
          <div id="spot" data-tag="spot" v-if="isdetail"></div>
        </div>
        <div class="btn"></div>
      </div> -->

    </div>
    <img src="../../assets/image/app/circleAd/small.png" alt="" v-show="isfull&&!isplay" class="small-icon"
         @click.stop="closeFull">
    <div class="full-mask" v-show="isfull&&!isplay"></div>
  </div>

</template>

<script>
import { getNetworkType, isWeixin } from '@/common'
export default {
  components: {},
  data () {
    return {
      // 视频是否播放
      isplay: false,
      // canvas持续绘制视频
      timer: null,
      // 是否双击
      clickTime: 0,
      // 视频总长度
      totalLength: 0,
      // 视频进度条Interval对象
      lineTimer: null,
      // 视频进度条长度
      lineW: { width: 0 },
      // 拖拽按钮距离左边多远
      dx: 0,
      muted: true,
      isfull: false,
      isMounted: false,
      videoTime: 0,
      isWifi: true,
      isfrist: true,
      loadWaitng: false
    };
  },
  props: {
    picUrlsImgWHObj: {
      type: Object,
      default: () => ({})
    },
    scVideoUrl: {
      type: String,
    },
    videoSign: {
      type: String,
    },
    videoCover: {
      type: String,
    },
    videoDesc: {
      type: Number,
    },
    vid: {
      type: Number,
    },
    isdetail: {
      type: Boolean,
      default: false
    },
    play: {
      type: Boolean,
      default: false
    },
    mtop: {
      type: Number
    },
    scrollTop: {
      type: Number
    }
  },
  computed: {},
  watch: {

    scrollTop: {
      handler (val) {
        this.$nextTick(() => {
          let wh = window.screen.height
          // 盒子上距离
          let vt = this.mtop
          let vh = this.picUrlsImgWHObj.h
          // console.log(this.scrollTop, 'this.scrollTop');
          // console.log(vt, 'vt');
          // console.log(vh, 'vh');
          // console.log(vt, 'vt');
          if (vt < wh && this.scrollTop < (vt + vh / 2)) {
            if (this.isplay || !this.isWifi) return
            if (this.isfrist) return
            let canvas = document.getElementById('mask-box' + this.vid);
            canvas.click()
            return
          }
          if (this.scrollTop > vt && this.scrollTop < (vh + vt)) {
            if (this.isplay || !this.isWifi) return
            if (this.isfrist) return
            let canvas = document.getElementById('mask-box' + this.vid);
            canvas.click()
            return
          }
          if (this.isplay) {
            this.stopVideoWatch()
          }
        })


      },
      immediate: true,
    },

  },
  filters: {
    trans (time) {
      let hour = parseInt((time) / 3600);
      let minute = parseInt((time % 3600) / 60);
      let second = Math.ceil(time % 60);
      if (minute < 10) {
        minute = '0' + minute
      }
      if (second < 10) {
        second = '0' + second
      }
      if (!hour) {
        return `${minute}:${second}`
      } else {
        return `${hour}:${minute}:${second}`
      }
    }
  },
  methods: {
    closeFull () {
      this.isfull = false
      this.muted = true
      document.body.style.overflow = 'auto'
    },
    playVideoClick () {
      if (this.isfrist) {
        let video = document.getElementById('video1' + this.vid);
        video.load()
        this.isfrist = false
      }
      this.playVideo()


    },
    drawVideo () {
      let video = document.getElementById('video1' + this.vid);
      let canvas = document.getElementById('mycanvas1' + this.vid);
      let ctx = canvas.getContext('2d')
      video.play();
      this.renderCanvas(video, canvas, ctx)
      this.isplay = true;


    },
    renderCanvas () {
      window.requestAnimationFrame(this.renderCanvas)
      let video = document.getElementById('video1' + this.vid);
      let canvas = document.getElementById('mycanvas1' + this.vid);
      let ctx = canvas.getContext('2d')
      // ctx.clearRect(0, 0, canvas.width, canvas.height)
      // 视频寻找中 
      video.onseeking = () => {
        this.loadWaitng = true
      }
      // 视频寻找完毕
      video.onseeked = () => {
        this.loadWaitng = false
      }
      // 视频等待数据，并非错误
      video.onwaiting = () => {
        this.loadWaitng = true
      }
      // video.onplaying = () => {
      //   this.loadWaitng = false
      // }
      // video.oncanplay = () => {
      //   this.loadWaitng = false
      // }
      // 可以播放，歌曲全部加载完毕
      video.oncanplaythrough = () => {
        this.loadWaitng = false
      }
      // canvas重复绘制视频
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)  //绘制视频
    },

    // 点击播放按钮
    playVideo (e) {
      e = e || event;
      this.drawVideo();
      this.lineLength();
      // console.log(e, 'e');
      // e.target.style.display = 'none';
    },
    // 开始拖拽时暂停视频
    dragStart (e) {
      if (e.target.dataset.tag == 'spot') {
        let video = document.getElementById('video1' + this.vid);
        video.pause();
        clearInterval(this.timer);
        this.isplay = false;
      }
    },
    // 拖拽中 获取拖拽的距离和拖拽的百分比(需要注意拖拽超出屏幕宽度后)  拖拽百分比 * 视频总长度 = 视频播放的进度
    lineDrag (e) {
      // 判断是否是拖拽的进度条右边的圆点
      if (e.target.dataset.tag == 'spot') {
        let lineBoxW = e.target.parentNode.clientWidth;
        let x = e.touches[0].clientX;
        // 判断拖拽是否超出范围
        if (x < 0) {
          x = 0;
        } else if (x > lineBoxW) {
          x = lineBoxW;
        }
        // 计算出拖拽的百分比并赋值给进度条
        let linex = ((x - e.target.offsetWidth) / lineBoxW * 100).toFixed(1) * 1
        this.lineW.width = linex;
        // 为视频设置播放进度
        let video = document.getElementById('video1' + this.vid);
        video.currentTime = this.totalLength * linex / 100;
        this.isplay = true;
        video.play();
        this.lineLength();
      }
    },
    lineLength () {
      let video = document.getElementById('video1' + this.vid);
      video.onloadeddata = () => {
        this.totalLength = video.duration.toFixed(1);
      }
      if (this.isplay) {
        // 视频播放长度和视频长度一致时再次点播放按钮使进度条为0
        if (this.totalLength == video.currentTime) {
          this.lineW.width = 0;
        }

        // 视频播放长度 / 视频总长度 * 100 = 进度条长度
        this.lineTimer = setInterval(() => {
          this.lineW.width = (video.currentTime / this.totalLength * 100).toFixed(1) * 1;
          this.videoTime = this.videoDesc - video.currentTime
          if (this.totalLength && (this.isplay == false || this.lineW.width >= 100)) {
            clearInterval(this.lineTimer)
            if (this.isplay && this.videoTime <= 0) {
              this.lineLength()
            }
          }

          // if()
        }, 1000 / 60)
      } else if (this.isplay == false || this.lineW.width >= 100) {
        clearInterval(this.lineTimer)
      }

    },
    videoStatusClick () {
      let video = document.getElementById('video1' + this.vid);
      if (this.isplay) {
        // 单机显示视频的控件
        video.pause();
        clearInterval(this.timer);
        this.isplay = false;
      } else {
        this.playVideo()
        if (this.isfrist) {
          this.isfrist = false
        }
        if (!this.isfull) {
          this.isfull = true
          this.muted = false
          document.body.style.overflow = 'hidden'
        }
      }


    },
    stopVideoWatch () {
      let video = document.getElementById('video1' + this.vid);
      video.pause();
      clearInterval(this.timer);
      this.isplay = false;
    }
  },
  created () { },
  mounted () {
    this.isMounted = true
    this.videoTime = this.videoDesc

    this.isWifi = getNetworkType() === 'wifi' || getNetworkType() === 'other' ? true : false


  },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
.markpops {
  position: fixed;
  z-index: 6;
  left: 0;
  top: 0;
  margin-top: 0;
  bottom: 0;
  background: #000;
  right: 0;
}
#video-box {
  width: 100%;
  position: relative;
  margin-top: 5px;
  &.fill {
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
  }
  .mask-box {
    position: absolute;
  }
  .mask-img {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }
  // .imgcover {
  //   position: absolute;
  // }
  /* 视频播放遮罩层按钮 */
  .startBtn {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    transform: translate(-50%, -50%);
  }
  .iswifi-text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.12rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    width: 100%;
    text-align: center;
    margin-top: 0.32rem;
  }
}
#video1 {
  width: 100%;
  height: 100%;
}
/* canvas */
.canvas-box {
  position: relative;
  width: 100%;
  height: 100%;
}
.canvas-video {
  width: 100%;
  height: 100%;
  z-index: 9999;
}

#image {
  width: 100%;
  height: 200px;
  position: absolute;
  top: 0;
  left: 0;
}

.muted-icon {
  width: 24px;
  height: 24px;
  position: absolute;
  bottom: 0.1rem;
  left: 0.1rem;
  z-index: 2;
}
.small-icon {
  width: 30px;
  height: 30px;
  position: absolute;
  bottom: 0.1rem;
  right: 0.1rem;
  z-index: 2;
}
.full-mask {
  height: 1.8rem;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  width: 100%;
  position: absolute;
  bottom: 0rem;
}
.totalTime {
  position: absolute;
  bottom: 0.1rem;
  right: 0.1rem;
  z-index: 2;
  font-size: 0.12rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 0.2rem;
}
/* 视频控件 */
#videoControl {
  width: 100%;
  height: 25px;
  position: absolute;
  bottom: 3px;
  left: 0;
  z-index: 1;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-image: linear-gradient(to bottom, #ffffff00, #000000ff);
  padding: 0 1%;
  box-sizing: border-box;
}
/* 进度条 */
#lineBox {
  width: 100%;
  height: 2px;
  background: rgb(114, 114, 114);
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
}
#line {
  width: 0%;
  height: 100%;
  background: rgb(32, 184, 255);
  transition: all 0.1s linear;
}
#spot {
  width: 4px;
  height: 4px;
  background: rgb(32, 184, 255);
  border: 2px solid rgb(255, 255, 255);
  border-radius: 50%;
  margin-top: -3px;
  transition: all 0.1s linear;
}
#voice {
  width: 25px;
  height: 25px;
  position: absolute;
  right: 10px;
  bottom: 40px;
  // background-image: url('../../assets/images/voiceOpen.png');
  background-size: 25px 25px;
  z-index: 1;
}
/* 按钮 */
.btn {
  width: 10%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>