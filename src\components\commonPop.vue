<template>
  <div class="commonPop">
    <div class="commonPop-box">
      <div class="commonPop-title">
        <span>取消</span>
        <span>确定</span>
      </div>
      <div class="commonPop-content">
        <slot>123</slot>
      </div>
    </div>
    <div class="commonPop-mask"></div>
  </div>
</template>

<script>
  export default {
    props: {},
    data() {
      return {}
    },
    methods: {}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .commonPop{
    z-index: 2023;
      .commonPop-box{
        z-index: 2031;
        width: 100%;
        position: fixed;
        background: #fff;
        top: auto;
        right: auto;
        bottom: 0;
        left: 50%;
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0);
        .commonPop-title{
          height: .4rem;
          span{
            display: inline-block;
            float: left;
            color: #666;
            text-align: center;
            width: 50%;
            line-height: .4rem;
            &:last-child{
              background: @bgColor;
              color: #fff;
            }
          }
        }
        .commonPop-content{
          padding: .12rem;
        }
      }
    .commonPop-mask{
      z-index: 2030;
      position: fixed;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0.5;
      background: #000;
    }
  }
</style>
