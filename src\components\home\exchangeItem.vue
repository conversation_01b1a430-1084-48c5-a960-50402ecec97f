<template>
  <div class="clearfix">
    <router-link
      class="yz-exchange-item"
      v-for="item in list"
      :key="item.salesId"
      :to="{
        name: 'exchange',
        params: { id: item.salesId },
        query: { salesType: 1 },
      }">
      <status
        isNew
        :status="item.salesStatus"></status>
      <!-- <span class="status-tips" :class='item.salesStatus | statusClass'>
        {{item.salesStatus | statusText}}
      </span> -->
      <div class="pic">
        <img
          class="img"
          :src="(item.annexUrl + zmSize) | imgBaseURL" />
        <div
          class="time-box"
          v-if="item.salesStatus != 1">
          <img
            src="../../assets/image/server/i3.png"
            class="icon"
            alt="" />
          <span v-if="item.salesStatus == 2"
            >{{ item.startTime | formatDate('yyyy-MM-dd hh:mm') }} 开始</span
          >
          <span v-if="item.salesStatus == 3"
            >{{ item.endTime | formatDate('yyyy-MM-dd hh:mm') }} 结束</span
          >
        </div>
      </div>
      <div class="txt">
        <div class="tit">{{ item.salesName }}</div>
        <!-- <price class="mt" :salesPrice="item.salesPrice" :originalPrice="item.originalPrice"></price> -->
        <!-- <div class="fsc gap1" v-if="item.salesStatus==='2'">开始时间:{{item.startTime|formatDate('yyyy.MM.dd hh:mm')}}</div>
        <div class="fsc gap1" v-else-if="item.salesStatus==='3'">剩余:<span class="fc">{{item.goodsCount}}</span>个</div> -->
        <div class="price-box">
          <div class="price">
            <img
              src="../../assets/image/server/d1.png"
              v-if="item.salesStatus != 1"
              class="d-icon"
              alt="" />
            <img
              src="../../assets/image/server/d2.png"
              v-else
              class="d-icon"
              alt="" />
            <span
              class="t1"
              :class="{ grey: item.salesStatus == 1 }"
              >{{ item.salesPrice }}</span
            >
          </div>
          <div
            class="fsc"
            v-if="item.salesStatus != 1">
            已兑换<span class="red">{{ item.goodsSalesVolume || 0 }}</span
            >个
          </div>
        </div>
      </div>
    </router-link>
  </div>
</template>

<script>
import status from '@/components/status'
import loadBar from '@/components/loadBarThree'

export default {
  components: { status, loadBar },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  filters: {},
  data() {
    return {
      zmSize: '?x-oss-process=image/resize,m_fixed,h_170,w_170', // 智米商品首页的兑换尺寸
    }
  },
}
</script>

<style
  lang="less"
  scoped>
.yz-exchange-item {
  background-color: #fff;
  margin-bottom: 0.1rem;
  border-radius: 0.06rem;
  display: block;
  position: relative;
  float: left;
  width: 1.73rem;
  overflow: hidden;
  &:not(:nth-child(2n)) {
    margin-right: 0.09rem;
  }
  // &:after{ .borderBottom }
  // &:before{ .borderRight }
  &:nth-child(even) {
    &:before {
      display: none;
    }
  }
  .link {
    width: 100%;
  }
  &.h2 {
    height: 2.65rem;
  }
  .pic {
    width: 100%;
    height: 1.73rem;
    margin: 0 auto;
    position: relative;
  }
  .txt {
    padding: 0.1rem;
  }
  .tit {
    font-size: 0.15rem;
    color: #170606;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 1;
  }
  .time-box {
    position: absolute;
    z-index: 2;
    bottom: 0.1rem;
    left: 0.08rem;
    right: 0.08rem;
    background: linear-gradient(135deg, #fc905f 0%, #f85731 100%);
    color: #fff;
    // padding: 0 0.05rem;
    // padding-left: 0.05rem;
    height: 0.2rem;
    border-radius: 100px;
    font-weight: 600;
    font-size: 0.11rem;
    display: flex;
    align-items: center;
    padding: 0 0.05rem;
    .icon {
      width: 0.12rem;
      margin-right: 0.05rem;
    }
    &.yellow {
      background: linear-gradient(
        180deg,
        rgba(252, 216, 95, 0) 0%,
        rgba(248, 176, 49, 0.7) 100%
      );
    }
  }
  .status-tips {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    line-height: 0.2rem;
    padding: 0 0.06rem;
    text-align: center;
    color: #fff;
    font-size: 0.12rem;
    background: linear-gradient(
      135deg,
      rgba(252, 144, 95, 1) 0%,
      rgba(248, 87, 49, 1) 100%
    );
    border-radius: 0 0 0.05rem 0;
    &.yellow {
      background: linear-gradient(
        135deg,
        rgba(252, 216, 95, 1) 0%,
        rgba(248, 176, 49, 1) 100%
      );
    }
    &.grey {
      background: #b9b4b4;
    }
  }
  .price-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.15rem;
    .price {
      display: flex;
      align-items: center;
      color: #f85731;
      font-size: 0.18rem;
      font-weight: 600;
      .t1.grey {
        color: #b9b4b4;
      }
    }
    .d-icon {
      width: 0.15rem;
    }
  }
  .gap1 {
    margin-top: 0.04rem;
  }
  .fsc {
    color: #746a6a;
    font-size: 0.12rem;
  }
  .red {
    color: #f06e6c;
  }
}
</style>
