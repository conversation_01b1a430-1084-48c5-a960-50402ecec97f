<template>
  <picker :slots="slots" :isSetDefaultVal="isSetDefaultVal" v-model="val" ref="picker"/>
</template>

<script>
  import picker from '@/components/picker';

  export default {
    props: {
      dictName: String,
      options: Array,
      ext1: String,
      isSetDefaultVal: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      defaultIndex: {
        type: Number,
        default: 0
      },
      filterValue: Array
    },
    data() {
      return {
        slots: [],
        val: [{}],
        vals: []
      }
    },
    created() {
      // this.init();
    },
    methods: {
      init: function () {
        // this.$nextTick(() => {
        this.slots = [{flex: 1, values: this.getOptions(), defaultIndex: this.defaultIndex}];
        // })
      },
      getOptions: function () {
        let options = [];
        if (this.options.length >= '1') {
          // this.vals = this.options || [];
          let vals = [];
          for (let item of this.options) {
            vals.push({
              city: item,
              cityTxt: item
            })
            options.push(item);
          }
          this.vals = vals;
        }
        return options;
      },
      open: function () {
        if (this.disabled) return;
        this.$refs.picker.open();
      },
      setValues: function (value) {
        if (value) {
          this.$refs.picker.setValues([value]);
        }
      }
    },
    watch: {
      val: function () {
        this.$emit('input', this.vals[this.val[0].index] || this.vals[0]||{});
      }
    },
    components: {picker}
  }
</script>
