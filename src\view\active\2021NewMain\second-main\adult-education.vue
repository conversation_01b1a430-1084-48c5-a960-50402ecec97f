<template>
  <div class="yz-adult-education">
    <div class="enroll-methods" v-show='whiteTabActive == 0'>
      <div class="red-title nomt">方法1：选择心仪院校</div>
      <ul class="white-item-ul school">
        <template v-for="(item, index) in schoolLogoList">
          <li v-if='index < 9 || (index >= 9 && !schoolHide)' :key="item.imgName" @click='schoolClick(item)'>
            <div class="school-logo">
              <img :src="item.imgName | schoolLogo" class="logo" alt="">
            </div>
            <p class='school-name'>{{item.schoolName}}</p>
          </li>
        </template>

        <li v-if='schoolLogoList.length > 9' class="red" :class='{open: !schoolHide}' @click='showMore("school")'>
          <span>{{schoolHide ? "更多" : "收起"}}</span>
          <img src="../../../../assets/image/active/2021NewMain/red-up.png" class="arrow" alt="">
        </li>
      </ul>
      <div class="red-title">方法2：选择心仪专业</div>
      <ul class="white-item-ul pfsn">
        <template v-for="(item, index) in pfsnList">
          <li :class='{small: item.pfsnName.length > 6}' v-if='index < 5' :key="item.pfsnId" @click='toList(item)'>
            {{item.pfsnName}}</li>
        </template>
        <li class="red" @click='showMore("pfsn")'>
          <span>更多</span>
          <img src="../../../../assets/image/active/2021NewMain/red-up.png" class="arrow" alt="">
        </li>
      </ul>
      <div class="red-title">方法3：选择报读城市</div>
      <ul class="white-item-ul city">
        <template v-for="(item, index) in citys">
          <li v-if='index < 9 || (index >= 9 && !cityHide)' :key="item.id" @click='toBuildInfo(item.id)'>
            {{item.name}}
          </li>
        </template>
        <li class="red" :class='{open: !cityHide}' @click='showMore("city")'>
          <span>{{cityHide ? "更多" : "收起"}}</span>
          <img src="../../../../assets/image/active/2021NewMain/red-up.png" class="arrow" alt="">
        </li>
      </ul>
    </div>
    <div class="brief-introduction" v-show='whiteTabActive == 1'>
      <p class="t1">
        成人高考属于国民教育体系，年满18周岁、高中/中专毕业考生可统一参加全国成人高考，统考录取入学，一年考一次，2.5年~3年毕业，国家承认学历，学信网可查，可以报考研究生、公务员、职业资格等考试。
      </p>
      <img src="../../../../assets/image/active/2021NewMain/ad-4.png" class="ad-img1" alt="">
      <img src="../../../../assets/image/active/2021NewMain/ad2.png" class="ad-img2" alt="">
    </div>
    <!-- 专业弹窗 -->
    <van-popup v-model="pfsnShow">
      <img src="../../../../assets/image/close-grey.png" class="close" @click='closePop' alt="">
      <p class="pop-title">选择{{pfsnLevel == 1 ? "本科" : "专科"}}专业</p>
      <div class="input-btn">
        <input v-model="searchPfsnName" class='search-input' type="text" placeholder="请输入专业名字" @change='search'>
        <button class="btn" @click='search'>搜索</button>
      </div>
      <div class="pop-pfsn-list">
        <button v-for="(item, index) in searchPfsnList" :class='{small: item.pfsnName.length > 6}' :key="index"
                @click='toList(item)'>{{item.pfsnName}}</button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import statistic from '../statistic.json';

export default {
  props: {
    pfsnLevel: {
      type: [Number, String],
      default: '',
    },
    whiteTabActive: {
      type: [Number, String],
      default: 0,
    },
    inviteId: String,
    scholarship: String,
    actName: String,
    regChannel: [Number, String],
    regOrigin: [Number, String],
  },
  filters: {
    schoolLogo (val) {
      return require(`../../../../assets/image/schoolLogo/${val}.png`) || '';
    },
  },
  data () {
    return {
      cityHide: true,
      pfsnShow: false,
      schoolHide: true,
      citys: [
        { id: 'gz', name: '广州' },
        { id: 'sz', name: '深圳' },
        { id: 'hz', name: '惠州' },
        { id: 'dg', name: '东莞' },
        { id: 'hy', name: '河源' },
        { id: 'yj', name: '阳江' },
        { id: 'sw', name: '汕尾' },
        // { id: 'cz', name: '潮州' },
        { id: 'sg', name: '韶关' },
        { id: 'zj', name: '湛江' },
        { id: 'zq', name: '肇庆' },
        { id: 'mm', name: '茂名' },
        { id: 'qy', name: '清远' },
        { id: 'mz', name: '梅州' },
        { id: 'jm', name: '江门' },
        // { id: 'st', name: '汕头' },
        { id: 'fs', name: '佛山' },
      ],
      defaultSchool: [ // 包含了专科和本科的学校
        // { imgName: 'jn', routerName: 'jnUniversity', schoolName: '暨南大学' },
        { imgName: 'fnny', routerName: 'southChinaAgriculturalUniversity', schoolName: '华南农业大学' },
        { imgName: 'gzdx', routerName: 'guangZhouUniversity', schoolName: '广州大学' },
        { imgName: 'dglg', routerName: 'DongguanUniversity', schoolName: '东莞理工学院' },
        { imgName: 'lnsf', routerName: 'schoolHome', schoolName: '岭南师范学院', params: { name: 'lnsf' } },
        // { imgName: 'zk', routerName: 'schoolHome', schoolName: '仲恺农业工程学院', params: { name: 'zknygc' } },
        { imgName: 'jyxy', routerName: 'schoolHome', schoolName: '嘉应学院', params: { name: 'jy' } },
        // { imgName: 'sqxy', routerName: 'forward', schoolName: '肇庆学院', schoolId: 'sq' },
        // { imgName: 'zqxy', routerName: 'schoolHome', schoolName: '肇庆学院', params: { name: 'zqxy' } },
        // { imgName: 'gzzyy', routerName: 'schoolHome', schoolName: '广州中医药大学', params: { name: 'gzzyy' } },
        // { imgName: 'jr', routerName: 'schoolHome', schoolName: '广东金融学院', params: { name: 'jr' } },
      ],
      pfsn1School: [
        { imgName: 'gzrj', routerName: 'schoolHome', schoolName: '广州软件学院', params: { name: 'gzrj' } },
        { imgName: 'gzs', routerName: 'schoolHome', schoolName: '广州商学院', params: { name: 'gzs' } },
        { imgName: 'zqxy', routerName: 'schoolHome', schoolName: '肇庆学院', params: { name: 'zqxy' } },
        // { imgName: 'fzs', routerName: 'gccScholarship', schoolName: '广州商学院' },
        // { imgName: 'jr', routerName: 'gdjrUniversityForward.home', schoolName: '广东金融学院' },
        // { imgName: 'gzhl', routerName: 'schoolHome', schoolName: '广州华立学院', params: { name: 'gzhl' } },
        { imgName: 'dgcs', routerName: 'schoolHome', schoolName: '东莞城市学院', params: { name: 'dgcs' } },
        { imgName: 'zhkj', routerName: 'schoolHome', schoolName: '珠海科技学院', params: { name: 'zhkj' } },
        { imgName: 'gdxhxy', routerName: 'schoolHome', schoolName: '广州新华学院', params: { name: 'gdxhxy' } },
      ], // 仅本科院校
      pfsn5School: [
        { imgName: 'gzcj', routerName: 'enrollmentHomepage', schoolName: '广州城建职业学院' },
        // { imgName: 'jr', routerName: 'gdJinRong', schoolName: '广东金融学院' },
        { imgName: 'gzkf', routerName: 'guangzhouOpenUniversity', schoolName: '广州开放大学' },
        // { imgName: 'gdkm', routerName: 'schoolHome', schoolName: '广东科贸职业学院', params: { name: 'gdkmzy' } },
        // { imgName: 'dgzj', routerName: 'schoolHome', schoolName: '东莞职业技术学院', params: { name: 'dgzyjs' } },
        // { imgName: 'gdlnzj', routerName: 'gdLinNan', schoolName: '广东岭南职业技术学院' },
        // { imgName: 'qyzj', routerName: 'forward', schoolName: '清远职业技术学院', schoolId: 'qy' },
        { imgName: 'qyzy', routerName: 'schoolHome', schoolName: '清远职业技术学院', params: { name: 'qyzy' } },
        // { imgName: 'mmzj', routerName: 'schoolHome', schoolName: '茂名职业技术学院', params: { name: 'mmzyjs' } },
        { imgName: 'swzj', routerName: 'shanweiInstituteTechnology', schoolName: '汕尾职业技术学院' },
        // { imgName: 'sszy', routerName: 'ssProfressionUniversityForward', schoolName: '广东松山职业技术学院' },
        // { imgName: 'jtlogo', routerName: 'schoolHome', schoolName: '广东交通职业技术学院', params: { name: 'gdjtzy' } },
        // { imgName: 'gdjdzyLogo', routerName: 'jdProfressionUniversityForward.home', schoolName: '广东机电职业技术学院' },
        // { imgName: 'gdjdzyLogo', routerName: 'schoolHome', schoolName: '广东机电职业技术学院', params: { name: 'gdjdzy' } },
        { imgName: 'hxzy', routerName: 'schoolHome', schoolName: '广州华夏职业学院', params: { name: 'hxzy' } },
        { imgName: 'gdsszy', routerName: 'schoolHome', schoolName: '广东松山职业技术学院', params: { name: 'gdsszy' } },
        { imgName: 'gdstgczy', routerName: 'schoolHome', schoolName: '广东生态工程职业学院', params: { name: 'gdstgczy' } },
        // { imgName: 'dgzyjs', routerName: 'schoolHome', schoolName: '东莞职业技术学院', params: { name: 'dgzyjs' } },
        { imgName: 'nhcr', routerName: 'schoolHome', schoolName: '南海成人学院', params: { name: 'nhcr' } },
        { imgName: 'gzrp', routerName: 'schoolHome', schoolName: '广州铁路职业技术学院', params: { name: 'gzrp' } },
        { imgName: 'gzhsvc', routerName: 'schoolHome', schoolName: '广州华商职业学院', params: { name: 'gzhsvc' } },
        // { imgName: 'gdiei', routerName: 'schoolHome', schoolName: '广东信息工程职业学院', params: { name: 'gdiei' } },
        // { imgName: 'gdposat', routerName: 'schoolHome', schoolName: ' 广东科学技术职业学院', params: { name: 'gdposat' } },
        { imgName: 'gzkjzy', routerName: 'schoolHome', schoolName: '广州科技职业技术大学', params: { name: 'gzkjzy' } },
      ], // 仅专科院校
      pfsnList: [], // 专业列表
      searchPfsnName: '',
      isLoadAllPfsn: false,
      searchPfsnList: [],
    };
  },
  computed: {
    schoolLogoList () {
      let school = [];
      if (!this.pfsnLevel) {
        school = [...this.defaultSchool, ...this.pfsn1School, ...this.pfsn5School];
      } else {
        school = this.pfsnLevel == 1 ? this.pfsn1School : this.pfsn5School;
      }
      const list = this.defaultSchool.concat(school);
      return [].concat(list);
    },
  },
  mounted () {
    this.findPfsnByActId();
  },
  methods: {
    showMore (which) {
      const key = {
        city: 'clickCity',
        school: 'clickSchool',
        pfsn: 'clickPfsn',
      };
      this.setStatistic(key[which]);
      if (which == 'city') {
        this.cityHide = !this.cityHide;
      }
      if (which == 'school') {
        this.schoolHide = !this.schoolHide;
      }
      if (which == 'pfsn') {
        this.pfsnShow = true;
      }
    },
    setStatistic (key) {
      const pfsnLevel = this.pfsnLevel;
      const secondStatistic = statistic.secondMain[pfsnLevel][0];
      this.$yzStatistic(`marketing.base.click`, secondStatistic[key].id, secondStatistic[key].name);
    },
    schoolClick (item) {
      console.log(item,'item');
      this.setStatistic('clickSchool');
      const params = item.params || {};
      const query = {
        inviteId: this.inviteId,
        pfsnLevel: this.pfsnLevel,
        regOrigin: this.regOrigin,
        regChannel: this.regChannel,
      };
      if (item.schoolId) {
        query.schoolId = item.schoolId;
      }

      // 广东松山职业学院 奖学金活动 2023年
      if (item.imgName == 'gdsszy') {
        return this.$router.push({
          path: '/active/signActSongShan2023',
          params,
          query
        })
      }
      // 广州软件学院 奖学金活动
      if (item.imgName == 'gzrj') {
        return this.$router.push({
          path: '/active/signActGuangRuan',
          params,
          query
        })
      }
      
      this.$router.push({
        name: item.routerName,
        params,
        query
      });
    },
    toBuildInfo (id) {
      this.setStatistic('clickCity');
      this.$router.push({
        path: "/active/dreamBuild/newDreamBuildInfo/" + id,
        query: {
          inviteId: this.inviteId,
          pfsnLevel: this.pfsnLevel,
          regOrigin: this.regOrigin,
          regChannel: this.regChannel,
          city:id
        }
      });
    },
    toList (item) {
      this.setStatistic('clickPfsn');
      this.$router.push({
        name: 'secondMain.adMajor',
        query: {
          pfsnId: item.pfsnId,
          pfsnName: item.pfsnName,
          inviteId: this.inviteId,
          actName: this.actName,
          scholarship: this.scholarship,
          pfsnLevel: this.pfsnLevel,
          regOrigin: this.regOrigin,
          regChannel: this.regChannel,
        }
      });
    },
    search () {
      this.findPfsnByActId(this.searchPfsnName);
    },
    closePop () {
      this.pfsnShow = false;
    },
    async findPfsnByActId (pfsnName = '') {
      const { body, code } = await this.$http.post('/mkt/findPfsnByActId/1.0/', { actId: 164, pfsnLevel: this.pfsnLevel, pfsnName });
      if (code == '00') {
        if (!this.isLoadAllPfsn) {
          this.pfsnList = body || [];
          this.isLoadAllPfsn = true;
        }
        this.searchPfsnList = body || [];
      }
    },
  },
};
</script>

<style lang="less">
.yz-adult-education {
  &.fixed {
    margin-top: 0.5rem;
  }
  .enroll-methods {
    padding: 0.15rem 0.15rem 0.1rem;
    background: #f2f2f2;
  }
  .red-title {
    background: linear-gradient(339deg, rgba(200, 0, 2, 0) 0%, #c80002 100%);
    height: 0.32rem;
    line-height: 0.32rem;
    color: #fff;
    font-size: 0.15rem;
    font-weight: 500;
    padding-left: 0.1rem;
    width: 2.36rem;
    position: relative;
    margin-top: 0.15rem;
    &.nomt {
      margin-top: 0;
    }
    &::after {
      content: '';
      position: absolute;
      width: 0.32rem;
      height: 0.32rem;
      transform: skewX(-45deg);
      background: #f2f2f2;
      right: -0.16rem;
    }
  }
  .white-item-ul {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0.15rem;
    &.school {
      padding-top: 0.04rem;
      li {
        width: 0.64rem;
        height: 0.76rem;
        border-radius: 0.02rem;
        margin-top: 0.06rem;
        font-size: 0.09rem;
        margin-right: 0.06rem;
        padding: 0.05rem;
        display: inline-block;
        &:nth-child(5n) {
          margin-right: 0;
        }
      }
    }
    &.city {
      margin-bottom: 0.05rem;
      li {
        width: 0.61rem;
        height: 0.34rem;
        color: #000000;
        font-size: 0.16rem;
        padding: 0;
        &:nth-child(5n) {
          margin-right: 0;
        }
      }
    }
    &.pfsn {
      li {
        line-height: 1.4;
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
    li {
      background: #ffffff;
      box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.3);
      border-radius: 0.05rem;
      width: 1.08rem;
      height: 0.36rem;
      padding: 0.03rem 0.08rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      color: #414141;
      font-size: 0.15rem;
      margin-top: 0.1rem;
      line-height: 1.2;
      margin-right: 0.1rem;
      word-break: break-all;
      overflow: hidden;
      &.small {
        font-size: 0.12rem;
      }
      &.mr0 {
        margin-right: 0;
      }
      &.red {
        background: rgba(217, 50, 55, 0.1);
        border: 1px solid rgba(217, 50, 55, 0.2);
        color: #d93237;
        font-size: 0.14rem;
        box-shadow: none;
        display: flex;
        align-items: center;
        justify-content: center;
        &.open {
          .arrow {
            transform: rotate(0deg);
          }
        }
        .arrow {
          transition: all 0.2s;
          width: 0.1rem;
          height: 0.1rem;
          margin-left: 0.04rem;
          transform: rotate(180deg);
        }
      }
      .school-logo {
        width: 100%;
        text-align: center;
        .logo {
          width: 0.4rem;
          height: 0.4rem;
          vertical-align: middle;
        }
      }
      .school-name {
        margin-top: 0.04rem;
        width: 100%;
        text-align: center;
      }
    }
  }
  .brief-introduction {
    padding-bottom: 0.2rem;
    border-bottom: 0.1rem solid #f2f2f2;
    .t1 {
      text-indent: 0.2rem;
      padding: 0.17rem 0.15rem;
      font-size: 0.13rem;
    }
    .ad-img1 {
      width: 3.55rem;
      height: 2.64rem;
      margin-left: 0.1rem;
    }
    .ad-img2 {
      width: 3.45rem;
      height: 4.45rem;
      margin-left: 0.15rem;
      margin-top: 0.3rem;
    }
  }
  .van-popup {
    width: 3.55rem;
    height: 4.15rem;
    border-radius: 0.05rem;
    padding: 0.15rem;

    .close {
      width: 0.32rem;
      height: 0.32rem;
      position: absolute;
      right: 0.05rem;
      top: 0.05rem;
    }
    .pop-title {
      margin-bottom: 0.15rem;
      font-size: 0.15rem;
    }
    .input-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 0.1rem;
      .btn {
        width: 0.64rem;
        height: 0.3rem;
        background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
        border-radius: 0.05rem;
        text-align: center;
        color: #fff;
      }
    }
    .search-input {
      height: 0.3rem;
      flex: 1;
      margin-right: 0.1rem;
      border-radius: 0.05rem;
      border: 1px solid #ededed;
      font-size: 0.13rem;
      line-height: 0.3rem;
      padding-left: 0.1rem;
    }
    .pop-pfsn-list {
      max-height: 3.15rem;
      width: 100%;
      overflow: auto;
      padding-top: 0.1rem;
      display: flex;
      flex-wrap: wrap;
      button {
        height: 0.36rem;
        width: 1.02rem;
        border: 0;
        text-align: center;
        background: #f5f5f5;
        font-size: 0.14rem;
        color: #414141;
        line-height: 1.4;
        overflow: hidden;
        padding: 0.02rem 0.06rem;
        margin-bottom: 0.1rem;
        border-radius: 0.05rem;
        word-break: break-all;
        &.small {
          font-size: 0.12rem;
        }
        &:not(:nth-child(3n)) {
          margin-right: 0.09rem;
        }
      }
    }
  }
}
</style>
