<template>
    <div class="yz-down-app" v-if="downShow">
      <div class="left-box">
        <img src="../assets/image/ic_register_logo.png" alt="" />
        <span> 直播回放、随堂测试、复习资源下载等丰富功能等你来体验~</span>
      </div>
      <span class='down' @click="downApp" >下载APP</span>
    </div>
  </template>
  <script>
  import {downloadApp } from '@/common'
  export default {
    data() {
      return {
        downShow: true,
      };
    },
    props: {
      eventName: {
        type: String,
        default: "downapp",
      },
      show: {
        type: Boolean,
        default: false,
      },
    },
    mounted() {
      this.getStuInfo();
    },
    methods: {
    downApp () {
      downloadApp()
    },        
      getStuInfo() {
        if (!this.show) {
          const learnId = this.storage.getItem("learnId");
          this.$http.post("/mkt/stdLearnInfo/1.0/").then((res) => {
            if (res.code == "00" && res.body) {
              const current = res.body.learnInfos.find(
                (item) => item.stdStage != 10 && item.learnId == learnId
              );
              this.downShow = !(
                current.hasUnpaid == 1 && !["10", "8"].includes(current.stdStage)
              );
            }
          });
        }
      },
    },
  };
  </script>
  <style lang="less" scoped>
  .yz-down-app {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    z-index: 2;
    padding: 0.08rem 0.1rem;
    border-top: 1px solid #edebeb;
    .left-box {
      display: flex;
      align-items: center;
      width: 2.75rem;
      font-size: 0.12rem;
      img {
        width: 0.5rem;
        height: 0.5rem;
        margin-right: 0.1rem;
      }
    }
    .down {
      width: 80px;
      height: 30px;
      font-size: 12px;
      font-weight: 600;
      border-radius: 50px;
      background-image: linear-gradient(#f09190, #f07877, #f06e6c);
      color: #fff;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
    }  
  }
  #launch-btn {
    position: absolute;
    top: .2rem;
    left: 2.8rem;
    right: 0;
    bottom: 0;
  }
  </style>
  