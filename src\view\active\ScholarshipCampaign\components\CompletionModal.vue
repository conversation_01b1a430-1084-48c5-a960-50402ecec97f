<template>
  <div>
    <!-- 双打卡完成后弹框 -->
    <van-popup
      round
      v-model="allCompletionVisible"
      @click-overlay="handleSaveClickOpt('allStatus')"
    >
      <div class="canvas-box-all" v-show="canvasShow == true">
        <img class="canvasImg" :src="canvasUrl" alt />
        <p>长按保存图片</p>
      </div>
      <div v-show="canvasShow == false" class="completed-box" id="all-sign">
        <img
          class="img-bg"
          src="../../../../assets/image/active/signAct/prove-bgc2.png"
          alt
        />
        <div class="avatar">
          <div class="head-box">
            <img
              v-if="myData.head_img"
              class="head-img"
              :src="myData.head_img"
              alt
              crossorigin="anonymous"
            />
            <img
              class="head-img-bottom"
              src="../../../../assets/image/active/signAct/chick.png"
              alt
            />
          </div>
        </div>
        <div class="real-name">{{ myData.real_name }}同学</div>
        <div class="blessing-text">
          恭喜您完成全部挑战，成功激活
          <span class="money">{{ scholarshipAmountText }}</span>
          奖学金，感谢您选择与远智教育携手上进！
        </div>
        <div class="img-box">
          <div class="flag">已激活</div>
          <img
            class="coupon"
            :src="`https://static.yzou.cn/zmc/${cdnPath}/coupons.png`"
            alt
          />
        </div>
        <div class="sign-box">
          <div class="top">
            <span>校长：</span>
            <img src="../../../../assets/image/active/signAct/sign.png" alt />
          </div>
          <p>{{ successTime | formatDate("yyyy年MM月dd日") }}</p>
        </div>
      </div>
      <button
        v-show="canvasShow == false"
        class="save-btn"
        style="
          position: absolute;
          bottom: -50px;
          left: 50%;
          transform: translateX(-50%);
        "
        @click="handleSaveImg"
      >
        保存图片
      </button>
    </van-popup>

    <!-- 跑步完成后弹框 -->
    <van-popup
      round
      v-model="runCompletionVisible"
      @click-overlay="handleSaveClickOpt('runningStatus')"
    >
      <div class="canvas-box" v-show="canvasShow == true">
        <div class="main-box">
          <img class="canvasImg" :src="canvasUrl" alt />
        </div>
        <p>长按保存图片</p>
      </div>
      <div class="completed-run" v-show="canvasShow == false">
        <div class="main-box" id="run-sign">
          <img
            src="../../../../assets/image/active/signAct/running-sign.png"
            alt
          />
          <p>恭喜{{ myData.real_name }}同学</p>
          <p>您已经完成「21天跑步」挑战！</p>
        </div>
        <button @click="handleSaveRunImg">保存图片</button>
        <p
          style="
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          "
        >
          *您还有学习的挑战要完成哦，切勿提前松懈下来～
        </p>
      </div>
    </van-popup>

    <!-- 学习完成后弹框 -->
    <van-popup
      round
      v-model="studyCompletionVisible"
      @click-overlay="handleSaveClickOpt('studyStatus')"
    >
      <div class="canvas-box" v-show="canvasShow == true">
        <div class="main-box">
          <img class="canvasImg" :src="canvasUrl" alt />
        </div>
        <p>长按保存图片</p>
      </div>
      <div class="completed-run" v-show="canvasShow == false">
        <div class="main-box" id="study-sign">
          <img src="../../../../assets/image/active/signAct/study-sign.png" alt />
          <p>恭喜{{ myData.real_name }}同学</p>
          <p>您已经完成「21天学习」挑战！</p>
        </div>
        <button @click="handleSaveStudyImg">保存图片</button>
        <p
          style="
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          "
        >
          *您还有跑步的挑战要完成哦，切勿提前松懈下来～
        </p>
      </div>
    </van-popup>
  </div>
</template>

<script>
import html2canvas from "html2canvas"

export default {
  name: 'CompletionModal',
  props: {
    // 显示状态
    signShow: {
      type: Boolean,
      default: false
    },
    runSign: {
      type: Boolean,
      default: false
    },
    studySign: {
      type: Boolean,
      default: false
    },
    // 数据
    myData: {
      type: Object,
      default: () => ({})
    },
    scholarshipAmountText: {
      type: String,
      default: ''
    },
    cdnPath: {
      type: String,
      default: ''
    },
    successTime: {
      type: [String, Number, Date],
      default: ''
    }
  },
  data() {
    return {
      canvasShow: false,
      canvasUrl: ''
    }
  },
  computed: {
    allCompletionVisible: {
      get() {
        return this.signShow
      },
      set(val) {
        this.$emit('update:signShow', val)
      }
    },
    runCompletionVisible: {
      get() {
        return this.runSign
      },
      set(val) {
        this.$emit('update:runSign', val)
      }
    },
    studyCompletionVisible: {
      get() {
        return this.studySign
      },
      set(val) {
        this.$emit('update:studySign', val)
      }
    }
  },
  methods: {
    handleSaveClickOpt(name) {
      this.$emit('save-click-opt', name)
    },
    handleSaveStudyImg() {
      this.$emit('save-click-opt', 'studyStatus')
      this.$nextTick(() => {
        html2canvas(document.getElementById("study-sign"), {
          width: document.getElementById("study-sign").offsetWidth,
          height: document.getElementById("study-sign").offsetHeight,
          useCORS: true,
        }).then((canvas) => {
          this.canvasUrl = canvas.toDataURL()
          this.canvasShow = true
        })
      })
    },
    handleSaveRunImg() {
      this.$emit('save-click-opt', 'runningStatus')
      this.$nextTick(() => {
        html2canvas(document.getElementById("run-sign"), {
          width: document.getElementById("run-sign").offsetWidth,
          height: document.getElementById("run-sign").offsetHeight,
          useCORS: true,
        }).then((canvas) => {
          this.canvasUrl = canvas.toDataURL()
          this.canvasShow = true
        })
      })
    },
    handleSaveImg() {
      this.$emit('save-click-opt', 'allStatus')
      const targetDom = document.querySelector(".completed-box")
      const copyDom = targetDom.cloneNode(true)
      copyDom.style.width = targetDom.offsetWidth + "px"
      copyDom.style.height = targetDom.offsetHeight + "px"
      document.body.appendChild(copyDom)
      this.$nextTick(() => {
        html2canvas(copyDom, {
          useCORS: true,
          width: targetDom.offsetWidth,
          height: targetDom.offsetHeight,
        }).then((canvas) => {
          document.body.removeChild(copyDom)
          this.canvasUrl = canvas.toDataURL()
          this.canvasShow = true
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.canvas-box-all {
  width: 335 * @rem;
  height: 485 * @rem;
  position: relative;

  .canvasImg {
    width: 335 * @rem;
    height: 485 * @rem;
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;
  }

  p {
    position: absolute;
    bottom: -30 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 22 * @rem;
  }
}

.canvas-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 100%;
  padding: 0.13rem 0.13rem;

  .main-box {
    position: relative;

    .canvasImg {
      width: 100%;
      height: 3.63rem;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  p {
    position: absolute;
    bottom: -35 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 22 * @rem;
  }
}

.completed-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 3.35rem;
  height: 4.85rem;
  background-size: 100% 100%;
  position: relative;

  .img-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }

  .avatar {
    margin-left: auto;
    margin-right: 0.19rem;
    margin-top: 0.44rem;
    display: flex;
    justify-content: center;
    width: 1.1464rem;
    height: 1.0011rem;
    background: url(../../../../assets/image/active/signAct/medal2.png) no-repeat;
    background-size: 100% 100%;
    padding-top: 0.02rem;
    box-sizing: border-box;

    .head-box {
      width: 0.8rem;
      height: 0.8rem;
      box-shadow: 0 0.02rem 0.1rem 0 rgba(0, 0, 0, 0.1);
      border: 0.02rem solid #ffffff;
      border-radius: 50%;
      position: relative;
      overflow: hidden;

      .head-img {
        position: relative;
        width: 100%;
        height: 100%;
        display: block;
        z-index: 2;
        border-radius: 50%;
      }
      .head-img-bottom {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: block;
        z-index: 1;
        border-radius: 50%;
      }
    }
  }

  .real-name {
    width: 100%;
    padding-left: 0.26rem;
    font-size: 0.18rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 0.25rem;
  }

  .blessing-text {
    margin-top: 0.09rem;
    width: 2.83rem;
    height: 0.48rem;
    font-size: 0.14rem;
    color: #585858;
    line-height: 0.24rem;
    .money {
      color: #cc2725;
    }
  }

  .img-box {
    margin-top: 0.2rem;
    position: relative;
    width: 3rem;
    height: 0.92rem;
    .flag {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 0.47rem;
      height: 0.18rem;
      background: #cc2725;
      border-radius: 0.04rem 0 0.09rem 0;
      font-size: 0.12rem;
      font-weight: 500;
      color: #fff;
    }

    .coupon {
      width: 100%;
      height: 100%;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  .sign-box {
    margin-top: 0.19rem;
    margin-left: auto;
    margin-right: 0.26rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .top {
      display: flex;
      align-items: center;

      span {
        font-size: 0.14rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }

      img {
        width: 0.6rem;
        height: 0.7rem;
      }
    }

    p {
      font-size: 0.14rem;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #333333;
      line-height: 0.2rem;
    }
  }
}

.save-btn {
  width: 112 * @rem;
  height: 36 * @rem;
  background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
  border-radius: 18 * @rem;
  font-size: 14 * @rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  position: absolute;
  bottom: -55 * @rem;
  left: 50%;
  transform: translateX(-50%);
  border: none;
}

.completed-run {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 456 * @rem;
  background: #f8f7f7;
  border-radius: 4 * @rem;
  padding: 13 * @rem 13 * @rem;

  .main-box {
    width: 100%;
    height: 363 * @rem;
    background-color: #fff;

    img {
      width: 100%;
      height: 294 * @rem;
      margin-bottom: 13 * @rem;
    }

    p {
      margin-left: 11 * @rem;
      font-size: 12 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #000000;
      line-height: 17 * @rem;
    }
  }

  button {
    margin-top: 24 * @rem;
    width: 112 * @rem;
    height: 36 * @rem;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    border-radius: 18 * @rem;
    font-size: 14 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    border: none;
  }

  .save-tips {
    text-align: center;
    width: 100%;
    position: absolute;
    bottom: -30 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }
}
</style>
