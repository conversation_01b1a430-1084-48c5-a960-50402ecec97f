export default [
  {
    path: '/aspirantUniversity',
    name: 'aspirantUniversity',
    component: () => import('../view/aspirantUniversity/index'),
    meta: { title: '上进学社' },
  },
  {
    path: '/aspirantUniversity/aspirantCard',
    name: 'aspirantCard',
    component: () => import('../view/aspirantUniversity/aspirantCards'),
    meta: { title: '上进会员' },
  },
  {
    path: '/aspirantUniversity/classSelect',
    name: 'classSelect',
    component: () => import('../view/aspirantUniversity/classSelect'),
    meta: { title: '课程分类', keepAlive: true },
  },
  {
    path: '/aspirantUniversity/costPay',
    name: 'aspirantUniversityCostPay',
    component: () => import('../view/aspirantUniversity/costPay'),
    meta: { title: '费用缴纳' },
  },
  {
    path: '/aspirantUniversity/learningCenter',
    name: 'learningCenter',
    component: () => import('../view/aspirantUniversity/learningCenter'),
    meta: { title: '学习中心', requiresAuth: true, keepAlive: true },
  },
  {
    path: '/aspirantUniversity/studyCardAgree',
    name: 'studyCardAgree',
    component: () => import('../view/aspirantUniversity/studyCardAgree'),
    meta: { title: '学霸卡协议' },
  },
  {
    path: '/aspirantUniversity/plusAgreePrev',
    name: 'plusAgreePrev',
    component: () => import('../view/aspirantUniversity/plusAgreePrev'),
    meta: { title: '学霸卡协议' },
  },
  {
    path: '/aspirantUniversity/selectDetail',
    name: 'selectDetail',
    component: () => import('../view/aspirantUniversity/selectDetail'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/courseDetails',
    name: 'courseDetails',
    component: () => import('../view/aspirantUniversity/courseDetails'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/setmeal',
    name: 'setmeal',
    component: () => import('../view/aspirantUniversity/setmeal'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/classDetail',
    name: 'classDetail',
    component: () => import('../view/aspirantUniversity/classDetail'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/PurchaseRecords',
    name: 'PurchaseRecords',
    component: () => import('../view/aspirantUniversity/PurchaseRecords'),
    meta: { title: '购买记录' },
  },
  {
    path: '/aspirantUniversity/fire',
    name: 'fire',
    component: () => import('../view/aspirantUniversity/fire'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/freeVideo',
    name: 'freeVideo',
    component: () => import('../view/aspirantUniversity/freeVideo'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/postComment',
    name: 'postComment',
    component: () => import('../view/aspirantUniversity/postComment'),
    meta: { title: '上课心得' },
  },
  {
    path: '/aspirantUniversity/readBooks',
    name: 'readBooks',
    component: () => import('../view/aspirantUniversity/readBooks/index'),
    meta: {
      title: '读书',
    },
  },
  {
    path: '/aspirantUniversity/bookDetails',
    name: 'bookDetails',
    component: () =>
      import('../view/aspirantUniversity/readBooks/book-details'),
    meta: { title: '详情' },
  },
  {
    path: '/aspirantUniversity/bookNotice',
    name: 'bookNotice',
    component: () => import('../view/aspirantUniversity/readBooks/book-notice'),
    meta: { title: '课前预告' },
  },
  {
    path: '/aspirantUniversity/groupChat',
    name: 'groupChat',
    component: () => import('../view/aspirantUniversity/readBooks/group-chat'),
    meta: { title: '加入班级群' },
  },
  {
    path: '/aspirantUniversity/learnCalendar',
    name: 'learnCalendar',
    component: () =>
      import('../view/aspirantUniversity/readBooks/learn-calendar'),
    meta: { title: '学习日历' },
  },
  {
    path: '/aspirantUniversity/bookCatalogue',
    name: 'bookCatalogue',
    component: () =>
      import('../view/aspirantUniversity/readBooks/book-catalogue'),
    meta: { title: '课程目录' },
  },
  {
    path: '/aspirantUniversity/commentBook',
    name: 'commentBook',
    component: () =>
      import('../view/aspirantUniversity/readBooks/comment-book'),
    meta: { title: '上课心得' },
  },
  {
    path: '/aspirantUniversity/listenBook',
    name: 'listenBook',
    component: () => import('../view/aspirantUniversity/readBooks/listen-book'),
    meta: { title: '读书计划' },
  },
  {
    path: '/aspirantUniversity/wxCheckShare',
    name: 'wxCheckShare',
    component: () =>
      import('../view/aspirantUniversity/readBooks/wx-check-share'),
    meta: { title: '读书计划' },
  },
  {
    path: '/aspirantUniversity/graduationCompletion',
    name: 'graduationCompletion',
    component: () =>
      import('../view/aspirantUniversity/readBooks/graduation-completion'),
    meta: { title: '结业证书' },
  },
  {
    path: '/aspirantUniversity/howClock',
    name: 'howClock',
    component: () => import('../view/aspirantUniversity/readBooks/how-clock'),
    meta: { title: '如何打卡' },
  },
  {
    path: '/aspirantUniversity/goods/:id',
    component: () => import('../view/aspirantUniversity/goods'),
  },
  {
    path: '/aspirantUniversity/voucher',
    name: 'voucher',
    component: () => import('../view/aspirantUniversity/voucher/index'),
    meta: { title: '优惠券' },
  },
  {
    path: '/aspirantUniversity/voucher/share',
    name: 'voucherShare',
    component: () => import('../view/aspirantUniversity/voucher/share'),
    meta: { title: '上进学社优惠券' },
  },
  {
    path: '/aspirantUniversity/singleBook',
    name: 'singleBook',
    component: () => import('../view/aspirantUniversity/readBooks/single-book'),
    meta: { title: '读书计划' },
  },
  {
    path: '/aspirantUniversity/courseLearning',
    name: 'courseLearning',
    component: () => import('../view/aspirantUniversity/courseLearning'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/lecture',
    name: 'lecture',
    component: () => import('../view/aspirantUniversity/lecture'),
    meta: { title: '课程详情' },
  },
  {
    path: '/aspirantUniversity/addTeachEqcode',
    name: 'addTeachEqcode',
    component: () => import('../view/aspirantUniversity/addTeachEqcode'),
    meta: { title: '添加微信' },
  },
  {
    path: '/aspirantUniversity/trainingCamp',
    name: 'trainingCamp',
    component: () =>
      import('../view/aspirantUniversity/trainingCamp/trainingCamp'),
    meta: { title: '训练营' },
  },
  {
    path: '/aspirantUniversity/examWarn',
    name: 'examWarn',
    component: () =>
      import('../view/aspirantUniversity/exercise/exam/examWarn'),
    meta: { title: '开始考试' },
  },
  {
    path: '/aspirantUniversity/testPaper',
    name: 'testPaper',
    component: () =>
      import('../view/aspirantUniversity/exercise/exam/testPaper'),
    meta: { title: '考试中' },
  },
  {
    path: '/aspirantUniversity/paperResult',
    name: 'paperResult',
    component: () =>
      import('../view/aspirantUniversity/exercise/exam/paperResult'),
    meta: { title: '查看试卷' },
  },
  {
    path: '/aspirantUniversity/campDetail',
    name: 'campDetail',
    component: () => import('../view/aspirantUniversity/campDetail'),
    meta: { title: '训练营详情' },
  },
  {
    path: '/aspirantUniversity/topicAnalysis',
    name: 'topicAnalysis',
    component: () =>
      import('../view/aspirantUniversity/exercise/exam/topicAnalysis'),
    meta: { title: '错题解析' },
  },
  {
    path: '/aspirantUniversity/fission/order',
    name: 'fissionOrder',
    component: () => import('../view/aspirantUniversity/fission/order'),
    meta: { title: '分享赚' },
  },
  {
    path: '/aspirantUniversity/fission/settleAccounts',
    name: 'fissionSettleAccounts',
    component: () =>
      import('../view/aspirantUniversity/fission/settleAccounts'),
    meta: { title: '历史结算' },
  },
  {
    path: '/a/:code',
    component: () => import('../view/aspirantUniversity/shortLink'),
    meta: { title: '上进大学' },
  },
  {
    path: '/aspirantUniversity/learning',
    component: () => import('../view/aspirantUniversity/learning/index'),
    meta: { title: '学习', keepAlive: true, requiresAuth: true },
  },
  {
    path: '/aspirantUniversity/serve/order',
    component: () =>
      import('../view/aspirantUniversity/serve/orderModule/orderList'),
    meta: { title: '订单' },
  },
  {
    path: '/aspirantUniversity/serve/order/details',
    name: 'orderDetails',
    component: () =>
      import('../view/aspirantUniversity/serve/orderModule/orderDetails'),
    meta: { title: '订单详情' },
  },
  {
    path: '/aspirantUniversity/distribute',
    name: 'distribute',
    component: () => import('../view/aspirantUniversity/fission/material'),
    meta: { title: '分享素材' },
  },
  {
    path: '/aspirantUniversity/instalment',
    name: 'aspirantInstalment',
    component: () => import('../view/aspirantUniversity/instalment'),
    meta: {
      title: '分期贷款',
    },
  },
  {
    path: '/aspirantUniversity/xjinstalment',
    name: 'aspirantXJInstalment',
    component: () => import('../view/aspirantUniversity/instalment/instalment.vue'),
    meta: {
      title: '分期贷款',
    },
  },
  {
    path: '/aspirantUniversity/updataUserInfo',
    name: 'aspirantUpdataUserInfo',
    component: () =>
      import('../view/aspirantUniversity/instalment/updataUserInfo'),
    meta: { title: '修改个人信息' },
  },
  {
    path: '/pay/aspInstalmentPay',
    name: 'aspirantInstalmentDownPayment',
    component: () =>
      import('../view/aspirantUniversity/instalment/pay'),
    meta: { title: '我要缴费' },
    children: [
      {
        path: "alipay",
        name: "alipaymentCoustomPay",
        component: () => import("../view/student/home/<USER>"),
        meta: {
          title: "支付宝支付",
          requiresAuth: false,
        },
      },
    ],
  },
  {
    path: '/aspirantUniversity/lastApply',
    name: 'aspirantStagedLastApply',
    component: () =>
      import('../view/aspirantUniversity/instalment/lastApply'),
    meta: { title: '分期贷款' },
  },
//   {
//     path: '/aspirantUniversity/agreement',
//     name: 'aspirantAgreement',
//     component: () =>
//       import('../view/aspirantUniversity/instalment/agreement'),
//     meta: { title: '协议' },
//   },
  {
    path: '/aspirantUniversity/agreementCourse',
    name: 'agreementCourse',
    component: () =>
      import('../view/aspirantUniversity/agreementCourse'),
    meta: { title: '培训协议' },
  },
]
