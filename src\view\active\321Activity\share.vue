<template>
    <div class="share_wrap">
      <div class="info" :class="{active:inviteId}">
        <img class="advance" src="../../../assets/image/active/321Activity/advance.png" alt="">
        <div class="num">
          已有{{joinNum}}人参与</div>
        <div class="info_detail">
          <img :src="info.headImg | defaultAvatar" alt="">
          <div class="txt">
            <p>{{info.nikeName||'' |b64DecodeUnicode}}</p>
            <h2>我是第{{info.udNum}}位上进者</h2>
          </div>
        </div>
        <div class="advance_img" :class="{active:info.udType=='2'}">
          <img class="txt" src="../../../assets/image/active/321Activity/321_advance.png" alt="">
          <div class="audio" v-if="info.udType=='1'" @click="play(info.udType,i,info.udId)">{{info.resourcesTime}}
            <img src="../../../assets/image/active/321Activity/''@2x.png" alt="">
            <img src="../../../assets/image/active/321Activity/audio_icon.png" alt="" v-if="info.state!='play'">
            <img src="../../../assets/image/active/321Activity/audio.gif" alt="" v-else>
            <audio :src="info.resourcesUrl|imgBaseURL" ref="media"></audio>
          </div>

          <div class="video" v-if="info.udType=='2'" @click="play(info.udType,info.udId)">
            <template v-if="info.state!='play'">
              <div class="bg"></div>
              <img :src="info.resourcesUrl+'?x-oss-process=video/snapshot,t_1000,f_jpg' | imgBaseURL" alt="">
              <img src="../../../assets/image/active/321Activity/play.png" alt="">
            </template>

            <video :src="info.resourcesUrl|imgBaseURL" ref="media"></video>
          </div>
          <div class="heart" @click="heart(info.udId,info.isPraise)">
            <img src="../../../assets/image/active/321Activity/mycert_heart.png" alt="" v-if="!info.isPraise">
            <img src="../../../assets/image/active/321Activity/mycert_heart_sel.png" alt="" v-else>
            <p>{{info.praiseNum||0}}人祝福</p>
          </div>
        </div>
        <button @click="getShare" v-if="!inviteId">生成海报</button>
      </div>
      <div class="footer" v-if="inviteId" @click="toHome">去查看活动👉</div>
      <sharePoster v-if="share"  :info="info" @cancel="share=false"  :joinNum="joinNum" />
      <share title="我在参加321上进日活动，快来围观我的上进宣言，为上进发声！" :imgUrl="imgUrl" desc="3月21日，发布上进宣言，干一件上进的事，影响更多的人一起上进。" :link="shareLink" />
    </div>
</template>

<script>
  import sharePoster from "./sharePoster"
  import share from "@/components/share"
  import {Toast} from 'vant'
    export default {
      components:{sharePoster,share},
      data(){
          return{
            share:false,
            info:{},
            joinNum:0,
            title:'',
            imgUrl:'http://yzims.oss-cn-shenzhen.aliyuncs.com/logo/321act/321logo.png',
            shareLink:'',
            showInvite:false,
            invite:{},
            inviteId:'',
            endTime:new Date(2020,2,28,0,0,0).getTime(),
            flag:true
          }
      },
      created(){
         this.getInfo()
        this.getActivityInfo()
        this.shareLink=`${window.location.origin}/active/321Activity/share?udId=${this.$route.query.udId}&ykAuthtoken=${this.storage.getItem('ykAuthtoken')}`;
         this.inviteId=this.$route.query.ykAuthtoken;
      },
      methods:{
        getInviteInfo() {
          let inviteId = (
            window.sessionStorage.getItem("inviteId") ||
            this.$route.query.inviteId ||
            decodeURIComponent(
              this.$route.query.inviteId ||
              ""
            )
          ).replace(/ /g, "+");
          if (inviteId) {
            this.$http
              .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
              .then(res => {
                let { code, body } = res;
                if (code !== "00") return;
                this.invite = body || {};
                this.showInvite = true;
              });
          }
        },
        getShare(){
          this.share=true
          MtaH5.clickStat('4')
        },
        toHome(){
          this.$router.push({name:"321Activity"})
          MtaH5.clickStat('1')
        },
          getInfo(){
            this.$http.post('/mkt/getUpDeclarationInfo/1.0/',{udId:this.$route.query.udId}).then(res=>{
              if(res.code=='00'){
                this.info=res.body
                this.title=`上进者${this.b64DecodeUnicode(this.info.nickName||'')}，邀您一起见证TA的上进宣言，为上进发声！`
              }
            })
          },
        b64DecodeUnicode(str)  {
          return decodeURIComponent(atob(str).split('').map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          }).join(''));
        },
        play(type,udId){
          //检测播放是否已暂停.audio.paused 在播放器播放时返回false.
          if(this.$refs.media.paused){
            let audio=document.getElementsByTagName('audio');
            for (let i=0;i<audio.length;i++){
              audio[i].pause();
            }
            this.$refs.media.play()
            this.info.state='play'
            this.$refs.media.addEventListener('pause',()=>{
              this.info.state='stop'
            })
          }else{
            this.$refs.media.pause()
            this.info.state='stop'
          }
        },
        modal(){
          this.$modal({message:'今年321上进日活动已经结束了哦~欢迎您明年赶早来参与上进啦！',icon:'warning'})
        },
        heart(praiseId,isPraise){
          if(this.endTime<Date.now()){
            this.modal();
            return;
          }
          if(this.flag) {
            let fabulousNum=isPraise?-1:1;
            this.flag =false;
            this.$http.post('/mkt/praiseUpDeclaration/1.0/',{praiseType:4,praiseId:praiseId,fabulousNum:fabulousNum}).then(res=>{
              if(res.code=='00'){
                this.info.isPraise=!isPraise;
                isPraise?this.info.praiseNum--:this.info.praiseNum++;
                this.flag =true;
              }
            })
          }else {
            Toas('手速慢一点!')
          }

        },
        getActivityInfo(){
          this.$http.post('/mkt/getUpDeclarationActInfo/1.0/').then(res=>{
            if(res.code=='00'){
              this.joinNum=res.body.joinNum
            }
          })
        },
      }
    }

</script>

<style scoped lang="less">
.share_wrap{
  width: 100%;
  height: auto;
  overflow: hidden;
  max-width: 640px;
  margin: 0 auto;
  background-color: rgba(194, 22, 16, 1);
  min-height: 100vh;
  .info {
    width: 100%;
    height: auto;
    overflow: hidden;
    min-height: 94vh;
    margin-top: .1rem;
    background-image: url("../../../assets/image/active/321Activity/mycert_bg.png");
    background-size: 100% 100%;
    margin-bottom: .2rem;
    padding-bottom: .22rem;
    &.active{
      padding-bottom: .82rem;
    }
    .info_detail{
      margin-top: .74rem;
      height: auto;
      overflow: hidden;
      img{
        float: left;
        margin-left: .35rem;
        width: .48rem;
        height: .48rem;
        border-radius: 50%;
      }
      .txt{
        float: left;
        margin-left: .08rem;
        p{
          font-size: .12rem;
          margin-top: .07rem;
          color: white;
        }
        h2{
          font-weight: bold;
          color: rgba(253, 188, 0, 1);
        }
      }
    }
    .advance{
      width: .66rem;
      height: .24rem;
      position: absolute;
      left:.37rem;
      top:.44rem
    }
    .num{
      color: white;
      font-size: .12rem;
      line-height: .17rem;
      position: absolute;
      top: .48rem;
      right:.34rem
    }
    .advance_img{
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 4.2rem;
      &.active{
        height: 5.25rem;
      }
      .txt{
        width: 1.93rem;
        height:.2rem;
        margin: 0 auto;
      }
      .audio{
        width: 2.24rem;
        height: .32rem;
        border-radius: .04rem;
        background-color: rgba(132, 201, 102, 1);
        font-size: .16rem;
        line-height: .32rem;
        padding-left: .13rem;
        margin: .71rem auto .15rem;
        img:first-of-type{
          width: .06rem;
          height: .06rem;
          margin-top: .04rem;
        }
        img:last-of-type{
          width: .11rem;
          height: .14rem;
          margin-top: .1rem;
          margin-left: .05rem;
        }
      }
      .video{
        float: left;
        width: 2.32rem;
        height: 3.32rem;
        position: relative;
        margin: .16rem auto;
        border: 2px solid RGBA(253, 187, 118, 1);
        border-radius: .1rem;
        .bg{
          width: 100%;
          height: 100%;
          position: absolute;
          left:0;
          top:0;
          background-color: rgba(0, 0, 0, .1);
          z-index: 2;
        }
        video{
          width: 100%;
          height: 100%;
        }
        img:first-of-type{
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: .1rem;
        }
        img:last-of-type{
          position: absolute;
          width: .32rem;
          height: .32rem;
          left: 0;
          right: 0;
          top:0;
          bottom:0;
          margin: auto;
        }
      }
      .heart{
        display: flex;
        align-items: center;
        flex-direction: column;
        margin: .27rem auto 0;
        img{
          width: .44rem;
          height: .42rem;
          margin-bottom: .05rem;
        }
        p{
          color: white;
        }
      }
    }
    button{
      width: 1.7rem;
      height: .45rem;
      line-height: .45rem;
      display: block;
      color: white;
      text-align: center;
      font-size: .16rem;
      background-image: linear-gradient(to bottom,rgba(252, 190, 95, 1),rgba(248, 137, 49, 1));
      border-radius: .28rem;
      margin: .11rem auto .2rem;
      box-shadow: 0 0 .09rem 0 rgba(235, 83, 0, 1);
      border-bottom:solid .03rem rgba(235, 83, 0, 1) ;
      border-right:solid .02rem rgba(235, 83, 0, 1) ;
    }
  }
}
.footer{
  position: fixed;
  width: 100%;
  background-color: rgba(237, 152, 66, 1);
  height: .6rem;
  line-height: .6rem;
  color: white;
  font-size: .16rem;
  bottom:0;
  left:0;
  text-align: center;
}
.invite {
  padding: 0.16rem 0;
  background-image: url("../../../assets/image/active/dreamBuild/content-bg.png");
  background-size: 100%;
  img {
    width: 0.48rem;
    height: 0.48rem;
    border-radius: 50%;
    float: left;
    margin-left: 0.24rem;
  }
  .headTxt {
    float: left;
    margin-left: 0.12rem;
    p {
      font-size: 0.14rem;
      span {
        color: #e15443;
        font-size: 0.14rem;
      }
      &:first-of-type {
        font-size: 0.14rem;
      }
    }
  }
}
.invite-top {
   background-color: #f3b422;
   height: 0.72rem;
   position: relative;
   z-index: 10;
 }
</style>
