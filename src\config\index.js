module.exports = {
  title: '智米中心',
  corpId: 'ding0247179bae2cfc7b35c2f4657eb6378f',
  // 微信appid（远智教育：wxe8c6c837e12b7e94  远智服务：wx2bef0242297fc1d9）
  appid: process.env.appId,

  // 百度地图api
  baiduApiUrl: '//api.map.baidu.com/api?v=2.0&ak=6A0hz4BxGPbPWfawkVmEmG1oVpkivrAw',

  imgPosterBaseURL: process.env.basePath,
  imgBaseURL: process.env.originPath,
  //activityTime:new Date(2019,7,1,9,30,0).getTime(),
  activityTime: new Date(2019, 8, 22, 0, 0, 0).getTime(),
  mapHTTPType: process.env.mapHTTP,
  examPath: process.env.examPath,
  articleURL: process.env.articleUrl,

  // 上传图片的
  imgBasePath: '//yzimstemp.oss-cn-shenzhen.aliyuncs.com/',

  // 微信JS-SDK
  jweixin: '//res.wx.qq.com/open/js/jweixin-1.3.2.js',
  //签名前缀
  signPath: process.env.signPath,
  // 省市区
  pcd: `${process.env.originPath}cache/pcd.js`,
  // pcd: `https://img2.yzwill.cn/cache/pcd.js`,
  dict: `${process.env.dictPath}dict.js?${Date.now()}`,

  // 智米换算比例
  zmScale: 100,

  // 智米抵扣倍数从30000改为10000
  zmMultiple: 10000,

  // 最小提现额
  minAmount: 1,

  // 招生应用ajax请求域名
  domain:  window.location.protocol + process.env.domain,

  // 员工知识库ajax请求域名
  ygzskDoMain: /Mobi|Android|iPhone/i.test(navigator.userAgent) ? window.location.protocol + process.env.domain : /wxwork/i.test(navigator.userAgent.toLowerCase())?window.location.protocol + process.env.domain:window.location.protocol + process.env.ygzskDoMain,

  // bms系统配置文件
  paramUrl: `${process.env.basePath}cache/param.json?${Date.now()}`,

  // 直播userId
  userId: '52554be7b4',
  //点播secretkey
  secretkey: 'WMduKtUm0O',
  appId: 'eynogj82jc',
  appSecret: 'd3b81fd951f14fe783df5085bce76328',
  // 直播
  playerjs: '//player.polyv.net/livescript/liveplayer.js',
  liveplayer: '//player.polyv.net/livesdk/polyv-live.min.js',
  liveBase64Js: '//livestatic.videocc.net/v_62/assets/wjs/base64.js',
  //播放器
  player: '//player.polyv.net/script/player.js',
  // 直播聊天室
  //socketio: '//livestatic.videocc.net/assets/wjs/dist/socket.io.min.js',
  socketio: '//player.polyv.net/jssdk/polyv-chatroom.min.js',

  // 京东图片
  jsImgHost: '//img10.360buyimg.com/',
  postagePayType: process.env.payType,

  // 平台用户类型-员工
  N_USER_TYPE_EMPLOYEE: 2,
  N_USER_TYPE_EMPLOYEE_STUDENT: 6,

  // 平台用户类型-学员
  N_USER_TYPE_STUDENT: 4,
  // 平台用户类型-粉丝
  N_USER_TYPE_FANS: 0,
  //扫描王
  app_key: 'a9700da150e9c98e929c0a11eab62a6c',
  //app_key:'cdc66f244ba8f13161b6749a2ed5ba30',
  // app_secret:'c77e09d8c61562f7146a81c54dde8de0',
  app_secret: 'ed0858d36c41414f3ebe2df00a840bf8',
  appKey: process.env.appKey,
  client_secret: '4ea537aaa82c0c1646d800638f3e9369',
  examUrl: process.env.examUrl,
  //打开
  openApp: '//res.wx.qq.com/open/js/jweixin-1.6.0.js',
  loginUrl: window.location.protocol + process.env.loginUrl,
  AppShareUrl: process.env.AppShareUrl,
  //调起App的ID
  OpenAppId: 'wx3c221ac1a34c78ca',
  schoolWxHelp: process.env.schoolWxHelp
}
