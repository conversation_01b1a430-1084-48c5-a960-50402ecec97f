<template>
  <div>
     <div class="fixBottom" v-if="!showCard">
      <div class="leftBox">
        <img src="../../assets/image/active/newDreamBuild/default_photo.png" alt />
        <span class="textOne">远智教育</span>
        <span class="textTwo">提升学历就来远智教育</span>
      </div>
      <div class="rightBox">
        <div class="phoneIcon" @click="phoneNumber()">
          <img src="../../assets/image/active/newDreamBuild/icon_iphone.png" alt />
          <p>咨询</p>
        </div>
        <div class="line"></div>
        <router-link
          :to="{name:'adultExamEnrollCheck',query:{action:'login',activityName:'scholarship',inviteId:inviteId,scholarship:scholarship,actName:this.actName,unvs:JSON.stringify({
                      unvsName:'广东岭南职业技术学院',
                      unvsId:'154564027670030437'
                    })}}"
          v-if="!enrollEnd"
        >
          <div class="signUpIcon">
            <img src="../../assets/image/active/newDreamBuild/icon_signup.png" alt />
            <p>马上抢</p>
          </div>
        </router-link>
        <a v-else @click="enrollEndTip">
          <div class="signUpIcon">
            <img src="../../assets/image/active/newDreamBuild/icon_signup.png" alt />
            <p>马上抢</p>
          </div>
        </a>
      </div>
    </div>
    <div class="fixBottom1" v-if="showCard">
      <div class="leftBox" @click="flag=!flag">
        <img :src="cardHeadUrl+headLimit" alt />
        <span class="textOne">{{cardNickname}}</span>
        <span class="textTwo">{{slogan}}</span>
        <i class="pic-right" :class="{active:flag}"></i>
      </div>
      <div class="rightBox">
        <div class="phoneIcon" @click="phoneNumberMobile()">
          <img src="../../assets/image/teacherKit/<EMAIL>" alt />
          <p>咨询</p>
        </div>
        <div class="line"></div>
        <router-link :to="jumpUrl">
          <div class="signUpIcon">
            <img src="../../assets/image/teacherKit/<EMAIL>" alt />
            <p>{{urlGuide||'免费报名'}}</p>
          </div>
        </router-link>
      </div>
    </div>
    <div class="card_wrap" v-if="flag">
      <div class="card">
        <div class="talk">{{motto}}</div>
        <div class="txt">
          <h3>{{cardNickname }}</h3>
        </div>
        <div class="tel">
          <p>
            <span :class="{active:company.length>13}">{{company||'公司名称未填写'}}</span>
            <i v-if="company">
              <img src="../../assets/image/teacherKit/认证管理@3x.png" alt />
            </i>
          </p>
          <span>{{position || '职位未填写'}}</span>
          <span>{{cardMobile || '手机号未填写'}}</span>
        </div>
        <div class="code" v-if="cardWechatQrcode" @click="isShow=true">
          <img :src="cardWechatQrcode | defaultCode" alt />
          <span>加我微信</span>
        </div>
      </div>
    </div>
    <div class="img_wrap" v-if="isShow">
      <div class="bg" @click="isShow=false"></div>
      <img :src="cardWechatQrcode" alt />
    </div>        
  </div>
</template>

<script>

import { toLogin } from "../../common";
import { Popup } from "vant";
  export default {
    name: 'bottomTab',
    props:[
      'scholarship',
    ],

    data() {
      return {
          showCard:false,
          enrollEnd: false, //停止报名
          flag:false,
          company:"",
          cardWechatQrcode:"",
          isShow:false,
          cardMobile:"",
          position:"",
          cardNickname:"",
          motto:"",
          urlGuide:"",
          slogan:"",
          cardNickname:"",
          cardHeadUrl:"",
          headLimit: "?x-oss-process=image/resize,m_fixed,h_30,w_30",















      }
    },
    computed: {
    jumpUrl: function() {
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName: this.actName
        }
      };
      return url;
    }
  },  
    created() {

    },
    mounted: function() {
    //获取邀约人信息
    if (!!this.inviteId) {
      this.getInviteInfo();
    }
  },
    methods:{
   getInviteInfo() {
       let inviteId = (
        window.sessionStorage.getItem("inviteId") ||
        decodeURIComponent(
          this.$route.query.inviteId ||
            this.getQueryString(this.redirect, "inviteId") ||
            ""
        )
      ).replace(/ /g, "+");   
      if (inviteId) {
        this.$http
          .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
          .then(res => {
            let { code, body } = res;
            if (code !== "00") return;
            this.showInvite = true;
            if (this.invite.relation == "2" || this.invite.relation == "6") {
              this.getArtile(this.invite.userId);
            } else {
              this.getCard(this.invite.userId);
            }
          });
      } 
      
   },
    getArtile(id) {
      this.$http
        .post("/mkt/getSpreadCardByRelationId/1.0/ ", { relationId: id })
        .then(res => {
          if (res.code === "00") {
            if (res.body === null) {
              this.articleId = "";
            } else {
              this.showCard = true;
              Object.assign(this.$data, res.body);
              if (this.cardHeadUrl) {
                this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
              }
              if (this.cardWechatQrcode) {
                this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
              }
              this.propagateUrl = this.propagateUrl + "&articleId=" + id;
            }
          }
        });
    },   
    },

  }
</script>

<style lang="less" scoped>
   
</style>
