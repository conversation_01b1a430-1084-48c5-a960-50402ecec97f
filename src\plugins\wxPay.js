/*
* 微信浏览器内置对象支付
* 注意：WeixinJSBridge内置对象在其他浏览器中无效。
*/

function wxPay(params,callback) {
  if (typeof WeixinJSBridge === "undefined") {
    if (document.addEventListener) {
      document.addEventListener('WeixinJSBridgeReady', onBridgeReady(params,callback), false);
    } else if (document.attachEvent){
      document.attachEvent('WeixinJSBridgeReady', onBridgeReady(params,callback));
      document.attachEvent('onWeixinJSBridgeReady', onBridgeReady(params,callback));
    }
  } else {
    onBridgeReady(params,callback);
  }
}

function onBridgeReady(params, callback) {
  let that = this;
  WeixinJSBridge.invoke(
    'getBrandWCPayRequest', {
      "appId":params.appId, //公众号ID，由商户传入
      "timeStamp":params.timeStamp, //时间戳，自1970年以来的秒数
      "nonceStr":params.nonceStr,  //随机串
      "package":params.package, // 订单详情扩展字符串
      "signType":params.signType, //微信签名方式：
      "paySign":params.paySign //微信签名
    },
    function(res){
      callback(res)
    }
  );
}

export default wxPay;
