// 自定义数据访问统计插件
import axios from "./axios"
import storage from "./storage"
import sensors from '@/common/sensors'

/**
 * @param {String} targetType 事件类型
 * @param {String} targetId  描述id
 * @param {String} targetReamrk  参数描述
 * @param {*} udata 预留参数
 */
function statistic(targetType, targetId, targetReamrk, udata) {
  const param = {
    targetId,
    targetType,
    targetReamrk,
    udata,
    version: "1.0",
    platform: "WECHAT",
    url: window.location.href,
    deviceId: storage.getItem("phoneID"),
  }
  if (udata) {
    param.udata = udata
  }
  return new Promise((resolve, reject) => {
    // 神策
    sensors.track(targetReamrk.includes("点击") ? "click" : "show", {
      event_title: targetReamrk,
      event_path: window.location.pathname,
      ...param,
    })
    axios
      .post("/us/operationStatistic/1.0/", param)
      .then((res) => {
        const { code, body } = res
        if (code == "00") {
          resolve(body)
          return
        }
        reject(false)
      })
      .catch(() => {
        reject(false)
      })
  })
}

export default statistic
