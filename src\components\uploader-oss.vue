<template>
  <div id="uploader" >
    <div :id="picker">
    </div>
    <slot><i class="icon i-upload"></i></slot>  <div style="opacity: 0;position: absolute;">
    <div style="opacity: 0;position: absolute;">
      <video  :src="url"  ref="video" v-if="url&&udType=='video'"></video>
      <audio  :src="url"  ref="audio" v-if="url&&udType=='audio'"></audio>
    </div>
  </div>

  </div>
</template>

<script>
   import config from "../config"
  export default {
    props:{
      picker:String,
      type:String,
      server:{
        type:String,
        default:'/proxy/uploadFileNewPrd'
      },
      list:{
        type:Boolean,
        default:false
      },
      addBtn:{
        type:String,
        default:''
      },
      progress:{
        type:Boolean,
        default:false
      },
      accept:{
        type:String,
        default:'image/*'
      },
      udType:{
        type:String
      }
    },
    data() {
      return {
        // 上传地址
        uploadUrl:'https://zm.yzou.cn/bcc/uploadFile',
        fileCount: 0,
        state: 'pedding',
        percentages: {},
        supportTransition: undefined,
        uploader: undefined,
        fileList: undefined,
        fileData: {
          refId: undefined,
          projectId: undefined
        },
        files:[],
        url:'',
        small_url:''
      }
    },
    created(){

    },
    mounted() {
      if(!window.jQueryStatus){
        this.getjQuery()
      }else{
        this.init();
      }
    },
    methods: {
      init(){
        if(global.$){
          if(global.WebUploader){
            this.initWebuploader(this.picker);
            $(".webuploader-pick").css("height","100%")
          }else{
            this.getWebuploader()
          }
        }else{
          setTimeout(()=>{
            this.init()
          },100)
        }
      },
      getjQuery(){
        let $script=document.createElement("script");
        $script.type = 'text/javascript';
        $script.charset = 'utf-8';
        $script.src= '//lib.baomitu.com/jquery/2.2.4/jquery.js';
        $script.onload = () => {
          this.getWebuploader()
        };
        global.document.body.appendChild($script);
        window.jQueryStatus=true;
      },
      getWebuploader(){
        let $script=document.createElement("script");
        $script.type = 'text/javascript';
        $script.charset = 'utf-8';
        $script.src= '//cdn.staticfile.org/webuploader/0.1.0/webuploader.html5only.js';
        //$script.src= '/static/webuploader/index.js';
        $script.onload = () => {
          this.initWebuploader(this.picker);
          $(".webuploader-pick").css("height","100%")
        };
        global.document.body.appendChild($script);
      },
      uuid() {
        var s = [];
        var hexDigits = "0123456789abcdef";
        for (var i = 0; i < 36; i++) {
          s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10),1);
        }
        s[14] = "4";
        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
        s[8] = s[13] = s[18] = s[23] = "";
        var uuid = s.join("");
        return uuid;
      },
      async putBlob () {
        var that=this;
        try {
            var data={};
            if(this.udType=='audio'){
              data={resourcesUrl:this.small_url,udType:1,resourcesTime:parseInt(this.duration)}
            }else{
              data={resourcesUrl:this.small_url,udType:2}
            }
            this.$http.post('/mkt/publishUpDeclaration/1.0/',data).then(res=>{
              if(res.code=='00'){
                that.$emit('publish',res.body)
              }
            })
        } catch (e) {
          console.log(e);
        }
      },
      initWebuploader(picker){
        var $ = jQuery,
          state = 'pending';
        this.uploader = WebUploader.create({
          // 选完文件后，是否自动上传。
          auto:true,
          server: this.server,      // 文件接收服务端。
          // 内部根据当前运行是创建，可能是input元素，也可能是flash.
          pick: {
            id:'#'+picker,
            multiple:false
          },
          // 只允许选择图片文件。
          accept: {
            extensions: this.udType=='video'?'mp4,mov':'mp3,m4a',
            mimeTypes: this.udType=='audio'?'audio/*':'video/*',
          },
          duplicate :true,
          compress:{compressSize:1024*1},
          fileSizeLimit:this.udType=='video'?1024*70*1024:5*1024*1024,
          formData:{typeName:'upDeclaration',fileName:this.uuid()},
          headers:{authToken:JSON.parse(localStorage.getItem('authToken'))}
        });
        var that=this;
        this.uploader.on('fileQueued', function (file) {
          that.files.push(file)
          if(that.files.length>1){
            that.uploader.removeFile( that.files[0],true )
          }

        });
        this.uploader.on( 'uploadSuccess', function( file ,res) {
          if(res.code=='00') {
            that.small_url=res.body.resourcesUrl
            that.duration=res.body.resourcesTime
            that.url=config.imgPosterBaseURL+res.body.resourcesUrl;
            if(that.udType=='audio'){

              that.$indicator.close()
                that.$emit('resource',that.url,res.body.resourcesTime,that.udType)
            }else{
              that.$nextTick(()=>{
                that.$indicator.close()
                that.$emit('resource',that.url,'',that.udType)
              })

            }
            // that.$emit('upload', res.body, that.type);
          }else{
            that.$indicator.close()
            that.$modal({message: res.message,icon:'warning'})
          }
        });
        this.uploader.on( 'error', function( type) {
          if(type=='Q_EXCEED_SIZE_LIMIT'){
            that.$indicator.close()
            if(that.udType=='video'){
              that.$modal({message:'文件已经超过70M太大啦，建议采用其他视频软件录制或者压缩一下视频上传更快哦！',icon:'warning'})
            }else{
              that.$modal({message:'文件已经超过5M太大啦，建议采用其他语音软件录制或者压缩一下语音上传更快哦！',icon:'warning'})
            }
          }
        });
        this.uploader.on('uploadStart',function(file ,res){
          that.$indicator.open()
          console.log(res);
        })

      },
      startUpload(){
        this.$indicator.open();
        this.uploader.upload();
      }
    },

  }
</script>

<style scoped lang="less">
  #uploader{
    height: 100%;
    width: 100%;
    position: absolute;
  }
  .webuploader-container{
    display: block;
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top:0;
    .webuploader-pick{
      height: 100%;
    }
  }
</style>
