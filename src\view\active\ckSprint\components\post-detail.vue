<template>
  <div class="postDetail">
    <div class="contentBox">
      <div class="postInfo">
        <img v-if="postInfo.headUrl" :src="postInfo.headUrl" alt class="userHeadImg" />
        <img v-else :src="xiaozhi" alt class="userHeadImg" />
        <div class="fl">
          <p class="userName">
            {{ userName?userName:postInfo.realName }}
            <span class="level" v-if="postInfo.advLevel">LV{{ postInfo.advLevel }}</span>
          </p>
          <p class="pfsnName" v-if="postInfo.unvsName&&postInfo.pfsnName">
            {{ postInfo.unvsName }} · {{ postInfo.pfsnName }}
          </p>
          <p class="pfsnName" v-else>初来乍到</p>
        </div>
        <div class="postInfo-follow" v-if="!postInfo.followId&&followBtn">
          关注
        </div>

      </div>
      <img src="../../../../assets/image/app/circleAd/intive.png" alt class="contentBox-intive"
           v-if="postInfo.recType=='1'" />
    </div>
    <!-- 普通 -->
    <div class="commentBox" v-if="postInfo.postType===1" @click="toPostDetail?toMomentDetails(postInfo):''">
      <div class="comment" v-if="postInfo.showText2">
        <div v-html="postInfo.scText2"></div>...
        <span @click="postInfo.showText2 = false">全文</span>
      </div>
      <div class="comment" v-else v-html="postInfo.scText"></div>
      <img-box :scPicUrl="postInfo.scPicUrl" />
    </div>
    <!-- 跑步 -->
    <template v-if="postInfo.postType===3">
      <div @click="toPostDetail?toMomentDetails(postInfo):''">
        <run-post :toPostApp="toPostApp" :postInfo="postInfo" />
      </div>
    </template>

    <!-- 读书 -->
    <template v-if="postInfo.postType===2">
      <div @click="toPostDetail?toMomentDetails(postInfo):''">
        <book-post :toPostApp="toPostApp" :postInfo="postInfo" />
      </div>
    </template>

    <!-- 评论/推荐 帖子 -->
    <div class="grey-block article" v-if="isConnection(postInfo.scType)" @click="toOtherAuthorMoment(postInfo)">
      <div class="img-box">
        <van-image width=".6rem" height=".6rem" radius=".04rem" :src="getImage(postInfo)" />
      </div>
      <div class="info">
        <template v-if="postInfo.pIsAllow !== '3'">
          <div class="title">
            <span class="blue">{{ postInfo.pRealName }}</span>
            <span class="gray" v-if="postInfo.pUnvsName && postInfo.pPfsnName">({{ postInfo.pUnvsName }} ·
              {{ postInfo.pPfsnName }})</span>
            <span class="black">的帖子</span>
          </div>
          <div class="desc">
            {{ postInfo.pScText }}
          </div>
        </template>
        <template v-else>
          <span>抱歉，这条帖子已被作者删除</span>
        </template>
      </div>
    </div>
    <!-- 关注信息 -->
    <div v-if="isConcern(postInfo.scType)" @click="toAuthorHome(postInfo)" class="grey-block attention">
      <div class="img-box">
        <img :src="postInfo.extContent.targetHeadUrl | defaultAvatar" alt="">
      </div>
      <div class="info">
        <div class="title">{{ postInfo.extContent.targetNickName }}</div>
        <div class="desc">
          <span v-if="postInfo.extContent.targetUnvsName && postInfo.extContent.targetPfsnName" class="gray">
            ({{ postInfo.extContent.targetUnvsName }} · {{ postInfo.extContent.targetPfsnName }})
          </span>
        </div>
      </div>
    </div>
    <div class="contentInfo-locat" v-if="postInfo.addressName&&postInfo.cityName">
      <div class="contentInfo-locat-box">
        <!-- <img src="../../../../assets/image/app/circleAd/loaction.png" alt class="location-icon" /> -->
        <div class="location-icon"></div>
        <div class="contentInfo-locat-box-name">
          {{postInfo.addressName}} · {{postInfo.cityName}}
        </div>
      </div>
      <div class="contentInfo-locat-other" v-if="postInfo.cirLocationData&&postInfo.cirLocationData.nearbyTotalNum">
        <div class="headBox"
             v-if="postInfo.cirLocationData&&postInfo.cirLocationData.headUrl&&postInfo.cirLocationData.headUrl.length>0">
          <template v-for="(itema, indexa) in postInfo.cirLocationData.headUrl">
            <img :src="itema" alt />
          </template>
        </div>
        <span v-if="postInfo.cirLocationData&&postInfo.cirLocationData.nearbyTotalNum">
          <span v-show="postInfo.postType=='3'">附近有{{postInfo.cirLocationData.nearbyTotalNum}}名跑友在跑</span>
          <span v-show="postInfo.postType=='2'">附近有{{postInfo.cirLocationData.nearbyTotalNum}}名书友在读书</span>
          <span v-show="postInfo.postType=='1'">附近有{{postInfo.cirLocationData.nearbyTotalNum}}名同学在发帖</span>
        </span>
        <img src="../../../../assets/image/app/circleAd/small-arrow.png" class='icon-right' alt="" srcset="">

      </div>
    </div>
    <div class="contentInfo">
      <span class="dateTime">{{ postInfo.createTime | timeBefore }}</span>
      <span class="read"
            v-if="!(isConnection(postInfo.scType)||isConcern(postInfo.scType))">·{{ postInfo.readNum || "0" }}人看过</span>
      <div class="hotlist-opera" v-if="!(isConnection(postInfo.scType)||isConcern(postInfo.scType)||hideCommentIcon)">
        <div class="hotlist-opera-coment" @click="toPostDetail?toMomentDetails(postInfo):$emit('toEdit')" />
        <span class="hotlist-opera-zan-box">
          <img v-if="!postInfo.fabulous||postInfo.fabulous=='0'" class="hotlist-opera-zan"
               src="../../../../assets/image/app/circleAd/new-zan-not.png" alt />
          <img v-else src="../../../../assets/image/app/circleAd/new_zan.png" alt class="hotlist-opera-zan" />
          <span
                v-if="postInfo.fabulousNum&&postInfo.fabulousNum!='0'">{{postInfo.fabulousNum >  999 ? '999+' : postInfo.fabulousNum }}</span>
        </span>
      </div>
    </div>

  </div>
</template>

<script>
import { imgBaseURL } from "@/config";
import { isIOS, isLogin } from '@/common'
import invite from '@/mixins/invite';
import imgBox from '@/components/post/imgBox';
import runPost from '@/components/post/runPost'
import bookPost from '@/components/post/bookPost'


export default {
  mixins: [invite],

  components: {
    imgBox,
    runPost,
    bookPost
  },
  data () {
    return {
      xiaozhi: require('../../../../assets/image/app/adExtend/xiaozhi.png'),
      autoLimit: "?x-oss-process=image/auto-orient,1",
      showImgInfo: false,
      ios: isIOS(),
      imgbaseurl: imgBaseURL,
      isLogin: isLogin()
    };
  },
  props: {
    postInfo: {
      type: Object,
      default: () => ({})
    },
    userName: {
      type: String,
      default: ''
    },
    toPostDetail: {
      type: Boolean,
      default: false
    },
    followBtn: {
      type: Boolean,
      default: false
    },
    toPostApp: {
      type: Boolean,
      default: false
    },
    hideCommentIcon: {
      type: Boolean,
      default: false
    },
    toNearby: {
      type: Boolean,
      default: false
    }
    // postInfo.postType: Number
  },
  computed: {},
  watch: {},
  methods: {

    toLogin () {
      this.$router.push({
        name: "login",
        query: {
          redirect: this.$route.fullPath,
          inviteId: this.$route.query.token || "",
          scholarship: this.$route.query.scholarship || "",
          action: "",
          regChannel: this.$route.query.regChannel || "",
          regOrigin: this.$route.query.regOrigin || "23",
        },
      });
    },

    // 是否关联别的帖子 推荐/评论
    isConnection (type) {
      return type === '-1' || type === '-2';
    },
    // 是否关注其他作者
    isConcern (type) {
      return type === '-3';
    },
    // 跳转到回复帖子的详情页面
    toOtherAuthorMoment (obj) {
      if (obj.pIsAllow === '3') {
        Toast('抱歉，这条帖子已被作者删除');
        return
      }
      this.$inviteJump('/user/moments/details/' + obj.pId);
    },
    // 是否自己发布的帖子，而不是系统创建的
    isSelfPublish (type) {
      let types = ['1', '2', '3', '4', '5'];
      return types.includes(type);
    },
    // 查看帖子详情
    toMomentDetails (obj) {
      if (this.isSelfPublish(obj.scType) || obj.subType) {
        this.$inviteJump('/user/moments/details/' + obj.id);
      } else if (obj.scType === '-1') {
        this.toOtherAuthorMoment(obj);
      }
    },
    // 跳转到其他用户的个人圈子
    toAuthorHome (obj) {
      this.$inviteJump('/user/moments/' + obj.pUserId);
    },
    getImage (obj) {
      // 如果关联帖子有图片 取第一张
      if (obj.pScPicUrl) {
        let imgs = obj.pScPicUrl.split(",");
        return imgBaseURL + imgs[0];
      }
      // 如果没有 直接取用户头像 || 默认头像

      return obj.pHeadUrl || require('../../../../assets/image/settings/ic_mine_head_man.png');
    },
    async like (item, event) {
      if (!!this.storage.getItem("authToken")) {
        // event.pre
        const res = await this.$http.post('/us/usPraise/1.0/', {
          fabulousNum: item.fabulous == 1 ? -1 : 1, // （用户第一次触发传1 第二触发取消点赞传 -1）
          praiseType: 3,
          praiseId: item.id,
          userName: this.nickName,
          circleUserId: '',
        });
        if (res.code == '00') {
          item.fabulous = item.fabulous == 1 ? 0 : 1;
          item.fabulousNum = item.fabulousNum ? Number(item.fabulousNum) : 0
          // 更新视图
          this.$emit('uploadFn', 'update')
          if (item.fabulous) {
            if (!item.fabulousNum) {
              item.fabulousNum = 1;
            } else {
              item.fabulousNum += 1;
            }
          } else {
            item.fabulousNum -= 1;
          }
        }
      } else {
        this.$router.push({
          name: "login",
          query: {
            redirect: this.$route.fullPath,
            inviteId: this.$route.query.token || "",
            scholarship: this.$route.query.scholarship || "",
            action: "",
            regChannel: this.$route.query.regChannel || "",
            regOrigin: this.$route.query.regOrigin || "23",
          },
        });
      }

    },

  },
  created () {
  },
  mounted () {

  },
  beforeCreate () { },
  beforeMount () { },
  beforeUpdate () { },
  updated () { },
  beforeDestroy () { },
  destroyed () { },
  activated () { },
}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类
.contentBox {
  overflow: hidden;
  background: #fff;
  padding: 0 0.16rem;
  font-family: PingFangSC-Semibold, PingFang SC;
  position: relative;
  &-intive {
    position: absolute;
    right: 0.9rem;
    top: 0.1rem;
  }
  .postInfo {
    position: relative;
    &-follow {
      width: 0.7rem;
      text-align: center;
      background: #fbeeee;
      border-radius: 0.15rem;
      font-size: 0.14rem;
      font-weight: 600;
      color: #cc2725;
      line-height: 0.3rem;
      height: 0.3rem;
      position: absolute;
      top: 0.13rem;
      right: 0;
    }
    .userHeadImg {
      width: 0.38rem;
      height: 0.38rem;
      border-radius: 50%;
      float: left;
      margin: 0.12rem 0 0 0rem;
    }
    .level {
      padding: 0.01rem 0.04rem;
      font-size: 0.1rem;
      transform: scale(0.8);
      border-radius: 0.25rem;
      color: #fff;
      background: #fdd580;
      vertical-align: text-bottom;
      display: inline-block;
    }
    .fl {
      float: left;
      margin-top: 0.12rem;
      margin-left: 0.1rem;
      max-width: 2.2rem;
      .userName {
        font-size: 0.15rem;
        color: rgba(23, 6, 6, 1);
        .hotIcon {
          width: 0.12rem;
          height: 0.14rem;
          margin-top: 0.04rem;
        }
      }
      .pfsnName {
        font-size: 0.12rem;
        color: rgba(51, 51, 51, 0.6);
      }
    }
  }
}
.commentBox {
  padding: 0.16rem;
  background: #fff;
  /deep/ .emotionimg {
    width: 0.24rem;
    height: 0.24rem;
    vertical-align: bottom;
  }
  .comment {
    font-size: 0.15rem;
    color: rgba(23, 6, 6, 1);
    overflow: hidden;
    text-overflow: ellipsis;
    span {
      color: #f06e6c;
    }
  }
}
.contentInfo {
  height: 0.35rem;
  // display: flex;
  align-items: center;
  padding: 0.1rem 0.14rem 0.32rem 0.14rem;
  background: #fff;
  position: relative;
  .collectIcon {
    width: 0.14rem;
    height: 0.15rem;
    margin-top: -0.01rem;
  }

  &-locat {
    background-color: #fff;
    padding: 0.01rem 0.16rem;
    height: 0.3rem;
    &-box {
      height: 0.25rem;
      border-radius: 0.13rem;
      border: 0.01rem solid #ebebeb;
      font-size: 0.1rem;
      padding: 0.04rem 0.06rem;
      max-width: 1.5rem;
      float: left;

      .location-icon {
        width: 12px;
        height: 12px;
        margin-top: 0.02rem;
        margin-right: 0.02rem;
        float: left;
        background: url('../../../../assets/image/app/circleAd/loaction.png') no-repeat;
        background-size: contain;
      }
      &-name {
        float: left;
        max-width: 1.14rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &-other {
      float: right;
      font-size: 0.12rem;
      font-weight: 400;
      color: #999999;
      padding: 0.04rem 0;
      line-height: 0.24rem;
      position: relative;
      .icon-right {
        margin-top: 0.07rem;
      }
      .headBox {
        float: left;
        min-width: 0.23rem;
        margin-top: -0.02rem;
        margin-right: 0.04rem;
        img {
          width: 0.23rem;
          height: 0.23rem;
          border-radius: 50%;
          float: left;
          margin-left: -0.1rem;
        }
        &-num {
          float: right;
          font-size: 0.12rem;
          font-weight: 400;
          color: #453838;
        }
        &-arrow {
          width: 0.32rem;
          height: 0.32rem;
          float: right;
          margin-top: 0.1rem;
        }
      }
    }
  }
  span {
    font-size: 0.12rem;
    color: rgba(51, 51, 51, 0.4);
    margin-left: 0.02rem;
  }
  .readIcon {
    width: 0.16rem;
    margin-left: 0.15rem;
    margin-top: -0.01rem;
  }
  .dateTime {
    margin-left: 0.05rem;
  }
  .read {
    margin-left: -0.03rem;
  }
  .hotlist-opera {
    float: right;
    // min-width: 0.65rem;
    &-zan {
      width: 0.24rem;
      height: 0.24rem;
      &-box {
        font-size: 0.12rem;
        font-weight: 400;
        color: #333333;
        line-height: 0.26rem;
        float: right;
        span {
          color: #333333;
          margin-left: -0.02rem;
          line-height: 0.28rem;
        }
      }
    }
    &-coment {
      display: inline-block;
      width: 0.24rem;
      height: 0.24rem;
      margin-right: 0.1rem;
      background: url('../../../../assets/image/app/circleAd/coment-more.png') no-repeat;
      background-size: contain;
    }
  }
}
.launch-follow {
  position: absolute;
  top: 0.13rem;
  right: 0rem;
  bottom: 0;
  width: 0.7rem;
  height: 0.3rem;
}
.zan-num {
  float: right;
  font-size: 12px;
  font-weight: 400;
  color: #333333;
  line-height: 26px;
  margin-left: -5px;
}
.postDetail {
  position: relative;
}
.launch-post {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 4;
  overflow: hidden;
}
.grey-block {
  padding: 0.07rem 0.1rem;
  background: #f7f8f9;
}

.article,
.attention {
  display: flex;
  align-items: center;

  .img-box {
    /deep/ .van-image {
      margin-right: 0.1rem;

      .van-image__error,
      .van-image__loading {
        background: #fff;
      }
    }

    img {
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 0.04rem;
      border: 1px solid #fff3e2;
      margin-right: 0.1rem;
      object-fit: cover;
    }
  }

  .info {
    font-size: 0.12rem;
    line-height: 0.24rem;
    width: calc(100% - 0.7rem);

    .desc,
    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>