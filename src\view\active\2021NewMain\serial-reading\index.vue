<template>
  <div class="yz-serial-reading">
    <!-- 高起本 -->
    <active-container
      ref="activeContainer"
      isNeedScroll
      scholarship="66"
      :noEnroll="activeIndex !== 2"
      :headerTabs="headerTabs"
      :shareTitle="shareTitle"
      :shareDesc="shareDesc"
      :link="shareLink"
      :initHeadActive="activeIndex"
      :bannerResource="bannerImg"
      regOrigin="41"
      :rollCount="count"
      :questionType="activeIndex == 0 ? 'dreamBuild' : 'openUniversity'"
      :showQuestion="activeIndex == 2 && whiteTabIndex == 0 ? false : true"
      :isNeedMsgBar="activeIndex == 2 && whiteTabIndex == 0 ? false : true"
      @getInviteId="getInviteId"
      @tabChange="tabChange"
      @footerMethod="footerMethod"
      @enroll="footerEnroll"
      @singleClick="footerEnroll"
    >
      <div
        class="white-tabs"
        ref="whiteTabs"
        :class="{ fixed: whiteTabsFixed }"
        v-if="activeIndex == 2"
      >
        <div
          class="item"
          :class="{ active: whiteTabIndex == 0 }"
          @click="whiteTabClick(0)"
        >
          <span>学校简介</span>
        </div>
        <div
          class="item"
          :class="{ active: whiteTabIndex == 1 }"
          @click="whiteTabClick(1)"
        >
          <span>报考指南</span>
        </div>
      </div>

      <template v-if="activeIndex !== 2 || whiteTabIndex == 1">
        <div class="reading-title-img t1"></div>
        <div class="white-shadow box1">
          <p>年满18周岁、高中/中专毕业学员</p>
          <p v-if="activeIndex !== 2">希望同时攻读专科、本科学历</p>
          <p v-if="activeIndex == 0 || activeIndex == 2">
            工作需要，提高自我竞争力
          </p>
          <p v-else>工作时间紧张，没时间参加成人高考</p>
          <p>基础薄弱，需专业指导</p>
        </div>
        <div class="reading-title-img t2"></div>
        <ul class="advantage-ul">
          <template v-if="activeIndex == 0">
            <li class="white-shadow nomb">
              <div class="top">
                <img
                  src="../../../../assets/image/active/serial-reading/i1.png"
                  alt=""
                />
                <b>最优搭配</b>
              </div>
              <p>
                报读成人高考[大专]+自学考试[本科]，为您提供优质的搭配报考路线，助你取得专本学历
              </p>
            </li>
            <!-- <li class="white-shadow">
            <div class="top">
              <img src="../../../../assets/image/active/serial-reading/i2.png" alt="">
              <b>学费补贴</b>
            </div>
            <p>报名城建、广生态、岭职、华夏院校，成考过295分，奖励3年学费奖学金，助力提升</p>
          </li> -->
            <!-- <li class="white-shadow nomb">
            <div class="top">
              <img src="../../../../assets/image/active/serial-reading/i3.png" alt="">
              <b>拿证时间短</b>
            </div>
            <p>高起本，学信网可查，成考入学后，顺利最快2.5年可拿双证</p>
          </li> -->
            <li class="white-shadow nomb">
              <div class="top">
                <img
                  src="../../../../assets/image/active/serial-reading/i4.png"
                  alt=""
                />
                <b>名校毕业</b>
              </div>
              <p>暨南大学、华南师范大学等近20余所重点高校可选，含金量高</p>
            </li>
          </template>
          <template v-else>
            <li class="white-shadow nomb">
              <div class="top">
                <img
                  src="../../../../assets/image/active/serial-reading/i5.png"
                  alt=""
                />
                <b>门槛低</b>
              </div>
              <p>一年两次招生，直接入学，不用参加全国成考</p>
            </li>
            <!-- <li class="white-shadow">
            <div class="top">
              <img src="../../../../assets/image/active/serial-reading/i3.png" alt="">
              <b>拿证时间短</b>
            </div>
            <p>高起本，学信网可查，顺利最快2.5年可拿双证</p>
          </li> -->
            <li class="white-shadow nomb" v-if="activeIndex !== 2">
              <div class="top">
                <img
                  src="../../../../assets/image/active/serial-reading/i1.png"
                  alt=""
                />
                <b>学习方式灵活</b>
              </div>
              <p>网络教学或网络自主学习，无地域限制</p>
            </li>
            <li class="white-shadow nomb" v-else>
              <div class="top">
                <img
                  src="../../../../assets/image/active/serial-reading/i1.png"
                  alt=""
                />
                <b>无缝衔接</b>
              </div>
              <p>专科本校直升，优先录取入学</p>
            </li>
          </template>
        </ul>
      </template>

      <template v-if="activeIndex == 2">
        <div class="reading-title-img t4" v-if="whiteTabIndex == 1"></div>
        <open-university :whiteTabActive="whiteTabIndex" />
      </template>

      <template v-if="activeIndex !== 2 || whiteTabIndex == 1">
        <div class="reading-title-img t3"></div>
        <ul class="step-ul">
          <template v-if="activeIndex !== 2">
            <li>
              <p class="t1">第一步</p>
              <div class="grey-box">
                <p>
                  进入【{{
                    activeIndex == 0 ? "成考专科" : "国开专科"
                  }}】报读页面，选择自己喜欢的院校、专业进行报读
                </p>
                <div class="btn-box">
                  <button @click="goEnroll(5)">去了解报读</button>
                </div>
              </div>
            </li>
            <li>
              <div class="t1 flex">
                <span>第二步</span>
                <!-- <span class="blue">支持分期</span> -->
              </div>
              <div class="grey-box">
                <p>
                  进入【自考本科】报读页面，选择自己喜欢的院校、专业进行报读
                </p>
                <div class="btn-box">
                  <button @click="goEnroll(1)">去了解报读</button>
                </div>
              </div>
            </li>
            <li>
              <p class="t1">第三步</p>
              <div class="grey-box" v-if="whiteTabIndex !== 1">
                在准备成考统考的同时，可以根据自考老师安排，进行本科学习，达成专本一起学习、备考！
              </div>
            </li>
          </template>
          <template v-else>
            <li>
              <p class="t1">第一步</p>
              <div class="grey-box">选择专业报名</div>
            </li>
            <li>
              <div class="t1 flex">
                <span>第二步</span>
                <!-- <span class="blue">支持分期</span> -->
              </div>
              <div class="grey-box">缴纳学费</div>
            </li>
            <li>
              <p class="t1">第三步</p>
              <div class="grey-box">提交报读材料</div>
            </li>
            <li>
              <p class="t1">第四步</p>
              <div class="grey-box">等待最终入学结果（一年招生两次）</div>
            </li>
          </template>
        </ul>
        <div class="warm-prompt">
          <p class="t1">温馨提示：</p>
          <p>有任何问题，都可点击下方在线客服/助学老师进行咨询</p>
        </div>
      </template>
      <div class="line"></div>
      <div slot="question" v-if="activeIndex == 2 && whiteTabIndex == 1">
        <questionList />
      </div>
    </active-container>
  </div>
</template>

<script>
import banner0 from "@/assets/image/active/serial-reading/b0.png";
import banner1 from "@/assets/image/active/serial-reading/b1.png";
import banner2 from "@/assets/image/active/serial-reading/b2.png";
import { getActivityInfo } from "@/common/request";
import ActiveContainer from "../components/active-container";
import statistic from "../statistic.json";
import OpenUniversity from "./open-university";
import { isLogin, toLogin, getIsAppOpen } from "@/common";
import { toAppLogin } from "@/common/jump";
import questionList from "./questionList";

export default {
  components: { ActiveContainer, OpenUniversity, questionList },
  data() {
    return {
      inviteId: "",
      shareTitle: "最优搭配的高起本，名校录取，学信网可查！",
      shareDesc:
        "远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！",
      recruitType: "",
      shareLink: "/active/serialReading",
      // headerTabs: ["成考奖学金", "免成考入学", "国开高起本"],
      headerTabs: ["成考奖学金", "免成考入学"],
      activeIndex: 0, // 0是成教 1是国开
      count: 0,
      scholarship: "164",
      whiteTabsFixed: false,
      whiteTabIndex: 0,
      actName: "",
      isLogin: isLogin(),
      isAppOpen: false,
    };
  },
  computed: {
    bannerImg() {
      return this.activeIndex == 0
        ? banner0
        : this.activeIndex == 1
        ? banner1
        : banner2;
    },
    regChannel() {
      return this.$route.query.regChannel || "";
    },
    regOrigin() {
      return this.$route.query.regOrigin || "";
    },
  },
  created() {
    const tab = this.$route.query.tab;
    if (tab >= 0) {
      this.activeIndex = parseInt(tab);
      this.setShare();
    }
    this.setStatistic("browse", "browse");
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.scholarship = this.activeIndex == 0 ? "164" : this.activeIndex == 1 ? "1" : "148";
    this.getActivityInfo();
  },
  watch: {
    activeIndex() {
      this.setShare();
    },
  },
  methods: {
    getInviteId(id) {
      this.inviteId = id;
    },
    footerMethod(which) {
      this.setStatistic("click", which);
    },
    setStatistic(type, key) {
      setTimeout(() => {
        this.$yzStatistic(
          `marketing.base.${type}`,
          statistic.serialReading[this.activeIndex][key].id,
          statistic.serialReading[this.activeIndex][key].name
        );
      }, 500);
    },
    tabChange(i) {
      this.activeIndex = i;
      this.scholarship =
        this.activeIndex == 0 ? "164" : this.activeIndex == 1 ? "1" : "148";
      this.getActivityInfo();
      this.setStatistic("browse", "browse");
    },
    setShare() {
      this.shareTitle =
        this.activeIndex == 0
          ? "最优搭配的高起本，名校录取，学信网可查！"
          : this.activeIndex == 1
          ? "高起本免成考入学，最快2.5年拿双证，学信网可查！"
          : "免试入学，专本无缝衔接，正规学历，学信网可查！";
      this.shareLink = `/active/serialReading?tab=${this.activeIndex}`;

      this.shareDesc =
        this.activeIndex == 2
          ? "远智教育携手国家开放大学，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力 ！"
          : "远智教育携手30多所高校，帮助更多产业工人和上进青年实现大学梦，提升个人竞争力！";
    },
    goEnroll(pfsnLevel) {
      const tab = pfsnLevel == 5 ? this.activeIndex : 2;
      const key = pfsnLevel == 5 ? "btn1" : "btn2";
      this.setStatistic("click", key);
      this.$router.push({
        name: "secondMain",
        query: {
          pfsnLevel,
          tab,
          inviteId: this.inviteId,
          regOrigin: this.regOrigin,
          regChannel: this.regChannel,
          from: "serialReading", // 必填 代表来源
        },
      });
    },
    async getActivityInfo() {
      const { code, body } = await getActivityInfo(this.scholarship);
      if (code == "00") {
        this.count = parseInt(body.learnCount);
        this.actName = body.actName;
      }
    },
    whiteTabClick(index) {
      this.whiteTabIndex = index;
      // 重新获取留言tabbar距离顶部的高度
      this.resetBarTop();
    },
    footerEnroll() {
      if(this.activeIndex != 2){
        return;
      }
      if (!this.login()) {
        return;
      }

      this.$router.push({
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          action: "login",
          scholarship: this.scholarship,
          actName: this.actName,
          pfsnLevel: 8,
          regChannel: this.regChannel,
          regOrigin: this.regOrigin,
          // unvs: JSON.stringify(unvs),
          recruitType: "2",
          isGk: false,
        },
      });
    },
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    resetBarTop() {
      this.$nextTick(() => {
        this.$refs.activeContainer.getMsgBarScrollTop();
      });
    },
  },
};
</script>

<style lang="less">
.yz-serial-reading {
  min-height: 100vh;
  background: #fff;
  .reading-title-img {
    margin: 0.2rem auto;
    width: 0.96rem;
    height: 0.27rem;
    &.t1 {
      background: url(../../../../assets/image/active/serial-reading/t1.png)
        no-repeat;
      background-size: 100%;
    }
    &.t2 {
      background: url(../../../../assets/image/active/serial-reading/t2.png)
        no-repeat;
      background-size: 100%;
    }
    &.t3 {
      background: url(../../../../assets/image/active/serial-reading/t3.png)
        no-repeat;
      background-size: 100%;
    }
    &.t4 {
      background: url(../../../../assets/image/active/serial-reading/t4.png)
        no-repeat;
      background-size: 100%;
      width: 1.57rem;
      height: 0.34rem;
    }
  }
  .white-shadow {
    background: #ffffff;
    box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
    border-radius: 0.02rem;
  }
  .box1 {
    margin: 0 0.15rem;
    font-size: 0.14rem;
    padding: 0.15rem;
    p {
      padding-left: 0.2rem;
      position: relative;
      &:not(:first-child) {
        margin-top: 0.05rem;
      }
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 0.06rem;
        height: 0.06rem;
        background: #ffd981;
      }
    }
  }
  .advantage-ul {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 0 0.15rem;
    li {
      width: 1.67rem;
      font-size: 0.12rem;
      padding: 0.13rem 0.07rem 0.1rem 0.16rem;
      margin-bottom: 0.1rem;
      &.nomb {
        margin-bottom: 0;
      }
      &:not(:nth-child(2n)) {
        margin-right: 0.1rem;
      }
      .top {
        display: flex;
        align-items: center;
        font-weight: 500;
        color: #b6161b;
        font-size: 0.16rem;
        margin-bottom: 0.07rem;
        img {
          width: 0.28rem;
          margin-right: 0.08rem;
          border-radius: 50%;
          box-shadow: 0 2px 4px 0 rgba(254, 176, 24, 0.3);
        }
      }
    }
  }
  .step-ul {
    padding: 0 0.15rem;
    margin-top: -0.05rem;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      background: #fd8d05;
      width: 1px;
      top: 0.1rem;
      left: 0.18rem;
      bottom: 0;
    }
    li {
      position: relative;
      padding-left: 0.21rem;
      font-size: 0.14rem;
      .t1 {
        color: #000000;
        font-size: 0.15rem;
        font-weight: 500;
        margin-bottom: 0.1rem;
      }
      .flex {
        display: flex;
        align-items: center;
      }
      .blue {
        display: inline-block;
        background: linear-gradient(180deg, #0ac0f9 0%, #069ff7 100%);
        border-radius: 0.02rem;
        color: #fff;
        line-height: 0.2rem;
        height: 0.2rem;
        margin-left: 0.1rem;
        padding: 0 0.05rem;
        font-weight: 400;
        font-size: 0.13rem;
      }
      .grey-box {
        background: rgba(145, 145, 145, 0.05);
        border-radius: 0.05rem;
        padding: 0.1rem;
      }
      .btn-box {
        text-align: right;
        button {
          height: 0.3rem;
          width: 0.9rem;
          border-radius: 100px;
          color: #fff;
          text-align: center;
          font-weight: 600;
          border: 0;
          background: linear-gradient(
            135deg,
            #f09190 0%,
            #f07877 66%,
            #f06e6c 100%
          );
        }
      }
      &:not(:last-child) {
        padding-bottom: 0.15rem;
      }
      &::before {
        content: "";
        position: absolute;
        width: 0.06rem;
        height: 0.06rem;
        border-radius: 50%;
        background: #fd8d05;
        left: 0;
        top: 0.08rem;
      }
    }
  }
  .warm-prompt {
    padding-left: 0.36rem;
    padding-bottom: 0.2rem;
    margin-top: 0.15rem;
    color: rgba(23, 6, 6, 0.6);
    font-size: 0.12rem;
    .t1 {
      color: rgba(23, 6, 6, 0.8);
      font-size: 0.13rem;
      margin-bottom: 0.05rem;
    }
  }
  .line {
    background: #f2f2f2;
    height: 0.1rem;
  }
}
.white-tabs {
  color: #000;
  font-size: 0.14rem;
  background: #fff;
  display: flex;
  position: relative;
  z-index: 3;
  border-bottom: 1px solid rgba(23, 6, 6, 0.08);
  &.fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }
  .item {
    height: 0.5rem;
    line-height: 0.5rem;
    flex: 1;
    text-align: center;
    position: relative;
    z-index: 3;
    &.active {
      color: #b6161b;
      &::after {
        content: "";
        height: 0.02rem;
        width: 0.4rem;
        border-radius: 10px;
        background: #b6161b;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
