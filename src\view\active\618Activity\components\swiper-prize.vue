<template>
  <div class="swiper-prize">
    <div class="gdBox" :class="{anim:animate==true}">
      <div
        class="gdBox_wrap"
        v-for="(item,index) in items"
        :key='index'
        :class="{anim1:index==1}"
      >
        <div class="txt_fr">
          <span>恭喜</span>
          <span>{{item.userName|hideNickname}}</span>
          <span>获得</span>
          <span>{{item.prizeVO.prizeName}}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    nacme: "swiper-prize",
    props: {
      list: {
        type: Array,
        default: () => [],
      },
    },
    data(){
      return{
        items:[],
        animate:false,
        timer:null
      };
    },
    watch: {
      list(val) {
        if (val.length > 0) {
          this.items = [].concat(this.list);
          this.start();
        }
      },
    },
    mounted() {
      this.items = [].concat(this.list);
      this.start();
    },
    methods:{
      start() {
        if (this.timer) {
          clearInterval(this.timer);
        }
        this.timer = setInterval(() => {
          if (this.list.length > 1) {
            this.scrollT();
          }
        }, 4000);
      },
      animation(oEle, obj, fn) {
        //1. 清除定时器
        clearInterval(oEle.timer);
        //2.开启定时器
        oEle.timer = setInterval(() => {
          var bflag = true;
          for (var attr in obj) {
            var current = 0;
            var target = 0;
            if (attr == "opacity") {
              current = this.getStyle(oEle, attr) * 100;
              target = obj[attr] * 100;
            } else {
              current = parseInt(this.getStyle(oEle, attr));
              target = parseInt(obj[attr]);
            }
            //2.1 步长=(目标-当前的位置)/10;
            var step = (target - current) / 10;
            //2.2 判断 步长
            step = step > 0 ? Math.ceil(step) : Math.floor(step);
            //2.3  新的位置=当前的位置+步长

            if (attr == "opacity") {
              oEle.style.opacity = (current + step) / 100;
              oEle.style.filter = `alpha(opacity=${current + step})`;
            } else if (attr == "zIndex") {
              oEle.style.zIndex = obj[attr];
            } else {
              oEle.style[attr] = current + step + "px";
            }
            if (current != target) {
              bflag = false;
            }
          }
          if (bflag) {
            clearInterval(oEle.timer);
            if (fn && typeof fn == "function") {
              // 如果 fn不等于 undefined ,就是 true
              fn.bind(oEle)(oEle);
            }
          }
        }, 40);
      },
      getStyle(oEle, attr) {
        if (window.getComputedStyle) {
          return window.getComputedStyle(oEle, null)[attr];
        }
        return oEle.currentStyle[attr];
      },
      scrollT() {
        let oG = document.getElementsByClassName("gdBox_wrap");
        oG[0].style.opacity = 0;
        setTimeout(() => {
          this.animate = true;
        }, 100);
        setTimeout(() => {
          this.items.push(this.items[0]);
          this.items.shift();
          this.animate = false;
          oG[1].style.opacity = 0;
          oG[0].style.opacity = 1;
          this.animation(oG[1], { opacity: 1 });
        }, 500);
      },
      // 轮播文字
      scroll() {
        this.animate = true; // 因为在消息向上滚动的时候需要添加css3过渡动画，所以这里需要设置true
        setTimeout(() => {
          //  这里直接使用了es6的箭头函数，省去了处理this指向偏移问题，代码也比之前简化了很多
          this.items.push(this.items[0]); // 将数组的第一个元素添加到数组的
          this.items.shift(); //删除数组的第一个元素
          this.animate = false; // margin-top 为0 的时候取消过渡动画，实现无缝滚动
        }, 500);
      },
    },
    beforeDestroy(){
        clearInterval(this.timer)
    }
  }
</script>

<style scoped lang="less">
  .swiper-prize {
    // position: absolute;
    // z-index: 50;
    height: 0.25rem;
    overflow: hidden;
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 100px;
    display: inline-block;
    padding: 0 0.1rem;
    .gdBox {
      &.anim {
        transition: all 1s;
        margin-top: -0.5rem;
      }
    }
    .gdBox_wrap {
      margin-bottom: 0.09rem;
      width: 100%;
      height: 0.25rem;
      clear: both;
      opacity: 0;
      .img {
        float: left;
        width: 0.25rem;
        height: 0.25rem;
        border-radius: 50%;
        img {
          border-radius: 50%;
          width: 100%;
        }
      }
    }
    .gdBox_wrap:first-of-type,
    .gdBox_wrap:nth-of-type(2) {
      opacity: 1;
    }
    .txt_fr {

      height: 0.25rem;
      font-size: 0.13rem;
      line-height: .25rem;
    }
  }
</style>
