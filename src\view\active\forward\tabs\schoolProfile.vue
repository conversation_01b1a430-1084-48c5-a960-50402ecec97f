<template>
  <div class="main">
    <!-- 分享组件 -->
    <share
      title="限时千元活动，报读可赢取上进专项奖学金！"
      desc="活动旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！"
      imgUrl='http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png'
      :link="shareLink"
      :isActivity="true"
      scholarship="125"
      ref="share"
    />

    <!-- 学校介绍 -->
      <div class="introdu" :class="{unfold2:unfold2}">
        <div class="school-img">
          <img src="../../../../assets/image/active/forward/zq.png">
        </div>
        <div class="content">
          <p>肇庆学院是全日制公办综合性大学，创建于1970年，位于广东省珠三角城市-肇庆（距广州90公里）。</p>
          <p> 肇庆市是国家级历史文化名城，首批中国优秀旅游城市、国家园林城市。肇庆星湖风景名胜区是我国首批国家级重点风景名胜区，中国十大文明风景区示范点之一。肇庆学院座落在星湖风景名胜区，总占地面积1200余亩，校园建筑与星湖山水遥相呼应，青山、绿水、蓝天、白云环抱中的美丽校园，尽显优美俊俏的岭南风光，堪称读书治学之佳境。学校是全国绿化模范单位、广东省文明单位、广东省普通本科转型试点高校、广东省创新创业教育示范校、广东省依法治校示范校、肇庆市花园式单位</p>
        </div>
      </div>
      <div class="shrink" @click="unfold2=!unfold2">
        <div>
          <van-icon :name="unfold2 ? 'arrow-up' :'arrow-down'" />
        </div>
        <div class="btn"> {{unfold2 ? "收起" : "查看更多" }} </div>
      </div>

      <div class="introdu" :class="{unfold:unfold}">
        <div class="school-img">
          <img src="../../../../assets/image/active/forward/qz.png">
        </div>
        <div class="content">
          <p>清远职业技术学院成立于2002年，是清远市人民政府举办的综合性公办高等学校。学院坐落在有“珠江三角洲后花园”之称的中国优秀旅游城市、中国宜居城市——清远。清远距广州市中心仅60公里，毗邻白云国际机场，广清高速、广乐高速、京广铁路纵贯南北，京广高铁在市区设清远站，交通便捷。</p>
          <p>学院设有护理学院、外语与经贸学院、机电与汽车工程学院、旅游家政与艺术学院、信息技术与创意设计学院、食品药品学院、继续教育学院等7个二级学院。现有全日制高职学生1.16万人。学院实施“强师工程”，加强师资队伍建设，现有清远市紧缺适用高层次人才27人，广东省高等职业教育专业领军人才培养对象2人，广东省高等学校优秀青年教师培养计划培养对象3人，广东省扬帆计划培养高层次人才项目1人，清远市重点人才项目“起航计划”项目1项。</p>
        </div>
      </div>
      <div class="shrink" @click="unfold=!unfold">
        <div>
          <van-icon :name="unfold ? 'arrow-up' :'arrow-down'" />
        </div>
        <div class="btn"> {{unfold ? "收起" : "查看更多" }} </div>
      </div>

      <!-- 上进故事 -->
      <div class="Storylist">
        <div class="motivate-title">
          <img src="../../../../assets/image/active/gccScholarship/storyTitle.png" alt="">
        </div>
        <mt-swipe class="list-banner" :auto="3000">
          <mt-swipe-item v-for="(item,index) in bannerList" :key="index">
            <img
              class="img"
              :src="(item.bannerUrl + bannerSize)|imgBaseURL"
              @click="bannerJump(item.redirectUrl)"
            />
          </mt-swipe-item>
        </mt-swipe>
         <router-link
            :to="{name:'scholarshipStoryInfo',query:{id:item.scholarshipId,inviteId:inviteId,resourcesUrl:item.resourcesUrl,createTime:item.createTime,regOrigin:regOrigin}}"
            v-for="(item,index) in storyList"
            :key="item.scholarshipId"
            class="item cl"
          >
            <div class="fl">
              <p class="title">{{item.articleTitle}}</p>
              <div class="date">
                <div class="heart">
                  <span
                    class="heartbeat"
                    :class="{active:item.fabulous}"
                    @click.prevent="updateFabulousNum(item.scholarshipId,item.fabulous,index)"
                  >{{item.fabulousNum}}</span>
                  <span class="read">{{item.readNum}}</span>
                </div>
                <span style="float: right">{{item.createTime.substring(0,11)}}</span>
              </div>
            </div>
            <div class="fr">
              <img class="pic" :src="item.articlePicUrl|imgBaseURL" alt />
            </div>
          </router-link>
          <div class="detail-btn">
            <div @click="lookMoreStotyList">查看更多>></div>
          </div>
      </div>

      <!-- 留言区，常见问题 -->
      <div class="service">
        <van-tabs v-model="serviceActive">
          <!-- <van-tab title="留言区">
            <userMessageContent :scholarship="scholarship" ></userMessageContent>
          </van-tab> -->
          <van-tab title="常见问题">
            <quest></quest>
          </van-tab>
        </van-tabs>
      </div>

      <!-- 活动声明 -->
      <div class="acStatement">
        <p>

          承办单位: 广州远智教育科技有限公司<br/>
          邮编: 516600 粤ICP备12034252号-1
        </p>
      </div>

  </div>
</template>
<script>
import { imgBaseURL } from "@/config";
import share from "@/components/share";
import quest from "@/components/activePage/questionList";
import userMessageContent from "@/components/activePage/userMessageContent";
export default {
  components:{
    quest,
    share,
    userMessageContent
  },
  data(){
    return {
      scholarship:'125',
      showall:false,
      unfold:false,
      unfold2:false,
      shareLink: window.location.origin + '/active/forward/index',
      serviceActive: 0,
      bannerList: [],
      regOrigin: '',
      storyList: [],//故事列表
      inviteId: '',//被邀约人 id
      bannerSize: "?x-oss-process=image/resize,m_fixed,h_250,w_640",
    }
  },
  created(){
    this.getBanner();
    this.getStoryList();
  },
  mounted(){
    this.regOrigin = this.$route.query.regOrigin || '';
    this.inviteId = this.$route.query.inviteId || '';
  },
  methods:{
    // 查看更多上进故事
    lookMoreStotyList() {
      this.$router.push({ name: "scholarshipStory",query:{inviteId:this.inviteId}});
    },
    updateFabulousNum(id, num, i) {
      let data = {
        scholarshipId: id,
        fabulousNum: num ? -1 : 1,
        authToken: this.storage.getItem("authToken"),
      };
      this.$http.post("/mkt/updateFabulousNum/1.0/", data).then((res) => {
        if (res.code == "00") {
          this.storyList[i].fabulous = num ? false : true;
          num ?
          (this.storyList[i].fabulousNum -= 1)
          :
          (this.storyList[i].fabulousNum = 1 + parseInt(this.storyList[i].fabulousNum));
        }
      });
    },
    // banner 跳转
    bannerJump: function (url) {
      if (url) {
        const origin = window.location.origin;
        if (url.startsWith(origin)) {
          if (url === "https://zm.yzou.cn/active/redEnveloped") {
            window.location.replace(url);
          } else {
            this.$router.push(url.replace(origin, ""));
          }
        } else {
          window.location.href = url;
        }
      }
    },
    //获取故事Banner list
    getBanner: function () {
      this.$http
        .post("/bds/bannerList/1.0/", { zmBannerType: "2" })
        .then((res) => {
          if (res.code !== "00") return;
          this.bannerList = res.body;
          this.bannerList = this.bannerList.filter((item) => {
            if (item.bannerBelong == "1") {
              return item;
            }
          });
        });
    },
     // 获取故事列表
    getStoryList() {
      this.$http
        .post("/mkt/scholarshipStoryList/1.0/", { informationType: 1 })
        .then((res) => {
          const { code, body } = res;
          if (code === "00") {
            this.storyList = body.slice(0, 3);
          }
        });
    },
  }
}
</script>
<style lang="less" scoped>
@import "../../../../assets/less/variable.less";
.main {
  background: #ffffff;

  .introdu {
    padding:0.1rem;
    height: 3.6rem;
    overflow: hidden;
    .school-img{
      img{
        width: 3.55rem;
        height: 1.72rem;
        border-radius: .01rem;
        margin-bottom: .1rem;
      }
    }
    .content{
      padding: 0 0.12rem;
      p {
        text-indent: 2em;
      }
    }
  }
  .unfold{
    height: auto;
  }
  .unfold2{
    height: auto;
  }
  .shrink {
    height: .54rem;
    text-align: center;
    margin-top: 0.1rem;
    color:rgba(23,6,6,.4);
    font-size: 0.14rem;
    border-bottom: 1px  dotted rgba(23,6,6,.1);
  }
  // 常见问题，留言区
  .service {
    margin-top: 0.35rem;
    /deep/ .van-tab{
      color:#B9B4B4;
      font-family: SourceHanSansCN-Bold, SourceHanSansCN;
      font-size: 0.16rem;
      background: #FEFAF0;
    }

    /deep/ .van-tabs__line{
      display: none;
    }
    /deep/ .van-tab--active{
      background-color: #FBC464;
      font-weight: bold;
      color:#170606;
    }
    /deep/ .van-tabs__wrap,
    .van-tabs__wrap scroll-view,
    .van-tabs__nav,
    .van-tab {
      height: 0.5rem!important;
    }
  }

   //承办单位
  .acStatement{
    padding-bottom: 0.2rem;
    p{
      font-size: 0.12rem;
      text-align: center;
    }
  }
}


//上进故事样式
.Storylist {
  margin-top: 0.2rem;
  .motivate-title{
    padding: 0 0.3rem;
    margin-bottom: 0.2rem;
    img{
      width: 100%;
      object-fit: contain;
      height: 0.8rem;
    }
  }
  .list-banner {
    height: 1.48rem;
    z-index: 1;
    background: white;
  }
  .invite {
    background-image: url("../../../../assets/image/active/enrollmentHomepage/invitBg2.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;
    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }
    .rightView {
      float: left;
      margin-top: 2.3%;
      p {
        font-size: 0.16rem;
        span {
          color: #e15443;
          font-size: 0.13rem;
        }
        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }
  .item {
    display: block;
    background-color: #fff;
    position: relative;
    padding: 0.2rem 0.16rem 0.1rem 0.16rem;
    &:before {
      .borderBottom;
    }
    .fl {
      width: 69%;
      p {
        line-height: 1.5;
      }
    }
    .fr {
      width: 30%;
      text-align: right;
    }
    .title {
      font-size: 0.15rem;
      color: #363636;
      min-height: 0.54rem;
      overflow: hidden;
    }
    .date {
      margin-top: 0.1rem;
      .heart {
        width: 1.5rem;
        float: left;
        span {
          display: block;
          float: left;
          margin-right: 0.17rem;
          padding-left: 0.21rem;
          line-height: 0.18rem;
          overflow: hidden;
          &.heartbeat {
            background: url("../../../../assets/image/scholarshipStory/ic_school_like.png") no-repeat;
            background-size: 0.18rem;
            &.active {
              background: url("../../../../assets/image/scholarshipStory/ic_school_like_select.png") no-repeat;
              background-size: 0.18rem;
            }
          }
          &.read {
            background: url("../../../../assets/image/scholarshipStory/ic_school_like copy.png")
              no-repeat 0 0.01rem;
            background-size: 0.16rem;
          }
        }
      }
      font-size: 0.12rem;
      color: rgba(54, 54, 54, 0.6);
    }
    .description {
      font-size: 0.12rem;
      color: #444;
    }
    .pic {
      width: 0.72rem;
      height: 0.72rem;
      object-fit: cover;
    }
  }
  .detail-btn {
    width: 100%;
    height: 0.9rem;
    background: white;
    padding-top: 0.27rem;
    div {
      width: 1rem;
      height: 0.36rem;
      background: #e64b23;
      text-align: center;
      line-height: 0.36rem;
      color: #fff;
      margin: 0 auto;
      font-size: 0.14rem;
      font-weight: bold;
    }
  }
}

</style>
