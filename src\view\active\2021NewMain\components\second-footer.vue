<template>
  <div class="yz-second-footer" :class='{iphonex: isIphoneX}'>
    <!-- 友情提示 -->
    <!-- <consult-tips v-model="showTips" :tipName='tipName' :right='consultTipsRight' :bottom='consultTipsBottom' /> -->
    <teacher-card v-model="showTeacherCard" :inviteId='inviteId' :bottom='isIphoneX ? "0.94rem" : "0.6rem"' @noTeacher='openCustomerService' />
    <!-- tab列表 -->
    <div class="item" @click='toInvite'>
      <img src="../../../../assets/image/active/2021NewMain/s-invite.png" class="icon" alt="">
      <img v-if='!isLogin' src="../../../../assets/image/active/2021NewMain/nologin.png" class="no-login" alt="">
      <p>{{ gitBtnText }}</p>
    </div>
    <div class="item" :class='{triangle: showTips}' @click='consultBtn'>
      <div class='consult' :class='{"red-point": showPoint}' >
        <img src="../../../../assets/image/active/2021NewMain/s-call.png" class="icon" alt="">
      </div>
      <p>在线咨询</p>
    </div>
    <button class="enroll-btn" v-if="!noEnroll" @click='enroll'>
      <span>{{btnText}}</span>
      <img src="../../../../assets/image/active/2021NewMain/yellow-arrow.png" alt="">
    </button>
  </div>
</template>

<script>
import { isIphoneX, isLogin, toLogin, getIsAppOpen } from '@/common';
// 统一七鱼客服与微信客服配置
import customerService from '@/mixins/customerService';
import { toAppLogin } from '@/common/jump';
import TeacherCard from './teacher-card';
import ConsultTips from './consult-tips';
import statistic from '../statistic.json';

export default {
  mixins: [customerService],
  components: { TeacherCard, ConsultTips },
  props: {
    inviteId: {
      type: String,
      default: '',
    },
    btnText: {
      type: String,
      default: '马上报读',
    },    
    noEnroll: { // 没有报名按钮
      type: Boolean,
      default: false,
    },
    inviteFrom: { // dreamBuild openUniversity selfTought graduate
      type: String,
      default: 'newMain2021',
    },
    statisticKey: {
      type: String,
      default: '',
    },
    gitBtnText: {
      type: String,
      default: '点击有礼'
    }
  },
  data() {
    return {
      isIphoneX: isIphoneX(),
      isLogin: isLogin(),
      tipName: '小Y客服',
      showTips: false,
      showPoint: false,
      headImg: '',
      showTeacherCard: false, // 老师卡片
      isAppOpen: false,
    };
  },
  computed: {
    consultTipsRight() {
      return this.noEnroll ? '0.15rem' : '1.26rem';
    },
    consultTipsBottom() {
      if (this.noEnroll) {
        return this.isIphoneX ? "0.99rem" : "0.65rem";
      }
      return this.isIphoneX ? "1.03rem" : "0.69rem";
    },
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.initPoint();
  },
  methods: {
    initPoint() {
      setTimeout(() => {
        this.showPoint = true;
      }, 5000);
      setTimeout(() => {
        this.showTips = true;
      }, 10000);
    },
    setStatistic(key) {
      if (this.statisticKey) {
        setTimeout(() => {
          this.$yzStatistic('marketing.base.click', statistic[this.statisticKey][key].id, statistic[this.statisticKey][key].name);
        }, 500);
      }
    },
    toInvite() {
      this.setStatistic('invite');
      this.$emit('footerMethod', 'invite');
      if (!this.isAppOpen) {
        this.$router.push({ name:'dreamBuildInvite', query:{ from: this.inviteFrom } });
        return;
      }
      if (!this.login()) {
        return;
      }
      this.$router.push({ name:'dreamBuildInvite', query:{ from: this.inviteFrom } });
    },
    login() {
      if (!this.isLogin) {
        if (!this.isAppOpen) {
          toLogin.call(this, null);
          return false;
        }
        toAppLogin(); // 调起app登录
        return false;
      }
      return true;
    },
    enroll() {
      this.setStatistic('enroll');
      if (!this.login()) {
        return;
      }
      this.$emit('enroll');
      this.$emit('footerMethod', 'enroll');
    },
    consultBtn() {
      this.$emit('footerMethod', 'consult');
      this.setStatistic('consult');
      if (!this.login()) {
        return;
      }
      this.showTips = false;
      this.showPoint = false;
      if (!this.inviteId) {
        this.openCustomerService();
        return;
      }
      this.showTeacherCard = true;
    },
  },
};
</script>

<style lang="less" scoped>
  .yz-second-footer{
    position: fixed;
    z-index: 7;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    display: flex;
    &.iphonex{
      padding-bottom: 0.34rem;
      .tips-box{
        bottom: 0.98rem;
      }
    }
    .item{
      text-align: center;
      flex: 1;
      font-size: 0.12rem;
      color: #A70A0D;
      font-weight: 600;
      line-height: 1;
      padding: 0.05rem 0;
      position: relative;
      border-top: 1px solid rgba(23, 6, 6, 0.08);
    }
    .no-login{
      position: absolute;
      top: -0.26rem;
      left: 50%;
      transform: translateX(-50%);
      width: 0.51rem;
      height: 0.3rem;
    }
    .consult{
      width: 0.28rem;
      height: 0.28rem;
      margin: 0 auto 0.05rem;
    }
    .red-point{
      position: relative;
      &::after{
        content: '';
        width: 0.08rem;
        height: 0.08rem;
        border-radius: 50%;
        box-shadow: 0px 1px 4px 0px rgba(243, 54, 0, 0.6);
        position: absolute;
        right: -0.05rem;
        top: 0;
        background: #F33600;
      }
    }
    // .triangle::before{
    //   content: '';
    //   // background: rgba(0, 0, 0, 0.7);
    //   border: 0.09rem solid #4c4c4c;
    //   border-left: 0.1rem solid transparent;
    //   border-right: 0.1rem solid transparent;
    //   border-bottom: 0.1rem solid transparent;
    //   width: 0;
    //   height: 0;
    //   position: absolute;
    //   top: -0.12rem;
    //   left: 50%;
    //   transform: translateX(-50%);
    // }
    .icon{
      width: 0.28rem;
      height: 0.28rem;
      vertical-align: middle;
      margin-bottom: 0.05rem;
      &.head-img{
        border-radius: 50%;
      }
    }
    .enroll-btn{
      width: 50%;
      height: 0.6rem;
      border: 0;
      background: linear-gradient(180deg, #DA3338 0%, #A70A0D 100%);
      font-size: 0.18rem;
      color: #FFD33E;
      font-weight: 600;
      text-align: center;
      position: relative;
      img{
        width: 0.12rem;
        height: 0.12rem;
        position: absolute;
        right: .45rem;
        top: 50%;
        transform: translateY(-50%);
      }
    }

  }
</style>
