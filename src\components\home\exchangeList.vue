<template>
  <div class="home-list">
    <!-- <div class="hd">
      <a href="javascript:;" :class="{active:goodsType==='1'}" @click="goodsType='1'">热门商品</a>
      <a href="javascript:;" :class="{active:goodsType==='3'}" @click="goodsType='3'">线下活动</a>
      <a href="javascript:;" :class="{active:goodsType==='2'}" @click="goodsType='2'">培训课程</a>
      <a href="javascript:;" :class="{active:goodsType==='4'}" @click="goodsType='4'">培训教材</a>
    </div> -->
    <div class="bd clearfix">
      <div v-if="!isNoData"  class="headBox">
        <!-- <img class="headIcon" src="../../assets/image/<EMAIL>" alt=""> 热门礼品推荐 -->
        <img class="headIcon" src="../../assets/image/server/i1.png" alt="">
      </div>
      <div class="list-box">
        <exchange-item :list='hotGoodsList'></exchange-item>
      </div>
      <load-bar-four :isLoading="isHotLoading" :allLoaded="hotLoaded" :total="hotGoodsList.length"></load-bar-four>
    </div>

    <div class="bd clearfix" v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
      <div  class="headBox all-gifts">
        <img class="headIcon" src="../../assets/image/server/i2.png" alt="">
      </div>
      <div class="list-box">
        <exchange-item :list='list'></exchange-item>
      </div>
      <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="list.length"></load-bar>
    </div>
  </div>
</template>

<script>
  import status from '@/components/status'
  import price from '@/components/price'
  import loadBar from '@/components/loadBarThree'
  import loadBarFour from '@/components/loadBarFour'
  import exchangeItem from './exchangeItem';

  export default {
    components: {status, price, loadBar,loadBarFour, exchangeItem},
    inject: ['searchForm'],
    props: ['salesType', 'active'],
    data() {
      return {
        pageNum: 0,
        pageSize: 10,
        goodsType: '5',
        hotGoosType:'1',
        list: [],
        hotGoodsList:[],
        zmSize:'?x-oss-process=image/resize,m_fixed,h_170,w_170', // 智米商品首页的兑换尺寸
        isLoading: true,
        allLoaded: false,
        isHotLoading: true,
        hotLoaded: false,
        isNoData:false
      }
    },
    filters: {
      statusClass (val) {
        const classObj = {
          '1': 'grey',
          '2': 'yellow',
          '3': ''
        };
        return classObj[val];
      }
    },
    watch: {
      goodsType: function () {
        this.reset();
      },
      active: function () {
        this.isLoading = !(this.active && !this.allLoaded);
        this.isHotLoading = !(this.active && !this.hotLoaded);
      },
      'searchForm.amountRange'(newVal, oldVal) {
        this.resetList();
        console.log(newVal, 'amountRange')
      }
    },
    created() {
      this.loadMore();
      this.getHotGoodsList();
    },
    methods: {
      resetList() {
        this.isLoading = true;
        this.allLoaded = false
        this.isHotLoading = true
        this.hotLoaded = false
        this.isNoData = false
        this.list = [];
        this.hotGoodsList = []
        this.pageNum = 0;
        this.loadMore();
        this.getHotGoodsList();
      },
      getHotGoodsList: function () {
        this.isLoading = true;
        let data = {
          salesType: this.salesType,
          goodsType: this.hotGoosType,
          amountRange: this.searchForm.amountRange,
          salesName: ''
        };

        this.$http.post('/gs/goodsListAll/1.0/', data).then(res => {
          if (res.code !== '00') return;

          const datas = res.body || {};
          this.hotGoodsList.push(...(datas.list || []));
          if(datas.list.length === 0) {
            this.isNoData = true;
          }
          this.$nextTick(() => {
            this.hotLoaded = datas.list.length === 0;
            this.isHotLoading = this.hotLoaded;
          });
        });
      },
      getGoodsList: function () {
        this.isLoading = true;
        let data = {
          salesType: this.salesType,
          pageNum:this.pageNum,
          pageSize:this.pageSize,
          goodsType: this.goodsType,
          amountRange: this.searchForm.amountRange,
          salesName: ''
        };

        this.$http.post('/gs/goodsList/1.0/', data).then(res => {
          if (res.code !== '00') return;
          const datas = res.body || {};
          this.list.push(...(datas.list || []));
          this.$nextTick(() => {
            this.allLoaded = datas.list.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        if (!this.active) return;
        //  && this.$route.name=='exchangeIntroduction'
        if(!this.$route.query.salesType){
          if(this.$route.name !='exchangeIntroduction'){
            this.pageNum++;
            this.getGoodsList();
         }
        }
      },
      reset: function () {
        this.pageNum = 1;
        this.allLoaded = false;
        this.list = [];
        this.getGoodsList();
      }
    },
  }
</script>
