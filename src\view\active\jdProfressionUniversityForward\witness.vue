<template>
    <div>
      <div class="sprint">
         <div class="myHour" @click="goscholarship"></div>
        <div class="sprint_wrap">
          <div class="sprint_invite" :class="{active:isSprint}" v-if="sprintStatus==1">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" class="head" v-if="invite" alt>
            <img :src="headImg?headImg+headLimit:headImg|defaultAvatar" class="head" v-else alt>
          <div class="headTxt">
            我是上进青年{{stdName}},<br />
            邀请你为我上进做见证!
          </div>
          <p class="sprintTxt" :class="{active:isSprint}">本人(姓名: {{stdName}})正在申请远智教育202{{scholarship==72?1:2}}级成人学历报读上进奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。</p>
          <button class="receive " v-if="!isSprint" @click="sprinttry">为TA见证</button>
          <template v-if="isSprint">
            <div class="info_edit">
              <div class="headBox">
                上进读书见证
              </div>
              <van-popup v-model="showPicker" position="bottom">
                <van-picker
                  show-toolbar
                  :columns="columns"
                  @cancel="showPicker = false"
                  @confirm="onConfirm"
                />
              </van-popup>
              <van-popup v-model="showInput" position="bottom">
                <div class="topView">
                  <button class="cancel" @click="cancel()">取消</button>
                  <button class="select" @click="select()">确定</button>
                </div>
                <input class="selectText" type="text" placeholder="请输入..." v-model.trim="jobName">
              </van-popup>

              <p><span>姓名:</span><input type="text" v-model.trim="name"></p>
              <p><span>工作单位:</span><input type="text" v-model.trim="work_address"></p>
              <p class="jobSelect" @click="showSelect()"><span>职务:</span><input  type="text" v-model.trim="job"><img src="../../../assets/image/arrow-r.png" alt=""></p>
             <p><span>上进点评:</span><input type="text" v-model.trim="comment"></p>
              <p class="red">*维护上进文化, 请真实填写, 远智教育后续将电话回访核实。</p>
            </div>

            <button class="btn_submit" :disabled="isSubmit" @click="submit"></button>
          </template>
        </div>
          <div class="sprint_success"  v-if="sprintStatus==2">
            <img src="../../../assets/image/active/sprintBeforeExam/sprint_success2.png" alt="" class="success">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" class="head" alt>
            <div class="headTxt">
              我是上进青年{{stdName>6?stdName.substr(0,6)+'...':stdName}},<br />
              邀请你为我上进读书做见证!
            </div>
            <p class="sprintTxt">本人(姓名: {{stdName>6?stdName.substr(0,6)+'...':stdName}})正在申请远智教育2022级上进专项奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。 </p>
              <div class="info_success">
                <div class="info" >
                  <p>见证人：{{sprintInfo.witnessName}}</p>
                  <p>职务：{{sprintInfo.occupationPosition}}</p>
                  <p>见证时间：{{sprintInfo.createTime|formatDate('yyyy-MM-dd')}}</p>
                </div>
                <div class="comment">
                  上进点评：<span>{{sprintInfo.comment}}</span>
                </div>
                <!-- <router-link :to="{path:'/active/forward/witnessRecord'}">见证记录>></router-link> -->
              </div>
          </div>
          <div class="sprint_success"  v-if="sprintStatus==3">
            <img src="../../../assets/image/active/sprintBeforeExam/sprint_success2.png" alt="" class="success">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" class="head" alt>
            <div class="headTxt">
              我是上进青年{{stdName>6?stdName.substr(0,6)+'...':stdName}},<br />
              邀请你为我上进读书做见证!
            </div>
            <p class="sprintTxt">本人(姓名: {{stdName>6?stdName.substr(0,6)+'...':stdName}})正在申请远智教育2022级广州开放大学奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。 </p>
              <div class="info_success">
                <div class="info" >
                  <p>见证人：{{sprintInfo.witnessName}}</p>
                  <p>职务：{{sprintInfo.occupationPosition}}</p>
                  <p>见证时间：{{sprintInfo.createTime|formatDate('yyyy-MM-dd')}}</p>
                </div>
                <div class="comment">
                  上进点评：<span>{{sprintInfo.comment}}</span>
                </div>
                <!-- <router-link :to="{path:'/active/forward/witnessRecord'}">见证记录>></router-link> -->
              </div>
          </div>
          <div class="sprint_success"  v-if="sprintStatus==4">
            <img src="../../../assets/image/active/sprintBeforeExam/sprint_success2.png" alt="" class="success">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" class="head" alt>
            <div class="headTxt">
              我是上进青年{{stdName>6?stdName.substr(0,6)+'...':stdName}},<br />
              邀请你为我上进读书做见证!
            </div>
            <p class="sprintTxt">本人(姓名: {{stdName>6?stdName.substr(0,6)+'...':stdName}})正在申请远智教育2022级广东松山职业技术学院奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。 </p>
              <div class="info_success">
                <div class="info" >
                  <p>见证人：{{sprintInfo.witnessName}}</p>
                  <p>职务：{{sprintInfo.occupationPosition}}</p>
                  <p>见证时间：{{sprintInfo.createTime|formatDate('yyyy-MM-dd')}}</p>
                </div>
                <div class="comment">
                  上进点评：<span>{{sprintInfo.comment}}</span>
                </div>
                <!-- <router-link :to="{path:'/active/forward/witnessRecord'}">见证记录>></router-link> -->
              </div>
          </div>
          <div class="sprint_success"  v-if="sprintStatus==5">
            <img src="../../../assets/image/active/sprintBeforeExam/sprint_success2.png" alt="" class="success">
            <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" class="head" alt>
            <div class="headTxt">
              我是上进青年{{stdName>6?stdName.substr(0,6)+'...':stdName}},<br />
              邀请你为我上进读书做见证!
            </div>
            <p class="sprintTxt">本人(姓名: {{stdName>6?stdName.substr(0,6)+'...':stdName}})正在申请远智教育2022级广东机电职业技术学院奖学金。本人承诺秉承上进精神，爱国、爱家、爱企业，将在工作与学习中不断拼搏上进，努力成为上进青年，为企业树立上进榜样。 </p>
              <div class="info_success">
                <div class="info" >
                  <p>见证人：{{sprintInfo.witnessName}}</p>
                  <p>职务：{{sprintInfo.occupationPosition}}</p>
                  <p>见证时间：{{sprintInfo.createTime|formatDate('yyyy-MM-dd')}}</p>
                </div>
                <div class="comment">
                  上进点评：<span>{{sprintInfo.comment}}</span>
                </div>
                <!-- <router-link :to="{path:'/active/forward/witnessRecord'}">见证记录>></router-link> -->
              </div>
          </div>
        </div>
        <div class="sprint_list">
            <div class="headBox_school">
              <img src="../../../assets/image/active/oneYear/xx.png" alt="">
              最新见证动态
            </div>
            <div class="list">
              <div v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50" >
                  <ul>
                    <li v-for="(item,index) in sprintList" :key="index">
                      <div class="head_info">
                        <div class="head">
                          <img :src="item.coverWitnessHead|defaultAvatar" alt="">
                        </div>
                        <div class="info">
                          <p>
                            <span class="name">{{item.coverWitnessName}}</span>
                            <span class="time">{{item.createTime | formatDate('yyyy-MM-dd')}}</span>
                          </p>
                          <p>完成了上进见证, 激活了3000元奖学金!</p>
                        </div>
                      </div>
                      <div class="detail">
                        <p><span>见证人：</span>{{item.witnessName}}   {{item.workUnit}}</p>
                        <p><span>上进点评：</span>{{item.comment}}</p>
                      </div>
                    </li>
                  </ul>
                  <div class="empty" v-if="sprintList.length==0">
                    暂无见证动态
                  </div>
              </div>
            </div>
          </div>
      </div>
      <div class="aHd" @click="goAt" v-if="sprintStatus==5">
        马上了解活动👉
      </div>
    </div>
</template>

<script>
import share from "@/components/share"
import {toLogin,filterEmoji} from "../../../common"
import loadBar from "@/components/loadBar"
import footBar from "@/components/footBar"
import { Picker,Popup } from 'vant';
import bridge from '@/plugins/bridge';
export default {
  components:{
    share,
    loadBar,
    footBar,
    Picker,
    Popup
  },
  data(){
    return{
      inviteId:"",
      isAppOpen:false,
      share:{
        title:'',
        desc:'',
        shareLink:''
      },
      imgUrl:'',
      isSprint:false,
      headImg:'',
      headLimit:"?x-oss-process=image/resize,m_fixed,h_38,w_38",
      sprintList:[],
      name:'',
      work_address:'',
      comment:'',
      job:"",
      sprintStatus:1,
      stdName:'',
      showRed:false,
      sprintInfo:{witnessName:'',workUnit:'',createTime:0},
      sprint_token:'',
      invite:{},
      isSubmit:false,
      allLoaded:false,
      isLoading:false,
      pageNum:0,
      pageSize:10,
      type:'advance.witness.act.202010705.ss',
      scholarship: "130",
      use:'',
      show:false,
      showPicker:false,
      showInput:false,
      jobName:'',
      columns:['主管','经理','总经理','总监','其他']
    }
  },
  created(){
      this.inviteId = this.$route.query.inviteId || "";
      document.title='2022级广东机电职业技术学院奖学金';
      this.stdName=decodeURIComponent(this.$route.query.stdName)||this.$route.query.stdName;
      this.sprint_token=decodeURIComponent(this.$route.query.sprint_token ||'').replace(/ /g, "+");
      if(this.sprint_token){
        this.getInviteInfo();
      }else{
        this.use='self';
        this.stdName=this.storage.getItem('realName')||this.storage.getItem('zmcName');
        this.headImg=this.storage.getItem('headImg');
        this.sprint_token=this.storage.getItem('authToken');
      }
      this.getSprint()

      bridge.callHandler('isAppOpen').then((res) => {
      if (res.appOpen) {
        this.isAppOpen = true;
      }
    });

  },

  methods:{
    goAt(){
      this.$router.push('/active/jdProfressionUniversityForward/index?inviteId='+this.inviteId)
    },
    cancel() {
      this.showInput =false;
    },
    select() {
      this.job=this.jobName
      this.showInput =false;
    },
    showSelect() {
      this.showPicker = true;
    },
    onConfirm(value) {
      if(value =='其他') {
        this.showPicker = false;
        this.showInput = true;
        return;
      }
      this.job = value;
      this.showPicker = false;
    },
    showPopup() {
      this.show = true;
    },
    sprinttry(){
      this.isSprint=true;
      if(!this.storage.getItem('authToken')){
        toLogin.call(this);
        return;
      }
    },
    getSprint(){
      console.log('getSprint');
        this.$http.post('/mkt/getAdvanceWitnessByCoverToken/1.0/',{coverToken:this.sprint_token,scholarship:this.scholarship}).then(res=>{
          if(res.code=='00'&&res.body){
            this.sprintInfo=res.body;
            this.sprintStatus=5;
          }
        })
    },
    getSptintList(){
      this.$http.post('/mkt/getAdvanceWitnessList/1.0/',{pageNum:this.pageNum,pageSize:this.pageSize,scholarship:this.scholarship}).then(res=>{
        if(res.code=='00'){
          const datas = (res.body || []);
          this.sprintList.push(...res.body);
          this.$nextTick(()=>{
            this.allLoaded = datas.length === 0;
            this.isLoading = this.allLoaded;
          })
        }
      })
    },
    getInviteInfo() {
      let inviteId = (
        decodeURIComponent(
          this.$route.query.sprint_token ||
          ""
        )
      ).replace(/ /g, "+");
      if (inviteId) {
        this.$http
          .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
          .then(res => {
            let { code, body } = res;
            if (code !== "00") return;
            this.invite = body || {};
          });
      }
    },
    loadMore(){
        this.pageNum++;
        this.getSptintList()
    },
    submit(){
        var message=''
        if(!this.comment) message='请填写上进点评！';
        if(!this.job) message='请填写职务！';
        if(!this.work_address) message='请填写工作单位！';
        if(!this.name) message='请填写姓名！';
        if(message){
          this.$modal({message:message,icon:'warning'});
          return;
        }
        if(this.sprint_token==this.storage.getItem('authToken')){
          this.$modal({message:'本人自己不可认证',icon:'warning'})
          return;
        }
        let reg=/[`~@#$%^&*_\-+=<>{}|\/'\\[\]·]/im;
        if(reg.test(this.name)||reg.test(this.job)||reg.test(this.work_address)){
          this.$modal({message:'请不要输入特殊字符！',icon:'warning'});
          return;
        }
        let reg1=/d+/im;
        if(reg1.test(this.name)){
          this.$modal({message:'姓名不能包含数字！',icon:'warning'})
          return;
        }
        if(filterEmoji(this.comment).length<10){
          this.$modal({message:'点评语要10个字以上哦！',icon:'warning'});
          return;
        }

        let data={
          coverToken:this.sprint_token,
          witnessName:filterEmoji(this.name),
          workUnit:filterEmoji(this.work_address),
          occupationPosition:filterEmoji(this.job),
          comment:filterEmoji(this.comment),
          type:this.type,
          scholarship:this.scholarship
        }
        this.isSubmit=true;
        this.$http.post('/mkt/submisAdvanceWitness/1.0/',data).then(res=>{
          if(res.code=='00'){
            this.$modal({message:'见证成功！',icon:'success',beforeClose:(action,instance,done)=>{
              this.goAt()
                done()
            }})
            this.isSubmit=false;
          }
        }).catch(()=>{
          this.isSubmit=false;
        })
    },
    goscholarship(){
      if(this.isAppOpen){
        window.location.href =`yuanzhiapp://yzwill.cn/Coupons`;
      }else{
        this.$router.push({name:"coupon"})
      }

    }
  },
  watch:{
      name:function(newValue,oldValue){
        if(newValue.length>10){
          this.name=newValue.substr(0,10)
        }
      },
    job:function(newValue,oldValue){
      if(newValue.length>20){
        this.job=newValue.substr(0,20)
      }
    },
    work_address:function(newValue,oldValue){
      if(newValue.length>20){
        this.work_address=newValue.substr(0,20)
      }
    },
    comment:function(newValue,oldValue){
      if(newValue.length>100){
        this.comment=newValue.substr(0,100)
      }
    }
  }
}
</script>

<style scoped lang="less">
.aHd{
  width: 100%;
  height: 0.6rem;;
  background:rgba(251,197,101,1);
  line-height: .6rem;
  font-size:.16rem;
  font-family:PingFang-SC-Bold,PingFang-SC;
  font-weight:bold;
  color:rgba(218,40,47,1);
  position: fixed;
  bottom: 0;
  text-align: center;
}
.sprint{
  width: 100%;
  max-width: 640px;
  margin: 0 auto;
  .myHour{
    position: absolute;
    left: 0;
    top: .50rem;
    background:url('../../../assets/image/active/old/jiang.png') no-repeat ;
    background-size:contain;
    width:.87rem ;
    height:.34rem ;
    z-index:9999;
  }
  .sprint_wrap{
    width: 100%;
    height: auto;
    overflow: hidden;
    background-color: #fff;
    // background-image:linear-gradient(to bottom,rgba(254, 103, 52, 1)rgba(110,16,2,1));
    .sprint_invite{
      width: 100%;
      height: 4.93rem;
      background-image: url("../../../assets/image/active/sprintBeforeExam/envelope.png");
      background-size: 100% 100%;
      position: relative;
      &.active{
        height: 7.74rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/bg2.png");
        background-size: 100% 100%;
      }
      .head{
        width: .6rem;
        height: .6rem;
        border-radius: 50%;
        position: absolute;
        top:.35rem;
        left:50%;
        margin-left: -.3rem;
      }
      .headTxt{
        width: 100%;
        position: absolute;
        color: rgba(54, 54, 54, 1);
        line-height: .3rem;
        font-size: .17rem;
        top:.98rem;
        text-align: center;
        font-weight: bold;
      }
      .sprintTxt{
        font-size: .14rem;
        color: rgba(54, 54, 54, 1);
        line-height: .23rem;
        position: absolute;
        width: 2.43rem;
        top:2.24rem;
        left:50%;
        margin-left: -1.21rem;
        text-indent: 2em;
        &.active{
          top:2.1rem;
        }
      }
      .receive{
        width: 1.4rem;
        height: .4rem;
        line-height: .4rem;
        text-align: center;
        background-color: rgba(252, 66, 27, 1);
        font-size: .17rem;
        color: #fff;
        background-image: linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
        border: none;
        position: absolute;
        top:4.09rem;
        left:50%;
        margin-left: -.7rem;
      }
      .info_edit {
        width: 100%;
        position: absolute;
        top:3.6rem;
        .headBox{
          width: .92rem;
          height: 0.3rem;
          text-align: center;
          margin: 0.4rem auto 0rem;
          color:rgba(54, 54, 54, 1) ;
          font-size: .15rem;
          position: relative;
          &:before{
            content:"";
            position: absolute;
            left:-.4rem;
            top:0rem;
            width: .39rem;
            height:.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_left.png");
            background-size: .39rem;
          }
          &:after{
            content:"";
            position: absolute;
            right:-.4rem;
            top:0rem;
            width: .39rem;
            height:.24rem;
            background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
            background-size: .39rem;
          }
        }
        p{
          width: 3.22rem;
          height: .5rem;
          margin-left: .26rem;
          padding-left: .2rem;
          &.red{
            color: rgba(252, 66, 27, 1);
            font-size: .12rem;
            line-height: .2rem;
            margin-top: .05rem;
          }
          span{
            display: block;
            float: left;
            height: .5rem;
            width: .63rem;
            text-align: justify;
            font-size: .14rem;
            line-height: .5rem;
            text-align-last: justify;
          }
          input{
            padding-left: .1rem;
            width: 2.21rem;
            height: .45rem;
            border: none;
            border-bottom: solid 1px rgba(54, 54, 54, .6);
          }
        }
      }
    }
    .sprint_success{
      width: 100%;
      height:auto;
      min-height: 5.95rem;
      background: url("../../../assets/image/active/gccScholarship/frame.png") no-repeat center;
      background-size: 100% 100%;
      position: relative;
      overflow: hidden;
      // margin-bottom: .6rem;
      .success{
        width: 1.37rem;
        height: .69rem;
        position: absolute;
        top:.53rem;
        right: .19rem;
      }
      .head{
        width: .6rem;
        height: .6rem;
        border-radius: 50%;
        position: absolute;
        top:.57rem;
        left:50%;
        margin-left: -.3rem;
      }
      .headTxt{
        width: 100%;
        position: absolute;
        color: rgba(54, 54, 54, 1);
        line-height: .3rem;
        font-size: .17rem;
        top:1.28rem;
        text-align: center;
        font-weight: bold;
      }
      .sprintTxt{
        font-size: .14rem;
        color: rgba(54, 54, 54, 1);
        background-image: url("../../../assets/image/active/sprintBeforeExam/regular.png");
        background-repeat: no-repeat;
        background-size: 100%;
        padding: .15rem .2rem .3rem .2rem;
        line-height: .23rem;
        position: absolute;
        width: 2.83rem;
        top:2.13rem;
        left:50%;
        margin-left: -1.41rem;
      }
      .receive{
        width: 1.4rem;
        height: .4rem;
        line-height: .4rem;
        text-align: center;
        background-color: rgba(252, 66, 27, 1);
        font-size: .17rem;
        color: #fff;
        background-image: linear-gradient(to bottom,rgba(254, 103, 52, 1),rgba(252, 57, 21, 1));
        border: none;
        position: absolute;
        top:4.09rem;
        left:50%;
        margin-left: -.7rem;
      }
      .info_success{
        /*position: absolute;*/
        margin:4.1rem auto .4rem;
        /*left:.47rem;*/
        width: 2.82rem;
        .info{
          width: 100%;
          p{
            line-height: .14rem;
            margin-bottom: .1rem;
            font-size: .14rem;
            &:last-of-type{
              margin-bottom: 0;
            }
          }
        }
        .comment{
          margin-top: .1rem;
          /*position: absolute;*/
          /*top:4.72rem;*/
          /*left:.47rem;*/
          width: 2.82rem;
          font-size: .14rem;
          line-height: .25rem;
          word-break: break-all;
          span{
            color: #6E1002;
            font-size: .14rem;
            line-height: .2rem;
            margin-top: .05rem;
            font-weight: bold;
          }
        }
        a{
          /*position: absolute;*/
          /*left:1.4rem;*/
          /*top:5.41rem;*/
          margin-left: .94rem;
          margin-top: .15rem;
          color: rgba(252, 66, 27, 1);
        }
      }
    }
  }
  .sprint_list{
    background-color: white;
    height:auto;
    overflow: hidden;
    margin-bottom: .6rem;
    .empty{
        margin: 0.5rem auto;
        width: 1rem;
        height: 1rem;
        padding-top: 0.83rem;
        background-image: url("../../../assets/image/active/sprintBeforeExam/no_sprint.png");
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        color: rgba(54, 54, 54, 1);
    }
    .headBox_school{
      width: 1.23rem;
      height: 0.3rem;
      text-align: center;
      margin: 0.4rem auto 0rem;
      color:rgba(54, 54, 54, 1) ;
      font-size: .16rem;
      position: relative;
      font-family:PingFang-SC-Bold,PingFang-SC;
      font-weight:bold;
      // &:before{
      //   content:"";
      //   position: absolute;
      //   left:-.23rem;
      //   top:-0.02rem;
      //   width: .22rem;
      //   height:.23rem;
      //   background-image: url("../../../assets/image/active/oneYear/xx.png");
      //   background-size:100% 100%;
      // }
      // &:after{
      //   content:"";
      //   position: absolute;
      //   right:-.4rem;
      //   top:0rem;
      //   width: .39rem;
      //   height:.24rem;
      //   background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
      //   background-size: .39rem;
      // }
      img{
          width: .22rem;
         height:.23rem;
      }
    }
    .list{
      width: 100%;
      height: auto;
      overflow: hidden;
      ul{
        li{
          margin: .1rem auto;
          width: 3.52rem;
          height: auto;
          overflow: hidden;
          padding: .1rem;
          background-color: white;
          border-bottom: 1px solid rgba(54,54,54,.08);
          .head_info{
            .head{
              width: .38rem;
              height: .38rem;
              overflow: hidden;
              float: left;
              border-radius: 50%;
              img{
                width: 100%;
              }
            }
            .info{
              float: left;
              width: 2.8rem;
              margin-left: .05rem;
              margin-top: .03rem;
              p{
                clear: both;
                margin-bottom: .1rem;
                font-size: .14rem;
                .name{
                  float: left;
                }
                .time{
                  float: right;
                  color: rgba(54, 54, 54, .4);
                  font-size: .12rem;
                }
              }
            }
          }
          .detail{
            clear: both;
            width: 3.04rem;
            padding: .1rem;
            border: dashed .01rem  rgba(218, 41, 47, 1);
            margin-left: .3rem;
            font-size: .14rem;
            color:rgba(218, 41, 47, 1);
            line-height: .25rem;
            span{
              color: rgba(69, 56, 56, 1);
            }
          }
        }
      }
    }
  }
}
  .red_bag{
    position: fixed;
    top:0;
    left:0;
    width: 100%;
    height: 100%;
    z-index: 9991;
    .bg{
      top:0;
      left:0;
      width: 100%;
      height: 100%;
      position: fixed;
      background-color: rgba(0,0,0,.8);
    }
    .presented{
      width: 2rem;
      height: .4rem;
      line-height: .4rem;
      text-align: center;
      color: rgba(130, 77, 9, 1);
      font-size: .17rem;
      background-color: rgba(252, 66, 27, 1);
      background-image: linear-gradient(to bottom,rgba(254, 232, 158, 1),rgba(237, 170, 37, 1));
      border-radius: .2rem;
      border: none;
      position: absolute;
      top:50%;
      margin-top: 1.23rem;
      left:.88rem;
      font-weight: bold;
    }
    p{
      text-align: center;
      position: fixed;
      z-index: 88;
      width: 1.8rem;
      height: .61rem;
      color: rgba(130, 77, 9, 1);
      font-size: .15rem;
      top:50%;
      margin-top: -1.3rem;
      font-weight: bold;
      left:1.03rem;
    }
    img{
      position: fixed;
      top:50%;
      left:50%;
      margin-left: -1.6rem;
      margin-top: -1.78rem;
      width: 3.19rem;
      height: 3.57rem;
    }
    .close{
      width: .09rem;
      height: .09rem;
      position: fixed;
      top:50%;
      left:50%;
      margin-left: 1.15rem;
      margin-top: -1.48rem;
      z-index: 999;
    }
  }
.activity_rule{
  position: absolute;
  left: 0;
  text-align: center;
  height: .28rem;
  top:.15rem;
  width: .93rem;
  font-size: .14rem;
  z-index: 999;
  background-image: url("../../../assets/image/active/enrollmentHomepage/home.png");
  background-size: 100%;
  &.coupon{
    width: 1.02rem;
    background-image: url("../../../assets/image/active/enrollmentHomepage/coupon.png");
  }
  /*img{*/
    /*width: .16rem;*/
    /*height: .14rem;*/
    /*float: left;*/
    /*margin-left: .05rem;*/
    /*margin-top: .07rem;*/
  /*}*/
  /*a{*/
    /*float: left;*/
    /*display: block;*/
    /*width: .59rem;*/
    /*line-height: .3rem;*/
    /*padding-left: .02rem;*/
    /*text-align: center;*/
    /*color: rgba(130, 77, 9, 1);*/
  /*}*/
}
  .btn_submit{
    position: absolute;width: 2rem;height:.5rem;opacity: 0;top:6.85rem;left:50%;margin-left: -1rem;
  }
.bottomBtn {
  position: fixed;
  width: 3.75rem;
  background:linear-gradient(0deg,rgba(254,103,52,1),rgba(252,57,21,1));
  line-height: .45rem;
  text-align: center;
  font-size: .17rem;
  font-weight: 600;
  color: #fff;
  bottom: 0;
  border: none;
}
.jobSelect {
  position: relative;
  img {
    position: absolute;
    right: .2rem;
    top: .15rem;
    width: .1rem;
    transform: rotate(90deg);
  }
}
.topView {
  width: 3.75rem;
  height: .5rem;
  position: relative;
  border-radius:.1rem .1rem 0px 0px;
  border-bottom:1px solid rgba(0,0,0,.1);
  button {
    background-color:white;
    width: .5rem;
    height: .5rem;
    font-size: .14rem;
     border-bottom:1px solid rgba(0,0,0,.1);
  }
  .cancel {
    color:#363636;
    float: left;
    margin-left: .1rem;
  }
  .select {
    color: #FC421B;
    float:right;
    margin-right: .1rem;
  }
}
.selectText {
  width: 3.55rem;
  height: .36rem;
  font-size: .17rem;
  color:#363636;
  border:none;
  margin: .09rem .1rem;
  border:1px solid rgba(252,66,27,1);
  border-radius:.18rem;
  padding-left: .15rem;
}
</style>
