<template>
  <div class="submit-box">
    <div class="top-box">
      <span @click="onClickLeft">返回</span>
      <button @click="submitAll" class="submit">发布</button>
    </div>
    <div class="content-box">
      <el-input v-model.trim="scText" :rows="6" type="textarea" placeholder="点击说说跑步心得吧！（不低于10个字）" />
      <div class="upload-box">
        <div class="run-box">
          <img class="book-img" :src="config.signPath + runInfo.runImg" alt />
          <div>
            <p class="book-name">今日跑步 · {{ runInfo.distance }}KM</p>
            <p class="book-info">用时{{ runInfo.runTime }}. 配速{{ runInfo.spendDesc }}</p>
            <p class="change" @click="showRun = true">编辑</p>
          </div>
        </div>
      </div>
    </div>
    <Run
      :show="showRun"
      @confirm="
        runInfo = $event
        showRun = false
      "
      @cancel="close"
    ></Run>
  </div>
</template>

<script>
import config from '@/config'
import Run from './components/run.vue'
import { Toast } from 'vant'
export default {
  components: { Run },
  data() {
    return {
      isPatchCard: 0, //是否补卡
      punchOrder: null, //打卡序号
      punchCardType: 2, //打卡类型
      smesterId: null, //学期id

      config: config,
      fileList: [],
      scPicUrl: [],
      scText: '今日跑步打卡+1，跑向未来跑向健康～',
      showRun: false,
      runInfo: {
        runImg: '',
        distance: '',
        spendDesc: ''
      }
    }
  },
  mounted() {},
  methods: {
    close() {
      location.reload()
    },
    onClickLeft() {
      this.$router.go(-1)
    },
    submitAll() {
      if (this.scText.length < 9) {
        Toast('请至少输入10个字的心得哦～')
        return
      }
      this.$indicator.open()
      const userId = localStorage.getItem('userId') || ''
      const params = {
        scText: this.scText,
        scPicUrl: this.scPicUrl.join(','),
        runJson: this.runInfo,
        scSource: '7',
        scType: '3',
        userId: userId,
        subType: 2
      }
      let data = {
        semesterId: this.$route.query.semesterId,
        punchCardType: '2',
        contents: this.scText,
        punchCardImg: this.scPicUrl.join(','),
        isPatchCard: this.isPatchCard,
        punchOrder: this.punchOrder
      }
      this.$http.post('/us/usRunningExt/1.0/', params).then(res => {
        this.$indicator.close()
        if (res.code !== '00') return
        this.$http.post('/mkt/sjScholarshipPunchCard/1.0/', data).then(res => {
          if (res.code === '00') {
            Toast('发帖成功')
            setTimeout(() => {
              this.$router.push({
                name: 'a20241023GuangTie'
              })
            }, 500)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.head-box {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  img {
    width: 32px;
    height: 32px;
  }
  button {
    background: #ea401c;
    padding: 5px 11px;
    border-radius: 5px;
    font-size: 14px;
    color: #fff;
    font-weight: bold;
  }
}
.upload-box {
  background: #fff;
  padding: 0.1rem;
}
.tips {
  margin: 2px 0;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 17px;
}

.run-box {
  width: 100%;
  background: #fff;
  margin: 0 auto;
  display: flex;
  padding: 17px;
  position: relative;
  .book-img {
    width: 47px;
    height: 47px;
  }
  div {
    margin-left: 21px;
    border-radius: 5px;
    .book-name {
      width: 140px;
      height: 20px;
      font-size: 14px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 20px;
    }
    .book-info {
      margin-top: 10px;
      font-size: 14px;
      color: #999999;
    }
    .change {
      position: absolute;
      right: 10px;
      top: 10px;
      margin-top: 13px;
      font-size: 14px;
      color: #999999;
      text-align: right;
    }
  }
}
.top-box {
  height: 0.4rem;
  width: 3.75rem;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 0.1rem;
  justify-content: space-between;
  span {
    font-size: 14px;
    font-weight: bold;
  }
  .submit {
    width: 0.5rem;
    height: 0.3rem;
    background: #f06e6c;
    border-radius: 5px;

    font-size: 14px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
  }
}
</style>
