<template>
  <div>
    <div class="loadmore" v-show="isLoading&&!allLoaded">
      <mt-spinner class="mint-loadmore-spinner" :size="20" type="fading-circle"></mt-spinner>
      <span class="mint-loadmore-text">加载中...</span>
    </div>
    <template v-if="allLoaded"><slot></slot></template>
  </div>
</template>

<script>
  export default {
    props: {
      isLoading: {
        type: Boolean,
        default: false
      },
      allLoaded: {
        type: Boolean,
        default: false
      },
      total: null
    },
    computed: {
      isNoData: function () {
        return 'number' === typeof this.total && this.total === 0 && this.allLoaded;
      }
    }
  }
</script>

<style lang="less" scoped>
  .loadmore{ padding:.13rem 0; clear:both; text-align:center; color:#868686; font-size:.12rem; }
</style>
