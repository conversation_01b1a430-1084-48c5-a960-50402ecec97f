<template>
  <dialog-container v-model="show" @close='close'>
    <div class="yz-ck-hadChange">
      <div class="t1"></div>
      <div class="t2"></div>
      <p class="t3">测测今天的幸运指数？</p>
      <div class="line"></div>
      <div class="test-box2">
        <button @click='startDraw'>开始<br />抽签</button>
      </div>

    </div>
  </dialog-container>
</template>

<script>
import DialogContainer from './dialog-container.vue';

export default {
  components: { DialogContainer },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      show: false,
    };
  },
  watch: {
    value (val) {
      this.show = val;
    },
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '8', '测测您今天运气指数');
    this.show = this.value
  },
  methods: {
    close () {
      this.show = false;
      this.$emit('input', false);
      this.$emit('close', false);
    },
    startDraw () {
      this.$yzStatistic('sprintAct.base.click', '9', '开始抽签');
      this.close();
      this.$emit('startDraw');
    },
  },
};
</script>

<style lang="less">
.yz-ck-hadChange {
  text-align: center;
  padding-top: 0.3rem;
  line-height: 1.2;
  .t1 {
    background: url(../../../../assets/image/active/ckSprint/congratulate.png) no-repeat;
    background-size: 100% 100%;
    width: 1.02rem;
    height: 0.33rem;
    margin: 0 auto 0.1rem;
  }
  .t2 {
    background: url(../../../../assets/image/active/ckSprint/got.png) no-repeat;
    background-size: 100% 100%;
    width: 1.88rem;
    height: 0.27rem;
    margin: 0 auto 0.18rem;
  }
  .line {
    background: url(../../../../assets/image/active/ckSprint/line.png) no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 0.51rem;
    margin: 0 auto 0.2rem;
  }
  .t3 {
    color: #fff;
    font-size: 0.14rem;
    font-weight: 500;
    text-align: center;
  }
}
.test-box2 {
  text-align: center;
  button {
    width: 0.93rem;
    height: 0.93rem;
    background: linear-gradient(180deg, #fbe472 0%, #f6c43e 100%);
    box-shadow: 0px 0px 0.05rem 0px rgba(255, 255, 255, 0.61);
    border-radius: 50%;
    color: #d0342a;
    font-size: 0.24rem;
    font-weight: 600;
    transform: scale(0.7);
    animation: heart 2s linear infinite;
  }
}

@keyframes heart {
  0% {
    transform: scale(0.7);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(0.7);
  }
}
</style>
