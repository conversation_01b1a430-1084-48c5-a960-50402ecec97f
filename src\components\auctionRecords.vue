<template>
  <div class="bc-w">
    <div class="no-data" v-if="records.length===0">这是最新发起的竞拍，暂无往期记录</div>
    <review-item v-else
                 v-for="(item,index) in records"
                 :key="index"
                 :planCount="item.planCount"
                 :time="item.auctionTime"
                 :headImgUrl="item.headImgUrl">
      <div class="fsc1">中拍人：{{item.userName}}({{item.mobile | hidePhone}})</div>
      <div class="fsc1">中拍价：<span class="fc1">{{item.auctionPrice}}</span>智米</div>
    </review-item>
  </div>
</template>

<script>
  import reviewItem from '@/components/reviewItem'
  
  export default {
    props: {
      records: {
        type: Array,
        default: []
      }
    },
    components: {reviewItem}
  }
</script>
