<template>
  <div>
    <div class="calendar">
      <div class="calendar-header">
        <button class="last" @click="lastMonth">
          <van-icon name="arrow-left" />
        </button>
        <span class="calendar-title">{{ year }}-{{ month < 10 ? '0'+ month : month }}</span>
        <button class="next" @click="nextMonth">
          <van-icon name="arrow" />
        </button>
      </div>

      <div class="calendar-body">
        <ul class="calendar-weekday">
          <li><span>一</span></li>
          <li><span>二</span></li>
          <li><span>三</span></li>
          <li><span>四</span></li>
          <li><span>五</span></li>
          <li><span>六</span></li>
          <li><span>日</span></li>
        </ul>

        <ul class="days">
          <li class="day" v-for="(item, index) in days" :key="index" :class="{}">
            <span v-if="!$scopedSlots.default">{{item}}</span>
            <slot :date="{ day:item, year, month }"></slot>
          </li>
        </ul>
      </div>
    </div>

    <div class="label">
<!--      <div class="item">-->
<!--        <span class="point"></span>-->
<!--        <span>未打卡</span>-->
<!--      </div>-->
      <div class="item">
        <span class="point pink"></span>
        <span>已打卡</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data(){
    return {
      year: null,//年份
      month: null,//月份
      days: [],//当月的日期列表
      daynum: [31,28,31,30,31,30,31,31,30,31,30,31]//1-12月每个月的天数
    }
  },
  created() {
    let time = new Date();
    this.year = time.getFullYear();
    this.month = time.getMonth() + 1;

    this.isyear(this.year); //判断平年闰年
    this.nowmonth();

  },
  mounted() {
    // console.log(this.)
  },
  methods:{
    // 判断平年闰年
    isyear(val){
      if(val%4===0 && val%100!==0 || val%100===0 && val%400===0){
        this.daynum[1]= 29;
      }else{
        this.daynum[1]= 28;
      }
    },
    // 计算当月多少天
    nowmonth() {
      let daylength = this.daynum[this.month-1];
      this.days.splice(0);  //清空原数组
      for(let i = 0; i < daylength; i++) {
        this.days.push(i + 1); //根据获取的天数向数组中添加日期
      }

      //获取当月1号是周几，然后向数组中添加空字符串占位
      let times = new Date(this.year,this.month-1,1).getDay();
      if(times === 0){//如果times是0则在前面添加6个空字符（视情况，有的星期天排在最前面）
        times=6;
      }else{
        times--; //返回6则添加5个空字符，以此内推，返回1则不添加
      }
      for(let f = 0; f < times; f++){
        this.days.unshift("");
      }
    },
    // 上个月
    lastMonth(){
      if(this.month === 1) {
        this.month= 12;
        this.year--;
        this.isyear(this.year);
      } else {
        this.month--;
      }
      this.nowmonth();

      let month = this.month < 10 ? '0' + this.month : this.month;
      this.$emit('change',this.year+ '-' + month);
    },
    // 下个月
    nextMonth(){
      if(this.month === 12) {
        this.month=1;
        this.year++;
        this.isyear(this.year);
      } else {
        this.month++;
      }
      this.nowmonth();

      let month = this.month < 10 ? '0' + this.month : this.month;
      this.$emit('change',this.year+ '-' + month);
    }
  }
};
</script>

<style lang='scss' scoped>
.calendar {
  width: 100%;
  background: #FFFFFF;
  border-radius: .1rem;

  .calendar-header {
    text-align: center;
    padding-top: .2rem;
    padding-bottom: .15rem;

    .calendar-title {
      font-size: .14rem;
      font-weight: 600;
      color: #333;
      font-family: PingFangSC-Medium, PingFang SC;
      line-height: .2rem;
    }

    .last {
      margin-right: .3rem;
    }

    .next {
      margin-left: .3rem;
    }

    .last,.next {
      color: #F0716F;
      display: inline-block;
      background: none;
      outline: none;

      i {
        width: .2rem;
        height: .2rem;
        line-height: .2rem;
        font-size: .14rem;
      }
    }
  }

  .calendar-body {
    overflow: hidden;

    .calendar-weekday {
      overflow: hidden;
      width: calc(7 * .4rem);
      margin: 0 auto .1rem auto;


      li {
        float: left;
        text-align: center;
        width: .4rem;
        height: .22rem;
        font-size: .14rem;
        font-weight: bold;
        color: #9B9B9B;

        span {
          display: inline-block;
          width: .22rem;
          height: .22rem;
          line-height: .22rem;
        }
      }
    }

    .days {
      overflow: hidden;
      width: calc(7 * .4rem);
      margin: 0 auto;
      padding-bottom: .08rem;

      .day {
        float: left;
        text-align: center;
        width: .4rem;
        height: .22rem;
        font-size: .14rem;
        color: #AFAFAF;
        margin-bottom: .12rem;

        span {
          display: inline-block;
          width: .22rem;
          height: .22rem;
          line-height: .22rem;
        }
      }

      .mark {
        span:not(:empty) {
          border-radius: 50%;
          background: #F0716F;
          color: #FEF7F7;
        }
      }


    }
  }
}

.label {
  .item + .item {
    margin-left: .15rem;
  }

  .item {
    display: inline-block;
    margin-top: .2rem;
    font-size: .12rem;
    color: #333;

    span {
      //vertical-align: top;
      vertical-align: -.02rem;
    }

    .point {
      width: .12rem;
      height: .12rem;
      display: inline-block;
      border-radius: 50%;
      background: #DCDCDE;
      margin-right: .02rem;
    }

    .pink {
      background: #F0716F;
    }
  }
}

</style>
