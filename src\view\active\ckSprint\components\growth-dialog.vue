<template>
  <dialog-container v-model="show">
    <div class="yz-ck-growthDialog">
      <div class="t1"></div>
      <div class="t2"></div>
      <p class="t3">本次学习{{time}}分钟，抽签运气值又增加了！</p>
      <img src="../../../../assets/image/active/ckSprint/g1.png" class="img" alt="">
    </div>
    <div class="growth-btn-box" slot='other'>
      <button @click='btnClick'>一起加油</button>
    </div>
  </dialog-container>
</template>

<script>
import { isLogin } from '@/common'
import DialogContainer from './dialog-container.vue';

export default {
  components: { DialogContainer },
  data () {
    return {
      show: false,
      time: '0',
    };
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      handler (newval, oldval) {
        this.show = newval
      },
      immediate: true,
    },
  },
  mounted () {
    this.$yzStatistic('sprintAct.base.browse', '15', '学习激励弹窗');

    if (isLogin()) {
      this.thisStudyTime();
    }
  },
  methods: {
    close () {
      this.show = false;
      this.$emit('input', false);
    },
    btnClick () {
      this.$yzStatistic('sprintAct.base.click', '16', '一起加油');
      this.close();
      this.$emit('comeTogether');
    },
    // 本次学习时间
    async thisStudyTime () {
      const res = await this.$http.post('/us/thisStudyTime/1.0/', { scholarship: 2522, type: 2 });
      if (res.code == '00') {
        if (res.body) {
          this.time = res.body.studyMinutes || 0;
          this.studyId = res.body.id;
          this.updateThisStudyState();
        }
      }
    },

    async updateThisStudyState () {
      const res = this.$http.post('/us/updateThisStudyState/1.0/', { id: this.studyId });
      if (res.code == '00') {
      }
    },

  },
};
</script>

<style lang="less">
.yz-ck-growthDialog {
  text-align: center;
  padding-top: 0.3rem;
  line-height: 1.2;
  .t1 {
    background: url(../../../../assets/image/active/ckSprint/congratulate.png) no-repeat;
    background-size: 100% 100%;
    width: 1.02rem;
    height: 0.33rem;
    margin: 0 auto 0.07rem;
  }
  .t2 {
    background: url(../../../../assets/image/active/ckSprint/t1.png) no-repeat;
    background-size: 100% 100%;
    width: 2.13rem;
    height: 0.25rem;
    margin: 0 auto 0.16rem;
  }
  .t3 {
    color: #fff;
    font-size: 0.12rem;
    font-weight: 500;
  }
  .img {
    width: 100%;
    vertical-align: middle;
  }
}
.growth-btn-box {
  margin-top: 0.28rem;
  text-align: center;
  button {
    width: 1.57rem;
    height: 0.4rem;
    background: #f9d65b;
    box-shadow: 0px 0px 0.05rem 0px rgba(255, 255, 255, 0.61);
    border-radius: 100px;
    font-size: 0.24rem;
    font-weight: 600;
    color: #d92424;
  }
}
</style>
