<template>
  <div class="winner" v-if="winners.length===0">
    <div class="item">
      <div class="cont" style="padding-top:.1rem">无人参与此次抽奖</div>
    </div>
  </div>
  <div class="winner" v-else-if="winners.length===1">
    <div class="item" v-for="(item,index) in winners" :key="index">
      <div class="cover"><img class="img" :src="item.headImgUrl|defaultAvatar" alt=""/></div>
      <div class="cont">
        <p>中奖者：{{item.userName}}</p>
        <p>开奖时间：{{runTime|formatDate('yyyy.MM.dd hh:mm:ss')}}</p>
      </div>
    </div>
  </div>
  <swiper class="winner" v-else :options="swiperOption">
    <swiper-slide class="item" v-for="(item,index) in winners" :key="index">
      <div class="cover"><img class="img" :src="item.headImgUrl|defaultAvatar" alt=""/></div>
      <div class="cont">
        <p>中奖者：{{item.userName}}</p>
        <p>开奖时间：{{runTime|formatDate('yyyy.MM.dd hh:mm:ss')}}</p>
      </div>
    </swiper-slide>
  </swiper>
</template>

<script>
  import {swiper, swiperSlide} from 'vue-awesome-swiper'

  export default {
    props: {
      winners: {
        type: Array,
        default: []
      },
      runTime: null
    },
    data(){
      return {
        swiperOption: {
          autoplay: 3000,
          autoplayDisableOnInteraction: false,
          direction: 'vertical',
          loop: true
        }
      }
    },
    components: {swiper, swiperSlide}
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";
  .winner{
     height:.66rem; margin:.08rem .12rem; overflow:hidden; background-color:#f26662; background-image:@bgColor; border-radius:.04rem;
    .item{ padding:.12rem; text-align:center; font-size:0; }
    .cover{ display:inline-block; width:.42rem; height:.42rem; vertical-align:top; overflow:hidden; border-radius:50%; }
    .cont{ display:inline-block; margin-left:.12rem; line-height:.21rem; vertical-align:top; text-align:left; color:#fdd7d7; font-size:.13rem; }
  }
</style>
