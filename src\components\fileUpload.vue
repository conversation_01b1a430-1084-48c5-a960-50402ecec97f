<template>
  <div class="uploader">
    <div @click="handleClick">
      <slot><i class="icon i-upload"></i></slot>
    </div>
    <input class="upload-input"  name="fileUpload" type="file" accept="image/*" ref="input" @change="onFileChange">
  </div>
</template>

<script>
  export default {
    name: 'fileUpload',
    props: {
      target: {
        type: String,
        default: '/uploadFile'
      },
      limit: {
        type: Number,
        default: 0
      },
      max: {
        type: Number,
        default: 1
      },
      count: {
        type: Number,
        default: 0
      },
      // accept: {
      //   type: String,
      //   default: 'image/gif,image/jpg,image/jpeg,image/bmp,image/png'
      // },
      isCompress: {
        type: Boolean,
        default: true
      }
    },
    data () {
      return {
        file: null,
        type: '',
        maxWidth: 1280,
        maxSize: 1024 * 1024  // 大于1MB的图片要压缩再上传
      }
    },
    methods: {
      handleClick() {
        if (this.count > 0 && this.count >= this.max) {
          this.$modal({message: `最多只能上传${this.max}张图片`, icon: 'warning'});
          return;
        }
        this.$refs.input.click();
      },
      emitter(event, data) {
        this.$emit(event, data)
      },
      onFileChange(e) {
        e=e || window.event;
        let target=e.target||e.srcElement;
        let files = target.files ||e.dataTransfer.files;
        if (!files.length) {
          this.$modal({message:'图片没有选中哦'})
          return;
        }
        this.file = files[0];
        this.type = (this.file.type || '').toLowerCase();
        
        if (!this.type || !this.type.includes('image/')) {
          this.$modal({message: '请选择正确的图片文件格式！', icon: 'warning'});
          return;
        }
        if (!['image/png', 'image/gif'].includes(this.type)) {
          this.type = 'image/jpeg';
        }
  
        this.$indicator.open('上传中...');
        e.srcElement.value = "";
        this.parse();
      },
      // 读取文件
      parse: function () {
        let fr = new FileReader();
        fr.onloadend = () => {
          if (this.file.size > this.maxSize) {
            this.compress(fr.result);
          } else {
            this.upload(fr.result);
          }
        };
        fr.readAsDataURL(this.file);
      },
      // 压缩图片
      compress: function (dataURI) {
        let img = new Image();
        img.onload = () => {
          const w = Math.min(this.maxWidth, img.width);
          const h = img.height * (w / img.width);
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
    
          canvas.width = w;
          canvas.height = h;
          ctx.drawImage(img, 0, 0, w, h);
    
          // 取出 base64 格式数据并上传
          this.upload(canvas.toDataURL(this.type));
        };
        img.src = dataURI;
      },
      // 上传进度
      uploadProgress(oEvent) {
        if (oEvent.lengthComputable) {
          let percentComplete = Math.round(oEvent.loaded * 100 / oEvent.total)
          this.emitter('progress', percentComplete)
        } else {
          this.emitter('progress', false)
        }
      },
      // 上传
      upload(file) {
        let formData = new FormData();
        const base64Img = file.split(',')[1];
        const ext = file.split(',')[0].split(':')[1].split(';')[0].split('/')[1]
        formData.append('file', `${ext},${base64Img}`);
        this.$http.post(this.target, formData, {onUploadProgress: this.uploadProgress}).then(res => {
          this.$indicator.close();
          (res.code === '00') && this.emitter('input', res.body);
        }).catch(error => {
          this.$indicator.close();
        });
      },
      // base64转blob
      dataURItoBlob(dataURI) {
        let byteString = atob(dataURI.split(',')[1]);
        let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
        let ab = new ArrayBuffer(byteString.length);
        let ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        return new Blob([ab], {type: mimeString});
      }
    }
  }
</script>

<style lang="less" scoped>
  .uploader{
    .upload-input{ display:none; }
    //.i-upload{ width:1.152rem; height:1.152rem; background-image:url(../assets/image/i-upload.png); }
  }
</style>
