<template>
  <swiper class="yz-newintroduce-swiper" :options='options' ref='mySwiper'>
    <!-- v-show='!Expired'  -->
    <swiper-slide v-for="(item) in bannerList" :key="item.imageUrl">
      <a href="javascript:;" @click='itemClick(item)'>
        <img :src="item.imageUrl | imgOssURL" alt="">
      </a>
    </swiper-slide>
    <div v-show='this.bannerList.length > 1' class="swiper-pagination page-custom" slot="pagination"></div>
  </swiper>
</template>

<script>
import { getQueryString, getIsAppOpen } from '@/common';
import { swiper, swiperSlide } from "vue-awesome-swiper";
import 'swiper/dist/css/swiper.css';

export default {
  components: {
    swiper,
    swiperSlide,
  },
  props: {
    Expired: { // 活动过期
      type: Boolean,
      default: false,
    },
    inviteId: String,
    pfsnLevel: [String, Number],
    zmBannerType: {
      type: [String, Number],
      default: 5
    },
  },
  data () {
    return {
      bannerList: [],
      isAppOpen: false,
      options: {
        initialSlide: 0,
        autoplay: 3000,
        loop: false,
        // autoHeight: true,
        pagination: '.swiper-pagination',
        paginationType: 'fraction',
        autoplayDisableOnInteraction: false,
      },
    };
  },
  mounted() {
    getIsAppOpen((bool) => {
      this.isAppOpen = bool;
    });
    this.getBannerList();
  },
  watch: {
    isAppOpen(val) {
      if (val) {
        this.getBannerList();
      }
    },
  },
  methods: {
    itemClick(item) {
      if (!item.url) {
        return;
      }
      const query = this.$route.query;
      let searchUrl = '';
      const activeShip = getQueryString(item.url, 'activeShip');
      if(item.url.includes('/active/elevenCoupon')) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=119`;
      } else if (activeShip) {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}&scholarship=${activeShip}`;
      } else {
        searchUrl = `inviteId=${this.inviteId}&regOrigin=${query.regOrigin || ''}`;
      }
      if (this.pfsnLevel && !item.url.includes('pfsnLevel')) {
        searchUrl += `${searchUrl}&pfsnLevel=${this.pfsnLevel}`;
      }

      if (item.url.includes('scholarship')) {
        this.$emit('toEnroll');
      } else {
        const url = item.url.includes('?') ? `${item.url}&${searchUrl}` : `${item.url}?${searchUrl}`;
        window.location.href = url;
      }
    },
    async getBannerList() {
      const data = { type: this.zmBannerType, bannerBelong: 1 }
      if (this.isAppOpen) {
        data.bannerBelong = 2;
        data.type = this.pfsnLevel == 1 ? 'ck_8' : 'ck_7';
      }
      const res = await this.$http.post('/sys/getMarketingBanner/1.0/', data);
      if (res.code == '00') {
        this.bannerList = res.body || [];
        if (this.bannerList.length > 0) {
          this.bannerList.forEach((item) => {
            if (item.imageUrl) {
              item.imageUrl += `?timestamp=${Date.now()}`;
            }
          });
        }
      }
    },
  }
};
</script>

<style lang="less" scoped>
.yz-newintroduce-swiper{
  position: relative;
  min-height: 3.72rem;
  img{
    width: 100%;
    height: 3.72rem;
    object-fit: cover;
  }

  .page-custom{
    position: absolute;
    background: rgba(0, 0, 0, 0.4);
    right: 0.15rem;
    bottom: 0.14rem;
    color: #fff;
    font-size: 0.15rem;
    padding: 0.03rem 0.07rem;
    border-radius: 0.05rem;
    width: auto;
    left: auto;
    line-height: 1;
    font-family: PingFangSC-Regular,PingFang SC,'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  }
}
</style>
