<template>
  <div>
    <div class="top cl">
      <span>时间段</span>
      <span>已选座位</span>
    </div>
    <div class="select-item cl" v-for="item in options" :class="{active:value.py_id==item.py_id}" @click="selected(item)">
      <span>{{item.start_time|formatDate('yyyy.MM.dd hh:mm')}}-{{item.end_time|formatDate('hh:mm')}}</span>
      <span>{{item.affirm_count+'/'+item.seats}}</span>
    </div>

    <load-bar :isLoading="isLoading" :allLoaded="allLoaded"></load-bar>
  </div>
</template>

<script>
  import loadBar from '@/components/loadBar';

  export default {
    props: ['value', 'eyId', 'epId'],
    data() {
      return {
        options: [],
        isLoading: true,
        allLoaded: false,
      }
    },
    created() {
      this.getPlaceYear();
    },
    methods: {
      // 获取考试时间列表
      getPlaceYear: function () {
        this.$http.post('/mkt/getPlaceYear/1.0/', {eyId: this.eyId, placeId: this.epId}).then(res => {
          const {code, body} = res;
          if (code === '00') {
            this.isLoading = false;
            this.allLoaded = true;
            this.options = body || [];
          }
        });
      },
      selected: function (val) {
        if (val.seats > val.affirm_count) {
          this.$emit('input', val);
        } else {
          this.$modal({message: '该考场已满，请重新选择', icon: 'warning'});
        }
      }
    },
    components: {loadBar}
  }
</script>

<style lang="less" scoped>
  @import "../../assets/less/invite";
  @import "../../assets/less/variable";
  .top{
    padding:.1rem 0 .06rem;
    font-size: .12rem;
    color: #999;
    position: relative;
    &:before{
      .borderBottom;
    }
    span{
      float: left;
      &:last-of-type{
        float: right;
        padding-right: .26rem;
      }
    }
  }
  .select-item{
    padding: 0;
    height: .44rem;
    span{
      float: left;
      font-size: 14px;
      color: #444444;
      &:last-of-type{
        float: right;
        padding-right: .26rem;
      }
    }
    &.active{
      position: relative;
      &:before{
        position: absolute;
        top: .04rem;
        right: -.08rem;
        width: .36rem;
        height: .36rem;
        background-image: url("../../assets/image/selected.png");
        background-size: 100% 100%;
        content: '';
      }
    }
  }
</style>
