<template>
  <comment-bar title=' ' :isSubmiting="isSubmiting" v-on:submit="addComment" ref="commentBar"></comment-bar>
</template>

<script>
  import commentBar from '@/components/commentBar'

  export default {
    props: ['salesId'],
    data() {
      return {
        isSubmiting: false
      }
    },
    methods: {
      // 评论
      addComment: function (content) {
        const reg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;
        if (reg.test(content)) {
          this.$modal($modal);
          return;
        }
        this.isSubmiting = true;

        this.$http.post('/gs/addComment/1.0/', {salesId: this.salesId, commentContent: content}).then(res => {
          if (res.code === '00') {
            this.$refs.commentBar.hide();
            this.$refs.commentBar.successModal();
          }

          this.isSubmiting = false;
        });
      }
    },
    components: {commentBar}
  }
</script>
