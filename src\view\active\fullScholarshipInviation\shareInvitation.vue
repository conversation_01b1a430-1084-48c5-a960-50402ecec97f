<template>
    <div class="main">
        <template>
            <swiper :options="swiperOption" class="swiper-box" style="height:100vh">
                <swiper-slide class="swiper-item">
                    <div class="index">
                        <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        <p>上滑查看</p>
                    </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="invitation"  :class="{'IOSinvitation':isIOS}">
                        <p>亲爱的同学</p>
                            <div class="detail">&emsp;&emsp;您好！属于远智人的“2021级全额奖学金”颁奖典礼，已如约而至。12月26日，在华南农业大学五山校区，全额奖学金颁奖典礼正式开启，即使无法亲临现场，也可来到直播间,体验现场欢快爆棚的气氛，不仅能跟“高分学霸”云聊天，还有至少3轮的百份限量定制款Q萌好物等你来抽取。
                            <br>&emsp;&emsp;心动不如行动，12月26日下午13:20，锁定远智教育直播间，抱枕、水杯、风衣等好礼和你不见不散~</div>
                        <p class="sponsor">远智教育</p>
                        <p class="sponsor">2020年12月20日</p>
                        <p class="state">活动最终解释权归远智所有</p>
                        <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                    </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="meeting" :class="{'IOSmeeting':isIOS}">
                            <p>【直播时间】</p>
                            <p>2020年12月26日13:20</p>
                            <p style="margin-top:.1rem">【观看直播方式】</p>
                            <p>打开远智教育App——发现——上进直播live——全额奖学金颁奖典礼</p>
                            <p class="way"><img src="../../../assets/image/active/fullScholarshipInviation/step_1.png" alt=""></p>
                            <p class="step">第一步：点击底部「发现」</p>
                            <p class="way"><img src="../../../assets/image/active/fullScholarshipInviation/step_2.png" alt=""></p>
                            <p class="step">第二步：找到「上进直播Live」</p>
                            <div class="btnDown" @click="downApp">点此下载远智教育App</div>
                            <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                        </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="process">
                        <p class="item"><span>13 : 00</span><span style="margin-left:.4rem;">签到</span></p>
                        <p class="item"><span>13 : 20</span><span style="margin-left:.4rem;">抽福气环节</span></p>
                        <p class="item"><span>14 : 00</span><span style="margin-left:.4rem;">典礼开始</span></p>
                        <p class="item"><span>14 : 05</span><span style="margin-left:.4rem;">校长致辞</span></p>
                        <p class="item"><span>14 : 20</span><span style="margin-left:.4rem;">颁发证书</span></p>
                        <p class="item"><span>15 : 00</span><span style="margin-left:.4rem;">优秀学员代表讲话</span></p>
                        <p class="item"><span>15 : 15</span><span style="margin-left:.4rem;">入学仪式</span></p>
                        <p class="item"><span>15 : 40</span><span style="margin-left:.4rem;">全员合影</span></p>
                        <p class="item"><span>16 : 00</span><span style="margin-left:.4rem;">活动结束</span></p>
                        <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                    </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="past" :class="{'IOSpast':isIOS}">
                        <p>&emsp;&emsp;2018年12月、2019年1月，在广州总部校区举办了2届隆重的全额奖学金颁奖典礼，获奖学员相聚于此，带上家人好友，走过红毯，在颁奖台上领取属于自己的荣耀，并在许愿树下许下对新学期的美好愿望，共同分享喜悦，感受人生高光时刻。</p>
                        <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_4.png" alt=""></div>
                        <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                    </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="abstract" :class="{'IOSabstract':isIOS}">
                        <p>&emsp;&emsp;远智教育成立至今，以严谨规范的办学宗旨得到了高校的高度认同，先后获得二十多所高校授权成为高校校外教学点，并与数所高校建立战略合作关系，现已在广东建有高校校外教学点19座，培养了数万名优秀毕业生，在读学员近10万人，学员转介绍率高达70%以上。</p>
                        <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_1.png" alt=""></div>
                        <img src="../../../assets/image/active/fullScholarshipInviation/topIcon.png" alt class="topIcon" />
                    </div>
                </swiper-slide>
                <swiper-slide class="swiper-item">
                    <div class="road">
                        <p>&emsp;&emsp;远智教育秉承“帮助每个人更上进”的使命，立志成为“上进文化的引领者”。在帮助学员获得学历文凭和能力提升的同时，我们更注重帮助学员建立上进的价值观，养成上进的行为习惯，从而影响身边更多的人成长为上进青年。</p>
                        <div class="photo"><img src="../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_icon_2.png" alt=""></div>
                        <div class="shareBtn" @click="guide"><img src="../../../assets/image/active/fullScholarshipInviation/shareBtn.png" alt=""></div>
                        <div class="lead" v-if="show" @click="show=false;">
                            <img src="../../../assets/image/active/fullScholarshipInviation/share.png" alt="">
                        </div>
                    </div>
                </swiper-slide>
            </swiper>
        </template>
    </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import {isIOS,isWeixin} from '../../../common';
export default {
    data() {
        return {
            swiperOption: {
                direction: "vertical",
                slidesPerView: 1,
                mousewheel: true
            },
            show:false,
            isIOS:false,
        };
    },
    created(){
        this.IOS();
    },
    mounted(){
        //页面访问次数
        MtaH5.clickStat("wechatfullscholarshipshare");
        window.sessionStorage.setItem("regOrigin",'13');
    },
    methods: {
        guide(){
            //点击分享按钮次数
            MtaH5.clickStat("wechatclicksharebtn");
            this.show=true;
        },
        IOS(){
            if(isIOS()){
                var scrWidth=window.document.documentElement.getBoundingClientRect().width;
                var scrheight=window.screen.height;
                if((scrWidth===375&&scrheight===667)||(scrWidth===414&&scrheight===736)||scrheight<=667){
                    this.isIOS=true;
                }
            }
        },
        downApp() {
            window.location.href ="https://sj.qq.com/myapp/detail.htm?apkName=cn.yzou.yzxt";
        },
    },
    components: {
        swiper,
        swiperSlide,
    }
};
</script>
<style lang="less" scoped>
.main {
  position: relative;
  height: 100%;
  .swiper-box {
    width: 100%;
    height: 100%;
    transform:translate3d(0,0,0);
  }
}
.swiper-item{
    width: 3.75rem;
    height: 100vh;
    transform:translate3d(0,0,0);
    position: relative;
    img{
        width: 100%;
        height: 100%;
    }
    .index{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_1.png') no-repeat;
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        p{
            position: absolute;
            bottom: .25rem;
            width: 3.75rem;
            text-align: center;
            color: #eec08a;
        }
    }
    .invitation{
        position: relative;
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_2.png') no-repeat;
        .detail{
            text-align: justify;
            margin-bottom: .1rem;
        }
        .sponsor{
            text-align: right;
            margin-top: .05rem;
            
        }
        .state{
            position: absolute;
            bottom: 1.12rem;
            left: 0;
            width: 3.75rem;
            text-align: center;
            font-size: .12rem;
            
        }
    }
    .abstract{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_3.png') no-repeat;
        .photo{
            margin-top: .3rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .road{
        position: relative;
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_4.png') no-repeat;
        .photo{
            margin-top: .4rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .shareBtn{
            width: .25rem;
            height: .25rem;
            position: absolute;
            right: .4rem;
            top: .4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .past{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_5.png') no-repeat;
        .photo{
            margin-top: .4rem;
            width: 2.66rem;
            height: 2.4rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
    }
    .meeting{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_6.png') no-repeat;
        .liveShow{
            color: #FFFFFF;
            width: 100%;
            overflow: hidden;
            margin-top: .3rem;
            .liveCode{
                width: .98rem;
                height: .98rem;
                float: left ;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
            .text{
                width: 1.4rem;
                margin-left: .2rem;
                padding-top: .2rem;
                float: left ;
                p{
                    font-size: .18rem;
                }
                span{
                    font-size: .12rem;
                }
            }
        }
        .way{
            margin-top: .34rem;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .step{
            text-align: center;
            font-weight: bold;
            color: #FFFFFF;
            margin-top: .1rem;
        }
        .btnDown{
            margin-top: .3rem;
            width: 2.67rem;
            height: .5rem;
            text-align: center;
            line-height: .5rem;
            color: #FFFFFF;
            border-radius: .25rem;
            background: linear-gradient(180deg, #FFCF6F 0%, #FFA72A 100%);
        }
    }
    .process{
        background: url('../../../assets/image/active/fullScholarshipInviation/fullScholarshipInviation_10.jpg') no-repeat;
        .item{
            margin-left: .5rem;
            margin-top: .1rem;
        }
    }
    .invitation,.abstract,.road,.past,.meeting,.process{
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        padding: 1.8rem .54rem 0 .54rem;
        font-size: .15rem;
        color: #eec08a;
        text-align: justify;
    }
}
.lead{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    img{
        width: 2.2rem;
        height: 3.07rem;
        margin-top: 1rem;
        margin-left: .95rem;
    }
}
.IOSinvitation,.IOSabstract,.IOSroad,.IOSpast{
    padding-top: 1.4rem !important;
}
.IOSmeeting{
    padding-top: 1.3rem !important;
}
.topIcon {
    position: absolute;
    width: 0.35rem !important;
    height: 0.21rem !important;
    bottom: 0.45rem;
    left: 1.7rem;
    animation: moveup 1.2s ease-out forwards infinite;
}
@keyframes moveup {
  from {
    transform: translateY(0);
    opcity: 1;
  }
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}
</style>