<template>
  <button class="yz-ck-btn" @click='click'>
    <slot>{{text}}</slot>
  </button>
</template>

<script>
export default {
  data () {
    return {

    };
  },
  props: {
    text: String,
  },
  methods: {
    click () {
      this.$emit('click');
    },
  },
};
</script>

<style lang="less" scoped>
.yz-ck-btn {
  min-width: 0.9rem;
  height: 0.3rem;
  padding: 0 0.15rem;
  text-align: center;
  color: #d92424;
  font-size: 0.14rem;
  font-weight: 600;
  background: #f9d65b;
  box-shadow: 0px 0px 5px 0px rgba(255, 255, 255, 0.61);
  border-radius: 0.25rem;
  line-height: 1;
}
</style>

