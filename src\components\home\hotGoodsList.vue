<template>
  <div class="home-list">
    <div class="bd bc-w clearfix" v-infinite-scroll="loadMore" infinite-scroll-disabled="isLoading" infinite-scroll-distance="50">
      <router-link class="item h1"
                   v-for="item in list"
                   :key="item.salesId"
                   :to="{name:'exchange',params:{id:item.salesId},query:{salesType:1}}">
        <div class="pic"><img class="img" :src="item.annexUrl|imgBaseURL"></div>
        <div class="txt">
          <status class="static" :status="item.salesStatus"></status>
          <div class="tit row2">{{item.salesName}}</div>
          <price class="mt" :salesPrice="item.salesPrice" :originalPrice="item.originalPrice"></price>
          <div class="fsc gap1" v-if="item.salesStatus==='2'">开始时间:{{item.startTime|formatDate('yyyy.MM.dd hh:mm')}}</div>
          <div class="fsc gap1" v-else-if="item.salesStatus==='3'">剩余:<span class="fc">{{item.goodsCount}}</span>个</div>
          <!--<div class="fsc" v-else>库存:{{item.goodsCount}}个</div>-->
        </div>
      </router-link>
      <load-bar :isLoading="isLoading" :allLoaded="allLoaded" :total="list.length"></load-bar>
    </div>
  </div>
</template>

<script>
  import status from '@/components/status'
  import price from '@/components/price'
  import loadBar from '@/components/loadBar'

  export default {
    props: ['salesType', 'active'],
    data() {
      return {
        pageNum: 0,
        pageSize: 10,
        goodsType: '1',
        list: [],
        isLoading: true,
        allLoaded: false
      }
    },
    created() {
      this.loadMore();
    },
    methods: {
      getGoodsList: function () {
        this.isLoading = true;
        let data = {
          salesType: this.salesType,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          goodsType: this.goodsType
        };
  
        this.$http.post('/gs/goodsList/1.0/', data).then(res => {
          if (res.code !== '00') return;
          
          const datas = res.body || {};
          this.list.push(...(datas.list || []));
  
          this.$nextTick(() => {
            this.allLoaded = datas.list.length === 0;
            this.isLoading = this.allLoaded;
          });
        });
      },
      loadMore: function () {
        if (!this.active) return;
        this.pageNum++;
        this.getGoodsList();
      },
      reset: function () {
        this.pageNum = 1;
        this.allLoaded = false;
        this.list = [];
        this.getGoodsList();
      }
    },
    watch: {
      goodsType: function () {
        this.reset();
      },
      active: function () {
        this.isLoading = !(this.active && !this.allLoaded);
      }
    },
    components: {status, price, loadBar}
  }
</script>
