<template>
  <div class="uploader">
    <div @click="handleClick">
      <slot><i class="icon i-upload"></i></slot>
    </div>
    <input class="upload-input"   name="fileUpload" type="file" :accept="accept" ref="input" @change="onFileChange">
    <div style="opacity: 0;position: absolute;">
      <video  :src="url" controls="controls" ref="video" v-if="url"></video>
    </div>

  </div>
</template>

<script>
  import OSS from "ali-oss"
  export default {
    name: 'fileUpload',
    props: {
      target: {
        type: String,
        default: '/uploadFile'
      },
      limit: {
        type: Number,
        default: 0
      },
      max: {
        type: Number,
        default: 1
      },
      count: {
        type: Number,
        default: 0
      },
      accept: {
        type: String,
        default: 'image/gif,image/jpg,image/jpeg,image/bmp,image/png'
      },
      isCompress: {
        type: Boolean,
        default: true
      },
      udType:{
        type:String
      }
    },
    created(){

    },
    data () {
      return {
        file: null,
        type: '',
        maxWidth: 1280,
        maxSize: 1024 * 1024,  // 大于1MB的图片要压缩再上传
        client:null,
        suffix:'',
        fileData:'',
        url:'',
        showPic:'',
        duration:0
      }
    },
    methods: {
      uuid() {
        var s = [];
        var hexDigits = "0123456789abcdef";
        for (var i = 0; i < 36; i++) {
          s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10),1);
        }
        s[14] = "4";
        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
        s[8] = s[13] = s[18] = s[23] = "";
        var uuid = s.join("");
        return uuid;
       },
      initOss(bucket,stsTokenInfo){
        this.client = new OSS({
          region: 'oss-cn-shenzhen',
          accessKeyId: stsTokenInfo.AccessKeyId,
          accessKeySecret:stsTokenInfo.AccessKeySecret,
          stsToken:stsTokenInfo.SecurityToken,
          bucket: bucket
        });
      },
      getStsToken(){
        this.$http.post('/proxy/getJsStsToken/1.0/').then(res=>{
          if(res.code=='00'){
            this.initOss(res.body.bucket,res.body.stsTokenInfo)
          }
        })
      },
      async putBlob () {
        this.$indicator.open()
        var that=this;
        const filename=this.uuid()+this.suffix;
        try {
          let result = await this.client.put('upDeclaration/'+filename, new Blob([this.file], {type: 'text/plain'}));
          if(this.udType=='video'){
            let base64=this.dataURItoBlob(this.showPic);
            var picname=this.uuid()+ '.'+this.showPic.split(',')[0].split(':')[1].split(';')[0].split("/")[1];
            var showPic=await this.client.put('upDeclaration/pic/'+picname,base64);
          }
          if(result.res.status==200){
            //this.emitter('input','upDeclaration/'+filename)
            var data={};
            if(this.udType=='audio'){
              data={resourcesUrl:'upDeclaration/'+filename,udType:1,resourcesTime:parseInt(this.duration)}
            }else{
              data={resourcesUrl:'upDeclaration/'+filename,udType:2,showPic:'upDeclaration/pic/'+picname}
            }
            this.$http.post('/mkt/publishUpDeclaration/1.0/',data).then(res=>{
              this.$indicator.close()
              if(res.code=='00'){
                that.$emit('publish',res.body)
              }
            })
          }
        } catch (e) {
          console.log(e);
        }
      },
      handleClick() {
        if (this.count > 0 && this.count >= this.max) {
          this.$modal({message: `最多只能上传${this.max}张图片`, icon: 'warning'});
          return;
        }
        this.$refs.input.click();
      },
      emitter(event, data) {
        this.$emit(event, data)
      },
      onFileChange(e) {
        e=e || window.event;
        let target=e.target||e.srcElement;
        let files = target.files ||e.dataTransfer.files;
        this.suffix = target.value.substr(target.value.indexOf("."));
        if (!files.length) {
          this.$modal({message:'图片没有选中哦'})
          return;
        }
        this.file = files[0];
        this.type = (this.file.type || '').toLowerCase();

        this.getStsToken()
        // if (!this.type || !this.type.includes('image/')) {
        //   this.$modal({message: '请选择正确的图片文件格式！', icon: 'warning'});
        //   return;
        // }
        if (!['image/png', 'image/gif'].includes(this.type)) {
          this.type = 'image/jpeg';
        }

        // this.$indicator.open('上传中...');
        e.srcElement.value = "";
        this.parse();
      },
      // 读取文件
      parse: function () {
        let fr = new FileReader();
        fr.onloadend = () => {
          this.fileData=this.dataURItoBlob(fr.result)
          console.log(this.file);
          var url = URL.createObjectURL(this.file)

          var that=this;
          if(this.udType=='audio'){
            var audioElement = new Audio(url);
            var duration;
            audioElement.addEventListener("loadedmetadata", function (_event) {
              duration = audioElement.duration;
              if(duration<10||duration>60){
                this.$modal({message:'上传的内容要在10s~60s之间哦，请重新选择合适文件！',icon:'warning'})
                return;
              }
              that.duration=duration
              that.$emit('resource',url,duration,that.udType)
            });
          }else{
            this.url=url;
              this.$nextTick(()=>{
                this.$refs.video.addEventListener("canplay", function(_event) {
                  var canvas = document.createElement("canvas");
                  canvas.width = that.$refs.video.videoWidth;
                  canvas.height = that.$refs.video.videoHeight;
                  that.videoDuration=_event.duration
                  console.log(_event.duration);
                  if(_event.duration<10||_event.duration>60){
                    this.$modal({message:'上传的内容要在10s~60s之间哦，请重新选择合适文件！',icon:'warning'})
                    return;
                  }
                  canvas.getContext("2d").drawImage(that.$refs.video, 0, 0, canvas.width, canvas.height);
                  that.showPic=canvas.toDataURL("image/png")
                  that.$emit('resource',url,canvas.toDataURL("image/png"),that.udType)
                })
              })

          }

        };
        fr.readAsDataURL(this.file);
      },
      // 上传进度
      uploadProgress(oEvent) {
        if (oEvent.lengthComputable) {
          let percentComplete = Math.round(oEvent.loaded * 100 / oEvent.total)
          this.emitter('progress', percentComplete)
        } else {
          this.emitter('progress', false)
        }
      },
      // base64转blob
      dataURItoBlob(dataURI) {
        let byteString = atob(dataURI.split(',')[1]);
        let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
        let ab = new ArrayBuffer(byteString.length);
        let ia = new Uint8Array(ab);
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        return new Blob([ab], {type: mimeString});
      }
    }
  }
</script>

<style lang="less" scoped>
  .uploader{
    .upload-input{ display:none; }
    //.i-upload{ width:1.152rem; height:1.152rem; background-image:url(../assets/image/i-upload.png); }
  }
</style>
