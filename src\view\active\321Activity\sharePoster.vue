<template>
  <div class="main_warp">
  <div class="shareImg_wrap">
   <div style="opacity: 0" v-show="show">
     <div class="shareImg" ref="shareImg">
       <img class="advance" src="../../../assets/image/active/321Activity/advance.png" alt="">
       <div class="num">
         已有{{joinNum}}人参与</div>
       <div class="info_detail">
         <img :src="imgUrl" alt="" v-if="imgUrl" ref="headImg">
         <img src="../../../assets/image/active/321Activity/xiaozhi.png" alt=""  ref="shareImgdefault" v-else>
         <div class="txt">
           <p>{{info.nikeName||''|b64DecodeUnicode}}</p>
           <h2>我是第{{info.udNum}}位上进者</h2>
         </div>
       </div>
       <div class="audio" v-if="info.udType=='1'" >{{info.resourcesTime}}
         <img src="../../../assets/image/active/321Activity/''@2x.png" alt="">
         <img src="../../../assets/image/active/321Activity/audio_icon.png" alt="">
         <!--<audio :src="info.resourcesUrl|imgBaseURL" ref="media"></audio>-->
       </div>
       <div class="video" v-if="info.udType=='2'">
           <div class="bg"></div>
           <img :src="imgUrl2" alt="" ref="videoImg">
           <img src="../../../assets/image/active/321Activity/play.png" alt="">
            <!--<video :src="info.resourcesUrl|imgBaseURL" ref="video"></video>-->
       </div>
       <div class="code_wrap">
         <div class="txt">
           <img src="../../../assets/image/active/321Activity/321_advance.png" alt="">
           <p v-if="info.udType=='2'">想看我拍了什么吗？<br />
             快来扫码观看！</p>
           <p v-if="info.udType=='1'">
             想听听我说了什么吗？<br />快来扫码听听！
           </p>
         </div>
         <div id="qrcode" ref="qrcode"></div>
       </div>
     </div>
   </div>

  </div>
    <div class="img_wrap" v-if="imgBase64">
      <div class="bg" @click="cancel"></div>
      <img :src="imgBase64" alt="">
      <p>长按保存图片</p>
    </div>
  </div>
</template>

<script>
  import html2canvas from "html2canvas"
  import {imgPosterBaseURL} from "../../../config";
  import QRCode from "qrcodejs2";
  export default {
    props:["headImg","joinNum","info"],
    data(){
      return{
        imgUrl:'',
        day:'',
        month:'',
        weekday:'',
        insistDay:1,
        shareImgUrl:'',
        showC:true,
        zhimi:5,
        timer:null,
        defaultFlag:false,
        showB:false,
        scholarship:32,
        shareLink:'',
        inviteId:'',
        qrcode:'',
        inviteUrl:'',
        change:false,
        imgBase64:'',
        show:true,
        imgUrl2:'',
        ShortUrlId:''
      }
    },
    created(){

          this.link = `${window.location.origin}/active/321Activity/share?udId=${this.info.udId}`;
        this.inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || '')).replace(/ /g, '+');
    },
    mounted(){
      this.getInfo();

      // this.$nextTick(()=>{
      //   if(this.info.udType=='2'){
      //     document.getElementsByTagName('video')[0].addEventListener('canplay',()=>{
      //       this.getInfo();
      //     })
      //   }
      // })
    },
    methods: {
      useqrcode(){
          // this.inviteUrl = `${window.location.origin}/active/321Activity/share?udId=${this.info.udId}&ykAuthtoken=${this.storage.getItem('ykAuthtoken')}`;
         this.inviteUrl = `${window.location.origin}/active/321Activity/urlTransfer?u=${this.ShortUrlId}`
        let qrcode = new QRCode('qrcode', {
          width: 71,  // 设置宽度
          height: 71, // 设置高度
          text: this.inviteUrl,
          correctLevel:QRCode.CorrectLevel.L,
          backgoround: '#fff'
        })
      },

      transferShortUrl(){
        let url = `${window.location.origin}/active/321Activity/share?udId=${this.info.udId}&ykAuthtoken=${this.storage.getItem('ykAuthtoken')}`
        this.$http.post('/proxy/commitShortUrl/1.0/',{url:url}).then((res=>{
            const {code,body} = res;
            if(code !=='00') return;
            this.ShortUrlId = body;
            this.$nextTick(()=>{
                this.useqrcode();
            })
        }))
      },
      cancel(){
        this.$emit('cancel')
      },
      getInfo() {
          this.$indicator.open();
          this.$nextTick (function () {
            // that.useqrcode();
            that.transferShortUrl();
          })
            var that=this;
            if(this.info.udType==1){
                this.loadHead()
            }else{
              this.main(imgPosterBaseURL+this.info.resourcesUrl+'?x-oss-process=video/snapshot,t_1000,f_jpg',(base64)=>{
                this.imgUrl2=base64
                  this.$nextTick(()=>{
                    this.$refs.videoImg.onload=function(){
                      that.loadHead()
                    }
                  })
              })
            }
      },
      loadHead(){
        var that=this;
        this.main(this.info.headImg,(base64)=> {
          this.imgUrl = base64;
          this.$nextTick(()=>{
            if(this.$refs.headImg){
              this.$refs.headImg.onload=function(){
                that.share()
              }
            }else{
              this.$refs.shareImgdefault.onload=function(){
                that.share()
              }
            }
          })
        });
      },
      share() {
        var that=this;

        console.log(1);
        //创建一个新的canvas
        var canvas2 = document.createElement("canvas");
        let _canvas = document.querySelector('.shareImg');
        console.log(_canvas);
        var w = parseInt(window.getComputedStyle(_canvas).width);
        var h = parseInt(window.getComputedStyle(_canvas).height);
        //将canvas画布放大若干倍，然后盛放在较小的容器内，就显得不模糊了
        canvas2.width = w * 3;
        canvas2.height = h * 3;
        canvas2.style.width = w + "px";
        canvas2.style.height = h + "px";
        //可以按照自己的需求，对context的参数修改,translate指的是偏移量
        //  var context = canvas.getContext("2d");
        //  context.translate(0,0);
        var context = canvas2.getContext("2d");
        context.scale(2, 2);

        if(this.info.udType=='2'){
         // this.$refs.video.addEventListener('loadedmetadata',()=>{
            html2canvas(document.querySelector('.shareImg'), {
              useCORS: true,
              width: document.querySelector('.shareImg').offsetWidth,
              height: document.querySelector('.shareImg').offsetHeight,
            }).then(function (canvas) {
              that.$indicator.close()
              //document.querySelector('#share').src=canvas.toDataURL();
               that.imgBase64=canvas.toDataURL()
               that.show=false
            });
       //   })
        }else{
          html2canvas(document.querySelector('.shareImg'), {
            useCORS: true,
            width: document.querySelector('.shareImg').offsetWidth,
            height: document.querySelector('.shareImg').offsetHeight,
          }).then(function (canvas) {
            that.$indicator.close()
            //document.querySelector('#share').src=canvas.toDataURL();
            that.imgBase64=canvas.toDataURL()
             that.show=false
          });
        }
        this.hide=true
      },
      getBase64Image(img) {
        var canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        var ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, img.width, img.height);
        try {
          var i = 1/0;
          var dataURL = canvas.toDataURL({format:'png'});  // 可选其他值 image/jpeg
        }catch (e) {
          console.log(e);
          this.showB=true;
          this.defaultFlag=true;
        }
        return dataURL;
      },
      main(src, cb) {
        var image = new Image();
        image.crossOrigin = "Anonymous";  // 支持跨域图片
        image.src = src ; // 处理缓存
        var that=this;
        image.onload = function () {
          let ratio=1;
          image.width=image.width*ratio;
          image.height=image.height*ratio;
          var base64 = that.getBase64Image(image);
          cb && cb(base64);
        }
        image.onerror=function(){
          that.defaultFlag=true;
          that.$nextTick(()=>{
              that.share()
          })
        }
      },
    },
    components:{QRCode}
  }
</script>

<style scoped lang="less">
/*.main_warp /deep/  #qrcode{*/
  /*img {*/
    /*width: .71rem;*/
  /*}*/
/*}*/

  .bg{
    width: 100%;
    max-width: 640px;
    height: 100%;
    background-color: #000;
    opacity: .6;
    overflow: hidden;
    position: fixed;
    top:0;
    z-index: 2003;
    left:0;
  }
  .shareImg_wrap {
    position: fixed;
    width: 100%;
    z-index: 3000;
    /*top:.13rem;*/
    /*left:50%;*/
    top:0;
    left:0;
    /*margin-left: -1.73rem;*/
    height: auto;
    min-height: 100vh;
    .shareImg {
      width: 3.47rem;
      max-width: 608px;
      height: auto;
      margin: 0 auto;
      color: white;
      position: relative;
      overflow: hidden;
      z-index: 3000;
      background-color: rgba(194, 22, 16, 1);
      background-image: url("../../../assets/image/active/321Activity/shareBg.png");
      background-size: 100% 100%;
      img {
        width: 100%;
      }
      .audio{
        width: 2.24rem;
        height: .32rem;
        border-radius: .04rem;
        background-color: rgba(132, 201, 102, 1);
        font-size: .16rem;
        line-height: .32rem;
        padding-left: .13rem;
        margin: .55rem auto .55rem;
        color: rgba(51, 51, 51, 1);
        img:first-of-type{
          width: .06rem;
          height: .06rem;
          margin-top: .04rem;
        }
        img:last-of-type{
          width: .11rem;
          height: .14rem;
          margin-top: .1rem;
          margin-left: .05rem;
        }
      }
      .video{
        width: 2.48rem;
        height: 3.57rem;
        position: relative;
        margin: .16rem auto;
        border: 2px solid RGBA(253, 187, 118, 1);
        border-radius: .1rem;
        overflow: hidden;
        .bg{
          width: 100%;
          height: 100%;
          position: absolute;
          left:0;
          top:0;
          background-color: rgba(0, 0, 0, .1);
          z-index: 2;
        }
        video{
          width: 100%;
          height: 100%;
        }
        img:first-of-type{
          position: absolute;
          width: 100%;
          height: 100%;
          border-radius: .1rem;
        }
        img:last-of-type{
          position: absolute;
          width: .55rem;
          height: .55rem;
          left: 0;
          right: 0;
          top:0;
          bottom:0;
          margin: auto;
        }
      }
      .advance{
        width: .66rem;
        height: .24rem;
        position: absolute;
        left:.45rem;
        top:.3rem
      }
      .num{
        color: white;
        font-size: .12rem;
        line-height: .17rem;
        position: absolute;
        top: .34rem;
        right:.46rem
      }
      .info_detail{
          margin-top: .68rem;
          height: auto;
          overflow: hidden;
          img{
            float: left;
            margin-left: .45rem;
            width: .48rem;
            height: .48rem;
            border-radius: 50%;
          }
          .txt{
            float: left;
            margin-left: .08rem;
            p{
              font-size: .12rem;
              margin-top: .07rem;
              color: white;
            }
            h2{
              font-weight: bold;
              color: rgba(253, 188, 0, 1);
            }
          }
        }
      .code_wrap{
        height: auto;
        overflow: hidden;
        margin-left: .44rem;
        margin-top: .18rem;
        margin-bottom: .42rem;
        .txt{
          float: left;
          img{
            width: 1.5rem;
            height: .16rem;
            margin-bottom: .17rem;
          }
          p{
            font-size: .12rem;
            color: rgba(255, 232, 226, 1);
            line-height: .17rem;
          }
        }
        #qrcode{
          float: right;
          margin-right: .47rem;
          width: .71rem;
          height: .71rem;
        }
      }
    }

    a {
      /*position: fixed;*/
      display: block;
      /*left:50%;*/
      /*bottom:.16rem;*/
      width: 100%;
      /*margin-left: -1.13rem;*/
      margin: .2rem auto 0;
      height: .26rem;
      font-size: .16rem;
      /*z-index: 3000;*/
      text-align: center;
      line-height: .26rem;
      color: white
    }
  }
  .showImg1{
    position: relative;

    z-index: 3002;
    width: 100%;
    height: 100%;
    .wrap_img{
      top:0;
      right:0;
      left:0;
      bottom:0;
      margin: auto;
      position: absolute;
    }
    img{
      width: 3rem;
    }
    p{
      color: white;
      text-align: center;
      margin-top: .2rem;
    }
  }
  .img_wrap{
    position: fixed;
    width: 100%;
    height:100%;
    overflow: hidden;
    left:0;
    right: 0;
    top:0;
    bottom:0;
    margin: auto;
    z-index:9999;
    .bg{
      position: fixed;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.8);
      z-index: 9999;
    }
    img{
      width: 2.5rem;
      position: absolute;
      left:0;
      right: 0;
      top:0;
      bottom:0;
      margin: auto;
      z-index:99999;
    }
    p{
      color: white;
      width: 100%;
      text-align: center;
      position: absolute;
      left:0;
      bottom:.6rem;
      margin: auto;
      z-index:99999;
    }
  }
</style>
