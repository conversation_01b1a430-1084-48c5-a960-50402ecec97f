.yz-618-activity{
  min-height:100vh;
  background: #FF4531;
  padding-bottom: 0.2rem;
  .act-header{
    height: 7.24rem;
    background: url(../../../assets/image/active/618Activity/bg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    color: #fff;
    padding-top: 1.87rem;
  }

  .time-txt{
    font-size: 0.16rem;
    text-align: center;
    margin-bottom: 0.02rem;
    b{
      position: relative;
      display: inline-block;
      &::before, &::after{
        content: '';
        background: #fff;
        width: 0.8rem;
        height: 1px;
        position: absolute;
        top: 50%;
        transform: translate(-50%);
      }
      &::before{
        left: -0.54rem;
      }
      &::after{
        right: -1.34rem;
      }
    }
  }
  .time{
    text-align: center;
  }
  .act-rule{
    position: absolute;
    bottom: 1.08rem;
    right: 0;
    border-radius: 100px 0 0 100px;
    z-index: 2;
    font-size: 0.12rem;
    font-weight: 400;
    padding: 0 0.1rem;
    line-height: 1;
    height: 0.25rem;
    color: #fff;
    background: rgba(0, 0, 0, 0.2);
  }
  .yellow-box{
    margin: 0.07rem auto 0;
    width: 3.15rem;
    background: linear-gradient(180deg, #FCD302 0%, #FCA100 100%);
    box-shadow: 0px 0.02rem 0.04rem 0px #FD7400;
    border-radius: 100px;
    height: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 0.08rem;
    padding-left: 0.3rem;
    font-size: 500;
    .red{
      color: #FF1E1E;
    }
    button{
      background: none;
      border: 0;
      text-align: center;
      line-height: 1;
      width: 1.2rem;
      border-radius: 100px;
      height: 0.35rem;
      background: linear-gradient(180deg, #FCB900 0%, #FC8C00 100%);
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2), 0px 1px 4px 0px rgba(253, 99, 0, 0.6);
    }
    &.nologin{
      // border: 1px solid #fff;
      box-shadow: none;
      background: rgba(0, 0, 0, 0.4);
    }
  }
  .winner-list{
    background: #FEE7CD;
    border-radius: 0.1rem;
    position: relative;
    z-index: 2;
    margin: 0.1rem 0.1rem 0.2rem;
    .title{
      // background: url(../../../assets/image/active/618Activity/list.png) no-repeat;
      // background-size: 100% 100%;
      height: 0.4rem;
      background: #FFDFBF;
      position: relative;
      border-radius: 0.1rem 0.1rem 0 0 ;
      .img{
        width: 3.06rem;
        height: 0.83rem;
        position: absolute;
        left: 0.33rem;
        top: -0.43rem;
      }
      // margin-top: -0.25rem;
    }
    .no-data{
      padding: 0.3rem 0;
      text-align: center;
      font-size: 0.13rem;
      color: #000;
    }
    .list{
      padding-bottom: 0.2rem;
      li{
        display: flex;
        justify-content: space-between;
        font-size: 0.13rem;
        height: 0.28rem;
        line-height: 0.28rem;
        padding: 0 0.15rem;
        &:nth-of-type(2n) {
          background: #FFDFBF;
        }
        .left{
          width: 2.27rem;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 0.28rem;
        }
        .right{
          color: rgba(23, 6, 6, 0.6);
          white-space: nowrap;
        }
      }
    }
    .btns{
      padding: 0 0.23rem 0.15rem;
      display: flex;
      justify-content: space-between;
    }
  }
  .y-swiper{
    &.invite{
      top: 1.34rem;
    }
  }
  .swiper-murquee1{
    left: 0.43rem;
    top: 2.31rem;
    &.invite{
      top: 2.91rem;
    }
  }
  .swiper-murquee2{
    top: 2.6rem;
    &.invite{
      top: 3.2rem;
    }
  }
  .pic-box{
    margin: 0 0.1rem;
    &.before{
      margin: 0.16rem 0.1rem 0;
    }
    img{
      width: 100%;
      float: left;
    }
  }
  .back-top{
    text-align: center;
    font-size: 0.14rem;
    line-height: 0.2rem;
    padding: 0.1rem 0 0.2rem;
    color: #fff;
    .arrow{
      background: url(../../../assets/image/active/618Activity/arrow-up.png) no-repeat;
      background-size: 100% 100%;
      width: 0.18rem;
      height: 0.18rem;
      margin: 0 auto;
    }
  }
  .more-activity{
    background: #fff;
    border-radius: 0.1rem;
    z-index: 2;
    margin: 0 0.1rem;
    .title-box{
      background: rgba(252, 183, 1, 0.05);
      text-align: center;
      padding-top: 0.12rem;
      height: 0.53rem;
      img{
        width: 2.18rem;
      }
    }
    .list{
      li{
        float: left;
        width: 50%;
        padding: 0.15rem;
        position: relative;
        font-size: 0.12rem;
        color: #746A6A;
        &.line::after{
          content: '';
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: -0.01rem;
          width: 0.02rem;
          height: 0.3rem;
          background: #EDEBEB;
        }
        &:nth-of-type(2n) {
          padding-left: 0.1rem;
          img{
            right: 0.15rem;
          }
        }
        .t1{
          font-size: 0.13rem;
          color: #453838;
          font-weight: 600;
        }
        img{
          position: absolute;
          top: 0.1rem;
          right: 0.08rem;
          width: 0.5rem;
          height: 0.5rem;
        }
      }

    }
  }
  .yellow-table{
    width: 100%;
    &.one-line{
      li{
        padding: 0 0.15rem;
      }
    }
    li{
      color: #453838;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #fff;
      background: rgba(252, 183, 1, 0.05);
      font-size: 0.12rem;
      min-height: 0.4rem;
      &.head{
        background: rgb(254, 247, 223);
        display: block;
        text-align: left;
        font-weight: 600;
        font-size: 0.14rem;
        line-height: 0.4rem;
      }
      .span{
        width: 0.55rem;
        min-height: 0.4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-size: 0.12rem;
        &:not(:last-child){
          border-right: 1px solid #fff;
        }
        &.w1{
          flex: 1;
        }
        &.w2{
          width: 0.9rem;
        }
      }
    }
    thead td{
      background: rgba(252, 183, 1, 0.1);
      color: #453838;
      font-size: 0.14rem;
      padding: 0.09rem 0;
      font-weight: 500;
      vertical-align: middle;
    }
    tbody td{
      background: rgba(252, 183, 1, 0.05);
      font-size: 0.12rem;
      padding: 0.1rem 0;
      vertical-align: middle;
      border-color: #fff;
    }
    tbody.small td{
      padding: 0.07rem 0;
    }
    .w1{
      width: 0.55rem;
    }
    .w2{
      width: 0.9rem;
    }
    .w-half{
      width: 50%;
    }
  }

  .share-black{
    position: fixed;
    z-index: 9999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    animation-duration: 0.3s;
    img{
      width: 0.7rem;
      height: 0.68rem;
      margin-top: 0.03rem;
      margin-right: 0.16rem;
    }
    .text{
      color:#fff;
      text-align: center;
      margin-top: 0.6rem;
      font-size: 0.13rem;
      margin-right: -0.1rem;
    }
  }
}

