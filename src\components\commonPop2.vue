<template>
  <transition name="slide">
    <div class="commonPop" v-if="show">
      <div class="commonPop-box">
        <div class="commonPop-box-in">
          <div class="commonPop-content">
            <slot>123</slot>
          </div>
          <div class="commonPop-title">
            <span v-if="showClose" @click="close">取消</span>
            <span @click="confirm" :class="{'percent':!showClose}">{{confirmText}}</span>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
  export default {
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      showClose: {
        type: Boolean,
        default: true
      },
      confirmText: {
        type: String,
        default: '确定'
      },
    },
    data() {
      return {
        show: this.isShow
      }
    },
    methods: {
      open: function () {
        this.show = true;
      },
      close: function () {
        this.show = false;
      },
      confirm: function () {
//        this.show = false;
        this.$emit('submit')
      }
    }
  }
</script>

<style lang="less" scoped>
  @import "../assets/less/variable";

  .commonPop {
    z-index: 2023;
    /*z-index: 2030;*/
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color:rgba(0, 0, 0, .5);
    transition:opacity .2s ease;
    .commonPop-box {
      z-index: 2031;
      width: 100%;
      height: 2.05rem;
      position: fixed;
      padding:0 .12rem;
      background: transparent;
      transition:all .3s ease;
      top: 30%;
      right: auto;
      bottom: 0;
      left: 0;
      .commonPop-box-in{
        background-color: #fff;
        padding: .18rem .15rem;
        border-radius: .04rem;
        width: 3.42rem;
        margin: 0 auto;
        .commonPop-title {
          width: auto;
          height: .44rem;
          margin-top: .12rem;
          span {
            display: inline-block;
            text-align: center;
            float: left;
            color: #666;
            border: 1px solid #EFEFEF;
            width: 1.5rem;
            line-height: .44rem;
            border-radius: .02rem;
            /*font-weight: 00;*/
            font-size: .17rem;
            &.percent{
              width: 100%;
            }
            &:last-child {
              background: @bgColor;
              float: right;
              color: #fff;
              /*margin-left: .12rem;*/
            }
          }
        }
        .commonPop-content {
          height: 1.2rem;
          overflow: hidden;
        }
      }
    }
  }

  .slide-enter, .slide-leave-active{
    opacity:0;
  }
  .slide-enter .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
    /*transform: translate3d(-50%, 0, 0);*/
  }
  .slide-enter-active .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
  }
  .slide-leave .commonPop-box{
    /*transform: scale(.5) translate3d(-50%, 0, 0);*/
  }
  .slide-enter .commonPop-box,.slide-leave-active .commonPop-box{
    transform: scale(.8);
    opacity:0;
  }
</style>
